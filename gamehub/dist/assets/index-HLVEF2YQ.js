function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
function qg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const l=Object.getOwnPropertyDescriptor(r,o);l&&Object.defineProperty(e,o,l.get?l:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const a of l.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerPolicy&&(l.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?l.credentials="include":o.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();function xp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Sp={exports:{}},Aa={},Ep={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var il=Symbol.for("react.element"),e0=Symbol.for("react.portal"),t0=Symbol.for("react.fragment"),n0=Symbol.for("react.strict_mode"),r0=Symbol.for("react.profiler"),o0=Symbol.for("react.provider"),l0=Symbol.for("react.context"),a0=Symbol.for("react.forward_ref"),i0=Symbol.for("react.suspense"),s0=Symbol.for("react.memo"),u0=Symbol.for("react.lazy"),Zc=Symbol.iterator;function c0(e){return e===null||typeof e!="object"?null:(e=Zc&&e[Zc]||e["@@iterator"],typeof e=="function"?e:null)}var Cp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},$p=Object.assign,kp={};function Gr(e,t,n){this.props=e,this.context=t,this.refs=kp,this.updater=n||Cp}Gr.prototype.isReactComponent={};Gr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Gr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Pp(){}Pp.prototype=Gr.prototype;function Cu(e,t,n){this.props=e,this.context=t,this.refs=kp,this.updater=n||Cp}var $u=Cu.prototype=new Pp;$u.constructor=Cu;$p($u,Gr.prototype);$u.isPureReactComponent=!0;var Jc=Array.isArray,Tp=Object.prototype.hasOwnProperty,ku={current:null},Np={key:!0,ref:!0,__self:!0,__source:!0};function _p(e,t,n){var r,o={},l=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(l=""+t.key),t)Tp.call(t,r)&&!Np.hasOwnProperty(r)&&(o[r]=t[r]);var i=arguments.length-2;if(i===1)o.children=n;else if(1<i){for(var s=Array(i),u=0;u<i;u++)s[u]=arguments[u+2];o.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)o[r]===void 0&&(o[r]=i[r]);return{$$typeof:il,type:e,key:l,ref:a,props:o,_owner:ku.current}}function d0(e,t){return{$$typeof:il,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Pu(e){return typeof e=="object"&&e!==null&&e.$$typeof===il}function f0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var qc=/\/+/g;function yi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?f0(""+e.key):t.toString(36)}function Yl(e,t,n,r,o){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(l){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case il:case e0:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+yi(a,0):r,Jc(o)?(n="",e!=null&&(n=e.replace(qc,"$&/")+"/"),Yl(o,t,n,"",function(u){return u})):o!=null&&(Pu(o)&&(o=d0(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(qc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",Jc(e))for(var i=0;i<e.length;i++){l=e[i];var s=r+yi(l,i);a+=Yl(l,t,n,s,o)}else if(s=c0(e),typeof s=="function")for(e=s.call(e),i=0;!(l=e.next()).done;)l=l.value,s=r+yi(l,i++),a+=Yl(l,t,n,s,o);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function xl(e,t,n){if(e==null)return e;var r=[],o=0;return Yl(e,r,"","",function(l){return t.call(n,l,o++)}),r}function p0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Be={current:null},Xl={transition:null},m0={ReactCurrentDispatcher:Be,ReactCurrentBatchConfig:Xl,ReactCurrentOwner:ku};function Mp(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:xl,forEach:function(e,t,n){xl(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return xl(e,function(){t++}),t},toArray:function(e){return xl(e,function(t){return t})||[]},only:function(e){if(!Pu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=Gr;J.Fragment=t0;J.Profiler=r0;J.PureComponent=Cu;J.StrictMode=n0;J.Suspense=i0;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=m0;J.act=Mp;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=$p({},e.props),o=e.key,l=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,a=ku.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(s in t)Tp.call(t,s)&&!Np.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&i!==void 0?i[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){i=Array(s);for(var u=0;u<s;u++)i[u]=arguments[u+2];r.children=i}return{$$typeof:il,type:e.type,key:o,ref:l,props:r,_owner:a}};J.createContext=function(e){return e={$$typeof:l0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:o0,_context:e},e.Consumer=e};J.createElement=_p;J.createFactory=function(e){var t=_p.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:a0,render:e}};J.isValidElement=Pu;J.lazy=function(e){return{$$typeof:u0,_payload:{_status:-1,_result:e},_init:p0}};J.memo=function(e,t){return{$$typeof:s0,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Xl.transition;Xl.transition={};try{e()}finally{Xl.transition=t}};J.unstable_act=Mp;J.useCallback=function(e,t){return Be.current.useCallback(e,t)};J.useContext=function(e){return Be.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Be.current.useDeferredValue(e)};J.useEffect=function(e,t){return Be.current.useEffect(e,t)};J.useId=function(){return Be.current.useId()};J.useImperativeHandle=function(e,t,n){return Be.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Be.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Be.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Be.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Be.current.useReducer(e,t,n)};J.useRef=function(e){return Be.current.useRef(e)};J.useState=function(e){return Be.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Be.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Be.current.useTransition()};J.version="18.3.1";Ep.exports=J;var p=Ep.exports;const q=xp(p),h0=qg({__proto__:null,default:q},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var v0=p,g0=Symbol.for("react.element"),y0=Symbol.for("react.fragment"),b0=Object.prototype.hasOwnProperty,w0=v0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,x0={key:!0,ref:!0,__self:!0,__source:!0};function Lp(e,t,n){var r,o={},l=null,a=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)b0.call(t,r)&&!x0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:g0,type:e,key:l,ref:a,props:o,_owner:w0.current}}Aa.Fragment=y0;Aa.jsx=Lp;Aa.jsxs=Lp;Sp.exports=Aa;var w=Sp.exports,os={},Ip={exports:{}},it={},Rp={exports:{}},jp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,R){var _=P.length;P.push(R);e:for(;0<_;){var V=_-1>>>1,H=P[V];if(0<o(H,R))P[V]=R,P[_]=H,_=V;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var R=P[0],_=P.pop();if(_!==R){P[0]=_;e:for(var V=0,H=P.length,te=H>>>1;V<te;){var K=2*(V+1)-1,re=P[K],G=K+1,we=P[G];if(0>o(re,_))G<H&&0>o(we,re)?(P[V]=we,P[G]=_,V=G):(P[V]=re,P[K]=_,V=K);else if(G<H&&0>o(we,_))P[V]=we,P[G]=_,V=G;else break e}}return R}function o(P,R){var _=P.sortIndex-R.sortIndex;return _!==0?_:P.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var a=Date,i=a.now();e.unstable_now=function(){return a.now()-i}}var s=[],u=[],c=1,d=null,f=3,y=!1,g=!1,b=!1,S=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=P)r(u),R.sortIndex=R.expirationTime,t(s,R);else break;R=n(u)}}function x(P){if(b=!1,v(P),!g)if(n(s)!==null)g=!0,N(C);else{var R=n(u);R!==null&&O(x,R.startTime-P)}}function C(P,R){g=!1,b&&(b=!1,h(M),M=-1),y=!0;var _=f;try{for(v(R),d=n(s);d!==null&&(!(d.expirationTime>R)||P&&!E());){var V=d.callback;if(typeof V=="function"){d.callback=null,f=d.priorityLevel;var H=V(d.expirationTime<=R);R=e.unstable_now(),typeof H=="function"?d.callback=H:d===n(s)&&r(s),v(R)}else r(s);d=n(s)}if(d!==null)var te=!0;else{var K=n(u);K!==null&&O(x,K.startTime-R),te=!1}return te}finally{d=null,f=_,y=!1}}var T=!1,L=null,M=-1,D=5,I=-1;function E(){return!(e.unstable_now()-I<D)}function k(){if(L!==null){var P=e.unstable_now();I=P;var R=!0;try{R=L(!0,P)}finally{R?z():(T=!1,L=null)}}else T=!1}var z;if(typeof m=="function")z=function(){m(k)};else if(typeof MessageChannel<"u"){var $=new MessageChannel,A=$.port2;$.port1.onmessage=k,z=function(){A.postMessage(null)}}else z=function(){S(k,0)};function N(P){L=P,T||(T=!0,z())}function O(P,R){M=S(function(){P(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){g||y||(g=!0,N(C))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(P){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var _=f;f=R;try{return P()}finally{f=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,R){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var _=f;f=P;try{return R()}finally{f=_}},e.unstable_scheduleCallback=function(P,R,_){var V=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?V+_:V):_=V,P){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=_+H,P={id:c++,callback:R,priorityLevel:P,startTime:_,expirationTime:H,sortIndex:-1},_>V?(P.sortIndex=_,t(u,P),n(s)===null&&P===n(u)&&(b?(h(M),M=-1):b=!0,O(x,_-V))):(P.sortIndex=H,t(s,P),g||y||(g=!0,N(C))),P},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(P){var R=f;return function(){var _=f;f=R;try{return P.apply(this,arguments)}finally{f=_}}}})(jp);Rp.exports=jp;var S0=Rp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var E0=p,at=S0;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var zp=new Set,Fo={};function ir(e,t){Fr(e,t),Fr(e+"Capture",t)}function Fr(e,t){for(Fo[e]=t,e=0;e<t.length;e++)zp.add(t[e])}var qt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ls=Object.prototype.hasOwnProperty,C0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ed={},td={};function $0(e){return ls.call(td,e)?!0:ls.call(ed,e)?!1:C0.test(e)?td[e]=!0:(ed[e]=!0,!1)}function k0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function P0(e,t,n,r){if(t===null||typeof t>"u"||k0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,o,l,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=a}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Tu=/[\-:]([a-z])/g;function Nu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Tu,Nu);Re[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Tu,Nu);Re[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Tu,Nu);Re[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function _u(e,t,n,r){var o=Re.hasOwnProperty(t)?Re[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(P0(t,n,o,r)&&(n=null),r||o===null?$0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var rn=E0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Sl=Symbol.for("react.element"),hr=Symbol.for("react.portal"),vr=Symbol.for("react.fragment"),Mu=Symbol.for("react.strict_mode"),as=Symbol.for("react.profiler"),Op=Symbol.for("react.provider"),Fp=Symbol.for("react.context"),Lu=Symbol.for("react.forward_ref"),is=Symbol.for("react.suspense"),ss=Symbol.for("react.suspense_list"),Iu=Symbol.for("react.memo"),dn=Symbol.for("react.lazy"),Dp=Symbol.for("react.offscreen"),nd=Symbol.iterator;function lo(e){return e===null||typeof e!="object"?null:(e=nd&&e[nd]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,bi;function wo(e){if(bi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);bi=t&&t[1]||""}return`
`+bi+e}var wi=!1;function xi(e,t){if(!e||wi)return"";wi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),l=r.stack.split(`
`),a=o.length-1,i=l.length-1;1<=a&&0<=i&&o[a]!==l[i];)i--;for(;1<=a&&0<=i;a--,i--)if(o[a]!==l[i]){if(a!==1||i!==1)do if(a--,i--,0>i||o[a]!==l[i]){var s=`
`+o[a].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=a&&0<=i);break}}}finally{wi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?wo(e):""}function T0(e){switch(e.tag){case 5:return wo(e.type);case 16:return wo("Lazy");case 13:return wo("Suspense");case 19:return wo("SuspenseList");case 0:case 2:case 15:return e=xi(e.type,!1),e;case 11:return e=xi(e.type.render,!1),e;case 1:return e=xi(e.type,!0),e;default:return""}}function us(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case vr:return"Fragment";case hr:return"Portal";case as:return"Profiler";case Mu:return"StrictMode";case is:return"Suspense";case ss:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Fp:return(e.displayName||"Context")+".Consumer";case Op:return(e._context.displayName||"Context")+".Provider";case Lu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Iu:return t=e.displayName||null,t!==null?t:us(e.type)||"Memo";case dn:t=e._payload,e=e._init;try{return us(e(t))}catch{}}return null}function N0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return us(t);case 8:return t===Mu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function _n(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ap(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function _0(e){var t=Ap(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,l.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function El(e){e._valueTracker||(e._valueTracker=_0(e))}function Wp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ap(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function sa(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function cs(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function rd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=_n(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vp(e,t){t=t.checked,t!=null&&_u(e,"checked",t,!1)}function ds(e,t){Vp(e,t);var n=_n(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?fs(e,t.type,n):t.hasOwnProperty("defaultValue")&&fs(e,t.type,_n(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function od(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function fs(e,t,n){(t!=="number"||sa(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var xo=Array.isArray;function Tr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+_n(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ps(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ld(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(xo(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:_n(n)}}function Bp(e,t){var n=_n(t.value),r=_n(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ad(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Hp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ms(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Hp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Cl,Up=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Cl=Cl||document.createElement("div"),Cl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Cl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Do(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var To={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},M0=["Webkit","ms","Moz","O"];Object.keys(To).forEach(function(e){M0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),To[t]=To[e]})});function Kp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||To.hasOwnProperty(e)&&To[e]?(""+t).trim():t+"px"}function Gp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Kp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var L0=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function hs(e,t){if(t){if(L0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function vs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gs=null;function Ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ys=null,Nr=null,_r=null;function id(e){if(e=cl(e)){if(typeof ys!="function")throw Error(j(280));var t=e.stateNode;t&&(t=Ua(t),ys(e.stateNode,e.type,t))}}function Qp(e){Nr?_r?_r.push(e):_r=[e]:Nr=e}function Yp(){if(Nr){var e=Nr,t=_r;if(_r=Nr=null,id(e),t)for(e=0;e<t.length;e++)id(t[e])}}function Xp(e,t){return e(t)}function Zp(){}var Si=!1;function Jp(e,t,n){if(Si)return e(t,n);Si=!0;try{return Xp(e,t,n)}finally{Si=!1,(Nr!==null||_r!==null)&&(Zp(),Yp())}}function Ao(e,t){var n=e.stateNode;if(n===null)return null;var r=Ua(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var bs=!1;if(qt)try{var ao={};Object.defineProperty(ao,"passive",{get:function(){bs=!0}}),window.addEventListener("test",ao,ao),window.removeEventListener("test",ao,ao)}catch{bs=!1}function I0(e,t,n,r,o,l,a,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var No=!1,ua=null,ca=!1,ws=null,R0={onError:function(e){No=!0,ua=e}};function j0(e,t,n,r,o,l,a,i,s){No=!1,ua=null,I0.apply(R0,arguments)}function z0(e,t,n,r,o,l,a,i,s){if(j0.apply(this,arguments),No){if(No){var u=ua;No=!1,ua=null}else throw Error(j(198));ca||(ca=!0,ws=u)}}function sr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function qp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function sd(e){if(sr(e)!==e)throw Error(j(188))}function O0(e){var t=e.alternate;if(!t){if(t=sr(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var l=o.alternate;if(l===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===l.child){for(l=o.child;l;){if(l===n)return sd(o),e;if(l===r)return sd(o),t;l=l.sibling}throw Error(j(188))}if(n.return!==r.return)n=o,r=l;else{for(var a=!1,i=o.child;i;){if(i===n){a=!0,n=o,r=l;break}if(i===r){a=!0,r=o,n=l;break}i=i.sibling}if(!a){for(i=l.child;i;){if(i===n){a=!0,n=l,r=o;break}if(i===r){a=!0,r=l,n=o;break}i=i.sibling}if(!a)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function em(e){return e=O0(e),e!==null?tm(e):null}function tm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=tm(e);if(t!==null)return t;e=e.sibling}return null}var nm=at.unstable_scheduleCallback,ud=at.unstable_cancelCallback,F0=at.unstable_shouldYield,D0=at.unstable_requestPaint,ye=at.unstable_now,A0=at.unstable_getCurrentPriorityLevel,ju=at.unstable_ImmediatePriority,rm=at.unstable_UserBlockingPriority,da=at.unstable_NormalPriority,W0=at.unstable_LowPriority,om=at.unstable_IdlePriority,Wa=null,Ft=null;function V0(e){if(Ft&&typeof Ft.onCommitFiberRoot=="function")try{Ft.onCommitFiberRoot(Wa,e,void 0,(e.current.flags&128)===128)}catch{}}var Tt=Math.clz32?Math.clz32:U0,B0=Math.log,H0=Math.LN2;function U0(e){return e>>>=0,e===0?32:31-(B0(e)/H0|0)|0}var $l=64,kl=4194304;function So(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function fa(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,l=e.pingedLanes,a=n&268435455;if(a!==0){var i=a&~o;i!==0?r=So(i):(l&=a,l!==0&&(r=So(l)))}else a=n&~o,a!==0?r=So(a):l!==0&&(r=So(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,l=t&-t,o>=l||o===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Tt(t),o=1<<n,r|=e[n],t&=~o;return r}function K0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function G0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,l=e.pendingLanes;0<l;){var a=31-Tt(l),i=1<<a,s=o[a];s===-1?(!(i&n)||i&r)&&(o[a]=K0(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}function xs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function lm(){var e=$l;return $l<<=1,!($l&4194240)&&($l=64),e}function Ei(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function sl(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Tt(t),e[t]=n}function Q0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Tt(n),l=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~l}}function zu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Tt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var oe=0;function am(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var im,Ou,sm,um,cm,Ss=!1,Pl=[],xn=null,Sn=null,En=null,Wo=new Map,Vo=new Map,pn=[],Y0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cd(e,t){switch(e){case"focusin":case"focusout":xn=null;break;case"dragenter":case"dragleave":Sn=null;break;case"mouseover":case"mouseout":En=null;break;case"pointerover":case"pointerout":Wo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vo.delete(t.pointerId)}}function io(e,t,n,r,o,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[o]},t!==null&&(t=cl(t),t!==null&&Ou(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function X0(e,t,n,r,o){switch(t){case"focusin":return xn=io(xn,e,t,n,r,o),!0;case"dragenter":return Sn=io(Sn,e,t,n,r,o),!0;case"mouseover":return En=io(En,e,t,n,r,o),!0;case"pointerover":var l=o.pointerId;return Wo.set(l,io(Wo.get(l)||null,e,t,n,r,o)),!0;case"gotpointercapture":return l=o.pointerId,Vo.set(l,io(Vo.get(l)||null,e,t,n,r,o)),!0}return!1}function dm(e){var t=Bn(e.target);if(t!==null){var n=sr(t);if(n!==null){if(t=n.tag,t===13){if(t=qp(n),t!==null){e.blockedOn=t,cm(e.priority,function(){sm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Es(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);gs=r,n.target.dispatchEvent(r),gs=null}else return t=cl(n),t!==null&&Ou(t),e.blockedOn=n,!1;t.shift()}return!0}function dd(e,t,n){Zl(e)&&n.delete(t)}function Z0(){Ss=!1,xn!==null&&Zl(xn)&&(xn=null),Sn!==null&&Zl(Sn)&&(Sn=null),En!==null&&Zl(En)&&(En=null),Wo.forEach(dd),Vo.forEach(dd)}function so(e,t){e.blockedOn===t&&(e.blockedOn=null,Ss||(Ss=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,Z0)))}function Bo(e){function t(o){return so(o,e)}if(0<Pl.length){so(Pl[0],e);for(var n=1;n<Pl.length;n++){var r=Pl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xn!==null&&so(xn,e),Sn!==null&&so(Sn,e),En!==null&&so(En,e),Wo.forEach(t),Vo.forEach(t),n=0;n<pn.length;n++)r=pn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<pn.length&&(n=pn[0],n.blockedOn===null);)dm(n),n.blockedOn===null&&pn.shift()}var Mr=rn.ReactCurrentBatchConfig,pa=!0;function J0(e,t,n,r){var o=oe,l=Mr.transition;Mr.transition=null;try{oe=1,Fu(e,t,n,r)}finally{oe=o,Mr.transition=l}}function q0(e,t,n,r){var o=oe,l=Mr.transition;Mr.transition=null;try{oe=4,Fu(e,t,n,r)}finally{oe=o,Mr.transition=l}}function Fu(e,t,n,r){if(pa){var o=Es(e,t,n,r);if(o===null)Ii(e,t,r,ma,n),cd(e,r);else if(X0(o,e,t,n,r))r.stopPropagation();else if(cd(e,r),t&4&&-1<Y0.indexOf(e)){for(;o!==null;){var l=cl(o);if(l!==null&&im(l),l=Es(e,t,n,r),l===null&&Ii(e,t,r,ma,n),l===o)break;o=l}o!==null&&r.stopPropagation()}else Ii(e,t,r,null,n)}}var ma=null;function Es(e,t,n,r){if(ma=null,e=Ru(r),e=Bn(e),e!==null)if(t=sr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=qp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ma=e,null}function fm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(A0()){case ju:return 1;case rm:return 4;case da:case W0:return 16;case om:return 536870912;default:return 16}default:return 16}}var vn=null,Du=null,Jl=null;function pm(){if(Jl)return Jl;var e,t=Du,n=t.length,r,o="value"in vn?vn.value:vn.textContent,l=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[l-r];r++);return Jl=o.slice(e,1<r?1-r:void 0)}function ql(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Tl(){return!0}function fd(){return!1}function st(e){function t(n,r,o,l,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=l,this.target=a,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(l):l[i]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Tl:fd,this.isPropagationStopped=fd,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Tl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Tl)},persist:function(){},isPersistent:Tl}),t}var Qr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Au=st(Qr),ul=me({},Qr,{view:0,detail:0}),ey=st(ul),Ci,$i,uo,Va=me({},ul,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Wu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==uo&&(uo&&e.type==="mousemove"?(Ci=e.screenX-uo.screenX,$i=e.screenY-uo.screenY):$i=Ci=0,uo=e),Ci)},movementY:function(e){return"movementY"in e?e.movementY:$i}}),pd=st(Va),ty=me({},Va,{dataTransfer:0}),ny=st(ty),ry=me({},ul,{relatedTarget:0}),ki=st(ry),oy=me({},Qr,{animationName:0,elapsedTime:0,pseudoElement:0}),ly=st(oy),ay=me({},Qr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),iy=st(ay),sy=me({},Qr,{data:0}),md=st(sy),uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=dy[e])?!!t[e]:!1}function Wu(){return fy}var py=me({},ul,{key:function(e){if(e.key){var t=uy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ql(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?cy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Wu,charCode:function(e){return e.type==="keypress"?ql(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ql(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),my=st(py),hy=me({},Va,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hd=st(hy),vy=me({},ul,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Wu}),gy=st(vy),yy=me({},Qr,{propertyName:0,elapsedTime:0,pseudoElement:0}),by=st(yy),wy=me({},Va,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xy=st(wy),Sy=[9,13,27,32],Vu=qt&&"CompositionEvent"in window,_o=null;qt&&"documentMode"in document&&(_o=document.documentMode);var Ey=qt&&"TextEvent"in window&&!_o,mm=qt&&(!Vu||_o&&8<_o&&11>=_o),vd=" ",gd=!1;function hm(e,t){switch(e){case"keyup":return Sy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var gr=!1;function Cy(e,t){switch(e){case"compositionend":return vm(t);case"keypress":return t.which!==32?null:(gd=!0,vd);case"textInput":return e=t.data,e===vd&&gd?null:e;default:return null}}function $y(e,t){if(gr)return e==="compositionend"||!Vu&&hm(e,t)?(e=pm(),Jl=Du=vn=null,gr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return mm&&t.locale!=="ko"?null:t.data;default:return null}}var ky={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ky[e.type]:t==="textarea"}function gm(e,t,n,r){Qp(r),t=ha(t,"onChange"),0<t.length&&(n=new Au("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Mo=null,Ho=null;function Py(e){Tm(e,0)}function Ba(e){var t=wr(e);if(Wp(t))return e}function Ty(e,t){if(e==="change")return t}var ym=!1;if(qt){var Pi;if(qt){var Ti="oninput"in document;if(!Ti){var bd=document.createElement("div");bd.setAttribute("oninput","return;"),Ti=typeof bd.oninput=="function"}Pi=Ti}else Pi=!1;ym=Pi&&(!document.documentMode||9<document.documentMode)}function wd(){Mo&&(Mo.detachEvent("onpropertychange",bm),Ho=Mo=null)}function bm(e){if(e.propertyName==="value"&&Ba(Ho)){var t=[];gm(t,Ho,e,Ru(e)),Jp(Py,t)}}function Ny(e,t,n){e==="focusin"?(wd(),Mo=t,Ho=n,Mo.attachEvent("onpropertychange",bm)):e==="focusout"&&wd()}function _y(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ba(Ho)}function My(e,t){if(e==="click")return Ba(t)}function Ly(e,t){if(e==="input"||e==="change")return Ba(t)}function Iy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var _t=typeof Object.is=="function"?Object.is:Iy;function Uo(e,t){if(_t(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ls.call(t,o)||!_t(e[o],t[o]))return!1}return!0}function xd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sd(e,t){var n=xd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xd(n)}}function wm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?wm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function xm(){for(var e=window,t=sa();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=sa(e.document)}return t}function Bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ry(e){var t=xm(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&wm(n.ownerDocument.documentElement,n)){if(r!==null&&Bu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,l=Math.min(r.start,o);r=r.end===void 0?l:Math.min(r.end,o),!e.extend&&l>r&&(o=r,r=l,l=o),o=Sd(n,l);var a=Sd(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var jy=qt&&"documentMode"in document&&11>=document.documentMode,yr=null,Cs=null,Lo=null,$s=!1;function Ed(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;$s||yr==null||yr!==sa(r)||(r=yr,"selectionStart"in r&&Bu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Lo&&Uo(Lo,r)||(Lo=r,r=ha(Cs,"onSelect"),0<r.length&&(t=new Au("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yr)))}function Nl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var br={animationend:Nl("Animation","AnimationEnd"),animationiteration:Nl("Animation","AnimationIteration"),animationstart:Nl("Animation","AnimationStart"),transitionend:Nl("Transition","TransitionEnd")},Ni={},Sm={};qt&&(Sm=document.createElement("div").style,"AnimationEvent"in window||(delete br.animationend.animation,delete br.animationiteration.animation,delete br.animationstart.animation),"TransitionEvent"in window||delete br.transitionend.transition);function Ha(e){if(Ni[e])return Ni[e];if(!br[e])return e;var t=br[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Sm)return Ni[e]=t[n];return e}var Em=Ha("animationend"),Cm=Ha("animationiteration"),$m=Ha("animationstart"),km=Ha("transitionend"),Pm=new Map,Cd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ln(e,t){Pm.set(e,t),ir(t,[e])}for(var _i=0;_i<Cd.length;_i++){var Mi=Cd[_i],zy=Mi.toLowerCase(),Oy=Mi[0].toUpperCase()+Mi.slice(1);Ln(zy,"on"+Oy)}Ln(Em,"onAnimationEnd");Ln(Cm,"onAnimationIteration");Ln($m,"onAnimationStart");Ln("dblclick","onDoubleClick");Ln("focusin","onFocus");Ln("focusout","onBlur");Ln(km,"onTransitionEnd");Fr("onMouseEnter",["mouseout","mouseover"]);Fr("onMouseLeave",["mouseout","mouseover"]);Fr("onPointerEnter",["pointerout","pointerover"]);Fr("onPointerLeave",["pointerout","pointerover"]);ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Eo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fy=new Set("cancel close invalid load scroll toggle".split(" ").concat(Eo));function $d(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,z0(r,t,void 0,e),e.currentTarget=null}function Tm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var a=r.length-1;0<=a;a--){var i=r[a],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&o.isPropagationStopped())break e;$d(o,i,u),l=s}else for(a=0;a<r.length;a++){if(i=r[a],s=i.instance,u=i.currentTarget,i=i.listener,s!==l&&o.isPropagationStopped())break e;$d(o,i,u),l=s}}}if(ca)throw e=ws,ca=!1,ws=null,e}function ue(e,t){var n=t[_s];n===void 0&&(n=t[_s]=new Set);var r=e+"__bubble";n.has(r)||(Nm(t,e,2,!1),n.add(r))}function Li(e,t,n){var r=0;t&&(r|=4),Nm(n,e,r,t)}var _l="_reactListening"+Math.random().toString(36).slice(2);function Ko(e){if(!e[_l]){e[_l]=!0,zp.forEach(function(n){n!=="selectionchange"&&(Fy.has(n)||Li(n,!1,e),Li(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_l]||(t[_l]=!0,Li("selectionchange",!1,t))}}function Nm(e,t,n,r){switch(fm(t)){case 1:var o=J0;break;case 4:o=q0;break;default:o=Fu}n=o.bind(null,t,n,e),o=void 0,!bs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ii(e,t,n,r,o){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var i=r.stateNode.containerInfo;if(i===o||i.nodeType===8&&i.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var s=a.tag;if((s===3||s===4)&&(s=a.stateNode.containerInfo,s===o||s.nodeType===8&&s.parentNode===o))return;a=a.return}for(;i!==null;){if(a=Bn(i),a===null)return;if(s=a.tag,s===5||s===6){r=l=a;continue e}i=i.parentNode}}r=r.return}Jp(function(){var u=l,c=Ru(n),d=[];e:{var f=Pm.get(e);if(f!==void 0){var y=Au,g=e;switch(e){case"keypress":if(ql(n)===0)break e;case"keydown":case"keyup":y=my;break;case"focusin":g="focus",y=ki;break;case"focusout":g="blur",y=ki;break;case"beforeblur":case"afterblur":y=ki;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=pd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=ny;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=gy;break;case Em:case Cm:case $m:y=ly;break;case km:y=by;break;case"scroll":y=ey;break;case"wheel":y=xy;break;case"copy":case"cut":case"paste":y=iy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=hd}var b=(t&4)!==0,S=!b&&e==="scroll",h=b?f!==null?f+"Capture":null:f;b=[];for(var m=u,v;m!==null;){v=m;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,h!==null&&(x=Ao(m,h),x!=null&&b.push(Go(m,x,v)))),S)break;m=m.return}0<b.length&&(f=new y(f,g,null,n,c),d.push({event:f,listeners:b}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",f&&n!==gs&&(g=n.relatedTarget||n.fromElement)&&(Bn(g)||g[en]))break e;if((y||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,y?(g=n.relatedTarget||n.toElement,y=u,g=g?Bn(g):null,g!==null&&(S=sr(g),g!==S||g.tag!==5&&g.tag!==6)&&(g=null)):(y=null,g=u),y!==g)){if(b=pd,x="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(b=hd,x="onPointerLeave",h="onPointerEnter",m="pointer"),S=y==null?f:wr(y),v=g==null?f:wr(g),f=new b(x,m+"leave",y,n,c),f.target=S,f.relatedTarget=v,x=null,Bn(c)===u&&(b=new b(h,m+"enter",g,n,c),b.target=v,b.relatedTarget=S,x=b),S=x,y&&g)t:{for(b=y,h=g,m=0,v=b;v;v=fr(v))m++;for(v=0,x=h;x;x=fr(x))v++;for(;0<m-v;)b=fr(b),m--;for(;0<v-m;)h=fr(h),v--;for(;m--;){if(b===h||h!==null&&b===h.alternate)break t;b=fr(b),h=fr(h)}b=null}else b=null;y!==null&&kd(d,f,y,b,!1),g!==null&&S!==null&&kd(d,S,g,b,!0)}}e:{if(f=u?wr(u):window,y=f.nodeName&&f.nodeName.toLowerCase(),y==="select"||y==="input"&&f.type==="file")var C=Ty;else if(yd(f))if(ym)C=Ly;else{C=_y;var T=Ny}else(y=f.nodeName)&&y.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=My);if(C&&(C=C(e,u))){gm(d,C,n,c);break e}T&&T(e,f,u),e==="focusout"&&(T=f._wrapperState)&&T.controlled&&f.type==="number"&&fs(f,"number",f.value)}switch(T=u?wr(u):window,e){case"focusin":(yd(T)||T.contentEditable==="true")&&(yr=T,Cs=u,Lo=null);break;case"focusout":Lo=Cs=yr=null;break;case"mousedown":$s=!0;break;case"contextmenu":case"mouseup":case"dragend":$s=!1,Ed(d,n,c);break;case"selectionchange":if(jy)break;case"keydown":case"keyup":Ed(d,n,c)}var L;if(Vu)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else gr?hm(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(mm&&n.locale!=="ko"&&(gr||M!=="onCompositionStart"?M==="onCompositionEnd"&&gr&&(L=pm()):(vn=c,Du="value"in vn?vn.value:vn.textContent,gr=!0)),T=ha(u,M),0<T.length&&(M=new md(M,e,null,n,c),d.push({event:M,listeners:T}),L?M.data=L:(L=vm(n),L!==null&&(M.data=L)))),(L=Ey?Cy(e,n):$y(e,n))&&(u=ha(u,"onBeforeInput"),0<u.length&&(c=new md("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=L))}Tm(d,t)})}function Go(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ha(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,l=o.stateNode;o.tag===5&&l!==null&&(o=l,l=Ao(e,n),l!=null&&r.unshift(Go(e,l,o)),l=Ao(e,t),l!=null&&r.push(Go(e,l,o))),e=e.return}return r}function fr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function kd(e,t,n,r,o){for(var l=t._reactName,a=[];n!==null&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(s!==null&&s===r)break;i.tag===5&&u!==null&&(i=u,o?(s=Ao(n,l),s!=null&&a.unshift(Go(n,s,i))):o||(s=Ao(n,l),s!=null&&a.push(Go(n,s,i)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Dy=/\r\n?/g,Ay=/\u0000|\uFFFD/g;function Pd(e){return(typeof e=="string"?e:""+e).replace(Dy,`
`).replace(Ay,"")}function Ml(e,t,n){if(t=Pd(t),Pd(e)!==t&&n)throw Error(j(425))}function va(){}var ks=null,Ps=null;function Ts(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ns=typeof setTimeout=="function"?setTimeout:void 0,Wy=typeof clearTimeout=="function"?clearTimeout:void 0,Td=typeof Promise=="function"?Promise:void 0,Vy=typeof queueMicrotask=="function"?queueMicrotask:typeof Td<"u"?function(e){return Td.resolve(null).then(e).catch(By)}:Ns;function By(e){setTimeout(function(){throw e})}function Ri(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Bo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Bo(t)}function Cn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Nd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Yr=Math.random().toString(36).slice(2),zt="__reactFiber$"+Yr,Qo="__reactProps$"+Yr,en="__reactContainer$"+Yr,_s="__reactEvents$"+Yr,Hy="__reactListeners$"+Yr,Uy="__reactHandles$"+Yr;function Bn(e){var t=e[zt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[en]||n[zt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Nd(e);e!==null;){if(n=e[zt])return n;e=Nd(e)}return t}e=n,n=e.parentNode}return null}function cl(e){return e=e[zt]||e[en],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function wr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function Ua(e){return e[Qo]||null}var Ms=[],xr=-1;function In(e){return{current:e}}function ce(e){0>xr||(e.current=Ms[xr],Ms[xr]=null,xr--)}function ie(e,t){xr++,Ms[xr]=e.current,e.current=t}var Mn={},De=In(Mn),Je=In(!1),Zn=Mn;function Dr(e,t){var n=e.type.contextTypes;if(!n)return Mn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},l;for(l in n)o[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function qe(e){return e=e.childContextTypes,e!=null}function ga(){ce(Je),ce(De)}function _d(e,t,n){if(De.current!==Mn)throw Error(j(168));ie(De,t),ie(Je,n)}function _m(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(j(108,N0(e)||"Unknown",o));return me({},n,r)}function ya(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Mn,Zn=De.current,ie(De,e),ie(Je,Je.current),!0}function Md(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=_m(e,t,Zn),r.__reactInternalMemoizedMergedChildContext=e,ce(Je),ce(De),ie(De,e)):ce(Je),ie(Je,n)}var Qt=null,Ka=!1,ji=!1;function Mm(e){Qt===null?Qt=[e]:Qt.push(e)}function Ky(e){Ka=!0,Mm(e)}function Rn(){if(!ji&&Qt!==null){ji=!0;var e=0,t=oe;try{var n=Qt;for(oe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qt=null,Ka=!1}catch(o){throw Qt!==null&&(Qt=Qt.slice(e+1)),nm(ju,Rn),o}finally{oe=t,ji=!1}}return null}var Sr=[],Er=0,ba=null,wa=0,pt=[],mt=0,Jn=null,Yt=1,Xt="";function Wn(e,t){Sr[Er++]=wa,Sr[Er++]=ba,ba=e,wa=t}function Lm(e,t,n){pt[mt++]=Yt,pt[mt++]=Xt,pt[mt++]=Jn,Jn=e;var r=Yt;e=Xt;var o=32-Tt(r)-1;r&=~(1<<o),n+=1;var l=32-Tt(t)+o;if(30<l){var a=o-o%5;l=(r&(1<<a)-1).toString(32),r>>=a,o-=a,Yt=1<<32-Tt(t)+o|n<<o|r,Xt=l+e}else Yt=1<<l|n<<o|r,Xt=e}function Hu(e){e.return!==null&&(Wn(e,1),Lm(e,1,0))}function Uu(e){for(;e===ba;)ba=Sr[--Er],Sr[Er]=null,wa=Sr[--Er],Sr[Er]=null;for(;e===Jn;)Jn=pt[--mt],pt[mt]=null,Xt=pt[--mt],pt[mt]=null,Yt=pt[--mt],pt[mt]=null}var lt=null,ot=null,de=!1,Pt=null;function Im(e,t){var n=ht(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ld(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,lt=e,ot=Cn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,lt=e,ot=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Jn!==null?{id:Yt,overflow:Xt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ht(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,lt=e,ot=null,!0):!1;default:return!1}}function Ls(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Is(e){if(de){var t=ot;if(t){var n=t;if(!Ld(e,t)){if(Ls(e))throw Error(j(418));t=Cn(n.nextSibling);var r=lt;t&&Ld(e,t)?Im(r,n):(e.flags=e.flags&-4097|2,de=!1,lt=e)}}else{if(Ls(e))throw Error(j(418));e.flags=e.flags&-4097|2,de=!1,lt=e}}}function Id(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;lt=e}function Ll(e){if(e!==lt)return!1;if(!de)return Id(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ts(e.type,e.memoizedProps)),t&&(t=ot)){if(Ls(e))throw Rm(),Error(j(418));for(;t;)Im(e,t),t=Cn(t.nextSibling)}if(Id(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ot=Cn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ot=null}}else ot=lt?Cn(e.stateNode.nextSibling):null;return!0}function Rm(){for(var e=ot;e;)e=Cn(e.nextSibling)}function Ar(){ot=lt=null,de=!1}function Ku(e){Pt===null?Pt=[e]:Pt.push(e)}var Gy=rn.ReactCurrentBatchConfig;function co(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var o=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(a){var i=o.refs;a===null?delete i[l]:i[l]=a},t._stringRef=l,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function Il(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Rd(e){var t=e._init;return t(e._payload)}function jm(e){function t(h,m){if(e){var v=h.deletions;v===null?(h.deletions=[m],h.flags|=16):v.push(m)}}function n(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function o(h,m){return h=Tn(h,m),h.index=0,h.sibling=null,h}function l(h,m,v){return h.index=v,e?(v=h.alternate,v!==null?(v=v.index,v<m?(h.flags|=2,m):v):(h.flags|=2,m)):(h.flags|=1048576,m)}function a(h){return e&&h.alternate===null&&(h.flags|=2),h}function i(h,m,v,x){return m===null||m.tag!==6?(m=Vi(v,h.mode,x),m.return=h,m):(m=o(m,v),m.return=h,m)}function s(h,m,v,x){var C=v.type;return C===vr?c(h,m,v.props.children,x,v.key):m!==null&&(m.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===dn&&Rd(C)===m.type)?(x=o(m,v.props),x.ref=co(h,m,v),x.return=h,x):(x=aa(v.type,v.key,v.props,null,h.mode,x),x.ref=co(h,m,v),x.return=h,x)}function u(h,m,v,x){return m===null||m.tag!==4||m.stateNode.containerInfo!==v.containerInfo||m.stateNode.implementation!==v.implementation?(m=Bi(v,h.mode,x),m.return=h,m):(m=o(m,v.children||[]),m.return=h,m)}function c(h,m,v,x,C){return m===null||m.tag!==7?(m=Yn(v,h.mode,x,C),m.return=h,m):(m=o(m,v),m.return=h,m)}function d(h,m,v){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Vi(""+m,h.mode,v),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Sl:return v=aa(m.type,m.key,m.props,null,h.mode,v),v.ref=co(h,null,m),v.return=h,v;case hr:return m=Bi(m,h.mode,v),m.return=h,m;case dn:var x=m._init;return d(h,x(m._payload),v)}if(xo(m)||lo(m))return m=Yn(m,h.mode,v,null),m.return=h,m;Il(h,m)}return null}function f(h,m,v,x){var C=m!==null?m.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:i(h,m,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Sl:return v.key===C?s(h,m,v,x):null;case hr:return v.key===C?u(h,m,v,x):null;case dn:return C=v._init,f(h,m,C(v._payload),x)}if(xo(v)||lo(v))return C!==null?null:c(h,m,v,x,null);Il(h,v)}return null}function y(h,m,v,x,C){if(typeof x=="string"&&x!==""||typeof x=="number")return h=h.get(v)||null,i(m,h,""+x,C);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Sl:return h=h.get(x.key===null?v:x.key)||null,s(m,h,x,C);case hr:return h=h.get(x.key===null?v:x.key)||null,u(m,h,x,C);case dn:var T=x._init;return y(h,m,v,T(x._payload),C)}if(xo(x)||lo(x))return h=h.get(v)||null,c(m,h,x,C,null);Il(m,x)}return null}function g(h,m,v,x){for(var C=null,T=null,L=m,M=m=0,D=null;L!==null&&M<v.length;M++){L.index>M?(D=L,L=null):D=L.sibling;var I=f(h,L,v[M],x);if(I===null){L===null&&(L=D);break}e&&L&&I.alternate===null&&t(h,L),m=l(I,m,M),T===null?C=I:T.sibling=I,T=I,L=D}if(M===v.length)return n(h,L),de&&Wn(h,M),C;if(L===null){for(;M<v.length;M++)L=d(h,v[M],x),L!==null&&(m=l(L,m,M),T===null?C=L:T.sibling=L,T=L);return de&&Wn(h,M),C}for(L=r(h,L);M<v.length;M++)D=y(L,h,M,v[M],x),D!==null&&(e&&D.alternate!==null&&L.delete(D.key===null?M:D.key),m=l(D,m,M),T===null?C=D:T.sibling=D,T=D);return e&&L.forEach(function(E){return t(h,E)}),de&&Wn(h,M),C}function b(h,m,v,x){var C=lo(v);if(typeof C!="function")throw Error(j(150));if(v=C.call(v),v==null)throw Error(j(151));for(var T=C=null,L=m,M=m=0,D=null,I=v.next();L!==null&&!I.done;M++,I=v.next()){L.index>M?(D=L,L=null):D=L.sibling;var E=f(h,L,I.value,x);if(E===null){L===null&&(L=D);break}e&&L&&E.alternate===null&&t(h,L),m=l(E,m,M),T===null?C=E:T.sibling=E,T=E,L=D}if(I.done)return n(h,L),de&&Wn(h,M),C;if(L===null){for(;!I.done;M++,I=v.next())I=d(h,I.value,x),I!==null&&(m=l(I,m,M),T===null?C=I:T.sibling=I,T=I);return de&&Wn(h,M),C}for(L=r(h,L);!I.done;M++,I=v.next())I=y(L,h,M,I.value,x),I!==null&&(e&&I.alternate!==null&&L.delete(I.key===null?M:I.key),m=l(I,m,M),T===null?C=I:T.sibling=I,T=I);return e&&L.forEach(function(k){return t(h,k)}),de&&Wn(h,M),C}function S(h,m,v,x){if(typeof v=="object"&&v!==null&&v.type===vr&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Sl:e:{for(var C=v.key,T=m;T!==null;){if(T.key===C){if(C=v.type,C===vr){if(T.tag===7){n(h,T.sibling),m=o(T,v.props.children),m.return=h,h=m;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===dn&&Rd(C)===T.type){n(h,T.sibling),m=o(T,v.props),m.ref=co(h,T,v),m.return=h,h=m;break e}n(h,T);break}else t(h,T);T=T.sibling}v.type===vr?(m=Yn(v.props.children,h.mode,x,v.key),m.return=h,h=m):(x=aa(v.type,v.key,v.props,null,h.mode,x),x.ref=co(h,m,v),x.return=h,h=x)}return a(h);case hr:e:{for(T=v.key;m!==null;){if(m.key===T)if(m.tag===4&&m.stateNode.containerInfo===v.containerInfo&&m.stateNode.implementation===v.implementation){n(h,m.sibling),m=o(m,v.children||[]),m.return=h,h=m;break e}else{n(h,m);break}else t(h,m);m=m.sibling}m=Bi(v,h.mode,x),m.return=h,h=m}return a(h);case dn:return T=v._init,S(h,m,T(v._payload),x)}if(xo(v))return g(h,m,v,x);if(lo(v))return b(h,m,v,x);Il(h,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,m!==null&&m.tag===6?(n(h,m.sibling),m=o(m,v),m.return=h,h=m):(n(h,m),m=Vi(v,h.mode,x),m.return=h,h=m),a(h)):n(h,m)}return S}var Wr=jm(!0),zm=jm(!1),xa=In(null),Sa=null,Cr=null,Gu=null;function Qu(){Gu=Cr=Sa=null}function Yu(e){var t=xa.current;ce(xa),e._currentValue=t}function Rs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Lr(e,t){Sa=e,Gu=Cr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Xe=!0),e.firstContext=null)}function yt(e){var t=e._currentValue;if(Gu!==e)if(e={context:e,memoizedValue:t,next:null},Cr===null){if(Sa===null)throw Error(j(308));Cr=e,Sa.dependencies={lanes:0,firstContext:e}}else Cr=Cr.next=e;return t}var Hn=null;function Xu(e){Hn===null?Hn=[e]:Hn.push(e)}function Om(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Xu(t)):(n.next=o.next,o.next=n),t.interleaved=n,tn(e,r)}function tn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var fn=!1;function Zu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fm(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Jt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $n(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ne&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,tn(e,n)}return o=r.interleaved,o===null?(t.next=t,Xu(r)):(t.next=o.next,o.next=t),r.interleaved=t,tn(e,n)}function ea(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zu(e,n)}}function jd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?o=l=a:l=l.next=a,n=n.next}while(n!==null);l===null?o=l=t:l=l.next=t}else o=l=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ea(e,t,n,r){var o=e.updateQueue;fn=!1;var l=o.firstBaseUpdate,a=o.lastBaseUpdate,i=o.shared.pending;if(i!==null){o.shared.pending=null;var s=i,u=s.next;s.next=null,a===null?l=u:a.next=u,a=s;var c=e.alternate;c!==null&&(c=c.updateQueue,i=c.lastBaseUpdate,i!==a&&(i===null?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(l!==null){var d=o.baseState;a=0,c=u=s=null,i=l;do{var f=i.lane,y=i.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:y,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var g=e,b=i;switch(f=t,y=n,b.tag){case 1:if(g=b.payload,typeof g=="function"){d=g.call(y,d,f);break e}d=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=b.payload,f=typeof g=="function"?g.call(y,d,f):g,f==null)break e;d=me({},d,f);break e;case 2:fn=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[i]:f.push(i))}else y={eventTime:y,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},c===null?(u=c=y,s=d):c=c.next=y,a|=f;if(i=i.next,i===null){if(i=o.shared.pending,i===null)break;f=i,i=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(c===null&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else l===null&&(o.shared.lanes=0);er|=a,e.lanes=a,e.memoizedState=d}}function zd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(j(191,o));o.call(r)}}}var dl={},Dt=In(dl),Yo=In(dl),Xo=In(dl);function Un(e){if(e===dl)throw Error(j(174));return e}function Ju(e,t){switch(ie(Xo,t),ie(Yo,e),ie(Dt,dl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ms(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ms(t,e)}ce(Dt),ie(Dt,t)}function Vr(){ce(Dt),ce(Yo),ce(Xo)}function Dm(e){Un(Xo.current);var t=Un(Dt.current),n=ms(t,e.type);t!==n&&(ie(Yo,e),ie(Dt,n))}function qu(e){Yo.current===e&&(ce(Dt),ce(Yo))}var fe=In(0);function Ca(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var zi=[];function ec(){for(var e=0;e<zi.length;e++)zi[e]._workInProgressVersionPrimary=null;zi.length=0}var ta=rn.ReactCurrentDispatcher,Oi=rn.ReactCurrentBatchConfig,qn=0,pe=null,ke=null,Ne=null,$a=!1,Io=!1,Zo=0,Qy=0;function je(){throw Error(j(321))}function tc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!_t(e[n],t[n]))return!1;return!0}function nc(e,t,n,r,o,l){if(qn=l,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ta.current=e===null||e.memoizedState===null?Jy:qy,e=n(r,o),Io){l=0;do{if(Io=!1,Zo=0,25<=l)throw Error(j(301));l+=1,Ne=ke=null,t.updateQueue=null,ta.current=e1,e=n(r,o)}while(Io)}if(ta.current=ka,t=ke!==null&&ke.next!==null,qn=0,Ne=ke=pe=null,$a=!1,t)throw Error(j(300));return e}function rc(){var e=Zo!==0;return Zo=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e,Ne}function bt(){if(ke===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=ke.next;var t=Ne===null?pe.memoizedState:Ne.next;if(t!==null)Ne=t,ke=e;else{if(e===null)throw Error(j(310));ke=e,e={memoizedState:ke.memoizedState,baseState:ke.baseState,baseQueue:ke.baseQueue,queue:ke.queue,next:null},Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e}return Ne}function Jo(e,t){return typeof t=="function"?t(e):t}function Fi(e){var t=bt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=ke,o=r.baseQueue,l=n.pending;if(l!==null){if(o!==null){var a=o.next;o.next=l.next,l.next=a}r.baseQueue=o=l,n.pending=null}if(o!==null){l=o.next,r=r.baseState;var i=a=null,s=null,u=l;do{var c=u.lane;if((qn&c)===c)s!==null&&(s=s.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};s===null?(i=s=d,a=r):s=s.next=d,pe.lanes|=c,er|=c}u=u.next}while(u!==null&&u!==l);s===null?a=r:s.next=i,_t(r,t.memoizedState)||(Xe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do l=o.lane,pe.lanes|=l,er|=l,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Di(e){var t=bt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,l=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do l=e(l,a.action),a=a.next;while(a!==o);_t(l,t.memoizedState)||(Xe=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Am(){}function Wm(e,t){var n=pe,r=bt(),o=t(),l=!_t(r.memoizedState,o);if(l&&(r.memoizedState=o,Xe=!0),r=r.queue,oc(Hm.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||Ne!==null&&Ne.memoizedState.tag&1){if(n.flags|=2048,qo(9,Bm.bind(null,n,r,o,t),void 0,null),_e===null)throw Error(j(349));qn&30||Vm(n,t,o)}return o}function Vm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bm(e,t,n,r){t.value=n,t.getSnapshot=r,Um(t)&&Km(e)}function Hm(e,t,n){return n(function(){Um(t)&&Km(e)})}function Um(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!_t(e,n)}catch{return!0}}function Km(e){var t=tn(e,1);t!==null&&Nt(t,e,1,-1)}function Od(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Jo,lastRenderedState:e},t.queue=e,e=e.dispatch=Zy.bind(null,pe,e),[t.memoizedState,e]}function qo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Gm(){return bt().memoizedState}function na(e,t,n,r){var o=Rt();pe.flags|=e,o.memoizedState=qo(1|t,n,void 0,r===void 0?null:r)}function Ga(e,t,n,r){var o=bt();r=r===void 0?null:r;var l=void 0;if(ke!==null){var a=ke.memoizedState;if(l=a.destroy,r!==null&&tc(r,a.deps)){o.memoizedState=qo(t,n,l,r);return}}pe.flags|=e,o.memoizedState=qo(1|t,n,l,r)}function Fd(e,t){return na(8390656,8,e,t)}function oc(e,t){return Ga(2048,8,e,t)}function Qm(e,t){return Ga(4,2,e,t)}function Ym(e,t){return Ga(4,4,e,t)}function Xm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Zm(e,t,n){return n=n!=null?n.concat([e]):null,Ga(4,4,Xm.bind(null,t,e),n)}function lc(){}function Jm(e,t){var n=bt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&tc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function qm(e,t){var n=bt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&tc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function eh(e,t,n){return qn&21?(_t(n,t)||(n=lm(),pe.lanes|=n,er|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Xe=!0),e.memoizedState=n)}function Yy(e,t){var n=oe;oe=n!==0&&4>n?n:4,e(!0);var r=Oi.transition;Oi.transition={};try{e(!1),t()}finally{oe=n,Oi.transition=r}}function th(){return bt().memoizedState}function Xy(e,t,n){var r=Pn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},nh(e))rh(t,n);else if(n=Om(e,t,n,r),n!==null){var o=Ve();Nt(n,e,r,o),oh(n,t,r)}}function Zy(e,t,n){var r=Pn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(nh(e))rh(t,o);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var a=t.lastRenderedState,i=l(a,n);if(o.hasEagerState=!0,o.eagerState=i,_t(i,a)){var s=t.interleaved;s===null?(o.next=o,Xu(t)):(o.next=s.next,s.next=o),t.interleaved=o;return}}catch{}finally{}n=Om(e,t,o,r),n!==null&&(o=Ve(),Nt(n,e,r,o),oh(n,t,r))}}function nh(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function rh(e,t){Io=$a=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function oh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zu(e,n)}}var ka={readContext:yt,useCallback:je,useContext:je,useEffect:je,useImperativeHandle:je,useInsertionEffect:je,useLayoutEffect:je,useMemo:je,useReducer:je,useRef:je,useState:je,useDebugValue:je,useDeferredValue:je,useTransition:je,useMutableSource:je,useSyncExternalStore:je,useId:je,unstable_isNewReconciler:!1},Jy={readContext:yt,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:yt,useEffect:Fd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,na(4194308,4,Xm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return na(4194308,4,e,t)},useInsertionEffect:function(e,t){return na(4,2,e,t)},useMemo:function(e,t){var n=Rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Xy.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:Od,useDebugValue:lc,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=Od(!1),t=e[0];return e=Yy.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,o=Rt();if(de){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),_e===null)throw Error(j(349));qn&30||Vm(r,t,n)}o.memoizedState=n;var l={value:n,getSnapshot:t};return o.queue=l,Fd(Hm.bind(null,r,l,e),[e]),r.flags|=2048,qo(9,Bm.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Rt(),t=_e.identifierPrefix;if(de){var n=Xt,r=Yt;n=(r&~(1<<32-Tt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Zo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Qy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},qy={readContext:yt,useCallback:Jm,useContext:yt,useEffect:oc,useImperativeHandle:Zm,useInsertionEffect:Qm,useLayoutEffect:Ym,useMemo:qm,useReducer:Fi,useRef:Gm,useState:function(){return Fi(Jo)},useDebugValue:lc,useDeferredValue:function(e){var t=bt();return eh(t,ke.memoizedState,e)},useTransition:function(){var e=Fi(Jo)[0],t=bt().memoizedState;return[e,t]},useMutableSource:Am,useSyncExternalStore:Wm,useId:th,unstable_isNewReconciler:!1},e1={readContext:yt,useCallback:Jm,useContext:yt,useEffect:oc,useImperativeHandle:Zm,useInsertionEffect:Qm,useLayoutEffect:Ym,useMemo:qm,useReducer:Di,useRef:Gm,useState:function(){return Di(Jo)},useDebugValue:lc,useDeferredValue:function(e){var t=bt();return ke===null?t.memoizedState=e:eh(t,ke.memoizedState,e)},useTransition:function(){var e=Di(Jo)[0],t=bt().memoizedState;return[e,t]},useMutableSource:Am,useSyncExternalStore:Wm,useId:th,unstable_isNewReconciler:!1};function Ct(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function js(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Qa={isMounted:function(e){return(e=e._reactInternals)?sr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=Pn(e),l=Jt(r,o);l.payload=t,n!=null&&(l.callback=n),t=$n(e,l,o),t!==null&&(Nt(t,e,o,r),ea(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=Pn(e),l=Jt(r,o);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=$n(e,l,o),t!==null&&(Nt(t,e,o,r),ea(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),r=Pn(e),o=Jt(n,r);o.tag=2,t!=null&&(o.callback=t),t=$n(e,o,r),t!==null&&(Nt(t,e,r,n),ea(t,e,r))}};function Dd(e,t,n,r,o,l,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,a):t.prototype&&t.prototype.isPureReactComponent?!Uo(n,r)||!Uo(o,l):!0}function lh(e,t,n){var r=!1,o=Mn,l=t.contextType;return typeof l=="object"&&l!==null?l=yt(l):(o=qe(t)?Zn:De.current,r=t.contextTypes,l=(r=r!=null)?Dr(e,o):Mn),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Qa,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=l),t}function Ad(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Qa.enqueueReplaceState(t,t.state,null)}function zs(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Zu(e);var l=t.contextType;typeof l=="object"&&l!==null?o.context=yt(l):(l=qe(t)?Zn:De.current,o.context=Dr(e,l)),o.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(js(e,t,l,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Qa.enqueueReplaceState(o,o.state,null),Ea(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Br(e,t){try{var n="",r=t;do n+=T0(r),r=r.return;while(r);var o=n}catch(l){o=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:o,digest:null}}function Ai(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Os(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var t1=typeof WeakMap=="function"?WeakMap:Map;function ah(e,t,n){n=Jt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ta||(Ta=!0,Gs=r),Os(e,t)},n}function ih(e,t,n){n=Jt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Os(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Os(e,t),typeof r!="function"&&(kn===null?kn=new Set([this]):kn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Wd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new t1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=h1.bind(null,e,t,n),t.then(e,e))}function Vd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Jt(-1,1),t.tag=2,$n(n,t,1))),n.lanes|=1),e)}var n1=rn.ReactCurrentOwner,Xe=!1;function We(e,t,n,r){t.child=e===null?zm(t,null,n,r):Wr(t,e.child,n,r)}function Hd(e,t,n,r,o){n=n.render;var l=t.ref;return Lr(t,o),r=nc(e,t,n,r,l,o),n=rc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,nn(e,t,o)):(de&&n&&Hu(t),t.flags|=1,We(e,t,r,o),t.child)}function Ud(e,t,n,r,o){if(e===null){var l=n.type;return typeof l=="function"&&!pc(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,sh(e,t,l,r,o)):(e=aa(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&o)){var a=l.memoizedProps;if(n=n.compare,n=n!==null?n:Uo,n(a,r)&&e.ref===t.ref)return nn(e,t,o)}return t.flags|=1,e=Tn(l,r),e.ref=t.ref,e.return=t,t.child=e}function sh(e,t,n,r,o){if(e!==null){var l=e.memoizedProps;if(Uo(l,r)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=r=l,(e.lanes&o)!==0)e.flags&131072&&(Xe=!0);else return t.lanes=e.lanes,nn(e,t,o)}return Fs(e,t,n,r,o)}function uh(e,t,n){var r=t.pendingProps,o=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ie(kr,rt),rt|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ie(kr,rt),rt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,ie(kr,rt),rt|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,ie(kr,rt),rt|=r;return We(e,t,o,n),t.child}function ch(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Fs(e,t,n,r,o){var l=qe(n)?Zn:De.current;return l=Dr(t,l),Lr(t,o),n=nc(e,t,n,r,l,o),r=rc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,nn(e,t,o)):(de&&r&&Hu(t),t.flags|=1,We(e,t,n,o),t.child)}function Kd(e,t,n,r,o){if(qe(n)){var l=!0;ya(t)}else l=!1;if(Lr(t,o),t.stateNode===null)ra(e,t),lh(t,n,r),zs(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,i=t.memoizedProps;a.props=i;var s=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=yt(u):(u=qe(n)?Zn:De.current,u=Dr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(i!==r||s!==u)&&Ad(t,a,r,u),fn=!1;var f=t.memoizedState;a.state=f,Ea(t,r,a,o),s=t.memoizedState,i!==r||f!==s||Je.current||fn?(typeof c=="function"&&(js(t,n,c,r),s=t.memoizedState),(i=fn||Dd(t,n,i,r,f,s,u))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=u,r=i):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Fm(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:Ct(t.type,i),a.props=u,d=t.pendingProps,f=a.context,s=n.contextType,typeof s=="object"&&s!==null?s=yt(s):(s=qe(n)?Zn:De.current,s=Dr(t,s));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(i!==d||f!==s)&&Ad(t,a,r,s),fn=!1,f=t.memoizedState,a.state=f,Ea(t,r,a,o);var g=t.memoizedState;i!==d||f!==g||Je.current||fn?(typeof y=="function"&&(js(t,n,y,r),g=t.memoizedState),(u=fn||Dd(t,n,u,r,f,g,s)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,g,s),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,g,s)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),a.props=r,a.state=g,a.context=s,r=u):(typeof a.componentDidUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ds(e,t,n,r,l,o)}function Ds(e,t,n,r,o,l){ch(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&Md(t,n,!1),nn(e,t,l);r=t.stateNode,n1.current=t;var i=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Wr(t,e.child,null,l),t.child=Wr(t,null,i,l)):We(e,t,i,l),t.memoizedState=r.state,o&&Md(t,n,!0),t.child}function dh(e){var t=e.stateNode;t.pendingContext?_d(e,t.pendingContext,t.pendingContext!==t.context):t.context&&_d(e,t.context,!1),Ju(e,t.containerInfo)}function Gd(e,t,n,r,o){return Ar(),Ku(o),t.flags|=256,We(e,t,n,r),t.child}var As={dehydrated:null,treeContext:null,retryLane:0};function Ws(e){return{baseLanes:e,cachePool:null,transitions:null}}function fh(e,t,n){var r=t.pendingProps,o=fe.current,l=!1,a=(t.flags&128)!==0,i;if((i=a)||(i=e!==null&&e.memoizedState===null?!1:(o&2)!==0),i?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ie(fe,o&1),e===null)return Is(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,l?(r=t.mode,l=t.child,a={mode:"hidden",children:a},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=a):l=Za(a,r,0,null),e=Yn(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Ws(n),t.memoizedState=As,e):ac(t,a));if(o=e.memoizedState,o!==null&&(i=o.dehydrated,i!==null))return r1(e,t,a,r,i,o,n);if(l){l=r.fallback,a=t.mode,o=e.child,i=o.sibling;var s={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=Tn(o,s),r.subtreeFlags=o.subtreeFlags&14680064),i!==null?l=Tn(i,l):(l=Yn(l,a,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,a=e.child.memoizedState,a=a===null?Ws(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},l.memoizedState=a,l.childLanes=e.childLanes&~n,t.memoizedState=As,r}return l=e.child,e=l.sibling,r=Tn(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ac(e,t){return t=Za({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Rl(e,t,n,r){return r!==null&&Ku(r),Wr(t,e.child,null,n),e=ac(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function r1(e,t,n,r,o,l,a){if(n)return t.flags&256?(t.flags&=-257,r=Ai(Error(j(422))),Rl(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,o=t.mode,r=Za({mode:"visible",children:r.children},o,0,null),l=Yn(l,o,a,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Wr(t,e.child,null,a),t.child.memoizedState=Ws(a),t.memoizedState=As,l);if(!(t.mode&1))return Rl(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var i=r.dgst;return r=i,l=Error(j(419)),r=Ai(l,r,void 0),Rl(e,t,a,r)}if(i=(a&e.childLanes)!==0,Xe||i){if(r=_e,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==l.retryLane&&(l.retryLane=o,tn(e,o),Nt(r,e,o,-1))}return fc(),r=Ai(Error(j(421))),Rl(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=v1.bind(null,e),o._reactRetry=t,null):(e=l.treeContext,ot=Cn(o.nextSibling),lt=t,de=!0,Pt=null,e!==null&&(pt[mt++]=Yt,pt[mt++]=Xt,pt[mt++]=Jn,Yt=e.id,Xt=e.overflow,Jn=t),t=ac(t,r.children),t.flags|=4096,t)}function Qd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Rs(e.return,t,n)}function Wi(e,t,n,r,o){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=o)}function ph(e,t,n){var r=t.pendingProps,o=r.revealOrder,l=r.tail;if(We(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Qd(e,n,t);else if(e.tag===19)Qd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ie(fe,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ca(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Wi(t,!1,o,n,l);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ca(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Wi(t,!0,n,null,l);break;case"together":Wi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ra(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function nn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),er|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=Tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function o1(e,t,n){switch(t.tag){case 3:dh(t),Ar();break;case 5:Dm(t);break;case 1:qe(t.type)&&ya(t);break;case 4:Ju(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ie(xa,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ie(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?fh(e,t,n):(ie(fe,fe.current&1),e=nn(e,t,n),e!==null?e.sibling:null);ie(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ph(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ie(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,uh(e,t,n)}return nn(e,t,n)}var mh,Vs,hh,vh;mh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Vs=function(){};hh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Un(Dt.current);var l=null;switch(n){case"input":o=cs(e,o),r=cs(e,r),l=[];break;case"select":o=me({},o,{value:void 0}),r=me({},r,{value:void 0}),l=[];break;case"textarea":o=ps(e,o),r=ps(e,r),l=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=va)}hs(n,r);var a;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var i=o[u];for(a in i)i.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Fo.hasOwnProperty(u)?l||(l=[]):(l=l||[]).push(u,null));for(u in r){var s=r[u];if(i=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&s!==i&&(s!=null||i!=null))if(u==="style")if(i){for(a in i)!i.hasOwnProperty(a)||s&&s.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in s)s.hasOwnProperty(a)&&i[a]!==s[a]&&(n||(n={}),n[a]=s[a])}else n||(l||(l=[]),l.push(u,n)),n=s;else u==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,i=i?i.__html:void 0,s!=null&&i!==s&&(l=l||[]).push(u,s)):u==="children"?typeof s!="string"&&typeof s!="number"||(l=l||[]).push(u,""+s):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Fo.hasOwnProperty(u)?(s!=null&&u==="onScroll"&&ue("scroll",e),l||i===s||(l=[])):(l=l||[]).push(u,s))}n&&(l=l||[]).push("style",n);var u=l;(t.updateQueue=u)&&(t.flags|=4)}};vh=function(e,t,n,r){n!==r&&(t.flags|=4)};function fo(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function l1(e,t,n){var r=t.pendingProps;switch(Uu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return qe(t.type)&&ga(),ze(t),null;case 3:return r=t.stateNode,Vr(),ce(Je),ce(De),ec(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ll(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Pt!==null&&(Xs(Pt),Pt=null))),Vs(e,t),ze(t),null;case 5:qu(t);var o=Un(Xo.current);if(n=t.type,e!==null&&t.stateNode!=null)hh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return ze(t),null}if(e=Un(Dt.current),Ll(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[zt]=t,r[Qo]=l,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(o=0;o<Eo.length;o++)ue(Eo[o],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":rd(r,l),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},ue("invalid",r);break;case"textarea":ld(r,l),ue("invalid",r)}hs(n,l),o=null;for(var a in l)if(l.hasOwnProperty(a)){var i=l[a];a==="children"?typeof i=="string"?r.textContent!==i&&(l.suppressHydrationWarning!==!0&&Ml(r.textContent,i,e),o=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(l.suppressHydrationWarning!==!0&&Ml(r.textContent,i,e),o=["children",""+i]):Fo.hasOwnProperty(a)&&i!=null&&a==="onScroll"&&ue("scroll",r)}switch(n){case"input":El(r),od(r,l,!0);break;case"textarea":El(r),ad(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=va)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Hp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[zt]=t,e[Qo]=r,mh(e,t,!1,!1),t.stateNode=e;e:{switch(a=vs(n,r),n){case"dialog":ue("cancel",e),ue("close",e),o=r;break;case"iframe":case"object":case"embed":ue("load",e),o=r;break;case"video":case"audio":for(o=0;o<Eo.length;o++)ue(Eo[o],e);o=r;break;case"source":ue("error",e),o=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),o=r;break;case"details":ue("toggle",e),o=r;break;case"input":rd(e,r),o=cs(e,r),ue("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=me({},r,{value:void 0}),ue("invalid",e);break;case"textarea":ld(e,r),o=ps(e,r),ue("invalid",e);break;default:o=r}hs(n,o),i=o;for(l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="style"?Gp(e,s):l==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&Up(e,s)):l==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&Do(e,s):typeof s=="number"&&Do(e,""+s):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Fo.hasOwnProperty(l)?s!=null&&l==="onScroll"&&ue("scroll",e):s!=null&&_u(e,l,s,a))}switch(n){case"input":El(e),od(e,r,!1);break;case"textarea":El(e),ad(e);break;case"option":r.value!=null&&e.setAttribute("value",""+_n(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?Tr(e,!!r.multiple,l,!1):r.defaultValue!=null&&Tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=va)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ze(t),null;case 6:if(e&&t.stateNode!=null)vh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=Un(Xo.current),Un(Dt.current),Ll(t)){if(r=t.stateNode,n=t.memoizedProps,r[zt]=t,(l=r.nodeValue!==n)&&(e=lt,e!==null))switch(e.tag){case 3:Ml(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ml(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[zt]=t,t.stateNode=r}return ze(t),null;case 13:if(ce(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&ot!==null&&t.mode&1&&!(t.flags&128))Rm(),Ar(),t.flags|=98560,l=!1;else if(l=Ll(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(j(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(j(317));l[zt]=t}else Ar(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ze(t),l=!1}else Pt!==null&&(Xs(Pt),Pt=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Pe===0&&(Pe=3):fc())),t.updateQueue!==null&&(t.flags|=4),ze(t),null);case 4:return Vr(),Vs(e,t),e===null&&Ko(t.stateNode.containerInfo),ze(t),null;case 10:return Yu(t.type._context),ze(t),null;case 17:return qe(t.type)&&ga(),ze(t),null;case 19:if(ce(fe),l=t.memoizedState,l===null)return ze(t),null;if(r=(t.flags&128)!==0,a=l.rendering,a===null)if(r)fo(l,!1);else{if(Pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Ca(e),a!==null){for(t.flags|=128,fo(l,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,a=l.alternate,a===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=a.childLanes,l.lanes=a.lanes,l.child=a.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=a.memoizedProps,l.memoizedState=a.memoizedState,l.updateQueue=a.updateQueue,l.type=a.type,e=a.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ie(fe,fe.current&1|2),t.child}e=e.sibling}l.tail!==null&&ye()>Hr&&(t.flags|=128,r=!0,fo(l,!1),t.lanes=4194304)}else{if(!r)if(e=Ca(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),fo(l,!0),l.tail===null&&l.tailMode==="hidden"&&!a.alternate&&!de)return ze(t),null}else 2*ye()-l.renderingStartTime>Hr&&n!==1073741824&&(t.flags|=128,r=!0,fo(l,!1),t.lanes=4194304);l.isBackwards?(a.sibling=t.child,t.child=a):(n=l.last,n!==null?n.sibling=a:t.child=a,l.last=a)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=ye(),t.sibling=null,n=fe.current,ie(fe,r?n&1|2:n&1),t):(ze(t),null);case 22:case 23:return dc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?rt&1073741824&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function a1(e,t){switch(Uu(t),t.tag){case 1:return qe(t.type)&&ga(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Vr(),ce(Je),ce(De),ec(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return qu(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));Ar()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return Vr(),null;case 10:return Yu(t.type._context),null;case 22:case 23:return dc(),null;case 24:return null;default:return null}}var jl=!1,Fe=!1,i1=typeof WeakSet=="function"?WeakSet:Set,W=null;function $r(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function Bs(e,t,n){try{n()}catch(r){he(e,t,r)}}var Yd=!1;function s1(e,t){if(ks=pa,e=xm(),Bu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var a=0,i=-1,s=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var y;d!==n||o!==0&&d.nodeType!==3||(i=a+o),d!==l||r!==0&&d.nodeType!==3||(s=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(y=d.firstChild)!==null;)f=d,d=y;for(;;){if(d===e)break t;if(f===n&&++u===o&&(i=a),f===l&&++c===r&&(s=a),(y=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=y}n=i===-1||s===-1?null:{start:i,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ps={focusedElem:e,selectionRange:n},pa=!1,W=t;W!==null;)if(t=W,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,W=e;else for(;W!==null;){t=W;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var b=g.memoizedProps,S=g.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?b:Ct(t.type,b),S);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(x){he(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,W=e;break}W=t.return}return g=Yd,Yd=!1,g}function Ro(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var l=o.destroy;o.destroy=void 0,l!==void 0&&Bs(t,n,l)}o=o.next}while(o!==r)}}function Ya(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Hs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function gh(e){var t=e.alternate;t!==null&&(e.alternate=null,gh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[zt],delete t[Qo],delete t[_s],delete t[Hy],delete t[Uy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function yh(e){return e.tag===5||e.tag===3||e.tag===4}function Xd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||yh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Us(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=va));else if(r!==4&&(e=e.child,e!==null))for(Us(e,t,n),e=e.sibling;e!==null;)Us(e,t,n),e=e.sibling}function Ks(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ks(e,t,n),e=e.sibling;e!==null;)Ks(e,t,n),e=e.sibling}var Le=null,kt=!1;function sn(e,t,n){for(n=n.child;n!==null;)bh(e,t,n),n=n.sibling}function bh(e,t,n){if(Ft&&typeof Ft.onCommitFiberUnmount=="function")try{Ft.onCommitFiberUnmount(Wa,n)}catch{}switch(n.tag){case 5:Fe||$r(n,t);case 6:var r=Le,o=kt;Le=null,sn(e,t,n),Le=r,kt=o,Le!==null&&(kt?(e=Le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Le.removeChild(n.stateNode));break;case 18:Le!==null&&(kt?(e=Le,n=n.stateNode,e.nodeType===8?Ri(e.parentNode,n):e.nodeType===1&&Ri(e,n),Bo(e)):Ri(Le,n.stateNode));break;case 4:r=Le,o=kt,Le=n.stateNode.containerInfo,kt=!0,sn(e,t,n),Le=r,kt=o;break;case 0:case 11:case 14:case 15:if(!Fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var l=o,a=l.destroy;l=l.tag,a!==void 0&&(l&2||l&4)&&Bs(n,t,a),o=o.next}while(o!==r)}sn(e,t,n);break;case 1:if(!Fe&&($r(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){he(n,t,i)}sn(e,t,n);break;case 21:sn(e,t,n);break;case 22:n.mode&1?(Fe=(r=Fe)||n.memoizedState!==null,sn(e,t,n),Fe=r):sn(e,t,n);break;default:sn(e,t,n)}}function Zd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new i1),t.forEach(function(r){var o=g1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Et(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var l=e,a=t,i=a;e:for(;i!==null;){switch(i.tag){case 5:Le=i.stateNode,kt=!1;break e;case 3:Le=i.stateNode.containerInfo,kt=!0;break e;case 4:Le=i.stateNode.containerInfo,kt=!0;break e}i=i.return}if(Le===null)throw Error(j(160));bh(l,a,o),Le=null,kt=!1;var s=o.alternate;s!==null&&(s.return=null),o.return=null}catch(u){he(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)wh(t,e),t=t.sibling}function wh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Et(t,e),Lt(e),r&4){try{Ro(3,e,e.return),Ya(3,e)}catch(b){he(e,e.return,b)}try{Ro(5,e,e.return)}catch(b){he(e,e.return,b)}}break;case 1:Et(t,e),Lt(e),r&512&&n!==null&&$r(n,n.return);break;case 5:if(Et(t,e),Lt(e),r&512&&n!==null&&$r(n,n.return),e.flags&32){var o=e.stateNode;try{Do(o,"")}catch(b){he(e,e.return,b)}}if(r&4&&(o=e.stateNode,o!=null)){var l=e.memoizedProps,a=n!==null?n.memoizedProps:l,i=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{i==="input"&&l.type==="radio"&&l.name!=null&&Vp(o,l),vs(i,a);var u=vs(i,l);for(a=0;a<s.length;a+=2){var c=s[a],d=s[a+1];c==="style"?Gp(o,d):c==="dangerouslySetInnerHTML"?Up(o,d):c==="children"?Do(o,d):_u(o,c,d,u)}switch(i){case"input":ds(o,l);break;case"textarea":Bp(o,l);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!l.multiple;var y=l.value;y!=null?Tr(o,!!l.multiple,y,!1):f!==!!l.multiple&&(l.defaultValue!=null?Tr(o,!!l.multiple,l.defaultValue,!0):Tr(o,!!l.multiple,l.multiple?[]:"",!1))}o[Qo]=l}catch(b){he(e,e.return,b)}}break;case 6:if(Et(t,e),Lt(e),r&4){if(e.stateNode===null)throw Error(j(162));o=e.stateNode,l=e.memoizedProps;try{o.nodeValue=l}catch(b){he(e,e.return,b)}}break;case 3:if(Et(t,e),Lt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Bo(t.containerInfo)}catch(b){he(e,e.return,b)}break;case 4:Et(t,e),Lt(e);break;case 13:Et(t,e),Lt(e),o=e.child,o.flags&8192&&(l=o.memoizedState!==null,o.stateNode.isHidden=l,!l||o.alternate!==null&&o.alternate.memoizedState!==null||(uc=ye())),r&4&&Zd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Fe=(u=Fe)||c,Et(t,e),Fe=u):Et(t,e),Lt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(W=e,c=e.child;c!==null;){for(d=W=c;W!==null;){switch(f=W,y=f.child,f.tag){case 0:case 11:case 14:case 15:Ro(4,f,f.return);break;case 1:$r(f,f.return);var g=f.stateNode;if(typeof g.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(b){he(r,n,b)}}break;case 5:$r(f,f.return);break;case 22:if(f.memoizedState!==null){qd(d);continue}}y!==null?(y.return=f,W=y):qd(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(l=o.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(i=d.stateNode,s=d.memoizedProps.style,a=s!=null&&s.hasOwnProperty("display")?s.display:null,i.style.display=Kp("display",a))}catch(b){he(e,e.return,b)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(b){he(e,e.return,b)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Et(t,e),Lt(e),r&4&&Zd(e);break;case 21:break;default:Et(t,e),Lt(e)}}function Lt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(yh(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Do(o,""),r.flags&=-33);var l=Xd(e);Ks(e,l,o);break;case 3:case 4:var a=r.stateNode.containerInfo,i=Xd(e);Us(e,i,a);break;default:throw Error(j(161))}}catch(s){he(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function u1(e,t,n){W=e,xh(e)}function xh(e,t,n){for(var r=(e.mode&1)!==0;W!==null;){var o=W,l=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||jl;if(!a){var i=o.alternate,s=i!==null&&i.memoizedState!==null||Fe;i=jl;var u=Fe;if(jl=a,(Fe=s)&&!u)for(W=o;W!==null;)a=W,s=a.child,a.tag===22&&a.memoizedState!==null?ef(o):s!==null?(s.return=a,W=s):ef(o);for(;l!==null;)W=l,xh(l),l=l.sibling;W=o,jl=i,Fe=u}Jd(e)}else o.subtreeFlags&8772&&l!==null?(l.return=o,W=l):Jd(e)}}function Jd(e){for(;W!==null;){var t=W;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Fe||Ya(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Fe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ct(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&zd(t,l,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}zd(t,a,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Bo(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}Fe||t.flags&512&&Hs(t)}catch(f){he(t,t.return,f)}}if(t===e){W=null;break}if(n=t.sibling,n!==null){n.return=t.return,W=n;break}W=t.return}}function qd(e){for(;W!==null;){var t=W;if(t===e){W=null;break}var n=t.sibling;if(n!==null){n.return=t.return,W=n;break}W=t.return}}function ef(e){for(;W!==null;){var t=W;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ya(4,t)}catch(s){he(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(s){he(t,o,s)}}var l=t.return;try{Hs(t)}catch(s){he(t,l,s)}break;case 5:var a=t.return;try{Hs(t)}catch(s){he(t,a,s)}}}catch(s){he(t,t.return,s)}if(t===e){W=null;break}var i=t.sibling;if(i!==null){i.return=t.return,W=i;break}W=t.return}}var c1=Math.ceil,Pa=rn.ReactCurrentDispatcher,ic=rn.ReactCurrentOwner,gt=rn.ReactCurrentBatchConfig,ne=0,_e=null,Ce=null,Ie=0,rt=0,kr=In(0),Pe=0,el=null,er=0,Xa=0,sc=0,jo=null,Ye=null,uc=0,Hr=1/0,Kt=null,Ta=!1,Gs=null,kn=null,zl=!1,gn=null,Na=0,zo=0,Qs=null,oa=-1,la=0;function Ve(){return ne&6?ye():oa!==-1?oa:oa=ye()}function Pn(e){return e.mode&1?ne&2&&Ie!==0?Ie&-Ie:Gy.transition!==null?(la===0&&(la=lm()),la):(e=oe,e!==0||(e=window.event,e=e===void 0?16:fm(e.type)),e):1}function Nt(e,t,n,r){if(50<zo)throw zo=0,Qs=null,Error(j(185));sl(e,n,r),(!(ne&2)||e!==_e)&&(e===_e&&(!(ne&2)&&(Xa|=n),Pe===4&&mn(e,Ie)),et(e,r),n===1&&ne===0&&!(t.mode&1)&&(Hr=ye()+500,Ka&&Rn()))}function et(e,t){var n=e.callbackNode;G0(e,t);var r=fa(e,e===_e?Ie:0);if(r===0)n!==null&&ud(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ud(n),t===1)e.tag===0?Ky(tf.bind(null,e)):Mm(tf.bind(null,e)),Vy(function(){!(ne&6)&&Rn()}),n=null;else{switch(am(r)){case 1:n=ju;break;case 4:n=rm;break;case 16:n=da;break;case 536870912:n=om;break;default:n=da}n=Nh(n,Sh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Sh(e,t){if(oa=-1,la=0,ne&6)throw Error(j(327));var n=e.callbackNode;if(Ir()&&e.callbackNode!==n)return null;var r=fa(e,e===_e?Ie:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=_a(e,r);else{t=r;var o=ne;ne|=2;var l=Ch();(_e!==e||Ie!==t)&&(Kt=null,Hr=ye()+500,Qn(e,t));do try{p1();break}catch(i){Eh(e,i)}while(!0);Qu(),Pa.current=l,ne=o,Ce!==null?t=0:(_e=null,Ie=0,t=Pe)}if(t!==0){if(t===2&&(o=xs(e),o!==0&&(r=o,t=Ys(e,o))),t===1)throw n=el,Qn(e,0),mn(e,r),et(e,ye()),n;if(t===6)mn(e,r);else{if(o=e.current.alternate,!(r&30)&&!d1(o)&&(t=_a(e,r),t===2&&(l=xs(e),l!==0&&(r=l,t=Ys(e,l))),t===1))throw n=el,Qn(e,0),mn(e,r),et(e,ye()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:Vn(e,Ye,Kt);break;case 3:if(mn(e,r),(r&130023424)===r&&(t=uc+500-ye(),10<t)){if(fa(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ve(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ns(Vn.bind(null,e,Ye,Kt),t);break}Vn(e,Ye,Kt);break;case 4:if(mn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-Tt(r);l=1<<a,a=t[a],a>o&&(o=a),r&=~l}if(r=o,r=ye()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*c1(r/1960))-r,10<r){e.timeoutHandle=Ns(Vn.bind(null,e,Ye,Kt),r);break}Vn(e,Ye,Kt);break;case 5:Vn(e,Ye,Kt);break;default:throw Error(j(329))}}}return et(e,ye()),e.callbackNode===n?Sh.bind(null,e):null}function Ys(e,t){var n=jo;return e.current.memoizedState.isDehydrated&&(Qn(e,t).flags|=256),e=_a(e,t),e!==2&&(t=Ye,Ye=n,t!==null&&Xs(t)),e}function Xs(e){Ye===null?Ye=e:Ye.push.apply(Ye,e)}function d1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],l=o.getSnapshot;o=o.value;try{if(!_t(l(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function mn(e,t){for(t&=~sc,t&=~Xa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Tt(t),r=1<<n;e[n]=-1,t&=~r}}function tf(e){if(ne&6)throw Error(j(327));Ir();var t=fa(e,0);if(!(t&1))return et(e,ye()),null;var n=_a(e,t);if(e.tag!==0&&n===2){var r=xs(e);r!==0&&(t=r,n=Ys(e,r))}if(n===1)throw n=el,Qn(e,0),mn(e,t),et(e,ye()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Vn(e,Ye,Kt),et(e,ye()),null}function cc(e,t){var n=ne;ne|=1;try{return e(t)}finally{ne=n,ne===0&&(Hr=ye()+500,Ka&&Rn())}}function tr(e){gn!==null&&gn.tag===0&&!(ne&6)&&Ir();var t=ne;ne|=1;var n=gt.transition,r=oe;try{if(gt.transition=null,oe=1,e)return e()}finally{oe=r,gt.transition=n,ne=t,!(ne&6)&&Rn()}}function dc(){rt=kr.current,ce(kr)}function Qn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wy(n)),Ce!==null)for(n=Ce.return;n!==null;){var r=n;switch(Uu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ga();break;case 3:Vr(),ce(Je),ce(De),ec();break;case 5:qu(r);break;case 4:Vr();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:Yu(r.type._context);break;case 22:case 23:dc()}n=n.return}if(_e=e,Ce=e=Tn(e.current,null),Ie=rt=t,Pe=0,el=null,sc=Xa=er=0,Ye=jo=null,Hn!==null){for(t=0;t<Hn.length;t++)if(n=Hn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,l=n.pending;if(l!==null){var a=l.next;l.next=o,r.next=a}n.pending=r}Hn=null}return e}function Eh(e,t){do{var n=Ce;try{if(Qu(),ta.current=ka,$a){for(var r=pe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}$a=!1}if(qn=0,Ne=ke=pe=null,Io=!1,Zo=0,ic.current=null,n===null||n.return===null){Pe=1,el=t,Ce=null;break}e:{var l=e,a=n.return,i=n,s=t;if(t=Ie,i.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var u=s,c=i,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=Vd(a);if(y!==null){y.flags&=-257,Bd(y,a,i,l,t),y.mode&1&&Wd(l,u,t),t=y,s=u;var g=t.updateQueue;if(g===null){var b=new Set;b.add(s),t.updateQueue=b}else g.add(s);break e}else{if(!(t&1)){Wd(l,u,t),fc();break e}s=Error(j(426))}}else if(de&&i.mode&1){var S=Vd(a);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Bd(S,a,i,l,t),Ku(Br(s,i));break e}}l=s=Br(s,i),Pe!==4&&(Pe=2),jo===null?jo=[l]:jo.push(l),l=a;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var h=ah(l,s,t);jd(l,h);break e;case 1:i=s;var m=l.type,v=l.stateNode;if(!(l.flags&128)&&(typeof m.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(kn===null||!kn.has(v)))){l.flags|=65536,t&=-t,l.lanes|=t;var x=ih(l,i,t);jd(l,x);break e}}l=l.return}while(l!==null)}kh(n)}catch(C){t=C,Ce===n&&n!==null&&(Ce=n=n.return);continue}break}while(!0)}function Ch(){var e=Pa.current;return Pa.current=ka,e===null?ka:e}function fc(){(Pe===0||Pe===3||Pe===2)&&(Pe=4),_e===null||!(er&268435455)&&!(Xa&268435455)||mn(_e,Ie)}function _a(e,t){var n=ne;ne|=2;var r=Ch();(_e!==e||Ie!==t)&&(Kt=null,Qn(e,t));do try{f1();break}catch(o){Eh(e,o)}while(!0);if(Qu(),ne=n,Pa.current=r,Ce!==null)throw Error(j(261));return _e=null,Ie=0,Pe}function f1(){for(;Ce!==null;)$h(Ce)}function p1(){for(;Ce!==null&&!F0();)$h(Ce)}function $h(e){var t=Th(e.alternate,e,rt);e.memoizedProps=e.pendingProps,t===null?kh(e):Ce=t,ic.current=null}function kh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=a1(n,t),n!==null){n.flags&=32767,Ce=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Pe=6,Ce=null;return}}else if(n=l1(n,t,rt),n!==null){Ce=n;return}if(t=t.sibling,t!==null){Ce=t;return}Ce=t=e}while(t!==null);Pe===0&&(Pe=5)}function Vn(e,t,n){var r=oe,o=gt.transition;try{gt.transition=null,oe=1,m1(e,t,n,r)}finally{gt.transition=o,oe=r}return null}function m1(e,t,n,r){do Ir();while(gn!==null);if(ne&6)throw Error(j(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(Q0(e,l),e===_e&&(Ce=_e=null,Ie=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||zl||(zl=!0,Nh(da,function(){return Ir(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=gt.transition,gt.transition=null;var a=oe;oe=1;var i=ne;ne|=4,ic.current=null,s1(e,n),wh(n,e),Ry(Ps),pa=!!ks,Ps=ks=null,e.current=n,u1(n),D0(),ne=i,oe=a,gt.transition=l}else e.current=n;if(zl&&(zl=!1,gn=e,Na=o),l=e.pendingLanes,l===0&&(kn=null),V0(n.stateNode),et(e,ye()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ta)throw Ta=!1,e=Gs,Gs=null,e;return Na&1&&e.tag!==0&&Ir(),l=e.pendingLanes,l&1?e===Qs?zo++:(zo=0,Qs=e):zo=0,Rn(),null}function Ir(){if(gn!==null){var e=am(Na),t=gt.transition,n=oe;try{if(gt.transition=null,oe=16>e?16:e,gn===null)var r=!1;else{if(e=gn,gn=null,Na=0,ne&6)throw Error(j(331));var o=ne;for(ne|=4,W=e.current;W!==null;){var l=W,a=l.child;if(W.flags&16){var i=l.deletions;if(i!==null){for(var s=0;s<i.length;s++){var u=i[s];for(W=u;W!==null;){var c=W;switch(c.tag){case 0:case 11:case 15:Ro(8,c,l)}var d=c.child;if(d!==null)d.return=c,W=d;else for(;W!==null;){c=W;var f=c.sibling,y=c.return;if(gh(c),c===u){W=null;break}if(f!==null){f.return=y,W=f;break}W=y}}}var g=l.alternate;if(g!==null){var b=g.child;if(b!==null){g.child=null;do{var S=b.sibling;b.sibling=null,b=S}while(b!==null)}}W=l}}if(l.subtreeFlags&2064&&a!==null)a.return=l,W=a;else e:for(;W!==null;){if(l=W,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Ro(9,l,l.return)}var h=l.sibling;if(h!==null){h.return=l.return,W=h;break e}W=l.return}}var m=e.current;for(W=m;W!==null;){a=W;var v=a.child;if(a.subtreeFlags&2064&&v!==null)v.return=a,W=v;else e:for(a=m;W!==null;){if(i=W,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Ya(9,i)}}catch(C){he(i,i.return,C)}if(i===a){W=null;break e}var x=i.sibling;if(x!==null){x.return=i.return,W=x;break e}W=i.return}}if(ne=o,Rn(),Ft&&typeof Ft.onPostCommitFiberRoot=="function")try{Ft.onPostCommitFiberRoot(Wa,e)}catch{}r=!0}return r}finally{oe=n,gt.transition=t}}return!1}function nf(e,t,n){t=Br(n,t),t=ah(e,t,1),e=$n(e,t,1),t=Ve(),e!==null&&(sl(e,1,t),et(e,t))}function he(e,t,n){if(e.tag===3)nf(e,e,n);else for(;t!==null;){if(t.tag===3){nf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(kn===null||!kn.has(r))){e=Br(n,e),e=ih(t,e,1),t=$n(t,e,1),e=Ve(),t!==null&&(sl(t,1,e),et(t,e));break}}t=t.return}}function h1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,_e===e&&(Ie&n)===n&&(Pe===4||Pe===3&&(Ie&130023424)===Ie&&500>ye()-uc?Qn(e,0):sc|=n),et(e,t)}function Ph(e,t){t===0&&(e.mode&1?(t=kl,kl<<=1,!(kl&130023424)&&(kl=4194304)):t=1);var n=Ve();e=tn(e,t),e!==null&&(sl(e,t,n),et(e,n))}function v1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ph(e,n)}function g1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),Ph(e,n)}var Th;Th=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Je.current)Xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Xe=!1,o1(e,t,n);Xe=!!(e.flags&131072)}else Xe=!1,de&&t.flags&1048576&&Lm(t,wa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ra(e,t),e=t.pendingProps;var o=Dr(t,De.current);Lr(t,n),o=nc(null,t,r,e,o,n);var l=rc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(r)?(l=!0,ya(t)):l=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Zu(t),o.updater=Qa,t.stateNode=o,o._reactInternals=t,zs(t,r,e,n),t=Ds(null,t,r,!0,l,n)):(t.tag=0,de&&l&&Hu(t),We(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ra(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=b1(r),e=Ct(r,e),o){case 0:t=Fs(null,t,r,e,n);break e;case 1:t=Kd(null,t,r,e,n);break e;case 11:t=Hd(null,t,r,e,n);break e;case 14:t=Ud(null,t,r,Ct(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Fs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Kd(e,t,r,o,n);case 3:e:{if(dh(t),e===null)throw Error(j(387));r=t.pendingProps,l=t.memoizedState,o=l.element,Fm(e,t),Ea(t,r,null,n);var a=t.memoizedState;if(r=a.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){o=Br(Error(j(423)),t),t=Gd(e,t,r,n,o);break e}else if(r!==o){o=Br(Error(j(424)),t),t=Gd(e,t,r,n,o);break e}else for(ot=Cn(t.stateNode.containerInfo.firstChild),lt=t,de=!0,Pt=null,n=zm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ar(),r===o){t=nn(e,t,n);break e}We(e,t,r,n)}t=t.child}return t;case 5:return Dm(t),e===null&&Is(t),r=t.type,o=t.pendingProps,l=e!==null?e.memoizedProps:null,a=o.children,Ts(r,o)?a=null:l!==null&&Ts(r,l)&&(t.flags|=32),ch(e,t),We(e,t,a,n),t.child;case 6:return e===null&&Is(t),null;case 13:return fh(e,t,n);case 4:return Ju(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wr(t,null,r,n):We(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Hd(e,t,r,o,n);case 7:return We(e,t,t.pendingProps,n),t.child;case 8:return We(e,t,t.pendingProps.children,n),t.child;case 12:return We(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,l=t.memoizedProps,a=o.value,ie(xa,r._currentValue),r._currentValue=a,l!==null)if(_t(l.value,a)){if(l.children===o.children&&!Je.current){t=nn(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var i=l.dependencies;if(i!==null){a=l.child;for(var s=i.firstContext;s!==null;){if(s.context===r){if(l.tag===1){s=Jt(-1,n&-n),s.tag=2;var u=l.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?s.next=s:(s.next=c.next,c.next=s),u.pending=s}}l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Rs(l.return,n,t),i.lanes|=n;break}s=s.next}}else if(l.tag===10)a=l.type===t.type?null:l.child;else if(l.tag===18){if(a=l.return,a===null)throw Error(j(341));a.lanes|=n,i=a.alternate,i!==null&&(i.lanes|=n),Rs(a,n,t),a=l.sibling}else a=l.child;if(a!==null)a.return=l;else for(a=l;a!==null;){if(a===t){a=null;break}if(l=a.sibling,l!==null){l.return=a.return,a=l;break}a=a.return}l=a}We(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Lr(t,n),o=yt(o),r=r(o),t.flags|=1,We(e,t,r,n),t.child;case 14:return r=t.type,o=Ct(r,t.pendingProps),o=Ct(r.type,o),Ud(e,t,r,o,n);case 15:return sh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),ra(e,t),t.tag=1,qe(r)?(e=!0,ya(t)):e=!1,Lr(t,n),lh(t,r,o),zs(t,r,o,n),Ds(null,t,r,!0,e,n);case 19:return ph(e,t,n);case 22:return uh(e,t,n)}throw Error(j(156,t.tag))};function Nh(e,t){return nm(e,t)}function y1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,n,r){return new y1(e,t,n,r)}function pc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function b1(e){if(typeof e=="function")return pc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Lu)return 11;if(e===Iu)return 14}return 2}function Tn(e,t){var n=e.alternate;return n===null?(n=ht(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function aa(e,t,n,r,o,l){var a=2;if(r=e,typeof e=="function")pc(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case vr:return Yn(n.children,o,l,t);case Mu:a=8,o|=8;break;case as:return e=ht(12,n,t,o|2),e.elementType=as,e.lanes=l,e;case is:return e=ht(13,n,t,o),e.elementType=is,e.lanes=l,e;case ss:return e=ht(19,n,t,o),e.elementType=ss,e.lanes=l,e;case Dp:return Za(n,o,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Op:a=10;break e;case Fp:a=9;break e;case Lu:a=11;break e;case Iu:a=14;break e;case dn:a=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=ht(a,n,t,o),t.elementType=e,t.type=r,t.lanes=l,t}function Yn(e,t,n,r){return e=ht(7,e,r,t),e.lanes=n,e}function Za(e,t,n,r){return e=ht(22,e,r,t),e.elementType=Dp,e.lanes=n,e.stateNode={isHidden:!1},e}function Vi(e,t,n){return e=ht(6,e,null,t),e.lanes=n,e}function Bi(e,t,n){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function w1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ei(0),this.expirationTimes=Ei(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ei(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function mc(e,t,n,r,o,l,a,i,s){return e=new w1(e,t,n,i,s),t===1?(t=1,l===!0&&(t|=8)):t=0,l=ht(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zu(l),e}function x1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:hr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function _h(e){if(!e)return Mn;e=e._reactInternals;e:{if(sr(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(qe(n))return _m(e,n,t)}return t}function Mh(e,t,n,r,o,l,a,i,s){return e=mc(n,r,!0,e,o,l,a,i,s),e.context=_h(null),n=e.current,r=Ve(),o=Pn(n),l=Jt(r,o),l.callback=t??null,$n(n,l,o),e.current.lanes=o,sl(e,o,r),et(e,r),e}function Ja(e,t,n,r){var o=t.current,l=Ve(),a=Pn(o);return n=_h(n),t.context===null?t.context=n:t.pendingContext=n,t=Jt(l,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=$n(o,t,a),e!==null&&(Nt(e,o,a,l),ea(e,o,a)),a}function Ma(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function rf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function hc(e,t){rf(e,t),(e=e.alternate)&&rf(e,t)}function S1(){return null}var Lh=typeof reportError=="function"?reportError:function(e){console.error(e)};function vc(e){this._internalRoot=e}qa.prototype.render=vc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));Ja(e,t,null,null)};qa.prototype.unmount=vc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tr(function(){Ja(null,e,null,null)}),t[en]=null}};function qa(e){this._internalRoot=e}qa.prototype.unstable_scheduleHydration=function(e){if(e){var t=um();e={blockedOn:null,target:e,priority:t};for(var n=0;n<pn.length&&t!==0&&t<pn[n].priority;n++);pn.splice(n,0,e),n===0&&dm(e)}};function gc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ei(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function of(){}function E1(e,t,n,r,o){if(o){if(typeof r=="function"){var l=r;r=function(){var u=Ma(a);l.call(u)}}var a=Mh(t,r,e,0,null,!1,!1,"",of);return e._reactRootContainer=a,e[en]=a.current,Ko(e.nodeType===8?e.parentNode:e),tr(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var i=r;r=function(){var u=Ma(s);i.call(u)}}var s=mc(e,0,!1,null,null,!1,!1,"",of);return e._reactRootContainer=s,e[en]=s.current,Ko(e.nodeType===8?e.parentNode:e),tr(function(){Ja(t,s,n,r)}),s}function ti(e,t,n,r,o){var l=n._reactRootContainer;if(l){var a=l;if(typeof o=="function"){var i=o;o=function(){var s=Ma(a);i.call(s)}}Ja(t,a,e,o)}else a=E1(n,t,e,o,r);return Ma(a)}im=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=So(t.pendingLanes);n!==0&&(zu(t,n|1),et(t,ye()),!(ne&6)&&(Hr=ye()+500,Rn()))}break;case 13:tr(function(){var r=tn(e,1);if(r!==null){var o=Ve();Nt(r,e,1,o)}}),hc(e,1)}};Ou=function(e){if(e.tag===13){var t=tn(e,134217728);if(t!==null){var n=Ve();Nt(t,e,134217728,n)}hc(e,134217728)}};sm=function(e){if(e.tag===13){var t=Pn(e),n=tn(e,t);if(n!==null){var r=Ve();Nt(n,e,t,r)}hc(e,t)}};um=function(){return oe};cm=function(e,t){var n=oe;try{return oe=e,t()}finally{oe=n}};ys=function(e,t,n){switch(t){case"input":if(ds(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ua(r);if(!o)throw Error(j(90));Wp(r),ds(r,o)}}}break;case"textarea":Bp(e,n);break;case"select":t=n.value,t!=null&&Tr(e,!!n.multiple,t,!1)}};Xp=cc;Zp=tr;var C1={usingClientEntryPoint:!1,Events:[cl,wr,Ua,Qp,Yp,cc]},po={findFiberByHostInstance:Bn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$1={bundleType:po.bundleType,version:po.version,rendererPackageName:po.rendererPackageName,rendererConfig:po.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:rn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=em(e),e===null?null:e.stateNode},findFiberByHostInstance:po.findFiberByHostInstance||S1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ol=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ol.isDisabled&&Ol.supportsFiber)try{Wa=Ol.inject($1),Ft=Ol}catch{}}it.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=C1;it.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!gc(t))throw Error(j(200));return x1(e,t,null,n)};it.createRoot=function(e,t){if(!gc(e))throw Error(j(299));var n=!1,r="",o=Lh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=mc(e,1,!1,null,null,n,!1,r,o),e[en]=t.current,Ko(e.nodeType===8?e.parentNode:e),new vc(t)};it.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=em(t),e=e===null?null:e.stateNode,e};it.flushSync=function(e){return tr(e)};it.hydrate=function(e,t,n){if(!ei(t))throw Error(j(200));return ti(null,e,t,!0,n)};it.hydrateRoot=function(e,t,n){if(!gc(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,o=!1,l="",a=Lh;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Mh(t,null,e,1,n??null,o,!1,l,a),e[en]=t.current,Ko(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new qa(t)};it.render=function(e,t,n){if(!ei(t))throw Error(j(200));return ti(null,e,t,!1,n)};it.unmountComponentAtNode=function(e){if(!ei(e))throw Error(j(40));return e._reactRootContainer?(tr(function(){ti(null,null,e,!1,function(){e._reactRootContainer=null,e[en]=null})}),!0):!1};it.unstable_batchedUpdates=cc;it.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ei(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return ti(e,t,n,!1,r)};it.version="18.3.1-next-f1338f8080-20240426";function Ih(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ih)}catch(e){console.error(e)}}Ih(),Ip.exports=it;var Rh=Ip.exports;const jh=xp(Rh);var lf=Rh;os.createRoot=lf.createRoot,os.hydrateRoot=lf.hydrateRoot;/**
 * @remix-run/router v1.16.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function tl(){return tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},tl.apply(this,arguments)}var yn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(yn||(yn={}));const af="popstate";function k1(e){e===void 0&&(e={});function t(r,o){let{pathname:l,search:a,hash:i}=r.location;return Zs("",{pathname:l,search:a,hash:i},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Oh(o)}return T1(t,n,null,e)}function $e(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function zh(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function P1(){return Math.random().toString(36).substr(2,8)}function sf(e,t){return{usr:e.state,key:e.key,idx:t}}function Zs(e,t,n,r){return n===void 0&&(n=null),tl({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Xr(t):t,{state:n,key:t&&t.key||r||P1()})}function Oh(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Xr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function T1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:l=!1}=r,a=o.history,i=yn.Pop,s=null,u=c();u==null&&(u=0,a.replaceState(tl({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function d(){i=yn.Pop;let S=c(),h=S==null?null:S-u;u=S,s&&s({action:i,location:b.location,delta:h})}function f(S,h){i=yn.Push;let m=Zs(b.location,S,h);u=c()+1;let v=sf(m,u),x=b.createHref(m);try{a.pushState(v,"",x)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(x)}l&&s&&s({action:i,location:b.location,delta:1})}function y(S,h){i=yn.Replace;let m=Zs(b.location,S,h);u=c();let v=sf(m,u),x=b.createHref(m);a.replaceState(v,"",x),l&&s&&s({action:i,location:b.location,delta:0})}function g(S){let h=o.location.origin!=="null"?o.location.origin:o.location.href,m=typeof S=="string"?S:Oh(S);return m=m.replace(/ $/,"%20"),$e(h,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,h)}let b={get action(){return i},get location(){return e(o,a)},listen(S){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(af,d),s=S,()=>{o.removeEventListener(af,d),s=null}},createHref(S){return t(o,S)},createURL:g,encodeLocation(S){let h=g(S);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:y,go(S){return a.go(S)}};return b}var uf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(uf||(uf={}));function N1(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?Xr(t):t,o=Ah(r.pathname||"/",n);if(o==null)return null;let l=Fh(e);_1(l);let a=null;for(let i=0;a==null&&i<l.length;++i){let s=V1(o);a=D1(l[i],s)}return a}function Fh(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(l,a,i)=>{let s={relativePath:i===void 0?l.path||"":i,caseSensitive:l.caseSensitive===!0,childrenIndex:a,route:l};s.relativePath.startsWith("/")&&($e(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(r.length));let u=Nn([r,s.relativePath]),c=n.concat(s);l.children&&l.children.length>0&&($e(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Fh(l.children,t,c,u)),!(l.path==null&&!l.index)&&t.push({path:u,score:O1(u,l.index),routesMeta:c})};return e.forEach((l,a)=>{var i;if(l.path===""||!((i=l.path)!=null&&i.includes("?")))o(l,a);else for(let s of Dh(l.path))o(l,a,s)}),t}function Dh(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return o?[l,""]:[l];let a=Dh(r.join("/")),i=[];return i.push(...a.map(s=>s===""?l:[l,s].join("/"))),o&&i.push(...a),i.map(s=>e.startsWith("/")&&s===""?"/":s)}function _1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:F1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const M1=/^:[\w-]+$/,L1=3,I1=2,R1=1,j1=10,z1=-2,cf=e=>e==="*";function O1(e,t){let n=e.split("/"),r=n.length;return n.some(cf)&&(r+=z1),t&&(r+=I1),n.filter(o=>!cf(o)).reduce((o,l)=>o+(M1.test(l)?L1:l===""?R1:j1),r)}function F1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function D1(e,t){let{routesMeta:n}=e,r={},o="/",l=[];for(let a=0;a<n.length;++a){let i=n[a],s=a===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",c=A1({path:i.relativePath,caseSensitive:i.caseSensitive,end:s},u);if(!c)return null;Object.assign(r,c.params);let d=i.route;l.push({params:r,pathname:Nn([o,c.pathname]),pathnameBase:K1(Nn([o,c.pathnameBase])),route:d}),c.pathnameBase!=="/"&&(o=Nn([o,c.pathnameBase]))}return l}function A1(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=W1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let l=o[0],a=l.replace(/(.)\/+$/,"$1"),i=o.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:y}=c;if(f==="*"){let b=i[d]||"";a=l.slice(0,l.length-b.length).replace(/(.)\/+$/,"$1")}const g=i[d];return y&&!g?u[f]=void 0:u[f]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:l,pathnameBase:a,pattern:e}}function W1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),zh(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,i,s)=>(r.push({paramName:i,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function V1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return zh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ah(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function B1(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Xr(e):e;return{pathname:n?n.startsWith("/")?n:H1(n,t):t,search:G1(r),hash:Q1(o)}}function H1(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Hi(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function U1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Wh(e,t){let n=U1(e);return t?n.map((r,o)=>o===e.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Vh(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Xr(e):(o=tl({},e),$e(!o.pathname||!o.pathname.includes("?"),Hi("?","pathname","search",o)),$e(!o.pathname||!o.pathname.includes("#"),Hi("#","pathname","hash",o)),$e(!o.search||!o.search.includes("#"),Hi("#","search","hash",o)));let l=e===""||o.pathname==="",a=l?"/":o.pathname,i;if(a==null)i=n;else{let d=t.length-1;if(!r&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),d-=1;o.pathname=f.join("/")}i=d>=0?t[d]:"/"}let s=B1(o,i),u=a&&a!=="/"&&a.endsWith("/"),c=(l||a===".")&&n.endsWith("/");return!s.pathname.endsWith("/")&&(u||c)&&(s.pathname+="/"),s}const Nn=e=>e.join("/").replace(/\/\/+/g,"/"),K1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),G1=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Q1=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Y1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Bh=["post","put","patch","delete"];new Set(Bh);const X1=["get",...Bh];new Set(X1);/**
 * React Router v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function nl(){return nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nl.apply(this,arguments)}const yc=p.createContext(null),Z1=p.createContext(null),Zr=p.createContext(null),ni=p.createContext(null),ur=p.createContext({outlet:null,matches:[],isDataRoute:!1}),Hh=p.createContext(null);function J1(e,t){let{relative:n}=t===void 0?{}:t;fl()||$e(!1);let{basename:r,navigator:o}=p.useContext(Zr),{hash:l,pathname:a,search:i}=tb(e,{relative:n}),s=a;return r!=="/"&&(s=a==="/"?r:Nn([r,a])),o.createHref({pathname:s,search:i,hash:l})}function fl(){return p.useContext(ni)!=null}function bc(){return fl()||$e(!1),p.useContext(ni).location}function Uh(e){p.useContext(Zr).static||p.useLayoutEffect(e)}function q1(){let{isDataRoute:e}=p.useContext(ur);return e?pb():eb()}function eb(){fl()||$e(!1);let e=p.useContext(yc),{basename:t,future:n,navigator:r}=p.useContext(Zr),{matches:o}=p.useContext(ur),{pathname:l}=bc(),a=JSON.stringify(Wh(o,n.v7_relativeSplatPath)),i=p.useRef(!1);return Uh(()=>{i.current=!0}),p.useCallback(function(u,c){if(c===void 0&&(c={}),!i.current)return;if(typeof u=="number"){r.go(u);return}let d=Vh(u,JSON.parse(a),l,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Nn([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,a,l,e])}function tb(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=p.useContext(Zr),{matches:o}=p.useContext(ur),{pathname:l}=bc(),a=JSON.stringify(Wh(o,r.v7_relativeSplatPath));return p.useMemo(()=>Vh(e,JSON.parse(a),l,n==="path"),[e,a,l,n])}function nb(e,t){return rb(e,t)}function rb(e,t,n,r){fl()||$e(!1);let{navigator:o}=p.useContext(Zr),{matches:l}=p.useContext(ur),a=l[l.length-1],i=a?a.params:{};a&&a.pathname;let s=a?a.pathnameBase:"/";a&&a.route;let u=bc(),c;if(t){var d;let S=typeof t=="string"?Xr(t):t;s==="/"||(d=S.pathname)!=null&&d.startsWith(s)||$e(!1),c=S}else c=u;let f=c.pathname||"/",y=f;if(s!=="/"){let S=s.replace(/^\//,"").split("/");y="/"+f.replace(/^\//,"").split("/").slice(S.length).join("/")}let g=N1(e,{pathname:y}),b=sb(g&&g.map(S=>Object.assign({},S,{params:Object.assign({},i,S.params),pathname:Nn([s,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?s:Nn([s,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),l,n,r);return t&&b?p.createElement(ni.Provider,{value:{location:nl({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:yn.Pop}},b):b}function ob(){let e=fb(),t=Y1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return p.createElement(p.Fragment,null,p.createElement("h2",null,"Unexpected Application Error!"),p.createElement("h3",{style:{fontStyle:"italic"}},t),n?p.createElement("pre",{style:o},n):null,null)}const lb=p.createElement(ob,null);class ab extends p.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?p.createElement(ur.Provider,{value:this.props.routeContext},p.createElement(Hh.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ib(e){let{routeContext:t,match:n,children:r}=e,o=p.useContext(yc);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),p.createElement(ur.Provider,{value:t},r)}function sb(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if((l=n)!=null&&l.errors)e=n.matches;else return null}let a=e,i=(o=n)==null?void 0:o.errors;if(i!=null){let c=a.findIndex(d=>d.route.id&&(i==null?void 0:i[d.route.id])!==void 0);c>=0||$e(!1),a=a.slice(0,Math.min(a.length,c+1))}let s=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let d=a[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:y}=n,g=d.route.loader&&f[d.route.id]===void 0&&(!y||y[d.route.id]===void 0);if(d.route.lazy||g){s=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,d,f)=>{let y,g=!1,b=null,S=null;n&&(y=i&&d.route.id?i[d.route.id]:void 0,b=d.route.errorElement||lb,s&&(u<0&&f===0?(mb("route-fallback"),g=!0,S=null):u===f&&(g=!0,S=d.route.hydrateFallbackElement||null)));let h=t.concat(a.slice(0,f+1)),m=()=>{let v;return y?v=b:g?v=S:d.route.Component?v=p.createElement(d.route.Component,null):d.route.element?v=d.route.element:v=c,p.createElement(ib,{match:d,routeContext:{outlet:c,matches:h,isDataRoute:n!=null},children:v})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?p.createElement(ab,{location:n.location,revalidation:n.revalidation,component:b,error:y,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()},null)}var Kh=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Kh||{}),Gh=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Gh||{});function ub(e){let t=p.useContext(yc);return t||$e(!1),t}function cb(e){let t=p.useContext(Z1);return t||$e(!1),t}function db(e){let t=p.useContext(ur);return t||$e(!1),t}function Qh(e){let t=db(),n=t.matches[t.matches.length-1];return n.route.id||$e(!1),n.route.id}function fb(){var e;let t=p.useContext(Hh),n=cb(),r=Qh();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function pb(){let{router:e}=ub(Kh.UseNavigateStable),t=Qh(Gh.UseNavigateStable),n=p.useRef(!1);return Uh(()=>{n.current=!0}),p.useCallback(function(o,l){l===void 0&&(l={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,nl({fromRouteId:t},l)))},[e,t])}const df={};function mb(e,t,n){df[e]||(df[e]=!0)}function mr(e){$e(!1)}function hb(e){let{basename:t="/",children:n=null,location:r,navigationType:o=yn.Pop,navigator:l,static:a=!1,future:i}=e;fl()&&$e(!1);let s=t.replace(/^\/*/,"/"),u=p.useMemo(()=>({basename:s,navigator:l,static:a,future:nl({v7_relativeSplatPath:!1},i)}),[s,i,l,a]);typeof r=="string"&&(r=Xr(r));let{pathname:c="/",search:d="",hash:f="",state:y=null,key:g="default"}=r,b=p.useMemo(()=>{let S=Ah(c,s);return S==null?null:{location:{pathname:S,search:d,hash:f,state:y,key:g},navigationType:o}},[s,c,d,f,y,g,o]);return b==null?null:p.createElement(Zr.Provider,{value:u},p.createElement(ni.Provider,{children:n,value:b}))}function vb(e){let{children:t,location:n}=e;return nb(Js(t),n)}new Promise(()=>{});function Js(e,t){t===void 0&&(t=[]);let n=[];return p.Children.forEach(e,(r,o)=>{if(!p.isValidElement(r))return;let l=[...t,o];if(r.type===p.Fragment){n.push.apply(n,Js(r.props.children,l));return}r.type!==mr&&$e(!1),!r.props.index||!r.props.children||$e(!1);let a={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Js(r.props.children,l)),n.push(a)}),n}/**
 * React Router DOM v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const gb="6";try{window.__reactRouterVersion=gb}catch{}const yb="startTransition",ff=h0[yb];function bb(e){let{basename:t,children:n,future:r,window:o}=e,l=p.useRef();l.current==null&&(l.current=k1({window:o,v5Compat:!0}));let a=l.current,[i,s]=p.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},c=p.useCallback(d=>{u&&ff?ff(()=>s(d)):s(d)},[s,u]);return p.useLayoutEffect(()=>a.listen(c),[a,c]),p.createElement(hb,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:a,future:r})}var pf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(pf||(pf={}));var mf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(mf||(mf={}));var wb={default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},xb={default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground"},Sb={default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger"},Eb={default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500"},Cb={default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger"},$b={default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger"},kb={default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger"},F={solid:wb,shadow:xb,bordered:Sb,flat:Eb,faded:Cb,light:$b,ghost:kb},Fl=["small","medium","large"],hf={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:Fl,borderRadius:Fl},classGroups:{shadow:[{shadow:Fl}],"font-size":[{text:["tiny",...Fl]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}},vf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ge=e=>!e||typeof e!="object"||Object.keys(e).length===0,Pb=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function Yh(e,t){e.forEach(function(n){Array.isArray(n)?Yh(n,t):t.push(n)})}function Xh(e){let t=[];return Yh(e,t),t}var Zh=(...e)=>Xh(e).filter(Boolean),Jh=(e,t)=>{let n={},r=Object.keys(e),o=Object.keys(t);for(let l of r)if(o.includes(l)){let a=e[l],i=t[l];Array.isArray(a)||Array.isArray(i)?n[l]=Zh(i,a):typeof a=="object"&&typeof i=="object"?n[l]=Jh(a,i):n[l]=i+" "+a}else n[l]=e[l];for(let l of o)r.includes(l)||(n[l]=t[l]);return n},gf=e=>!e||typeof e!="string"?e:e.replace(/\s+/g," ").trim();const wc="-",Tb=e=>{const t=_b(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:a=>{const i=a.split(wc);return i[0]===""&&i.length!==1&&i.shift(),qh(i,t)||Nb(a)},getConflictingClassGroupIds:(a,i)=>{const s=n[a]||[];return i&&r[a]?[...s,...r[a]]:s}}},qh=(e,t)=>{var a;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?qh(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const l=e.join(wc);return(a=t.validators.find(({validator:i})=>i(l)))==null?void 0:a.classGroupId},yf=/^\[(.+)\]$/,Nb=e=>{if(yf.test(e)){const t=yf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},_b=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Lb(Object.entries(e.classGroups),n).forEach(([l,a])=>{qs(a,r,l,t)}),r},qs=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const l=o===""?t:bf(t,o);l.classGroupId=n;return}if(typeof o=="function"){if(Mb(o)){qs(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([l,a])=>{qs(a,bf(t,l),n,r)})})},bf=(e,t)=>{let n=e;return t.split(wc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Mb=e=>e.isThemeGetter,Lb=(e,t)=>t?e.map(([n,r])=>{const o=r.map(l=>typeof l=="string"?t+l:typeof l=="object"?Object.fromEntries(Object.entries(l).map(([a,i])=>[t+a,i])):l);return[n,o]}):e,Ib=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(l,a)=>{n.set(l,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(l){let a=n.get(l);if(a!==void 0)return a;if((a=r.get(l))!==void 0)return o(l,a),a},set(l,a){n.has(l)?n.set(l,a):o(l,a)}}},ev="!",Rb=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],l=t.length,a=i=>{const s=[];let u=0,c=0,d;for(let S=0;S<i.length;S++){let h=i[S];if(u===0){if(h===o&&(r||i.slice(S,S+l)===t)){s.push(i.slice(c,S)),c=S+l;continue}if(h==="/"){d=S;continue}}h==="["?u++:h==="]"&&u--}const f=s.length===0?i:i.substring(c),y=f.startsWith(ev),g=y?f.substring(1):f,b=d&&d>c?d-c:void 0;return{modifiers:s,hasImportantModifier:y,baseClassName:g,maybePostfixModifierPosition:b}};return n?i=>n({className:i,parseClassName:a}):a},jb=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},zb=e=>({cache:Ib(e.cacheSize),parseClassName:Rb(e),...Tb(e)}),Ob=/\s+/,Fb=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],a=e.trim().split(Ob);let i="";for(let s=a.length-1;s>=0;s-=1){const u=a[s],{modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:y}=n(u);let g=!!y,b=r(g?f.substring(0,y):f);if(!b){if(!g){i=u+(i.length>0?" "+i:i);continue}if(b=r(f),!b){i=u+(i.length>0?" "+i:i);continue}g=!1}const S=jb(c).join(":"),h=d?S+ev:S,m=h+b;if(l.includes(m))continue;l.push(m);const v=o(b,g);for(let x=0;x<v.length;++x){const C=v[x];l.push(h+C)}i=u+(i.length>0?" "+i:i)}return i};function Db(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=tv(t))&&(r&&(r+=" "),r+=n);return r}const tv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=tv(e[r]))&&(n&&(n+=" "),n+=t);return n};function eu(e,...t){let n,r,o,l=a;function a(s){const u=t.reduce((c,d)=>d(c),e());return n=zb(u),r=n.cache.get,o=n.cache.set,l=i,i(s)}function i(s){const u=r(s);if(u)return u;const c=Fb(s,n);return o(s,c),c}return function(){return l(Db.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},nv=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ab=/^\d+\/\d+$/,Wb=new Set(["px","full","screen"]),Vb=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Bb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Hb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ub=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Kb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ht=e=>Rr(e)||Wb.has(e)||Ab.test(e),un=e=>Jr(e,"length",ew),Rr=e=>!!e&&!Number.isNaN(Number(e)),Ui=e=>Jr(e,"number",Rr),mo=e=>!!e&&Number.isInteger(Number(e)),Gb=e=>e.endsWith("%")&&Rr(e.slice(0,-1)),X=e=>nv.test(e),cn=e=>Vb.test(e),Qb=new Set(["length","size","percentage"]),Yb=e=>Jr(e,Qb,rv),Xb=e=>Jr(e,"position",rv),Zb=new Set(["image","url"]),Jb=e=>Jr(e,Zb,nw),qb=e=>Jr(e,"",tw),ho=()=>!0,Jr=(e,t,n)=>{const r=nv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},ew=e=>Bb.test(e)&&!Hb.test(e),rv=()=>!1,tw=e=>Ub.test(e),nw=e=>Kb.test(e),tu=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),l=se("borderRadius"),a=se("borderSpacing"),i=se("borderWidth"),s=se("contrast"),u=se("grayscale"),c=se("hueRotate"),d=se("invert"),f=se("gap"),y=se("gradientColorStops"),g=se("gradientColorStopPositions"),b=se("inset"),S=se("margin"),h=se("opacity"),m=se("padding"),v=se("saturate"),x=se("scale"),C=se("sepia"),T=se("skew"),L=se("space"),M=se("translate"),D=()=>["auto","contain","none"],I=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto",X,t],k=()=>[X,t],z=()=>["",Ht,un],$=()=>["auto",Rr,X],A=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],N=()=>["solid","dashed","dotted","double","none"],O=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",X],_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>[Rr,X];return{cacheSize:500,separator:":",theme:{colors:[ho],spacing:[Ht,un],blur:["none","",cn,X],brightness:V(),borderColor:[e],borderRadius:["none","","full",cn,X],borderSpacing:k(),borderWidth:z(),contrast:V(),grayscale:R(),hueRotate:V(),invert:R(),gap:k(),gradientColorStops:[e],gradientColorStopPositions:[Gb,un],inset:E(),margin:E(),opacity:V(),padding:k(),saturate:V(),scale:V(),sepia:R(),skew:V(),space:k(),translate:k()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[cn]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...A(),X]}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",mo,X]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",mo,X]}],"grid-cols":[{"grid-cols":[ho]}],"col-start-end":[{col:["auto",{span:["full",mo,X]},X]}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":[ho]}],"row-start-end":[{row:["auto",{span:[mo,X]},X]}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[m]}],px:[{px:[m]}],py:[{py:[m]}],ps:[{ps:[m]}],pe:[{pe:[m]}],pt:[{pt:[m]}],pr:[{pr:[m]}],pb:[{pb:[m]}],pl:[{pl:[m]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[L]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[L]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,t]}],"min-w":[{"min-w":[X,t,"min","max","fit"]}],"max-w":[{"max-w":[X,t,"none","full","min","max","fit","prose",{screen:[cn]},cn]}],h:[{h:[X,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,t,"auto","min","max","fit"]}],"font-size":[{text:["base",cn,un]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ui]}],"font-family":[{font:[ho]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",Rr,Ui]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ht,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...N(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ht,un]}],"underline-offset":[{"underline-offset":["auto",Ht,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...A(),Xb]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Yb]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Jb]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...N(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:N()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...N()]}],"outline-offset":[{"outline-offset":[Ht,X]}],"outline-w":[{outline:[Ht,un]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Ht,un]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",cn,qb]}],"shadow-color":[{shadow:[ho]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...O(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":O()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",cn,X]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[v]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:V()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:V()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[mo,X]}],"translate-x":[{"translate-x":[M]}],"translate-y":[{"translate-y":[M]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ht,un,Ui]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},rw=(e,{cacheSize:t,prefix:n,separator:r,experimentalParseClassName:o,extend:l={},override:a={}})=>{Co(e,"cacheSize",t),Co(e,"prefix",n),Co(e,"separator",r),Co(e,"experimentalParseClassName",o);for(const i in a)ow(e[i],a[i]);for(const i in l)lw(e[i],l[i]);return e},Co=(e,t,n)=>{n!==void 0&&(e[t]=n)},ow=(e,t)=>{if(t)for(const n in t)Co(e,n,t[n])},lw=(e,t)=>{if(t)for(const n in t){const r=t[n];r!==void 0&&(e[n]=(e[n]||[]).concat(r))}},aw=(e,...t)=>typeof e=="function"?eu(tu,e,...t):eu(()=>rw(tu(),e),...t),iw=eu(tu);var sw={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},ov=e=>e||void 0,rl=(...e)=>ov(Xh(e).filter(Boolean).join(" ")),Ki=null,Gt={},nu=!1,vo=(...e)=>t=>t.twMerge?((!Ki||nu)&&(nu=!1,Ki=Ge(Gt)?iw:aw({...Gt,extend:{theme:Gt.theme,classGroups:Gt.classGroups,conflictingClassGroupModifiers:Gt.conflictingClassGroupModifiers,conflictingClassGroups:Gt.conflictingClassGroups,...Gt.extend}})),ov(Ki(rl(e)))):rl(e),wf=(e,t)=>{for(let n in t)e.hasOwnProperty(n)?e[n]=rl(e[n],t[n]):e[n]=t[n];return e},xc=(e,t)=>{let{extend:n=null,slots:r={},variants:o={},compoundVariants:l=[],compoundSlots:a=[],defaultVariants:i={}}=e,s={...sw,...t},u=n!=null&&n.base?rl(n.base,e==null?void 0:e.base):e==null?void 0:e.base,c=n!=null&&n.variants&&!Ge(n.variants)?Jh(o,n.variants):o,d=n!=null&&n.defaultVariants&&!Ge(n.defaultVariants)?{...n.defaultVariants,...i}:i;!Ge(s.twMergeConfig)&&!Pb(s.twMergeConfig,Gt)&&(nu=!0,Gt=s.twMergeConfig);let f=Ge(n==null?void 0:n.slots),y=Ge(r)?{}:{base:rl(e==null?void 0:e.base,f&&(n==null?void 0:n.base)),...r},g=f?y:wf({...n==null?void 0:n.slots},Ge(y)?{base:e==null?void 0:e.base}:y),b=Ge(n==null?void 0:n.compoundVariants)?l:Zh(n==null?void 0:n.compoundVariants,l),S=m=>{if(Ge(c)&&Ge(r)&&f)return vo(u,m==null?void 0:m.class,m==null?void 0:m.className)(s);if(b&&!Array.isArray(b))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof b}`);if(a&&!Array.isArray(a))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof a}`);let v=(k,z,$=[],A)=>{let N=$;if(typeof z=="string")N=N.concat(gf(z).split(" ").map(O=>`${k}:${O}`));else if(Array.isArray(z))N=N.concat(z.reduce((O,P)=>O.concat(`${k}:${P}`),[]));else if(typeof z=="object"&&typeof A=="string"){for(let O in z)if(z.hasOwnProperty(O)&&O===A){let P=z[O];if(P&&typeof P=="string"){let R=gf(P);N[A]?N[A]=N[A].concat(R.split(" ").map(_=>`${k}:${_}`)):N[A]=R.split(" ").map(_=>`${k}:${_}`)}else Array.isArray(P)&&P.length>0&&(N[A]=P.reduce((R,_)=>R.concat(`${k}:${_}`),[]))}}return N},x=(k,z=c,$=null,A=null)=>{var N;let O=z[k];if(!O||Ge(O))return null;let P=(N=A==null?void 0:A[k])!=null?N:m==null?void 0:m[k];if(P===null)return null;let R=vf(P),_=Array.isArray(s.responsiveVariants)&&s.responsiveVariants.length>0||s.responsiveVariants===!0,V=d==null?void 0:d[k],H=[];if(typeof R=="object"&&_)for(let[re,G]of Object.entries(R)){let we=O[G];if(re==="initial"){V=G;continue}Array.isArray(s.responsiveVariants)&&!s.responsiveVariants.includes(re)||(H=v(re,we,H,$))}let te=R!=null&&typeof R!="object"?R:vf(V),K=O[te||"false"];return typeof H=="object"&&typeof $=="string"&&H[$]?wf(H,K):H.length>0?(H.push(K),$==="base"?H.join(" "):H):K},C=()=>c?Object.keys(c).map(k=>x(k,c)):null,T=(k,z)=>{if(!c||typeof c!="object")return null;let $=new Array;for(let A in c){let N=x(A,c,k,z),O=k==="base"&&typeof N=="string"?N:N&&N[k];O&&($[$.length]=O)}return $},L={};for(let k in m)m[k]!==void 0&&(L[k]=m[k]);let M=(k,z)=>{var $;let A=typeof(m==null?void 0:m[k])=="object"?{[k]:($=m[k])==null?void 0:$.initial}:{};return{...d,...L,...A,...z}},D=(k=[],z)=>{let $=[];for(let{class:A,className:N,...O}of k){let P=!0;for(let[R,_]of Object.entries(O)){let V=M(R,z)[R];if(Array.isArray(_)){if(!_.includes(V)){P=!1;break}}else{let H=te=>te==null||te===!1;if(H(_)&&H(V))continue;if(V!==_){P=!1;break}}}P&&(A&&$.push(A),N&&$.push(N))}return $},I=k=>{let z=D(b,k);if(!Array.isArray(z))return z;let $={};for(let A of z)if(typeof A=="string"&&($.base=vo($.base,A)(s)),typeof A=="object")for(let[N,O]of Object.entries(A))$[N]=vo($[N],O)(s);return $},E=k=>{if(a.length<1)return null;let z={};for(let{slots:$=[],class:A,className:N,...O}of a){if(!Ge(O)){let P=!0;for(let R of Object.keys(O)){let _=M(R,k)[R];if(_===void 0||(Array.isArray(O[R])?!O[R].includes(_):O[R]!==_)){P=!1;break}}if(!P)continue}for(let P of $)z[P]=z[P]||[],z[P].push([A,N])}return z};if(!Ge(r)||!f){let k={};if(typeof g=="object"&&!Ge(g))for(let z of Object.keys(g))k[z]=$=>{var A,N;return vo(g[z],T(z,$),((A=I($))!=null?A:[])[z],((N=E($))!=null?N:[])[z],$==null?void 0:$.class,$==null?void 0:$.className)(s)};return k}return vo(u,C(),D(b),m==null?void 0:m.class,m==null?void 0:m.className)(s)},h=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return S.variantKeys=h(),S.extend=n,S.base=u,S.slots=g,S.variants=c,S.defaultVariants=d,S.compoundSlots=a,S.compoundVariants=b,S},xt=(e,t)=>{var n,r,o;return xc(e,{...t,twMerge:(n=void 0)!=null?n:!0,twMergeConfig:{theme:{...(r=void 0)==null?void 0:r.theme,...hf.theme},classGroups:{...(o=void 0)==null?void 0:o.classGroups,...hf.classGroups}}})},Gi=xt({slots:{base:"inline-flex items-center justify-between h-fit rounded-large gap-2",pre:"bg-transparent text-inherit font-mono font-normal inline-block whitespace-nowrap",content:"flex flex-col",symbol:"select-none",copyButton:["group","relative","z-10","text-large","text-inherit","data-[hover=true]:bg-transparent"],copyIcon:["absolute text-inherit opacity-100 scale-100 group-data-[copied=true]:opacity-0 group-data-[copied=true]:scale-50"],checkIcon:["absolute text-inherit opacity-0 scale-50 group-data-[copied=true]:opacity-100 group-data-[copied=true]:scale-100"]},variants:{variant:{flat:"",solid:"",bordered:"border-medium bg-transparent",shadow:""},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{base:"px-1.5 py-0.5 text-tiny rounded-small"},md:{base:"px-3 py-1.5 text-small rounded-medium"},lg:{base:"px-4 py-2 text-medium rounded-large"}},radius:{none:{base:"rounded-none"},sm:{base:"rounded-small"},md:{base:"rounded-medium"},lg:{base:"rounded-large"}},fullWidth:{true:{base:"w-full"}},disableAnimation:{true:{},false:{copyIcon:"transition-transform-opacity",checkIcon:"transition-transform-opacity"}}},defaultVariants:{color:"default",variant:"flat",size:"md",fullWidth:!1},compoundVariants:[{variant:["solid","shadow"],color:"default",class:{copyButton:"data-[focus-visible]:outline-default-foreground"}},{variant:["solid","shadow"],color:"primary",class:{copyButton:"data-[focus-visible]:outline-primary-foreground"}},{variant:["solid","shadow"],color:"secondary",class:{copyButton:"data-[focus-visible]:outline-secondary-foreground"}},{variant:["solid","shadow"],color:"success",class:{copyButton:"data-[focus-visible]:outline-success-foreground"}},{variant:["solid","shadow"],color:"warning",class:{copyButton:"data-[focus-visible]:outline-warning-foreground"}},{variant:["solid","shadow"],color:"danger",class:{copyButton:"data-[focus-visible]:outline-danger-foreground"}},{variant:"flat",color:"default",class:{base:F.flat.default}},{variant:"flat",color:"primary",class:{base:F.flat.primary}},{variant:"flat",color:"secondary",class:{base:F.flat.secondary}},{variant:"flat",color:"success",class:{base:F.flat.success}},{variant:"flat",color:"warning",class:{base:F.flat.warning}},{variant:"flat",color:"danger",class:{base:F.flat.danger}},{variant:"solid",color:"default",class:{base:F.solid.default}},{variant:"solid",color:"primary",class:{base:F.solid.primary}},{variant:"solid",color:"secondary",class:{base:F.solid.secondary}},{variant:"solid",color:"success",class:{base:F.solid.success}},{variant:"solid",color:"warning",class:{base:F.solid.warning}},{variant:"solid",color:"danger",class:{base:F.solid.danger}},{variant:"shadow",color:"default",class:{base:F.shadow.default}},{variant:"shadow",color:"primary",class:{base:F.shadow.primary}},{variant:"shadow",color:"secondary",class:{base:F.shadow.secondary}},{variant:"shadow",color:"success",class:{base:F.shadow.success}},{variant:"shadow",color:"warning",class:{base:F.shadow.warning}},{variant:"shadow",color:"danger",class:{base:F.shadow.danger}},{variant:"bordered",color:"default",class:{base:F.bordered.default}},{variant:"bordered",color:"primary",class:{base:F.bordered.primary}},{variant:"bordered",color:"secondary",class:{base:F.bordered.secondary}},{variant:"bordered",color:"success",class:{base:F.bordered.success}},{variant:"bordered",color:"warning",class:{base:F.bordered.warning}},{variant:"bordered",color:"danger",class:{base:F.bordered.danger}}]}),xf=xt({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",label:"text-foreground dark:text-foreground-dark font-regular",circle1:"absolute w-full h-full rounded-full",circle2:"absolute w-full h-full rounded-full",dots:"relative rounded-full mx-auto",spinnerBars:["absolute","animate-fade-out","rounded-full","w-[25%]","h-[8%]","left-[calc(37.5%)]","top-[calc(46%)]","spinner-bar-animation"]},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",dots:"size-1",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",dots:"size-1.5",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",dots:"size-2",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current",dots:"bg-current",spinnerBars:"bg-current"},white:{circle1:"border-b-white",circle2:"border-b-white",dots:"bg-white",spinnerBars:"bg-white"},default:{circle1:"border-b-default",circle2:"border-b-default",dots:"bg-default",spinnerBars:"bg-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary",dots:"bg-primary",spinnerBars:"bg-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary",dots:"bg-secondary",spinnerBars:"bg-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success",dots:"bg-success",spinnerBars:"bg-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning",dots:"bg-warning",spinnerBars:"bg-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger",dots:"bg-danger",spinnerBars:"bg-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}},variant:{default:{circle1:["animate-spinner-ease-spin","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["opacity-75","animate-spinner-linear-spin","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"]},gradient:{circle1:["border-0","bg-gradient-to-b","from-transparent","via-transparent","to-primary","animate-spinner-linear-spin","[animation-duration:1s]","[-webkit-mask:radial-gradient(closest-side,rgba(0,0,0,0.0)calc(100%-3px),rgba(0,0,0,1)calc(100%-3px))]"],circle2:["hidden"]},wave:{wrapper:"translate-y-3/4",dots:["animate-sway","spinner-dot-animation"]},dots:{wrapper:"translate-y-2/4",dots:["animate-blink","spinner-dot-blink-animation"]},spinner:{},simple:{wrapper:"text-foreground h-5 w-5 animate-spin",circle1:"opacity-25",circle2:"opacity-75"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground",variant:"default"},compoundVariants:[{variant:"gradient",color:"current",class:{circle1:"to-current"}},{variant:"gradient",color:"white",class:{circle1:"to-white"}},{variant:"gradient",color:"default",class:{circle1:"to-default"}},{variant:"gradient",color:"primary",class:{circle1:"to-primary"}},{variant:"gradient",color:"secondary",class:{circle1:"to-secondary"}},{variant:"gradient",color:"success",class:{circle1:"to-success"}},{variant:"gradient",color:"warning",class:{circle1:"to-warning"}},{variant:"gradient",color:"danger",class:{circle1:"to-danger"}},{variant:"wave",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"wave",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"wave",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"dots",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"dots",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"dots",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"simple",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"simple",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",color:"current",class:{wrapper:"text-current"}},{variant:"simple",color:"white",class:{wrapper:"text-white"}},{variant:"simple",color:"default",class:{wrapper:"text-default"}},{variant:"simple",color:"primary",class:{wrapper:"text-primary"}},{variant:"simple",color:"secondary",class:{wrapper:"text-secondary"}},{variant:"simple",color:"success",class:{wrapper:"text-success"}},{variant:"simple",color:"warning",class:{wrapper:"text-warning"}},{variant:"simple",color:"danger",class:{wrapper:"text-danger"}}]}),pl=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],lv=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],pr={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]},uw=["font-inherit","text-[100%]","leading-[1.15]","m-0","p-0","overflow-visible","box-border","absolute","top-0","w-full","h-full","opacity-[0.0001]","z-[1]","cursor-pointer","disabled:cursor-default"],Sf=xt({slots:{base:"group relative max-w-fit inline-flex items-center justify-start cursor-pointer touch-none tap-highlight-transparent select-none",wrapper:["px-1","relative","inline-flex","items-center","justify-start","flex-shrink-0","overflow-hidden","bg-default-200","rounded-full",...lv],thumb:["z-10","flex","items-center","justify-center","bg-white","shadow-small","rounded-full","origin-right","pointer-events-none"],hiddenInput:uw,startContent:"z-0 absolute start-1.5 text-current",endContent:"z-0 absolute end-1.5 text-default-600",thumbIcon:"text-black",label:"relative text-foreground select-none ms-2"},variants:{color:{default:{wrapper:["group-data-[selected=true]:bg-default-400","group-data-[selected=true]:text-default-foreground"]},primary:{wrapper:["group-data-[selected=true]:bg-primary","group-data-[selected=true]:text-primary-foreground"]},secondary:{wrapper:["group-data-[selected=true]:bg-secondary","group-data-[selected=true]:text-secondary-foreground"]},success:{wrapper:["group-data-[selected=true]:bg-success","group-data-[selected=true]:text-success-foreground"]},warning:{wrapper:["group-data-[selected=true]:bg-warning","group-data-[selected=true]:text-warning-foreground"]},danger:{wrapper:["group-data-[selected=true]:bg-danger","data-[selected=true]:text-danger-foreground"]}},size:{sm:{wrapper:"w-10 h-6",thumb:["w-4 h-4 text-tiny","group-data-[selected=true]:ms-4"],endContent:"text-tiny",startContent:"text-tiny",label:"text-small"},md:{wrapper:"w-12 h-7",thumb:["w-5 h-5 text-small","group-data-[selected=true]:ms-5"],endContent:"text-small",startContent:"text-small",label:"text-medium"},lg:{wrapper:"w-14 h-8",thumb:["w-6 h-6 text-medium","group-data-[selected=true]:ms-6"],endContent:"text-medium",startContent:"text-medium",label:"text-large"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{wrapper:"transition-none",thumb:"transition-none"},false:{wrapper:"transition-background",thumb:"transition-all",startContent:["opacity-0","scale-50","transition-transform-opacity","group-data-[selected=true]:scale-100","group-data-[selected=true]:opacity-100"],endContent:["opacity-100","transition-transform-opacity","group-data-[selected=true]:translate-x-3","group-data-[selected=true]:opacity-0"]}}},defaultVariants:{color:"primary",size:"md",isDisabled:!1},compoundVariants:[{disableAnimation:!1,size:"sm",class:{thumb:["group-data-[pressed=true]:w-5","group-data-[selected]:group-data-[pressed]:ml-3"]}},{disableAnimation:!1,size:"md",class:{thumb:["group-data-[pressed=true]:w-6","group-data-[selected]:group-data-[pressed]:ml-4"]}},{disableAnimation:!1,size:"lg",class:{thumb:["group-data-[pressed=true]:w-7","group-data-[selected]:group-data-[pressed]:ml-5"]}}]}),Ef=xt({slots:{base:["z-0","relative","bg-transparent","before:content-['']","before:hidden","before:z-[-1]","before:absolute","before:rotate-45","before:w-2.5","before:h-2.5","before:rounded-sm","data-[arrow=true]:before:block","data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top]:before:left-1/2","data-[placement=top]:before:-translate-x-1/2","data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-start]:before:left-3","data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-end]:before:right-3","data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom]:before:left-1/2","data-[placement=bottom]:before:-translate-x-1/2","data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-start]:before:left-3","data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-end]:before:right-3","data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=left]:before:top-1/2","data-[placement=left]:before:-translate-y-1/2","data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-start]:before:top-1/4","data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-end]:before:bottom-1/4","data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=right]:before:top-1/2","data-[placement=right]:before:-translate-y-1/2","data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-start]:before:top-1/4","data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-end]:before:bottom-1/4",...pl],content:["z-10","px-2.5","py-1","w-full","inline-flex","flex-col","items-center","justify-center","box-border","subpixel-antialiased","outline-none","box-border"],trigger:["z-10"],backdrop:["hidden"],arrow:[]},variants:{size:{sm:{content:"text-tiny"},md:{content:"text-small"},lg:{content:"text-medium"}},color:{default:{base:"before:bg-content1 before:shadow-small",content:"bg-content1"},foreground:{base:"before:bg-foreground",content:F.solid.foreground},primary:{base:"before:bg-primary",content:F.solid.primary},secondary:{base:"before:bg-secondary",content:F.solid.secondary},success:{base:"before:bg-success",content:F.solid.success},warning:{base:"before:bg-warning",content:F.solid.warning},danger:{base:"before:bg-danger",content:F.solid.danger}},radius:{none:{content:"rounded-none"},sm:{content:"rounded-small"},md:{content:"rounded-medium"},lg:{content:"rounded-large"},full:{content:"rounded-full"}},shadow:{none:{content:"shadow-none"},sm:{content:"shadow-small"},md:{content:"shadow-medium"},lg:{content:"shadow-large"}},backdrop:{transparent:{},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-sm backdrop-saturate-150 bg-overlay/30"}},triggerScaleOnOpen:{true:{trigger:["aria-expanded:scale-[0.97]","aria-expanded:opacity-70","subpixel-antialiased"]},false:{}},disableAnimation:{true:{base:"animate-none"}},isTriggerDisabled:{true:{trigger:"opacity-disabled pointer-events-none"},false:{}}},defaultVariants:{color:"default",radius:"lg",size:"md",shadow:"md",backdrop:"transparent",triggerScaleOnOpen:!0},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"block w-full h-full fixed inset-0 -z-30"}}]}),Cf=xt({slots:{base:"group flex flex-col data-[hidden=true]:hidden",label:["absolute","z-10","pointer-events-none","origin-top-left","flex-shrink-0","rtl:origin-top-right","subpixel-antialiased","block","text-small","text-foreground-500"],mainWrapper:"h-full",inputWrapper:"relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3",innerWrapper:"inline-flex w-full items-center h-full box-border",input:["w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none","data-[has-start-content=true]:ps-1.5","data-[has-end-content=true]:pe-1.5","data-[type=color]:rounded-none","file:cursor-pointer file:bg-transparent file:border-0","autofill:bg-transparent bg-clip-text"],clearButton:["p-2","-m-2","z-10","absolute","end-3","start-auto","pointer-events-none","appearance-none","outline-none","select-none","opacity-0","cursor-pointer","active:!opacity-70","rounded-full",...pl],helperWrapper:"hidden group-data-[has-helper=true]:flex p-1 relative flex-col gap-1.5",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger"},variants:{variant:{flat:{inputWrapper:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-100"]},faded:{inputWrapper:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 focus-within:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground"},bordered:{inputWrapper:["border-medium","border-default-200","data-[hover=true]:border-default-400","group-data-[focus=true]:border-default-foreground"]},underlined:{inputWrapper:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","group-data-[focus=true]:after:w-full"],innerWrapper:"pb-1",label:"group-data-[filled-within=true]:text-foreground"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{label:"text-tiny",inputWrapper:"h-8 min-h-8 px-2 rounded-small",input:"text-small",clearButton:"text-medium"},md:{inputWrapper:"h-10 min-h-10 rounded-medium",input:"text-small",clearButton:"text-large hover:!opacity-100"},lg:{label:"text-medium",inputWrapper:"h-12 min-h-12 rounded-large",input:"text-medium",clearButton:"text-large hover:!opacity-100"}},radius:{none:{inputWrapper:"rounded-none"},sm:{inputWrapper:"rounded-small"},md:{inputWrapper:"rounded-medium"},lg:{inputWrapper:"rounded-large"},full:{inputWrapper:"rounded-full"}},labelPlacement:{outside:{mainWrapper:"flex flex-col"},"outside-left":{base:"flex-row items-center flex-nowrap data-[has-helper=true]:items-start",inputWrapper:"flex-1",mainWrapper:"flex flex-col",label:"relative text-foreground pe-2 ps-2 pointer-events-auto"},inside:{label:"cursor-text",inputWrapper:"flex-col items-start justify-center gap-0",innerWrapper:"group-data-[has-label=true]:items-end"}},fullWidth:{true:{base:"w-full"},false:{}},isClearable:{true:{input:"peer pe-6 input-search-cancel-button-none",clearButton:["peer-data-[filled=true]:pointer-events-auto","peer-data-[filled=true]:opacity-70 peer-data-[filled=true]:block","peer-data-[filled=true]:scale-100"]}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",inputWrapper:"pointer-events-none",label:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",input:"!placeholder:text-danger !text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",inputWrapper:"!h-auto",innerWrapper:"items-start group-data-[has-label=true]:items-start",input:"resize-none data-[hide-scroll=true]:scrollbar-hide",clearButton:"absolute top-2 right-2 rtl:right-auto rtl:left-2 z-10"}},disableAnimation:{true:{input:"transition-none",inputWrapper:"transition-none",label:"transition-none"},false:{inputWrapper:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","!duration-200","!ease-out","motion-reduce:transition-none","transition-[transform,color,left,opacity]"],clearButton:["scale-90","ease-out","duration-150","transition-[opacity,transform]","motion-reduce:transition-none","motion-reduce:scale-100"]}}},defaultVariants:{variant:"flat",color:"default",size:"md",fullWidth:!0,isDisabled:!1,isMultiline:!1},compoundVariants:[{variant:"flat",color:"default",class:{input:"group-data-[has-value=true]:text-default-foreground"}},{variant:"flat",color:"primary",class:{inputWrapper:["bg-primary-100","data-[hover=true]:bg-primary-50","text-primary","group-data-[focus=true]:bg-primary-50","placeholder:text-primary"],input:"placeholder:text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{inputWrapper:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50","placeholder:text-secondary"],input:"placeholder:text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{inputWrapper:["bg-success-100","text-success-600","dark:text-success","placeholder:text-success-600","dark:placeholder:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],input:"placeholder:text-success-600 dark:placeholder:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{inputWrapper:["bg-warning-100","text-warning-600","dark:text-warning","placeholder:text-warning-600","dark:placeholder:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],input:"placeholder:text-warning-600 dark:placeholder:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{inputWrapper:["bg-danger-100","text-danger","dark:text-danger-500","placeholder:text-danger","dark:placeholder:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],input:"placeholder:text-danger dark:placeholder:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{label:"text-primary",inputWrapper:"data-[hover=true]:border-primary focus-within:border-primary"}},{variant:"faded",color:"secondary",class:{label:"text-secondary",inputWrapper:"data-[hover=true]:border-secondary focus-within:border-secondary"}},{variant:"faded",color:"success",class:{label:"text-success",inputWrapper:"data-[hover=true]:border-success focus-within:border-success"}},{variant:"faded",color:"warning",class:{label:"text-warning",inputWrapper:"data-[hover=true]:border-warning focus-within:border-warning"}},{variant:"faded",color:"danger",class:{label:"text-danger",inputWrapper:"data-[hover=true]:border-danger focus-within:border-danger"}},{variant:"underlined",color:"default",class:{input:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{inputWrapper:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{inputWrapper:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{inputWrapper:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{inputWrapper:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{inputWrapper:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{inputWrapper:"group-data-[focus=true]:border-primary",label:"text-primary"}},{variant:"bordered",color:"secondary",class:{inputWrapper:"group-data-[focus=true]:border-secondary",label:"text-secondary"}},{variant:"bordered",color:"success",class:{inputWrapper:"group-data-[focus=true]:border-success",label:"text-success"}},{variant:"bordered",color:"warning",class:{inputWrapper:"group-data-[focus=true]:border-warning",label:"text-warning"}},{variant:"bordered",color:"danger",class:{inputWrapper:"group-data-[focus=true]:border-danger",label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled-within=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled-within=true]:text-foreground"}},{radius:"full",size:["sm"],class:{inputWrapper:"px-3"}},{radius:"full",size:"md",class:{inputWrapper:"px-4"}},{radius:"full",size:"lg",class:{inputWrapper:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{inputWrapper:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{inputWrapper:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{inputWrapper:[...lv]}},{isInvalid:!0,variant:"flat",class:{inputWrapper:["!bg-danger-50","data-[hover=true]:!bg-danger-100","group-data-[focus=true]:!bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{inputWrapper:"!border-danger group-data-[focus=true]:!border-danger"}},{isInvalid:!0,variant:"underlined",class:{inputWrapper:"after:!bg-danger"}},{labelPlacement:"inside",size:"sm",class:{inputWrapper:"h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{inputWrapper:"h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{inputWrapper:"h-16 py-2.5 gap-0"}},{labelPlacement:"inside",size:"sm",variant:["bordered","faded"],class:{inputWrapper:"py-1"}},{labelPlacement:["inside","outside"],class:{label:["group-data-[filled-within=true]:pointer-events-auto"]}},{labelPlacement:"outside",isMultiline:!1,class:{base:"relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled-within=true]:start-0"]}},{labelPlacement:["inside"],class:{label:["group-data-[filled-within=true]:scale-85"]}},{labelPlacement:["inside"],variant:"flat",class:{innerWrapper:"pb-0.5"}},{variant:"underlined",size:"sm",class:{innerWrapper:"pb-1"}},{variant:"underlined",size:["md","lg"],class:{innerWrapper:"pb-1.5"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px)]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",size:"lg",isMultiline:!1,class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.tiny)/2_+_16px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_8px)]"}},{labelPlacement:"outside",size:"md",isMultiline:!1,class:{label:["start-3","end-auto","text-small","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)]"}},{labelPlacement:"outside",size:"lg",isMultiline:!1,class:{label:["start-3","end-auto","text-medium","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_24px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_12px)]"}},{labelPlacement:"outside-left",size:"sm",class:{label:"group-data-[has-helper=true]:pt-2"}},{labelPlacement:"outside-left",size:"md",class:{label:"group-data-[has-helper=true]:pt-3"}},{labelPlacement:"outside-left",size:"lg",class:{label:"group-data-[has-helper=true]:pt-4"}},{labelPlacement:["outside","outside-left"],isMultiline:!0,class:{inputWrapper:"py-2"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:"inside",isMultiline:!0,class:{label:"pb-0.5",input:"pt-0"}},{isMultiline:!0,disableAnimation:!1,class:{input:"transition-height !duration-100 motion-reduce:transition-none"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}},{isMultiline:!0,radius:"full",class:{inputWrapper:"data-[has-multiple-rows=true]:rounded-large"}},{isClearable:!0,isMultiline:!0,class:{clearButton:["group-data-[has-value=true]:opacity-70 group-data-[has-value=true]:block","group-data-[has-value=true]:scale-100","group-data-[has-value=true]:pointer-events-auto"]}}]}),$f=xt({slots:{base:["px-1.5","py-0.5","inline-flex","space-x-0.5","rtl:space-x-reverse","items-center","font-sans","font-normal","text-center","text-small","shadow-small","bg-default-100","text-foreground-600","rounded-small"],abbr:"no-underline",content:""},variants:{},defaultVariants:{}}),ru=xt({base:["relative inline-flex items-center outline-none tap-highlight-transparent",...pl],variants:{size:{sm:"text-small",md:"text-medium",lg:"text-large"},color:{foreground:"text-foreground",primary:"text-primary",secondary:"text-secondary",success:"text-success",warning:"text-warning",danger:"text-danger"},underline:{none:"no-underline",hover:"hover:underline",always:"underline",active:"active:underline",focus:"focus:underline"},isBlock:{true:["px-2","py-1","hover:after:opacity-100","after:content-['']","after:inset-0","after:opacity-0","after:w-full","after:h-full","after:rounded-xl","after:transition-background","after:absolute"],false:"hover:opacity-hover active:opacity-disabled transition-opacity"},isDisabled:{true:"opacity-disabled cursor-default pointer-events-none"},disableAnimation:{true:"after:transition-none transition-none"}},compoundVariants:[{isBlock:!0,color:"foreground",class:"hover:after:bg-foreground/10"},{isBlock:!0,color:"primary",class:"hover:after:bg-primary/20"},{isBlock:!0,color:"secondary",class:"hover:after:bg-secondary/20"},{isBlock:!0,color:"success",class:"hover:after:bg-success/20"},{isBlock:!0,color:"warning",class:"hover:after:bg-warning/20"},{isBlock:!0,color:"danger",class:"hover:after:bg-danger/20"},{underline:["hover","always","active","focus"],class:"underline-offset-4"}],defaultVariants:{color:"primary",size:"md",isBlock:!1,underline:"none",isDisabled:!1}}),cw="flex mx-1 text-current self-center",kf=xt({slots:{base:["flex","z-40","w-full","h-auto","items-center","justify-center","data-[menu-open=true]:border-none"],wrapper:["z-40","flex","px-6","gap-4","w-full","flex-row","relative","flex-nowrap","items-center","justify-between","h-[var(--navbar-height)]"],toggle:["group","flex","items-center","justify-center","w-6","h-full","outline-none","rounded-small","tap-highlight-transparent",...pl],srOnly:["sr-only"],toggleIcon:["w-full","h-full","pointer-events-none","flex","flex-col","items-center","justify-center","text-inherit","group-data-[pressed=true]:opacity-70","transition-opacity","before:content-['']","before:block","before:h-px","before:w-6","before:bg-current","before:transition-transform","before:duration-150","before:-translate-y-1","before:rotate-0","group-data-[open=true]:before:translate-y-px","group-data-[open=true]:before:rotate-45","after:content-['']","after:block","after:h-px","after:w-6","after:bg-current","after:transition-transform","after:duration-150","after:translate-y-1","after:rotate-0","group-data-[open=true]:after:translate-y-0","group-data-[open=true]:after:-rotate-45"],brand:["flex","basis-0","flex-row","flex-grow","flex-nowrap","justify-start","bg-transparent","items-center","no-underline","text-medium","whitespace-nowrap","box-border"],content:["flex","gap-4","h-full","flex-row","flex-nowrap","items-center","data-[justify=start]:justify-start","data-[justify=start]:flex-grow","data-[justify=start]:basis-0","data-[justify=center]:justify-center","data-[justify=end]:justify-end","data-[justify=end]:flex-grow","data-[justify=end]:basis-0"],item:["text-medium","whitespace-nowrap","box-border","list-none","data-[active=true]:font-semibold"],menu:["z-30","px-6","pt-2","fixed","flex","max-w-full","top-[var(--navbar-height)]","inset-x-0","bottom-0","w-screen","flex-col","gap-2","overflow-y-auto"],menuItem:["text-large","data-[active=true]:font-semibold"]},variants:{position:{static:{base:"static"},sticky:{base:"sticky top-0 inset-x-0"}},maxWidth:{sm:{wrapper:"max-w-[640px]"},md:{wrapper:"max-w-[768px]"},lg:{wrapper:"max-w-[1024px]"},xl:{wrapper:"max-w-[1280px]"},"2xl":{wrapper:"max-w-[1536px]"},full:{wrapper:"max-w-full"}},hideOnScroll:{true:{base:["sticky","top-0","inset-x-0"]}},isBordered:{true:{base:["border-b","border-divider"]}},isBlurred:{false:{base:"bg-background",menu:"bg-background"},true:{base:["backdrop-blur-lg","data-[menu-open=true]:backdrop-blur-xl","backdrop-saturate-150","bg-background/70"],menu:["backdrop-blur-xl","backdrop-saturate-150","bg-background/70"]}},disableAnimation:{true:{menu:["hidden","h-[calc(100dvh_-_var(--navbar-height))]","data-[open=true]:flex"]}}},defaultVariants:{maxWidth:"lg",position:"sticky",isBlurred:!0}}),dw=xt({base:"flex flex-col gap-2 items-start"}),ou=xt({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-none","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","transform-gpu data-[pressed=true]:scale-[0.97]",...pl],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:F.solid.default},{variant:"solid",color:"primary",class:F.solid.primary},{variant:"solid",color:"secondary",class:F.solid.secondary},{variant:"solid",color:"success",class:F.solid.success},{variant:"solid",color:"warning",class:F.solid.warning},{variant:"solid",color:"danger",class:F.solid.danger},{variant:"shadow",color:"default",class:F.shadow.default},{variant:"shadow",color:"primary",class:F.shadow.primary},{variant:"shadow",color:"secondary",class:F.shadow.secondary},{variant:"shadow",color:"success",class:F.shadow.success},{variant:"shadow",color:"warning",class:F.shadow.warning},{variant:"shadow",color:"danger",class:F.shadow.danger},{variant:"bordered",color:"default",class:F.bordered.default},{variant:"bordered",color:"primary",class:F.bordered.primary},{variant:"bordered",color:"secondary",class:F.bordered.secondary},{variant:"bordered",color:"success",class:F.bordered.success},{variant:"bordered",color:"warning",class:F.bordered.warning},{variant:"bordered",color:"danger",class:F.bordered.danger},{variant:"flat",color:"default",class:F.flat.default},{variant:"flat",color:"primary",class:F.flat.primary},{variant:"flat",color:"secondary",class:F.flat.secondary},{variant:"flat",color:"success",class:F.flat.success},{variant:"flat",color:"warning",class:F.flat.warning},{variant:"flat",color:"danger",class:F.flat.danger},{variant:"faded",color:"default",class:F.faded.default},{variant:"faded",color:"primary",class:F.faded.primary},{variant:"faded",color:"secondary",class:F.faded.secondary},{variant:"faded",color:"success",class:F.faded.success},{variant:"faded",color:"warning",class:F.faded.warning},{variant:"faded",color:"danger",class:F.faded.danger},{variant:"light",color:"default",class:[F.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[F.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[F.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[F.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[F.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[F.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[F.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[F.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[F.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[F.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[F.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[F.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:pr.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:pr.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:pr.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:pr.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:pr.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:pr.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});xt({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}});var Pf=xt({base:["px-2","py-1","h-fit","font-mono","font-normal","inline-block","whitespace-nowrap"],variants:{color:{default:F.flat.default,primary:F.flat.primary,secondary:F.flat.secondary,success:F.flat.success,warning:F.flat.warning,danger:F.flat.danger},size:{sm:"text-small",md:"text-medium",lg:"text-large"},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"}},defaultVariants:{color:"default",size:"sm",radius:"sm"}});function Sc(e){return Array.isArray(e)}function fw(e){return Sc(e)&&e.length===0}function av(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")&&!Sc(e)}function pw(e){return av(e)&&Object.keys(e).length===0}function mw(e){return Sc(e)?fw(e):av(e)?pw(e):e==null||e===""}function hw(e){return typeof e=="function"}var B=e=>e?"true":void 0;function iv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=iv(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function ee(...e){for(var t=0,n,r,o="";t<e.length;)(n=e[t++])&&(r=iv(n))&&(o&&(o+=" "),o+=r);return o}var vw=(...e)=>{let t=" ";for(const n of e)if(typeof n=="string"&&n.length>0){t=n;break}return t};function gw(e){return`${e}-${Math.floor(Math.random()*1e6)}`}function Wt(e){if(!e||typeof e!="object")return"";try{return JSON.stringify(e)}catch{return""}}function yw(e,t,n){return Math.min(Math.max(e,t),n)}var Tf={};function bw(e,t,...n){const o=`[Hero UI] : ${e}`;typeof console>"u"||Tf[o]||(Tf[o]=!0)}const be=typeof document<"u"?q.useLayoutEffect:()=>{};function Oe(e){const t=p.useRef(null);return be(()=>{t.current=e},[e]),p.useCallback((...n)=>{const r=t.current;return r==null?void 0:r(...n)},[])}function ww(e){let[t,n]=p.useState(e),r=p.useRef(null),o=Oe(()=>{if(!r.current)return;let a=r.current.next();if(a.done){r.current=null;return}t===a.value?o():n(a.value)});be(()=>{r.current&&o()});let l=Oe(a=>{r.current=a(t),o()});return[t,l]}const sv={prefix:String(Math.round(Math.random()*1e10)),current:0},uv=q.createContext(sv),xw=q.createContext(!1);let Qi=new WeakMap;function Sw(e=!1){let t=p.useContext(uv),n=p.useRef(null);if(n.current===null&&!e){var r,o;let l=(o=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||o===void 0||(r=o.ReactCurrentOwner)===null||r===void 0?void 0:r.current;if(l){let a=Qi.get(l);a==null?Qi.set(l,{id:t.current,state:l.memoizedState}):l.memoizedState!==a.state&&(t.current=a.id,Qi.delete(l))}n.current=++t.current}return n.current}function Ew(e){let t=p.useContext(uv),n=Sw(!!e),r=`react-aria${t.prefix}`;return e||`${r}-${n}`}function Cw(e){let t=q.useId(),[n]=p.useState(ri()),r=n?"react-aria":`react-aria${sv.prefix}`;return e||`${r}-${t}`}const $w=typeof q.useId=="function"?Cw:Ew;function kw(){return!1}function Pw(){return!0}function Tw(e){return()=>{}}function ri(){return typeof q.useSyncExternalStore=="function"?q.useSyncExternalStore(Tw,kw,Pw):p.useContext(xw)}let Nw=!!(typeof window<"u"&&window.document&&window.document.createElement),jr=new Map,$o;typeof FinalizationRegistry<"u"&&($o=new FinalizationRegistry(e=>{jr.delete(e)}));function ol(e){let[t,n]=p.useState(e),r=p.useRef(null),o=$w(t),l=p.useRef(null);if($o&&$o.register(l,o),Nw){const a=jr.get(o);a&&!a.includes(r)?a.push(r):jr.set(o,[r])}return be(()=>{let a=o;return()=>{$o&&$o.unregister(l),jr.delete(a)}},[o]),p.useEffect(()=>{let a=r.current;return a&&n(a),()=>{a&&(r.current=null)}}),o}function _w(e,t){if(e===t)return e;let n=jr.get(e);if(n)return n.forEach(o=>o.current=t),t;let r=jr.get(t);return r?(r.forEach(o=>o.current=e),e):t}function Nf(e=[]){let t=ol(),[n,r]=ww(t),o=p.useCallback(()=>{r(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,r]);return be(o,[t,o,...e]),n}function At(...e){return(...t)=>{for(let n of e)typeof n=="function"&&n(...t)}}const le=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},vt=e=>e&&"window"in e&&e.window===e?e:le(e).defaultView||window;function Mw(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function Lw(e){return Mw(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}let Iw=!1;function oi(){return Iw}function Qe(e,t){if(!oi())return t&&e?e.contains(t):!1;if(!e||!t)return!1;let n=t;for(;n!==null;){if(n===e)return!0;n.tagName==="SLOT"&&n.assignedSlot?n=n.assignedSlot.parentNode:Lw(n)?n=n.host:n=n.parentNode}return!1}const tt=(e=document)=>{var t;if(!oi())return e.activeElement;let n=e.activeElement;for(;n&&"shadowRoot"in n&&(!((t=n.shadowRoot)===null||t===void 0)&&t.activeElement);)n=n.shadowRoot.activeElement;return n};function ge(e){return oi()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}class Rw{get currentNode(){return this._currentNode}set currentNode(t){if(!Qe(this.root,t))throw new Error("Cannot set currentNode to a node that is not contained by the root node.");const n=[];let r=t,o=t;for(this._currentNode=t;r&&r!==this.root;)if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const a=r,i=this._doc.createTreeWalker(a,this.whatToShow,{acceptNode:this._acceptNode});n.push(i),i.currentNode=o,this._currentSetFor.add(i),r=o=a.host}else r=r.parentNode;const l=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});n.push(l),l.currentNode=o,this._currentSetFor.add(l),this._walkerStack=n}get doc(){return this._doc}firstChild(){let t=this.currentNode,n=this.nextNode();return Qe(t,n)?(n&&(this.currentNode=n),n):(this.currentNode=t,null)}lastChild(){let n=this._walkerStack[0].lastChild();return n&&(this.currentNode=n),n}nextNode(){const t=this._walkerStack[0].nextNode();if(t){if(t.shadowRoot){var n;let o;if(typeof this.filter=="function"?o=this.filter(t):!((n=this.filter)===null||n===void 0)&&n.acceptNode&&(o=this.filter.acceptNode(t)),o===NodeFilter.FILTER_ACCEPT)return this.currentNode=t,t;let l=this.nextNode();return l&&(this.currentNode=l),l}return t&&(this.currentNode=t),t}else if(this._walkerStack.length>1){this._walkerStack.shift();let r=this.nextNode();return r&&(this.currentNode=r),r}else return null}previousNode(){const t=this._walkerStack[0];if(t.currentNode===t.root){if(this._currentSetFor.has(t))if(this._currentSetFor.delete(t),this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null;return null}const n=t.previousNode();if(n){if(n.shadowRoot){var r;let l;if(typeof this.filter=="function"?l=this.filter(n):!((r=this.filter)===null||r===void 0)&&r.acceptNode&&(l=this.filter.acceptNode(n)),l===NodeFilter.FILTER_ACCEPT)return n&&(this.currentNode=n),n;let a=this.lastChild();return a&&(this.currentNode=a),a}return n&&(this.currentNode=n),n}else if(this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(t,n,r,o){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=a=>{if(a.nodeType===Node.ELEMENT_NODE){const s=a.shadowRoot;if(s){const u=this._doc.createTreeWalker(s,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(u),NodeFilter.FILTER_ACCEPT}else{var i;if(typeof this.filter=="function")return this.filter(a);if(!((i=this.filter)===null||i===void 0)&&i.acceptNode)return this.filter.acceptNode(a);if(this.filter===null)return NodeFilter.FILTER_ACCEPT}}return NodeFilter.FILTER_SKIP},this._doc=t,this.root=n,this.filter=o??null,this.whatToShow=r??NodeFilter.SHOW_ALL,this._currentNode=n,this._walkerStack.unshift(t.createTreeWalker(n,r,this._acceptNode));const l=n.shadowRoot;if(l){const a=this._doc.createTreeWalker(l,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(a)}}}function jw(e,t,n,r){return oi()?new Rw(e,t,n,r):e.createTreeWalker(t,n,r)}function cv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=cv(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function La(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=cv(e))&&(r&&(r+=" "),r+=t);return r}function Z(...e){let t={...e[0]};for(let n=1;n<e.length;n++){let r=e[n];for(let o in r){let l=t[o],a=r[o];typeof l=="function"&&typeof a=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=At(l,a):(o==="className"||o==="UNSAFE_className")&&typeof l=="string"&&typeof a=="string"?t[o]=La(l,a):o==="id"&&l&&a?t.id=_w(l,a):t[o]=a!==void 0?a:l}}return t}function zw(...e){return e.length===1&&e[0]?e[0]:t=>{let n=!1;const r=e.map(o=>{const l=_f(o,t);return n||(n=typeof l=="function"),l});if(n)return()=>{r.forEach((o,l)=>{typeof o=="function"?o():_f(e[l],null)})}}}function _f(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}const Ow=new Set(["id"]),Fw=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),Dw=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),Aw=/^(data-.*)$/;function qr(e,t={}){let{labelable:n,isLink:r,propNames:o}=t,l={};for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(Ow.has(a)||n&&Fw.has(a)||r&&Dw.has(a)||o!=null&&o.has(a)||Aw.test(a))&&(l[a]=e[a]);return l}function Ur(e){if(Ww())e.focus({preventScroll:!0});else{let t=Vw(e);e.focus(),Bw(t)}}let Dl=null;function Ww(){if(Dl==null){Dl=!1;try{document.createElement("div").focus({get preventScroll(){return Dl=!0,!0}})}catch{}}return Dl}function Vw(e){let t=e.parentNode,n=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&n.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),n}function Bw(e){for(let{element:t,scrollTop:n,scrollLeft:r}of e)t.scrollTop=n,t.scrollLeft=r}function li(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(n=>e.test(n.brand)))||e.test(window.navigator.userAgent)}function Ec(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function jn(e){let t=null;return()=>(t==null&&(t=e()),t)}const ll=jn(function(){return Ec(/^Mac/i)}),Hw=jn(function(){return Ec(/^iPhone/i)}),dv=jn(function(){return Ec(/^iPad/i)||ll()&&navigator.maxTouchPoints>1}),Cc=jn(function(){return Hw()||dv()}),fv=jn(function(){return li(/AppleWebKit/i)&&!pv()}),pv=jn(function(){return li(/Chrome/i)}),$c=jn(function(){return li(/Android/i)}),Uw=jn(function(){return li(/Firefox/i)}),mv=p.createContext({isNative:!0,open:Gw,useHref:e=>e});function Kw(e){let{children:t,navigate:n,useHref:r}=e,o=p.useMemo(()=>({isNative:!1,open:(l,a,i,s)=>{gv(l,u=>{vv(u,a)?n(i,s):nr(u,a)})},useHref:r||(l=>l)}),[n,r]);return q.createElement(mv.Provider,{value:o},t)}function hv(){return p.useContext(mv)}function vv(e,t){let n=e.getAttribute("target");return(!n||n==="_self")&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function nr(e,t,n=!0){var r,o;let{metaKey:l,ctrlKey:a,altKey:i,shiftKey:s}=t;Uw()&&(!((o=window.event)===null||o===void 0||(r=o.type)===null||r===void 0)&&r.startsWith("key"))&&e.target==="_blank"&&(ll()?l=!0:a=!0);let u=fv()&&ll()&&!dv()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:l,ctrlKey:a,altKey:i,shiftKey:s}):new MouseEvent("click",{metaKey:l,ctrlKey:a,altKey:i,shiftKey:s,bubbles:!0,cancelable:!0});nr.isOpening=n,Ur(e),e.dispatchEvent(u),nr.isOpening=!1}nr.isOpening=!1;function gv(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let n=document.createElement("a");n.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(n.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(n.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(n.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(n.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(n.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(n),t(n),e.removeChild(n)}}function Gw(e,t){gv(e,n=>nr(n,t))}function Qw(e){let t=hv();var n;const r=t.useHref((n=e==null?void 0:e.href)!==null&&n!==void 0?n:"");return{href:e!=null&&e.href?r:void 0,target:e==null?void 0:e.target,rel:e==null?void 0:e.rel,download:e==null?void 0:e.download,ping:e==null?void 0:e.ping,referrerPolicy:e==null?void 0:e.referrerPolicy}}let hn=new Map,lu=new Set;function Mf(){if(typeof window>"u")return;function e(r){return"propertyName"in r}let t=r=>{if(!e(r)||!r.target)return;let o=hn.get(r.target);o||(o=new Set,hn.set(r.target,o),r.target.addEventListener("transitioncancel",n,{once:!0})),o.add(r.propertyName)},n=r=>{if(!e(r)||!r.target)return;let o=hn.get(r.target);if(o&&(o.delete(r.propertyName),o.size===0&&(r.target.removeEventListener("transitioncancel",n),hn.delete(r.target)),hn.size===0)){for(let l of lu)l();lu.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",n)}typeof document<"u"&&(document.readyState!=="loading"?Mf():document.addEventListener("DOMContentLoaded",Mf));function Yw(){for(const[e]of hn)"isConnected"in e&&!e.isConnected&&hn.delete(e)}function yv(e){requestAnimationFrame(()=>{Yw(),hn.size===0?e():lu.add(e)})}function kc(){let e=p.useRef(new Map),t=p.useCallback((o,l,a,i)=>{let s=i!=null&&i.once?(...u)=>{e.current.delete(a),a(...u)}:a;e.current.set(a,{type:l,eventTarget:o,fn:s,options:i}),o.addEventListener(l,s,i)},[]),n=p.useCallback((o,l,a,i)=>{var s;let u=((s=e.current.get(a))===null||s===void 0?void 0:s.fn)||a;o.removeEventListener(l,u,i),e.current.delete(a)},[]),r=p.useCallback(()=>{e.current.forEach((o,l)=>{n(o.eventTarget,o.type,l,o.options)})},[n]);return p.useEffect(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}function Xw(e,t){let{id:n,"aria-label":r,"aria-labelledby":o}=e;return n=ol(n),o&&r?o=[...new Set([n,...o.trim().split(/\s+/)])].join(" "):o&&(o=o.trim().split(/\s+/).join(" ")),!r&&!o&&t&&(r=t),{id:n,"aria-label":r,"aria-labelledby":o}}function Zw(e){const t=p.useRef(null),n=p.useRef(void 0),r=p.useCallback(o=>{if(typeof e=="function"){const l=e,a=l(o);return()=>{typeof a=="function"?a():l(null)}}else if(e)return e.current=o,()=>{e.current=null}},[e]);return p.useMemo(()=>({get current(){return t.current},set current(o){t.current=o,n.current&&(n.current(),n.current=void 0),o!=null&&(n.current=r(o))}}),[r])}function Jw(){return typeof window.ResizeObserver<"u"}function au(e){const{ref:t,box:n,onResize:r}=e;p.useEffect(()=>{let o=t==null?void 0:t.current;if(o)if(Jw()){const l=new window.ResizeObserver(a=>{a.length&&r()});return l.observe(o,{box:n}),()=>{o&&l.unobserve(o)}}else return window.addEventListener("resize",r,!1),()=>{window.removeEventListener("resize",r,!1)}},[r,t,n])}function bv(e,t){be(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}function Lf(e,t){if(!e)return!1;let n=window.getComputedStyle(e),r=/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}function wv(e,t){let n=e;for(Lf(n,t)&&(n=n.parentElement);n&&!Lf(n,t);)n=n.parentElement;return n||document.scrollingElement||document.documentElement}function xv(e){return e.mozInputSource===0&&e.isTrusted?!0:$c()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function qw(e){return!$c()&&e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"}function Sv(e,t,n){let r=p.useRef(t),o=Oe(()=>{n&&n(r.current)});p.useEffect(()=>{var l;let a=e==null||(l=e.current)===null||l===void 0?void 0:l.form;return a==null||a.addEventListener("reset",o),()=>{a==null||a.removeEventListener("reset",o)}},[e,o])}const Pc=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],ex=Pc.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";Pc.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const tx=Pc.join(':not([hidden]):not([tabindex="-1"]),');function Ev(e){return e.matches(ex)}function nx(e){return e.matches(tx)}function ml(e,t,n){let[r,o]=p.useState(e||t),l=p.useRef(e!==void 0),a=e!==void 0;p.useEffect(()=>{l.current,l.current=a},[a]);let i=a?e:r,s=p.useCallback((u,...c)=>{let d=(f,...y)=>{n&&(Object.is(i,f)||n(f,...y)),a||(i=f)};typeof u=="function"?o((y,...g)=>{let b=u(a?i:y,...g);return d(b,...c),a?y:b}):(a||o(u),d(u,...c))},[a,i,n]);return[i,s]}function iu(e,t=-1/0,n=1/0){return Math.min(Math.max(e,t),n)}function rx(e){const t=vt(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:n,visibility:r}=e.style,o=n!=="none"&&r!=="hidden"&&r!=="collapse";if(o){const{getComputedStyle:l}=e.ownerDocument.defaultView;let{display:a,visibility:i}=l(e);o=a!=="none"&&i!=="hidden"&&i!=="collapse"}return o}function ox(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function Cv(e,t){return e.nodeName!=="#comment"&&rx(e)&&ox(e,t)&&(!e.parentElement||Cv(e.parentElement,e))}function Tc(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function $v(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function kv(e){let t=p.useRef({isFocused:!1,observer:null});be(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let n=Oe(r=>{e==null||e(r)});return p.useCallback(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let o=r.target,l=a=>{if(t.current.isFocused=!1,o.disabled){let i=Tc(a);n(i)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};o.addEventListener("focusout",l,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&o.disabled){var a;(a=t.current.observer)===null||a===void 0||a.disconnect();let i=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:i})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:i}))}}),t.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[n])}let Ia=!1;function lx(e){for(;e&&!Ev(e);)e=e.parentElement;let t=vt(e),n=t.document.activeElement;if(!n||n===e)return;Ia=!0;let r=!1,o=c=>{(c.target===n||r)&&c.stopImmediatePropagation()},l=c=>{(c.target===n||r)&&(c.stopImmediatePropagation(),!e&&!r&&(r=!0,Ur(n),s()))},a=c=>{(c.target===e||r)&&c.stopImmediatePropagation()},i=c=>{(c.target===e||r)&&(c.stopImmediatePropagation(),r||(r=!0,Ur(n),s()))};t.addEventListener("blur",o,!0),t.addEventListener("focusout",l,!0),t.addEventListener("focusin",i,!0),t.addEventListener("focus",a,!0);let s=()=>{cancelAnimationFrame(u),t.removeEventListener("blur",o,!0),t.removeEventListener("focusout",l,!0),t.removeEventListener("focusin",i,!0),t.removeEventListener("focus",a,!0),Ia=!1,r=!1},u=requestAnimationFrame(s);return s}let Pr="default",su="",ia=new WeakMap;function ax(e){if(Cc()){if(Pr==="default"){const t=le(e);su=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}Pr="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";ia.set(e,e.style[t]),e.style[t]="none"}}function If(e){if(Cc()){if(Pr!=="disabled")return;Pr="restoring",setTimeout(()=>{yv(()=>{if(Pr==="restoring"){const t=le(e);t.documentElement.style.webkitUserSelect==="none"&&(t.documentElement.style.webkitUserSelect=su||""),su="",Pr="default"}})},300)}else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&ia.has(e)){let t=ia.get(e),n="userSelect"in e.style?"userSelect":"webkitUserSelect";e.style[n]==="none"&&(e.style[n]=t),e.getAttribute("style")===""&&e.removeAttribute("style"),ia.delete(e)}}const Nc=q.createContext({register:()=>{}});Nc.displayName="PressResponderContext";function ix(e,t){return t.get?t.get.call(e):t.value}function Pv(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function sx(e,t){var n=Pv(e,t,"get");return ix(e,n)}function ux(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function cx(e,t,n){ux(e,t),t.set(e,n)}function dx(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}function Rf(e,t,n){var r=Pv(e,t,"set");return dx(e,r,n),n}function fx(e){let t=p.useContext(Nc);if(t){let{register:n,...r}=t;e=Z(r,e),n()}return bv(t,e.ref),e}var Al=new WeakMap;class Wl{continuePropagation(){Rf(this,Al,!1)}get shouldStopPropagation(){return sx(this,Al)}constructor(t,n,r,o){cx(this,Al,{writable:!0,value:void 0}),Rf(this,Al,!0);var l;let a=(l=o==null?void 0:o.target)!==null&&l!==void 0?l:r.currentTarget;const i=a==null?void 0:a.getBoundingClientRect();let s,u=0,c,d=null;r.clientX!=null&&r.clientY!=null&&(c=r.clientX,d=r.clientY),i&&(c!=null&&d!=null?(s=c-i.left,u=d-i.top):(s=i.width/2,u=i.height/2)),this.type=t,this.pointerType=n,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=u}}const jf=Symbol("linkClicked"),zf="react-aria-pressable-style",Of="data-react-aria-pressable";function Kr(e){let{onPress:t,onPressChange:n,onPressStart:r,onPressEnd:o,onPressUp:l,onClick:a,isDisabled:i,isPressed:s,preventFocusOnPress:u,shouldCancelOnPointerExit:c,allowTextSelectionOnPress:d,ref:f,...y}=fx(e),[g,b]=p.useState(!1),S=p.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:h,removeAllGlobalListeners:m}=kc(),v=Oe((E,k)=>{let z=S.current;if(i||z.didFirePressStart)return!1;let $=!0;if(z.isTriggeringEvent=!0,r){let A=new Wl("pressstart",k,E);r(A),$=A.shouldStopPropagation}return n&&n(!0),z.isTriggeringEvent=!1,z.didFirePressStart=!0,b(!0),$}),x=Oe((E,k,z=!0)=>{let $=S.current;if(!$.didFirePressStart)return!1;$.didFirePressStart=!1,$.isTriggeringEvent=!0;let A=!0;if(o){let N=new Wl("pressend",k,E);o(N),A=N.shouldStopPropagation}if(n&&n(!1),b(!1),t&&z&&!i){let N=new Wl("press",k,E);t(N),A&&(A=N.shouldStopPropagation)}return $.isTriggeringEvent=!1,A}),C=Oe((E,k)=>{let z=S.current;if(i)return!1;if(l){z.isTriggeringEvent=!0;let $=new Wl("pressup",k,E);return l($),z.isTriggeringEvent=!1,$.shouldStopPropagation}return!0}),T=Oe(E=>{let k=S.current;if(k.isPressed&&k.target){k.didFirePressStart&&k.pointerType!=null&&x(On(k.target,E),k.pointerType,!1),k.isPressed=!1,k.isOverTarget=!1,k.activePointerId=null,k.pointerType=null,m(),d||If(k.target);for(let z of k.disposables)z();k.disposables=[]}}),L=Oe(E=>{c&&T(E)}),M=Oe(E=>{a==null||a(E)}),D=Oe((E,k)=>{if(a){let z=new MouseEvent("click",E);$v(z,k),a(Tc(z))}}),I=p.useMemo(()=>{let E=S.current,k={onKeyDown($){if(Yi($.nativeEvent,$.currentTarget)&&Qe($.currentTarget,ge($.nativeEvent))){var A;Ff(ge($.nativeEvent),$.key)&&$.preventDefault();let N=!0;if(!E.isPressed&&!$.repeat){E.target=$.currentTarget,E.isPressed=!0,E.pointerType="keyboard",N=v($,"keyboard");let O=$.currentTarget,P=R=>{Yi(R,O)&&!R.repeat&&Qe(O,ge(R))&&E.target&&C(On(E.target,R),"keyboard")};h(le($.currentTarget),"keyup",At(P,z),!0)}N&&$.stopPropagation(),$.metaKey&&ll()&&((A=E.metaKeyEvents)===null||A===void 0||A.set($.key,$.nativeEvent))}else $.key==="Meta"&&(E.metaKeyEvents=new Map)},onClick($){if(!($&&!Qe($.currentTarget,ge($.nativeEvent)))&&$&&$.button===0&&!E.isTriggeringEvent&&!nr.isOpening){let A=!0;if(i&&$.preventDefault(),!E.ignoreEmulatedMouseEvents&&!E.isPressed&&(E.pointerType==="virtual"||xv($.nativeEvent))){let N=v($,"virtual"),O=C($,"virtual"),P=x($,"virtual");M($),A=N&&O&&P}else if(E.isPressed&&E.pointerType!=="keyboard"){let N=E.pointerType||$.nativeEvent.pointerType||"virtual",O=C(On($.currentTarget,$),N),P=x(On($.currentTarget,$),N,!0);A=O&&P,E.isOverTarget=!1,M($),T($)}E.ignoreEmulatedMouseEvents=!1,A&&$.stopPropagation()}}},z=$=>{var A;if(E.isPressed&&E.target&&Yi($,E.target)){var N;Ff(ge($),$.key)&&$.preventDefault();let P=ge($),R=Qe(E.target,ge($));x(On(E.target,$),"keyboard",R),R&&D($,E.target),m(),$.key!=="Enter"&&_c(E.target)&&Qe(E.target,P)&&!$[jf]&&($[jf]=!0,nr(E.target,$,!1)),E.isPressed=!1,(N=E.metaKeyEvents)===null||N===void 0||N.delete($.key)}else if($.key==="Meta"&&(!((A=E.metaKeyEvents)===null||A===void 0)&&A.size)){var O;let P=E.metaKeyEvents;E.metaKeyEvents=void 0;for(let R of P.values())(O=E.target)===null||O===void 0||O.dispatchEvent(new KeyboardEvent("keyup",R))}};if(typeof PointerEvent<"u"){k.onPointerDown=N=>{if(N.button!==0||!Qe(N.currentTarget,ge(N.nativeEvent)))return;if(qw(N.nativeEvent)){E.pointerType="virtual";return}E.pointerType=N.pointerType;let O=!0;if(!E.isPressed){E.isPressed=!0,E.isOverTarget=!0,E.activePointerId=N.pointerId,E.target=N.currentTarget,d||ax(E.target),O=v(N,E.pointerType);let P=ge(N.nativeEvent);"releasePointerCapture"in P&&P.releasePointerCapture(N.pointerId),h(le(N.currentTarget),"pointerup",$,!1),h(le(N.currentTarget),"pointercancel",A,!1)}O&&N.stopPropagation()},k.onMouseDown=N=>{if(Qe(N.currentTarget,ge(N.nativeEvent))&&N.button===0){if(u){let O=lx(N.target);O&&E.disposables.push(O)}N.stopPropagation()}},k.onPointerUp=N=>{!Qe(N.currentTarget,ge(N.nativeEvent))||E.pointerType==="virtual"||N.button===0&&!E.isPressed&&C(N,E.pointerType||N.pointerType)},k.onPointerEnter=N=>{N.pointerId===E.activePointerId&&E.target&&!E.isOverTarget&&E.pointerType!=null&&(E.isOverTarget=!0,v(On(E.target,N),E.pointerType))},k.onPointerLeave=N=>{N.pointerId===E.activePointerId&&E.target&&E.isOverTarget&&E.pointerType!=null&&(E.isOverTarget=!1,x(On(E.target,N),E.pointerType,!1),L(N))};let $=N=>{if(N.pointerId===E.activePointerId&&E.isPressed&&N.button===0&&E.target){if(Qe(E.target,ge(N))&&E.pointerType!=null){let O=!1,P=setTimeout(()=>{E.isPressed&&E.target instanceof HTMLElement&&(O?T(N):(Ur(E.target),E.target.click()))},80);h(N.currentTarget,"click",()=>O=!0,!0),E.disposables.push(()=>clearTimeout(P))}else T(N);E.isOverTarget=!1}},A=N=>{T(N)};k.onDragStart=N=>{Qe(N.currentTarget,ge(N.nativeEvent))&&T(N)}}return k},[h,i,u,m,d,T,L,x,v,C,M,D]);return p.useEffect(()=>{if(!f)return;const E=le(f.current);if(!E||!E.head||E.getElementById(zf))return;const k=E.createElement("style");k.id=zf,k.textContent=`
@layer {
  [${Of}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),E.head.prepend(k)},[f]),p.useEffect(()=>{let E=S.current;return()=>{var k;d||If((k=E.target)!==null&&k!==void 0?k:void 0);for(let z of E.disposables)z();E.disposables=[]}},[d]),{isPressed:s||g,pressProps:Z(y,I,{[Of]:!0})}}function _c(e){return e.tagName==="A"&&e.hasAttribute("href")}function Yi(e,t){const{key:n,code:r}=e,o=t,l=o.getAttribute("role");return(n==="Enter"||n===" "||n==="Spacebar"||r==="Space")&&!(o instanceof vt(o).HTMLInputElement&&!Tv(o,n)||o instanceof vt(o).HTMLTextAreaElement||o.isContentEditable)&&!((l==="link"||!l&&_c(o))&&n!=="Enter")}function On(e,t){let n=t.clientX,r=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:n,clientY:r}}function px(e){return e instanceof HTMLInputElement?!1:e instanceof HTMLButtonElement?e.type!=="submit"&&e.type!=="reset":!_c(e)}function Ff(e,t){return e instanceof HTMLInputElement?!Tv(e,t):px(e)}const mx=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Tv(e,t){return e.type==="checkbox"||e.type==="radio"?t===" ":mx.has(e.type)}let cr=null,uu=new Set,Oo=new Map,rr=!1,cu=!1;const hx={Tab:!0,Escape:!0};function ai(e,t){for(let n of uu)n(e,t)}function vx(e){return!(e.metaKey||!ll()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Ra(e){rr=!0,vx(e)&&(cr="keyboard",ai("keyboard",e))}function zr(e){cr="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(rr=!0,ai("pointer",e))}function Nv(e){xv(e)&&(rr=!0,cr="virtual")}function _v(e){e.target===window||e.target===document||Ia||!e.isTrusted||(!rr&&!cu&&(cr="virtual",ai("virtual",e)),rr=!1,cu=!1)}function Mv(){Ia||(rr=!1,cu=!0)}function du(e){if(typeof window>"u"||typeof document>"u"||Oo.get(vt(e)))return;const t=vt(e),n=le(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){rr=!0,r.apply(this,arguments)},n.addEventListener("keydown",Ra,!0),n.addEventListener("keyup",Ra,!0),n.addEventListener("click",Nv,!0),t.addEventListener("focus",_v,!0),t.addEventListener("blur",Mv,!1),typeof PointerEvent<"u"&&(n.addEventListener("pointerdown",zr,!0),n.addEventListener("pointermove",zr,!0),n.addEventListener("pointerup",zr,!0)),t.addEventListener("beforeunload",()=>{Lv(e)},{once:!0}),Oo.set(t,{focus:r})}const Lv=(e,t)=>{const n=vt(e),r=le(e);t&&r.removeEventListener("DOMContentLoaded",t),Oo.has(n)&&(n.HTMLElement.prototype.focus=Oo.get(n).focus,r.removeEventListener("keydown",Ra,!0),r.removeEventListener("keyup",Ra,!0),r.removeEventListener("click",Nv,!0),n.removeEventListener("focus",_v,!0),n.removeEventListener("blur",Mv,!1),typeof PointerEvent<"u"&&(r.removeEventListener("pointerdown",zr,!0),r.removeEventListener("pointermove",zr,!0),r.removeEventListener("pointerup",zr,!0)),Oo.delete(n))};function gx(e){const t=le(e);let n;return t.readyState!=="loading"?du(e):(n=()=>{du(e)},t.addEventListener("DOMContentLoaded",n)),()=>Lv(e,n)}typeof document<"u"&&gx();function Mc(){return cr!=="pointer"}function Lc(){return cr}function yx(e){cr=e,ai(e,null)}const bx=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function wx(e,t,n){let r=le(n==null?void 0:n.target);const o=typeof window<"u"?vt(n==null?void 0:n.target).HTMLInputElement:HTMLInputElement,l=typeof window<"u"?vt(n==null?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a=typeof window<"u"?vt(n==null?void 0:n.target).HTMLElement:HTMLElement,i=typeof window<"u"?vt(n==null?void 0:n.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof o&&!bx.has(r.activeElement.type)||r.activeElement instanceof l||r.activeElement instanceof a&&r.activeElement.isContentEditable,!(e&&t==="keyboard"&&n instanceof i&&!hx[n.key])}function xx(e,t,n){du(),p.useEffect(()=>{let r=(o,l)=>{wx(!!(n!=null&&n.isTextInput),o,l)&&e(Mc())};return uu.add(r),()=>{uu.delete(r)}},t)}function Iv(e){const t=le(e),n=tt(t);if(Lc()==="virtual"){let r=n;yv(()=>{tt(t)===r&&e.isConnected&&Ur(e)})}else Ur(e)}function Rv(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e;const l=p.useCallback(s=>{if(s.target===s.currentTarget)return r&&r(s),o&&o(!1),!0},[r,o]),a=kv(l),i=p.useCallback(s=>{const u=le(s.target),c=u?tt(u):tt();s.target===s.currentTarget&&c===ge(s.nativeEvent)&&(n&&n(s),o&&o(!0),a(s))},[o,n,a]);return{focusProps:{onFocus:!t&&(n||o||r)?i:void 0,onBlur:!t&&(r||o)?l:void 0}}}function Df(e){if(!e)return;let t=!0;return n=>{let r={...n,preventDefault(){n.preventDefault()},isDefaultPrevented(){return n.isDefaultPrevented()},stopPropagation(){t=!0},continuePropagation(){t=!1},isPropagationStopped(){return t}};e(r),t&&n.stopPropagation()}}function Sx(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:Df(e.onKeyDown),onKeyUp:Df(e.onKeyUp)}}}let Ex=q.createContext(null);function Cx(e){let t=p.useContext(Ex)||{};bv(t,e);let{ref:n,...r}=t;return r}function eo(e,t){let{focusProps:n}=Rv(e),{keyboardProps:r}=Sx(e),o=Z(n,r),l=Cx(t),a=e.isDisabled?{}:l,i=p.useRef(e.autoFocus);p.useEffect(()=>{i.current&&t.current&&Iv(t.current),i.current=!1},[t]);let s=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(s=void 0),{focusableProps:Z({...o,tabIndex:s},a)}}function $x({children:e}){let t=p.useMemo(()=>({register:()=>{}}),[]);return q.createElement(Nc.Provider,{value:t},e)}function ii(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,l=p.useRef({isFocusWithin:!1}),{addGlobalListener:a,removeAllGlobalListeners:i}=kc(),s=p.useCallback(d=>{d.currentTarget.contains(d.target)&&l.current.isFocusWithin&&!d.currentTarget.contains(d.relatedTarget)&&(l.current.isFocusWithin=!1,i(),n&&n(d),o&&o(!1))},[n,o,l,i]),u=kv(s),c=p.useCallback(d=>{if(!d.currentTarget.contains(d.target))return;const f=le(d.target),y=tt(f);if(!l.current.isFocusWithin&&y===ge(d.nativeEvent)){r&&r(d),o&&o(!0),l.current.isFocusWithin=!0,u(d);let g=d.currentTarget;a(f,"focus",b=>{if(l.current.isFocusWithin&&!Qe(g,b.target)){let S=new f.defaultView.FocusEvent("blur",{relatedTarget:b.target});$v(S,g);let h=Tc(S);s(h)}},{capture:!0})}},[r,o,u,a,s]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:c,onBlur:s}}}let fu=!1,Xi=0;function kx(){fu=!0,setTimeout(()=>{fu=!1},50)}function Af(e){e.pointerType==="touch"&&kx()}function Px(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",Af),Xi++,()=>{Xi--,!(Xi>0)&&typeof PointerEvent<"u"&&document.removeEventListener("pointerup",Af)}}function or(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[l,a]=p.useState(!1),i=p.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;p.useEffect(Px,[]);let{addGlobalListener:s,removeAllGlobalListeners:u}=kc(),{hoverProps:c,triggerHoverEnd:d}=p.useMemo(()=>{let f=(b,S)=>{if(i.pointerType=S,o||S==="touch"||i.isHovered||!b.currentTarget.contains(b.target))return;i.isHovered=!0;let h=b.currentTarget;i.target=h,s(le(b.target),"pointerover",m=>{i.isHovered&&i.target&&!Qe(i.target,m.target)&&y(m,m.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:h,pointerType:S}),n&&n(!0),a(!0)},y=(b,S)=>{let h=i.target;i.pointerType="",i.target=null,!(S==="touch"||!i.isHovered||!h)&&(i.isHovered=!1,u(),r&&r({type:"hoverend",target:h,pointerType:S}),n&&n(!1),a(!1))},g={};return typeof PointerEvent<"u"&&(g.onPointerEnter=b=>{fu&&b.pointerType==="mouse"||f(b,b.pointerType)},g.onPointerLeave=b=>{!o&&b.currentTarget.contains(b.target)&&y(b,b.pointerType)}),{hoverProps:g,triggerHoverEnd:y}},[t,n,r,o,i,s,u]);return p.useEffect(()=>{o&&d({currentTarget:i.target},i.pointerType)},[o]),{hoverProps:c,isHovered:l}}function Tx(e){let{ref:t,onInteractOutside:n,isDisabled:r,onInteractOutsideStart:o}=e,l=p.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),a=Oe(s=>{n&&Wf(s,t)&&(o&&o(s),l.current.isPointerDown=!0)}),i=Oe(s=>{n&&n(s)});p.useEffect(()=>{let s=l.current;if(r)return;const u=t.current,c=le(u);if(typeof PointerEvent<"u"){let d=f=>{s.isPointerDown&&Wf(f,t)&&i(f),s.isPointerDown=!1};return c.addEventListener("pointerdown",a,!0),c.addEventListener("click",d,!0),()=>{c.removeEventListener("pointerdown",a,!0),c.removeEventListener("click",d,!0)}}},[t,r,a,i])}function Wf(e,t){if(e.button>0)return!1;if(e.target){const n=e.target.ownerDocument;if(!n||!n.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current?!e.composedPath().includes(t.current):!1}const Vf=q.createContext(null),pu="react-aria-focus-scope-restore";let ae=null;function Nx(e){let{children:t,contain:n,restoreFocus:r,autoFocus:o}=e,l=p.useRef(null),a=p.useRef(null),i=p.useRef([]),{parentNode:s}=p.useContext(Vf)||{},u=p.useMemo(()=>new hu({scopeRef:i}),[i]);be(()=>{let f=s||Ee.root;if(Ee.getTreeNode(f.scopeRef)&&ae&&!ja(ae,f.scopeRef)){let y=Ee.getTreeNode(ae);y&&(f=y)}f.addChild(u),Ee.addNode(u)},[u,s]),be(()=>{let f=Ee.getTreeNode(i);f&&(f.contain=!!n)},[n]),be(()=>{var f;let y=(f=l.current)===null||f===void 0?void 0:f.nextSibling,g=[],b=S=>S.stopPropagation();for(;y&&y!==a.current;)g.push(y),y.addEventListener(pu,b),y=y.nextSibling;return i.current=g,()=>{for(let S of g)S.removeEventListener(pu,b)}},[t]),Rx(i,r,n),Mx(i,n),jx(i,r,n),Ix(i,o),p.useEffect(()=>{const f=tt(le(i.current?i.current[0]:void 0));let y=null;if(wt(f,i.current)){for(let g of Ee.traverse())g.scopeRef&&wt(f,g.scopeRef.current)&&(y=g);y===Ee.getTreeNode(i)&&(ae=y.scopeRef)}},[i]),be(()=>()=>{var f,y,g;let b=(g=(y=Ee.getTreeNode(i))===null||y===void 0||(f=y.parent)===null||f===void 0?void 0:f.scopeRef)!==null&&g!==void 0?g:null;(i===ae||ja(i,ae))&&(!b||Ee.getTreeNode(b))&&(ae=b),Ee.removeTreeNode(i)},[i]);let c=p.useMemo(()=>_x(i),[]),d=p.useMemo(()=>({focusManager:c,parentNode:u}),[u,c]);return q.createElement(Vf.Provider,{value:d},q.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:l}),t,q.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:a}))}function _x(e){return{focusNext(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:a}=t;var i;let s=r||tt(le((i=n[0])!==null&&i!==void 0?i:void 0)),u=n[0].previousElementSibling,c=Kn(n),d=wn(c,{tabbable:o,accept:a},n);d.currentNode=wt(s,n)?s:u;let f=d.nextNode();return!f&&l&&(d.currentNode=u,f=d.nextNode()),f&&Zt(f,!0),f},focusPrevious(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:a}=t;var i;let s=r||tt(le((i=n[0])!==null&&i!==void 0?i:void 0)),u=n[n.length-1].nextElementSibling,c=Kn(n),d=wn(c,{tabbable:o,accept:a},n);d.currentNode=wt(s,n)?s:u;let f=d.previousNode();return!f&&l&&(d.currentNode=u,f=d.previousNode()),f&&Zt(f,!0),f},focusFirst(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=Kn(n),a=wn(l,{tabbable:r,accept:o},n);a.currentNode=n[0].previousElementSibling;let i=a.nextNode();return i&&Zt(i,!0),i},focusLast(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=Kn(n),a=wn(l,{tabbable:r,accept:o},n);a.currentNode=n[n.length-1].nextElementSibling;let i=a.previousNode();return i&&Zt(i,!0),i}}}function Kn(e){return e[0].parentElement}function ko(e){let t=Ee.getTreeNode(ae);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function Mx(e,t){let n=p.useRef(void 0),r=p.useRef(void 0);be(()=>{let o=e.current;if(!t){r.current&&(cancelAnimationFrame(r.current),r.current=void 0);return}const l=le(o?o[0]:void 0);let a=u=>{if(u.key!=="Tab"||u.altKey||u.ctrlKey||u.metaKey||!ko(e)||u.isComposing)return;let c=tt(l),d=e.current;if(!d||!wt(c,d))return;let f=Kn(d),y=wn(f,{tabbable:!0},d);if(!c)return;y.currentNode=c;let g=u.shiftKey?y.previousNode():y.nextNode();g||(y.currentNode=u.shiftKey?d[d.length-1].nextElementSibling:d[0].previousElementSibling,g=u.shiftKey?y.previousNode():y.nextNode()),u.preventDefault(),g&&Zt(g,!0)},i=u=>{(!ae||ja(ae,e))&&wt(ge(u),e.current)?(ae=e,n.current=ge(u)):ko(e)&&!bn(ge(u),e)?n.current?n.current.focus():ae&&ae.current&&mu(ae.current):ko(e)&&(n.current=ge(u))},s=u=>{r.current&&cancelAnimationFrame(r.current),r.current=requestAnimationFrame(()=>{let c=Lc(),d=(c==="virtual"||c===null)&&$c()&&pv(),f=tt(l);if(!d&&f&&ko(e)&&!bn(f,e)){ae=e;let g=ge(u);if(g&&g.isConnected){var y;n.current=g,(y=n.current)===null||y===void 0||y.focus()}else ae.current&&mu(ae.current)}})};return l.addEventListener("keydown",a,!1),l.addEventListener("focusin",i,!1),o==null||o.forEach(u=>u.addEventListener("focusin",i,!1)),o==null||o.forEach(u=>u.addEventListener("focusout",s,!1)),()=>{l.removeEventListener("keydown",a,!1),l.removeEventListener("focusin",i,!1),o==null||o.forEach(u=>u.removeEventListener("focusin",i,!1)),o==null||o.forEach(u=>u.removeEventListener("focusout",s,!1))}},[e,t]),be(()=>()=>{r.current&&cancelAnimationFrame(r.current)},[r])}function jv(e){return bn(e)}function wt(e,t){return!e||!t?!1:t.some(n=>n.contains(e))}function bn(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:n}of Ee.traverse(Ee.getTreeNode(t)))if(n&&wt(e,n.current))return!0;return!1}function Lx(e){return bn(e,ae)}function ja(e,t){var n;let r=(n=Ee.getTreeNode(t))===null||n===void 0?void 0:n.parent;for(;r;){if(r.scopeRef===e)return!0;r=r.parent}return!1}function Zt(e,t=!1){if(e!=null&&!t)try{Iv(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function zv(e,t=!0){let n=e[0].previousElementSibling,r=Kn(e),o=wn(r,{tabbable:t},e);o.currentNode=n;let l=o.nextNode();return t&&!l&&(r=Kn(e),o=wn(r,{tabbable:!1},e),o.currentNode=n,l=o.nextNode()),l}function mu(e,t=!0){Zt(zv(e,t))}function Ix(e,t){const n=q.useRef(t);p.useEffect(()=>{if(n.current){ae=e;const r=le(e.current?e.current[0]:void 0);!wt(tt(r),ae.current)&&e.current&&mu(e.current)}n.current=!1},[e])}function Rx(e,t,n){be(()=>{if(t||n)return;let r=e.current;const o=le(r?r[0]:void 0);let l=a=>{let i=ge(a);wt(i,e.current)?ae=e:jv(i)||(ae=null)};return o.addEventListener("focusin",l,!1),r==null||r.forEach(a=>a.addEventListener("focusin",l,!1)),()=>{o.removeEventListener("focusin",l,!1),r==null||r.forEach(a=>a.removeEventListener("focusin",l,!1))}},[e,t,n])}function Bf(e){let t=Ee.getTreeNode(ae);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return(t==null?void 0:t.scopeRef)===e}function jx(e,t,n){const r=p.useRef(typeof document<"u"?tt(le(e.current?e.current[0]:void 0)):null);be(()=>{let o=e.current;const l=le(o?o[0]:void 0);if(!t||n)return;let a=()=>{(!ae||ja(ae,e))&&wt(tt(l),e.current)&&(ae=e)};return l.addEventListener("focusin",a,!1),o==null||o.forEach(i=>i.addEventListener("focusin",a,!1)),()=>{l.removeEventListener("focusin",a,!1),o==null||o.forEach(i=>i.removeEventListener("focusin",a,!1))}},[e,n]),be(()=>{const o=le(e.current?e.current[0]:void 0);if(!t)return;let l=a=>{if(a.key!=="Tab"||a.altKey||a.ctrlKey||a.metaKey||!ko(e)||a.isComposing)return;let i=o.activeElement;if(!bn(i,e)||!Bf(e))return;let s=Ee.getTreeNode(e);if(!s)return;let u=s.nodeToRestore,c=wn(o.body,{tabbable:!0});c.currentNode=i;let d=a.shiftKey?c.previousNode():c.nextNode();if((!u||!u.isConnected||u===o.body)&&(u=void 0,s.nodeToRestore=void 0),(!d||!bn(d,e))&&u){c.currentNode=u;do d=a.shiftKey?c.previousNode():c.nextNode();while(bn(d,e));a.preventDefault(),a.stopPropagation(),d?Zt(d,!0):jv(u)?Zt(u,!0):i.blur()}};return n||o.addEventListener("keydown",l,!0),()=>{n||o.removeEventListener("keydown",l,!0)}},[e,t,n]),be(()=>{const o=le(e.current?e.current[0]:void 0);if(!t)return;let l=Ee.getTreeNode(e);if(l){var a;return l.nodeToRestore=(a=r.current)!==null&&a!==void 0?a:void 0,()=>{let i=Ee.getTreeNode(e);if(!i)return;let s=i.nodeToRestore,u=tt(o);if(t&&s&&(u&&bn(u,e)||u===o.body&&Bf(e))){let c=Ee.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let d=c.getTreeNode(e);for(;d;){if(d.nodeToRestore&&d.nodeToRestore.isConnected){Hf(d.nodeToRestore);return}d=d.parent}for(d=c.getTreeNode(e);d;){if(d.scopeRef&&d.scopeRef.current&&Ee.getTreeNode(d.scopeRef)){let f=zv(d.scopeRef.current,!0);Hf(f);return}d=d.parent}}})}}}},[e,t])}function Hf(e){e.dispatchEvent(new CustomEvent(pu,{bubbles:!0,cancelable:!0}))&&Zt(e)}function wn(e,t,n){let r=t!=null&&t.tabbable?nx:Ev,o=(e==null?void 0:e.nodeType)===Node.ELEMENT_NODE?e:null,l=le(o),a=jw(l,e||l,NodeFilter.SHOW_ELEMENT,{acceptNode(i){var s;return!(t==null||(s=t.from)===null||s===void 0)&&s.contains(i)?NodeFilter.FILTER_REJECT:r(i)&&Cv(i)&&(!n||wt(i,n))&&(!(t!=null&&t.accept)||t.accept(i))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t!=null&&t.from&&(a.currentNode=t.from),a}class Ic{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,n,r){let o=this.fastMap.get(n??null);if(!o)return;let l=new hu({scopeRef:t});o.addChild(l),l.parent=o,this.fastMap.set(t,l),r&&(l.nodeToRestore=r)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let n=this.fastMap.get(t);if(!n)return;let r=n.parent;for(let l of this.traverse())l!==n&&n.nodeToRestore&&l.nodeToRestore&&n.scopeRef&&n.scopeRef.current&&wt(l.nodeToRestore,n.scopeRef.current)&&(l.nodeToRestore=n.nodeToRestore);let o=n.children;r&&(r.removeChild(n),o.size>0&&o.forEach(l=>r&&r.addChild(l))),this.fastMap.delete(n.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let n of t.children)yield*this.traverse(n)}clone(){var t;let n=new Ic;var r;for(let o of this.traverse())n.addTreeNode(o.scopeRef,(r=(t=o.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&r!==void 0?r:null,o.nodeToRestore);return n}constructor(){this.fastMap=new Map,this.root=new hu({scopeRef:null}),this.fastMap.set(null,this.root)}}class hu{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}let Ee=new Ic;function lr(e={}){let{autoFocus:t=!1,isTextInput:n,within:r}=e,o=p.useRef({isFocused:!1,isFocusVisible:t||Mc()}),[l,a]=p.useState(!1),[i,s]=p.useState(()=>o.current.isFocused&&o.current.isFocusVisible),u=p.useCallback(()=>s(o.current.isFocused&&o.current.isFocusVisible),[]),c=p.useCallback(y=>{o.current.isFocused=y,a(y),u()},[u]);xx(y=>{o.current.isFocusVisible=y,u()},[],{isTextInput:n});let{focusProps:d}=Rv({isDisabled:r,onFocusChange:c}),{focusWithinProps:f}=ii({isDisabled:!r,onFocusWithinChange:c});return{isFocused:l,isFocusVisible:i,focusProps:r?f:d}}function zx(e,t){let{elementType:n="a",onPress:r,onPressStart:o,onPressEnd:l,onClick:a,isDisabled:i,...s}=e,u={};n!=="a"&&(u={role:"link",tabIndex:i?void 0:0});let{focusableProps:c}=eo(e,t),{pressProps:d,isPressed:f}=Kr({onClick:a,onPress:r,onPressStart:o,onPressEnd:l,isDisabled:i,ref:t}),y=qr(s,{labelable:!0,isLink:n==="a"}),g=Z(c,d),b=hv(),S=Qw(e);return{isPressed:f,linkProps:Z(y,S,{...g,...u,"aria-disabled":i||void 0,"aria-current":e["aria-current"],onClick:h=>{var m;(m=d.onClick)==null||m.call(d,h),!b.isNative&&h.currentTarget instanceof HTMLAnchorElement&&h.currentTarget.href&&!h.isDefaultPrevented()&&vv(h.currentTarget,h)&&e.href&&(h.preventDefault(),b.open(h.currentTarget,h,e.href,e.routerOptions))}})}}function Rc(e={}){const{strict:t=!0,errorMessage:n="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:r}=e,o=p.createContext(void 0);o.displayName=r;function l(){var a;const i=p.useContext(o);if(!i&&t){const s=new Error(n);throw s.name="ContextError",(a=Error.captureStackTrace)==null||a.call(Error,s,l),s}return i}return[o.Provider,l,o]}function Ox(e){return{UNSAFE_getDOMNode(){return e.current}}}function Ze(e){const t=p.useRef(null);return p.useImperativeHandle(e,()=>t.current),t}function Fx(e,t){if(e!=null){if(hw(e)){e(t);return}try{e.current=t}catch{throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}}}function Ov(...e){return t=>{e.forEach(n=>Fx(n,t))}}var Dx=(e,t)=>{var n;let r=[];const o=(n=p.Children.map(e,a=>p.isValidElement(a)&&a.type===t?(r.push(a),null):a))==null?void 0:n.filter(Boolean),l=r.length>=0?r:void 0;return[o,l]},Ax=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),Wx=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),Uf=/^(data-.*)$/,Vx=/^(aria-.*)$/,Vl=/^(on[A-Z].*)$/;function za(e,t={}){let{labelable:n=!0,enabled:r=!0,propNames:o,omitPropNames:l,omitEventNames:a,omitDataProps:i,omitEventProps:s}=t,u={};if(!r)return e;for(const c in e)l!=null&&l.has(c)||a!=null&&a.has(c)&&Vl.test(c)||Vl.test(c)&&!Wx.has(c)||i&&Uf.test(c)||s&&Vl.test(c)||(Object.prototype.hasOwnProperty.call(e,c)&&(Ax.has(c)||n&&Vx.test(c)||o!=null&&o.has(c)||Uf.test(c))||Vl.test(c))&&(u[c]=e[c]);return u}var[Bx,on]=Rc({name:"ProviderContext",strict:!1});const Hx=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Ux=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Fv(e){if(Intl.Locale){let n=new Intl.Locale(e).maximize(),r=typeof n.getTextInfo=="function"?n.getTextInfo():n.textInfo;if(r)return r.direction==="rtl";if(n.script)return Hx.has(n.script)}let t=e.split("-")[0];return Ux.has(t)}const Kx=Symbol.for("react-aria.i18n.locale");function Dv(){let e=typeof window<"u"&&window[Kx]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Fv(e)?"rtl":"ltr"}}let vu=Dv(),Po=new Set;function Kf(){vu=Dv();for(let e of Po)e(vu)}function Av(){let e=ri(),[t,n]=p.useState(vu);return p.useEffect(()=>(Po.size===0&&window.addEventListener("languagechange",Kf),Po.add(n),()=>{Po.delete(n),Po.size===0&&window.removeEventListener("languagechange",Kf)}),[]),e?{locale:"en-US",direction:"ltr"}:t}const Wv=q.createContext(null);function Gx(e){let{locale:t,children:n}=e,r=Av(),o=q.useMemo(()=>t?{locale:t,direction:Fv(t)?"rtl":"ltr"}:r,[r,t]);return q.createElement(Wv.Provider,{value:o},n)}function Qx(){let e=Av();return p.useContext(Wv)||e}const Ot={top:"top",bottom:"top",left:"left",right:"left"},Oa={top:"bottom",bottom:"top",left:"right",right:"left"},Yx={top:"left",left:"top"},gu={top:"height",left:"width"},Vv={width:"totalWidth",height:"totalHeight"},Bl={};let Te=typeof document<"u"?window.visualViewport:null;function Gf(e){let t=0,n=0,r=0,o=0,l=0,a=0,i={};var s;let u=((s=Te==null?void 0:Te.scale)!==null&&s!==void 0?s:1)>1;if(e.tagName==="BODY"){let g=document.documentElement;r=g.clientWidth,o=g.clientHeight;var c;t=(c=Te==null?void 0:Te.width)!==null&&c!==void 0?c:r;var d;n=(d=Te==null?void 0:Te.height)!==null&&d!==void 0?d:o,i.top=g.scrollTop||e.scrollTop,i.left=g.scrollLeft||e.scrollLeft,Te&&(l=Te.offsetTop,a=Te.offsetLeft)}else({width:t,height:n,top:l,left:a}=Or(e)),i.top=e.scrollTop,i.left=e.scrollLeft,r=t,o=n;if(fv()&&(e.tagName==="BODY"||e.tagName==="HTML")&&u){i.top=0,i.left=0;var f;l=(f=Te==null?void 0:Te.pageTop)!==null&&f!==void 0?f:0;var y;a=(y=Te==null?void 0:Te.pageLeft)!==null&&y!==void 0?y:0}return{width:t,height:n,totalWidth:r,totalHeight:o,scroll:i,top:l,left:a}}function Xx(e){return{top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}}function Qf(e,t,n,r,o,l,a){var i;let s=(i=o.scroll[e])!==null&&i!==void 0?i:0,u=r[gu[e]],c=r.scroll[Ot[e]]+l,d=u+r.scroll[Ot[e]]-l,f=t-s+a[e]-r[Ot[e]],y=t-s+n+a[e]-r[Ot[e]];return f<c?c-f:y>d?Math.max(d-y,c-f):0}function Zx(e){let t=window.getComputedStyle(e);return{top:parseInt(t.marginTop,10)||0,bottom:parseInt(t.marginBottom,10)||0,left:parseInt(t.marginLeft,10)||0,right:parseInt(t.marginRight,10)||0}}function Yf(e){if(Bl[e])return Bl[e];let[t,n]=e.split(" "),r=Ot[t]||"right",o=Yx[r];Ot[n]||(n="center");let l=gu[r],a=gu[o];return Bl[e]={placement:t,crossPlacement:n,axis:r,crossAxis:o,size:l,crossSize:a},Bl[e]}function Zi(e,t,n,r,o,l,a,i,s,u){let{placement:c,crossPlacement:d,axis:f,crossAxis:y,size:g,crossSize:b}=r,S={};var h;S[y]=(h=e[y])!==null&&h!==void 0?h:0;var m,v,x,C;d==="center"?S[y]+=(((m=e[b])!==null&&m!==void 0?m:0)-((v=n[b])!==null&&v!==void 0?v:0))/2:d!==y&&(S[y]+=((x=e[b])!==null&&x!==void 0?x:0)-((C=n[b])!==null&&C!==void 0?C:0)),S[y]+=l;const T=e[y]-n[b]+s+u,L=e[y]+e[b]-s-u;if(S[y]=iu(S[y],T,L),c===f){const M=i?a[g]:t[Vv[g]];S[Oa[f]]=Math.floor(M-e[f]+o)}else S[f]=Math.floor(e[f]+e[g]+o);return S}function Jx(e,t,n,r,o,l,a,i){const s=r?n.height:t[Vv.height];var u;let c=e.top!=null?n.top+e.top:n.top+(s-((u=e.bottom)!==null&&u!==void 0?u:0)-a);var d,f,y,g,b,S;let h=i!=="top"?Math.max(0,t.height+t.top+((d=t.scroll.top)!==null&&d!==void 0?d:0)-c-(((f=o.top)!==null&&f!==void 0?f:0)+((y=o.bottom)!==null&&y!==void 0?y:0)+l)):Math.max(0,c+a-(t.top+((g=t.scroll.top)!==null&&g!==void 0?g:0))-(((b=o.top)!==null&&b!==void 0?b:0)+((S=o.bottom)!==null&&S!==void 0?S:0)+l));return Math.min(t.height-l*2,h)}function Xf(e,t,n,r,o,l){let{placement:a,axis:i,size:s}=l;var u,c;if(a===i)return Math.max(0,n[i]-e[i]-((u=e.scroll[i])!==null&&u!==void 0?u:0)+t[i]-((c=r[i])!==null&&c!==void 0?c:0)-r[Oa[i]]-o);var d;return Math.max(0,e[s]+e[i]+e.scroll[i]-t[i]-n[i]-n[s]-((d=r[i])!==null&&d!==void 0?d:0)-r[Oa[i]]-o)}function qx(e,t,n,r,o,l,a,i,s,u,c,d,f,y,g,b){let S=Yf(e),{size:h,crossAxis:m,crossSize:v,placement:x,crossPlacement:C}=S,T=Zi(t,i,n,S,c,d,u,f,g,b),L=c,M=Xf(i,u,t,o,l+c,S);if(a&&r[h]>M){let K=Yf(`${Oa[x]} ${C}`),re=Zi(t,i,n,K,c,d,u,f,g,b);Xf(i,u,t,o,l+c,K)>M&&(S=K,T=re,L=c)}let D="bottom";S.axis==="top"?S.placement==="top"?D="top":S.placement==="bottom"&&(D="bottom"):S.crossAxis==="top"&&(S.crossPlacement==="top"?D="bottom":S.crossPlacement==="bottom"&&(D="top"));let I=Qf(m,T[m],n[v],i,s,l,u);T[m]+=I;let E=Jx(T,i,u,f,o,l,n.height,D);y&&y<E&&(E=y),n.height=Math.min(n.height,E),T=Zi(t,i,n,S,L,d,u,f,g,b),I=Qf(m,T[m],n[v],i,s,l,u),T[m]+=I;let k={},z=t[m]+.5*t[v]-T[m]-o[Ot[m]];const $=g/2+b;var A,N,O,P;const R=Ot[m]==="left"?((A=o.left)!==null&&A!==void 0?A:0)+((N=o.right)!==null&&N!==void 0?N:0):((O=o.top)!==null&&O!==void 0?O:0)+((P=o.bottom)!==null&&P!==void 0?P:0),_=n[v]-R-g/2-b,V=t[m]+g/2-(T[m]+o[Ot[m]]),H=t[m]+t[v]-g/2-(T[m]+o[Ot[m]]),te=iu(z,V,H);return k[m]=iu(te,$,_),{position:T,maxHeight:E,arrowOffsetLeft:k.left,arrowOffsetTop:k.top,placement:S.placement}}function e2(e){let{placement:t,targetNode:n,overlayNode:r,scrollNode:o,padding:l,shouldFlip:a,boundaryElement:i,offset:s,crossOffset:u,maxHeight:c,arrowSize:d=0,arrowBoundaryOffset:f=0}=e,y=r instanceof HTMLElement?t2(r):document.documentElement,g=y===document.documentElement;const b=window.getComputedStyle(y).position;let S=!!b&&b!=="static",h=g?Or(n):Zf(n,y);if(!g){let{marginTop:k,marginLeft:z}=window.getComputedStyle(n);h.top+=parseInt(k,10)||0,h.left+=parseInt(z,10)||0}let m=Or(r),v=Zx(r);var x,C;m.width+=((x=v.left)!==null&&x!==void 0?x:0)+((C=v.right)!==null&&C!==void 0?C:0);var T,L;m.height+=((T=v.top)!==null&&T!==void 0?T:0)+((L=v.bottom)!==null&&L!==void 0?L:0);let M=Xx(o),D=Gf(i),I=Gf(y),E=i.tagName==="BODY"?Or(y):Zf(y,i);return y.tagName==="HTML"&&i.tagName==="BODY"&&(I.scroll.top=0,I.scroll.left=0),qx(t,h,m,M,v,l,a,D,I,E,s,u,S,c,d,f)}function Or(e){let{top:t,left:n,width:r,height:o}=e.getBoundingClientRect(),{scrollTop:l,scrollLeft:a,clientTop:i,clientLeft:s}=document.documentElement;return{top:t+l-i,left:n+a-s,width:r,height:o}}function Zf(e,t){let n=window.getComputedStyle(e),r;if(n.position==="fixed"){let{top:o,left:l,width:a,height:i}=e.getBoundingClientRect();r={top:o,left:l,width:a,height:i}}else{r=Or(e);let o=Or(t),l=window.getComputedStyle(t);o.top+=(parseInt(l.borderTopWidth,10)||0)-t.scrollTop,o.left+=(parseInt(l.borderLeftWidth,10)||0)-t.scrollLeft,r.top-=o.top,r.left-=o.left}return r.top-=parseInt(n.marginTop,10)||0,r.left-=parseInt(n.marginLeft,10)||0,r}function t2(e){let t=e.offsetParent;if(t&&t===document.body&&window.getComputedStyle(t).position==="static"&&!Jf(t)&&(t=document.documentElement),t==null)for(t=e.parentElement;t&&!Jf(t);)t=t.parentElement;return t||document.documentElement}function Jf(e){let t=window.getComputedStyle(e);return t.transform!=="none"||/transform|perspective/.test(t.willChange)||t.filter!=="none"||t.contain==="paint"||"backdropFilter"in t&&t.backdropFilter!=="none"||"WebkitBackdropFilter"in t&&t.WebkitBackdropFilter!=="none"}const n2=new WeakMap;function r2(e){let{triggerRef:t,isOpen:n,onClose:r}=e;p.useEffect(()=>{if(!n||r===null)return;let o=l=>{let a=l.target;if(!t.current||a instanceof Node&&!a.contains(t.current)||l.target instanceof HTMLInputElement||l.target instanceof HTMLTextAreaElement)return;let i=r||n2.get(t.current);i&&i()};return window.addEventListener("scroll",o,!0),()=>{window.removeEventListener("scroll",o,!0)}},[n,r,t])}let ve=typeof document<"u"?window.visualViewport:null;function o2(e){let{direction:t}=Qx(),{arrowSize:n=0,targetRef:r,overlayRef:o,scrollRef:l=o,placement:a="bottom",containerPadding:i=12,shouldFlip:s=!0,boundaryElement:u=typeof document<"u"?document.body:null,offset:c=0,crossOffset:d=0,shouldUpdatePosition:f=!0,isOpen:y=!0,onClose:g,maxHeight:b,arrowBoundaryOffset:S=0}=e,[h,m]=p.useState(null),v=[f,a,o.current,r.current,l.current,i,s,u,c,d,y,t,b,S,n],x=p.useRef(ve==null?void 0:ve.scale);p.useEffect(()=>{y&&(x.current=ve==null?void 0:ve.scale)},[y]);let C=p.useCallback(()=>{if(f===!1||!y||!o.current||!r.current||!u||(ve==null?void 0:ve.scale)!==x.current)return;let I=null;if(l.current&&l.current.contains(document.activeElement)){var E;let P=(E=document.activeElement)===null||E===void 0?void 0:E.getBoundingClientRect(),R=l.current.getBoundingClientRect();var k;if(I={type:"top",offset:((k=P==null?void 0:P.top)!==null&&k!==void 0?k:0)-R.top},I.offset>R.height/2){I.type="bottom";var z;I.offset=((z=P==null?void 0:P.bottom)!==null&&z!==void 0?z:0)-R.bottom}}let $=o.current;if(!b&&o.current){var A;$.style.top="0px",$.style.bottom="";var N;$.style.maxHeight=((N=(A=window.visualViewport)===null||A===void 0?void 0:A.height)!==null&&N!==void 0?N:window.innerHeight)+"px"}let O=e2({placement:a2(a,t),overlayNode:o.current,targetNode:r.current,scrollNode:l.current||o.current,padding:i,shouldFlip:s,boundaryElement:u,offset:c,crossOffset:d,maxHeight:b,arrowSize:n,arrowBoundaryOffset:S});if(O.position){if($.style.top="",$.style.bottom="",$.style.left="",$.style.right="",Object.keys(O.position).forEach(P=>$.style[P]=O.position[P]+"px"),$.style.maxHeight=O.maxHeight!=null?O.maxHeight+"px":"",I&&document.activeElement&&l.current){let P=document.activeElement.getBoundingClientRect(),R=l.current.getBoundingClientRect(),_=P[I.type]-R[I.type];l.current.scrollTop+=_-I.offset}m(O)}},v);be(C,v),l2(C),au({ref:o,onResize:C}),au({ref:r,onResize:C});let T=p.useRef(!1);be(()=>{let I,E=()=>{T.current=!0,clearTimeout(I),I=setTimeout(()=>{T.current=!1},500),C()},k=()=>{T.current&&E()};return ve==null||ve.addEventListener("resize",E),ve==null||ve.addEventListener("scroll",k),()=>{ve==null||ve.removeEventListener("resize",E),ve==null||ve.removeEventListener("scroll",k)}},[C]);let L=p.useCallback(()=>{T.current||g==null||g()},[g,T]);r2({triggerRef:r,isOpen:y,onClose:g&&L});var M,D;return{overlayProps:{style:{position:"absolute",zIndex:1e5,...h==null?void 0:h.position,maxHeight:(M=h==null?void 0:h.maxHeight)!==null&&M!==void 0?M:"100vh"}},placement:(D=h==null?void 0:h.placement)!==null&&D!==void 0?D:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:h==null?void 0:h.arrowOffsetLeft,top:h==null?void 0:h.arrowOffsetTop}},updatePosition:C}}function l2(e){be(()=>(window.addEventListener("resize",e,!1),()=>{window.removeEventListener("resize",e,!1)}),[e])}function a2(e,t){return t==="rtl"?e.replace("start","right").replace("end","left"):e.replace("start","left").replace("end","right")}const It=[];function i2(e,t){let{onClose:n,shouldCloseOnBlur:r,isOpen:o,isDismissable:l=!1,isKeyboardDismissDisabled:a=!1,shouldCloseOnInteractOutside:i}=e;p.useEffect(()=>{if(o&&!It.includes(t))return It.push(t),()=>{let g=It.indexOf(t);g>=0&&It.splice(g,1)}},[o,t]);let s=()=>{It[It.length-1]===t&&n&&n()},u=g=>{(!i||i(g.target))&&It[It.length-1]===t&&(g.stopPropagation(),g.preventDefault())},c=g=>{(!i||i(g.target))&&(It[It.length-1]===t&&(g.stopPropagation(),g.preventDefault()),s())},d=g=>{g.key==="Escape"&&!a&&!g.nativeEvent.isComposing&&(g.stopPropagation(),g.preventDefault(),s())};Tx({ref:t,onInteractOutside:l&&o?c:void 0,onInteractOutsideStart:u});let{focusWithinProps:f}=ii({isDisabled:!r,onBlurWithin:g=>{!g.relatedTarget||Lx(g.relatedTarget)||(!i||i(g.relatedTarget))&&(n==null||n())}}),y=g=>{g.target===g.currentTarget&&g.preventDefault()};return{overlayProps:{onKeyDown:d,...f},underlayProps:{onPointerDown:y}}}const Ji=typeof document<"u"&&window.visualViewport,s2=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);let Hl=0,qi;function u2(e={}){let{isDisabled:t}=e;be(()=>{if(!t)return Hl++,Hl===1&&(Cc()?qi=d2():qi=c2()),()=>{Hl--,Hl===0&&qi()}},[t])}function c2(){let e=window.innerWidth-document.documentElement.clientWidth;return At(e>0&&("scrollbarGutter"in document.documentElement.style?Gn(document.documentElement,"scrollbarGutter","stable"):Gn(document.documentElement,"paddingRight",`${e}px`)),Gn(document.documentElement,"overflow","hidden"))}function d2(){let e,t,n=u=>{e=wv(u.target,!0),!(e===document.documentElement&&e===document.body)&&e instanceof HTMLElement&&window.getComputedStyle(e).overscrollBehavior==="auto"&&(t=Gn(e,"overscrollBehavior","contain"))},r=u=>{if(!e||e===document.documentElement||e===document.body){u.preventDefault();return}e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&u.preventDefault()},o=()=>{t&&t()},l=u=>{let c=u.target;f2(c)&&(i(),c.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{c.style.transform="",Ji&&(Ji.height<window.innerHeight?requestAnimationFrame(()=>{qf(c)}):Ji.addEventListener("resize",()=>qf(c),{once:!0}))}))},a=null,i=()=>{if(a)return;let u=()=>{window.scrollTo(0,0)},c=window.pageXOffset,d=window.pageYOffset;a=At(go(window,"scroll",u),Gn(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),Gn(document.documentElement,"overflow","hidden"),Gn(document.body,"marginTop",`-${d}px`),()=>{window.scrollTo(c,d)}),window.scrollTo(0,0)},s=At(go(document,"touchstart",n,{passive:!1,capture:!0}),go(document,"touchmove",r,{passive:!1,capture:!0}),go(document,"touchend",o,{passive:!1,capture:!0}),go(document,"focus",l,!0));return()=>{t==null||t(),a==null||a(),s()}}function Gn(e,t,n){let r=e.style[t];return e.style[t]=n,()=>{e.style[t]=r}}function go(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function qf(e){let t=document.scrollingElement||document.documentElement,n=e;for(;n&&n!==t;){let r=wv(n);if(r!==document.documentElement&&r!==document.body&&r!==n){let o=r.getBoundingClientRect().top,l=n.getBoundingClientRect().top;l>o+n.clientHeight&&(r.scrollTop+=l-o)}n=r.parentElement}}function f2(e){return e instanceof HTMLInputElement&&!s2.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}const p2=p.createContext({});function Bv(){var e;return(e=p.useContext(p2))!==null&&e!==void 0?e:{}}const yu=q.createContext(null);function m2(e){let{children:t}=e,n=p.useContext(yu),[r,o]=p.useState(0),l=p.useMemo(()=>({parent:n,modalCount:r,addModal(){o(a=>a+1),n&&n.addModal()},removeModal(){o(a=>a-1),n&&n.removeModal()}}),[n,r]);return q.createElement(yu.Provider,{value:l},t)}function h2(){let e=p.useContext(yu);return{modalProviderProps:{"aria-hidden":e&&e.modalCount>0?!0:void 0}}}function v2(e){let{modalProviderProps:t}=h2();return q.createElement("div",{"data-overlay-container":!0,...e,...t})}function Hv(e){return q.createElement(m2,null,q.createElement(v2,e))}function ep(e){let t=ri(),{portalContainer:n=t?null:document.body,...r}=e,{getContainer:o}=Bv();if(!e.portalContainer&&o&&(n=o()),q.useEffect(()=>{if(n!=null&&n.closest("[data-overlay-container]"))throw new Error("An OverlayContainer must not be inside another container. Please change the portalContainer prop.")},[n]),!n)return null;let l=q.createElement(Hv,r);return jh.createPortal(l,n)}const tp={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function g2(e={}){let{style:t,isFocusable:n}=e,[r,o]=p.useState(!1),{focusWithinProps:l}=ii({isDisabled:!n,onFocusWithinChange:i=>o(i)}),a=p.useMemo(()=>r?t:t?{...tp,...t}:tp,[r]);return{visuallyHiddenProps:{...l,style:a}}}function y2(e){let{children:t,elementType:n="div",isFocusable:r,style:o,...l}=e,{visuallyHiddenProps:a}=g2(e);return q.createElement(n,Z(l,a),t)}const b2=q.createContext(null);function np(e){let t=ri(),{portalContainer:n=t?null:document.body,isExiting:r}=e,[o,l]=p.useState(!1),a=p.useMemo(()=>({contain:o,setContain:l}),[o,l]),{getContainer:i}=Bv();if(!e.portalContainer&&i&&(n=i()),!n)return null;let s=e.children;return e.disableFocusManagement||(s=q.createElement(Nx,{restoreFocus:!0,contain:(e.shouldContainFocus||o)&&!r},s)),s=q.createElement(b2.Provider,{value:a},q.createElement($x,null,s)),jh.createPortal(s,n)}function w2(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}function Uv(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const x2=e=>Array.isArray(e);function bu(e){return typeof e=="string"||Array.isArray(e)}function rp(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function S2(e,t,n,r){if(typeof t=="function"){const[o,l]=rp(r);t=t(n!==void 0?n:e.custom,o,l)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,l]=rp(r);t=t(n!==void 0?n:e.custom,o,l)}return t}const E2=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],C2=["initial",...E2],si=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Kv=new Set(si),$2={skipAnimations:!1,useManualTiming:!1},k2=e=>e;function P2(e){let t=new Set,n=new Set,r=!1,o=!1;const l=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function i(u){l.has(u)&&(s.schedule(u),e()),u(a)}const s={schedule:(u,c=!1,d=!1)=>{const y=d&&r?t:n;return c&&l.add(u),y.has(u)||y.add(u),u},cancel:u=>{n.delete(u),l.delete(u)},process:u=>{if(a=u,r){o=!0;return}r=!0,[t,n]=[n,t],t.forEach(i),t.clear(),r=!1,o&&(o=!1,s.process(u))}};return s}const Ul=["read","resolveKeyframes","update","preRender","render","postRender"],T2=40;function Gv(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>n=!0,a=Ul.reduce((h,m)=>(h[m]=P2(l),h),{}),{read:i,resolveKeyframes:s,update:u,preRender:c,render:d,postRender:f}=a,y=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,T2),1),o.timestamp=h,o.isProcessing=!0,i.process(o),s.process(o),u.process(o),c.process(o),d.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(y))},g=()=>{n=!0,r=!0,o.isProcessing||e(y)};return{schedule:Ul.reduce((h,m)=>{const v=a[m];return h[m]=(x,C=!1,T=!1)=>(n||g(),v.schedule(x,C,T)),h},{}),cancel:h=>{for(let m=0;m<Ul.length;m++)a[Ul[m]].cancel(h)},state:o,steps:a}}const{schedule:op,cancel:iC,state:sC}=Gv(typeof requestAnimationFrame<"u"?requestAnimationFrame:k2,!0),Qv=e=>t=>typeof t=="string"&&t.startsWith(e),N2=Qv("--"),_2=Qv("var(--"),uC=e=>_2(e)?M2.test(e.split("/*")[0].trim()):!1,M2=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,L2=(e,t,n)=>n>t?t:n<e?e:n,jc={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},wu={...jc,transform:e=>L2(0,1,e)},Kl={...jc,default:1},hl=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Fn=hl("deg"),es=hl("%"),Q=hl("px"),cC=hl("vh"),dC=hl("vw"),lp={...es,parse:e=>es.parse(e)/100,transform:e=>es.transform(e*100)},I2={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q},R2={rotate:Fn,rotateX:Fn,rotateY:Fn,rotateZ:Fn,scale:Kl,scaleX:Kl,scaleY:Kl,scaleZ:Kl,skew:Fn,skewX:Fn,skewY:Fn,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:wu,originX:lp,originY:lp,originZ:Q},ap={...jc,transform:Math.round},Yv={...I2,...R2,zIndex:ap,size:Q,fillOpacity:wu,strokeOpacity:wu,numOctaves:ap},j2=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),fC=e=>x2(e)?e[e.length-1]||0:e,Xv=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),z2="framerAppearId",O2="data-"+Xv(z2),ar=e=>!!(e&&e.getVelocity);function Zv(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const zc=p.createContext(null),Jv=p.createContext({}),F2=p.createContext({}),D2={},{schedule:A2}=Gv(queueMicrotask,!1);function W2(e){const t=ar(e)?e.get():e;return j2(t)?t.toValue():t}const al=p.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),ui=p.createContext({}),qv=typeof window<"u",eg=qv?p.useLayoutEffect:p.useEffect,Oc=p.createContext({strict:!1});function V2(e,t,n,r,o){var l,a;const{visualElement:i}=p.useContext(ui),s=p.useContext(Oc),u=p.useContext(zc),c=p.useContext(al).reducedMotion,d=p.useRef(null);r=r||s.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:i,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,y=p.useContext(F2);f&&!f.projection&&o&&(f.type==="html"||f.type==="svg")&&B2(d.current,n,o,y);const g=p.useRef(!1);p.useInsertionEffect(()=>{f&&g.current&&f.update(n,u)});const b=n[O2],S=p.useRef(!!b&&!(!((l=window.MotionHandoffIsComplete)===null||l===void 0)&&l.call(window,b))&&((a=window.MotionHasOptimisedAnimation)===null||a===void 0?void 0:a.call(window,b)));return eg(()=>{f&&(g.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),A2.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),p.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var h;(h=window.MotionHandoffMarkAsComplete)===null||h===void 0||h.call(window,b)}),S.current=!1))}),f}function B2(e,t,n,r){const{layoutId:o,layout:l,drag:a,dragConstraints:i,layoutScroll:s,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:tg(e.parent)),e.projection.setOptions({layoutId:o,layout:l,alwaysMeasureLayout:!!a||i&&Zv(i),visualElement:e,animationType:typeof l=="string"?l:"both",initialPromotionConfig:r,layoutScroll:s,layoutRoot:u})}function tg(e){if(e)return e.options.allowProjection!==!1?e.projection:tg(e.parent)}function H2(e,t,n){return p.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Zv(n)&&(n.current=r))},[t])}function Fc(e){return Uv(e.animate)||C2.some(t=>bu(e[t]))}function U2(e){return!!(Fc(e)||e.variants)}function K2(e,t){if(Fc(e)){const{initial:n,animate:r}=e;return{initial:n===!1||bu(n)?n:void 0,animate:bu(r)?r:void 0}}return e.inherit!==!1?t:{}}function G2(e){const{initial:t,animate:n}=K2(e,p.useContext(ui));return p.useMemo(()=>({initial:t,animate:n}),[ip(t),ip(n)])}function ip(e){return Array.isArray(e)?e.join(" "):e}const sp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Fa={};for(const e in sp)Fa[e]={isEnabled:t=>sp[e].some(n=>!!t[n])};function up(e){for(const t in e)Fa[t]={...Fa[t],...e[t]}}const Q2=Symbol.for("motionComponentSymbol");function Y2({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){function l(i,s){let u;const c={...p.useContext(al),...i,layoutId:X2(i)},{isStatic:d}=c,f=G2(i),y=r(i,d);if(!d&&qv){Z2();const g=J2(c);u=g.MeasureLayout,f.visualElement=V2(o,y,c,t,g.ProjectionNode)}return w.jsxs(ui.Provider,{value:f,children:[u&&f.visualElement?w.jsx(u,{visualElement:f.visualElement,...c}):null,n(o,i,H2(y,f.visualElement,s),y,d,f.visualElement)]})}const a=p.forwardRef(l);return a[Q2]=o,a}function X2({layoutId:e}){const t=p.useContext(Jv).id;return t&&e!==void 0?t+"-"+e:e}function Z2(e,t){p.useContext(Oc).strict}function J2(e){const{drag:t,layout:n}=Fa;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const q2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ng(e){return typeof e!="string"||e.includes("-")?!1:!!(q2.indexOf(e)>-1||/[A-Z]/u.test(e))}function eS(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const l in n)e.style.setProperty(l,n[l])}const tS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nS(e,t,n,r){eS(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(tS.has(o)?o:Xv(o),t.attrs[o])}function rg(e,{layout:t,layoutId:n}){return Kv.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!D2[e]||e==="opacity")}function og(e,t,n){var r;const{style:o}=e,l={};for(const a in o)(ar(o[a])||t.style&&ar(t.style[a])||rg(a,e)||((r=n==null?void 0:n.getValue(a))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(l[a]=o[a]);return l}function rS(e,t,n){const r=og(e,t,n);for(const o in e)if(ar(e[o])||ar(t[o])){const l=si.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[l]=e[o]}return r}function ci(e){const t=p.useRef(null);return t.current===null&&(t.current=e()),t.current}function oS({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,l){const a={latestValues:lS(r,o,l,e),renderState:t()};return n&&(a.mount=i=>n(r,i,a)),a}const lg=e=>(t,n)=>{const r=p.useContext(ui),o=p.useContext(zc),l=()=>oS(e,t,r,o);return n?l():ci(l)};function lS(e,t,n,r){const o={},l=r(e,{});for(const f in l)o[f]=W2(l[f]);let{initial:a,animate:i}=e;const s=Fc(e),u=U2(e);t&&u&&!s&&e.inherit!==!1&&(a===void 0&&(a=t.initial),i===void 0&&(i=t.animate));let c=n?n.initial===!1:!1;c=c||a===!1;const d=c?i:a;if(d&&typeof d!="boolean"&&!Uv(d)){const f=Array.isArray(d)?d:[d];for(let y=0;y<f.length;y++){const g=S2(e,f[y]);if(g){const{transitionEnd:b,transition:S,...h}=g;for(const m in h){let v=h[m];if(Array.isArray(v)){const x=c?v.length-1:0;v=v[x]}v!==null&&(o[m]=v)}for(const m in b)o[m]=b[m]}}}return o}const Dc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),ag=()=>({...Dc(),attrs:{}}),ig=(e,t)=>t&&typeof e=="number"?t.transform(e):e,aS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iS=si.length;function sS(e,t,n){let r="",o=!0;for(let l=0;l<iS;l++){const a=si[l],i=e[a];if(i===void 0)continue;let s=!0;if(typeof i=="number"?s=i===(a.startsWith("scale")?1:0):s=parseFloat(i)===0,!s||n){const u=ig(i,Yv[a]);if(!s){o=!1;const c=aS[a]||a;r+=`${c}(${u}) `}n&&(t[a]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function sg(e,t,n){const{style:r,vars:o,transformOrigin:l}=e;let a=!1,i=!1;for(const s in t){const u=t[s];if(Kv.has(s)){a=!0;continue}else if(N2(s)){o[s]=u;continue}else{const c=ig(u,Yv[s]);s.startsWith("origin")?(i=!0,l[s]=c):r[s]=c}}if(t.transform||(a||n?r.transform=sS(t,e.transform,n):r.transform&&(r.transform="none")),i){const{originX:s="50%",originY:u="50%",originZ:c=0}=l;r.transformOrigin=`${s} ${u} ${c}`}}function cp(e,t,n){return typeof e=="string"?e:Q.transform(t+n*e)}function uS(e,t,n){const r=cp(t,e.x,e.width),o=cp(n,e.y,e.height);return`${r} ${o}`}const cS={offset:"stroke-dashoffset",array:"stroke-dasharray"},dS={offset:"strokeDashoffset",array:"strokeDasharray"};function fS(e,t,n=1,r=0,o=!0){e.pathLength=1;const l=o?cS:dS;e[l.offset]=Q.transform(-r);const a=Q.transform(t),i=Q.transform(n);e[l.array]=`${a} ${i}`}function ug(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:l,pathLength:a,pathSpacing:i=1,pathOffset:s=0,...u},c,d){if(sg(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:y,dimensions:g}=e;f.transform&&(g&&(y.transform=f.transform),delete f.transform),g&&(o!==void 0||l!==void 0||y.transform)&&(y.transformOrigin=uS(g,o!==void 0?o:.5,l!==void 0?l:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),a!==void 0&&fS(f,a,i,s,!1)}const cg=e=>typeof e=="string"&&e.toLowerCase()==="svg",pS={useVisualState:lg({scrapeMotionValuesFromProps:rS,createRenderState:ag,onMount:(e,t,{renderState:n,latestValues:r})=>{op.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),op.render(()=>{ug(n,r,cg(t.tagName),e.transformTemplate),nS(t,n)})}})},mS={useVisualState:lg({scrapeMotionValuesFromProps:og,createRenderState:Dc})};function dg(e,t,n){for(const r in t)!ar(t[r])&&!rg(r,n)&&(e[r]=t[r])}function hS({transformTemplate:e},t){return p.useMemo(()=>{const n=Dc();return sg(n,t,e),Object.assign({},n.vars,n.style)},[t])}function vS(e,t){const n=e.style||{},r={};return dg(r,n,e),Object.assign(r,hS(e,t)),r}function gS(e,t){const n={},r=vS(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const yS=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Da(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||yS.has(e)}let fg=e=>!Da(e);function pg(e){e&&(fg=t=>t.startsWith("on")?!Da(t):e(t))}try{pg(require("@emotion/is-prop-valid").default)}catch{}function bS(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(fg(o)||n===!0&&Da(o)||!t&&!Da(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function wS(e,t,n,r){const o=p.useMemo(()=>{const l=ag();return ug(l,t,cg(r),e.transformTemplate),{...l.attrs,style:{...l.style}}},[t]);if(e.style){const l={};dg(l,e.style,e),o.style={...l,...o.style}}return o}function xS(e=!1){return(n,r,o,{latestValues:l},a)=>{const s=(ng(n)?wS:gS)(r,l,a,n),u=bS(r,typeof n=="string",e),c=n!==p.Fragment?{...u,...s,ref:o}:{},{children:d}=r,f=p.useMemo(()=>ar(d)?d.get():d,[d]);return p.createElement(n,{...c,children:f})}}function SS(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const a={...ng(r)?pS:mS,preloadedFeatures:e,useRender:xS(o),createVisualElement:t,Component:r};return Y2(a)}}const ES=SS(),di=w2(ES);class CS extends p.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function $S({children:e,isPresent:t}){const n=p.useId(),r=p.useRef(null),o=p.useRef({width:0,height:0,top:0,left:0}),{nonce:l}=p.useContext(al);return p.useInsertionEffect(()=>{const{width:a,height:i,top:s,left:u}=o.current;if(t||!r.current||!a||!i)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${i}px !important;
            top: ${s}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),w.jsx(CS,{isPresent:t,childRef:r,sizeRef:o,children:p.cloneElement(e,{ref:r})})}const kS=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:l,mode:a})=>{const i=ci(PS),s=p.useId(),u=p.useCallback(d=>{i.set(d,!0);for(const f of i.values())if(!f)return;r&&r()},[i,r]),c=p.useMemo(()=>({id:s,initial:t,isPresent:n,custom:o,onExitComplete:u,register:d=>(i.set(d,!1),()=>i.delete(d))}),l?[Math.random(),u]:[n,u]);return p.useMemo(()=>{i.forEach((d,f)=>i.set(f,!1))},[n]),p.useEffect(()=>{!n&&!i.size&&r&&r()},[n]),a==="popLayout"&&(e=w.jsx($S,{isPresent:n,children:e})),w.jsx(zc.Provider,{value:c,children:e})};function PS(){return new Map}const Gl=e=>e.key||"";function dp(e){const t=[];return p.Children.forEach(e,n=>{p.isValidElement(n)&&t.push(n)}),t}const Ac=({children:e,exitBeforeEnter:t,custom:n,initial:r=!0,onExitComplete:o,presenceAffectsLayout:l=!0,mode:a="sync"})=>{const i=p.useMemo(()=>dp(e),[e]),s=i.map(Gl),u=p.useRef(!0),c=p.useRef(i),d=ci(()=>new Map),[f,y]=p.useState(i),[g,b]=p.useState(i);eg(()=>{u.current=!1,c.current=i;for(let m=0;m<g.length;m++){const v=Gl(g[m]);s.includes(v)?d.delete(v):d.get(v)!==!0&&d.set(v,!1)}},[g,s.length,s.join("-")]);const S=[];if(i!==f){let m=[...i];for(let v=0;v<g.length;v++){const x=g[v],C=Gl(x);s.includes(C)||(m.splice(v,0,x),S.push(x))}a==="wait"&&S.length&&(m=S),b(dp(m)),y(i);return}const{forceRender:h}=p.useContext(Jv);return w.jsx(w.Fragment,{children:g.map(m=>{const v=Gl(m),x=i===g||s.includes(v),C=()=>{if(d.has(v))d.set(v,!0);else return;let T=!0;d.forEach(L=>{L||(T=!1)}),T&&(h==null||h(),b(c.current),o&&o())};return w.jsx(kS,{isPresent:x,initial:!u.current||r?void 0:!1,custom:x?void 0:n,presenceAffectsLayout:l,mode:a,onExitComplete:x?void 0:C,children:m},v)})})};function TS({children:e,isValidProp:t,...n}){t&&pg(t),n={...p.useContext(al),...n},n.isStatic=ci(()=>n.isStatic);const r=p.useMemo(()=>n,[JSON.stringify(n.transition),n.transformPagePoint,n.reducedMotion]);return w.jsx(al.Provider,{value:r,children:e})}function fi({children:e,features:t,strict:n=!1}){const[,r]=p.useState(!ts(t)),o=p.useRef(void 0);if(!ts(t)){const{renderer:l,...a}=t;o.current=l,up(a)}return p.useEffect(()=>{ts(t)&&t().then(({renderer:l,...a})=>{up(a),o.current=l,r(!0)})},[]),w.jsx(Oc.Provider,{value:{renderer:o.current,strict:n},children:e})}function ts(e){return typeof e=="function"}var NS=({children:e,navigate:t,disableAnimation:n,useHref:r,disableRipple:o=!1,skipFramerMotionAnimations:l=n,reducedMotion:a="never",validationBehavior:i,locale:s="en-US",labelPlacement:u,defaultDates:c,createCalendar:d,spinnerVariant:f,...y})=>{let g=e;t&&(g=w.jsx(Kw,{navigate:t,useHref:r,children:g}));const b=p.useMemo(()=>(n&&l&&($2.skipAnimations=!0),{createCalendar:d,defaultDates:c,disableAnimation:n,disableRipple:o,validationBehavior:i,labelPlacement:u,spinnerVariant:f}),[d,c==null?void 0:c.maxDate,c==null?void 0:c.minDate,n,o,i,u,f]);return w.jsx(Bx,{value:b,children:w.jsx(Gx,{locale:s,children:w.jsx(TS,{reducedMotion:a,children:w.jsx(Hv,{...y,children:g})})})})};function _S(e){const t=on(),n=t==null?void 0:t.labelPlacement;return p.useMemo(()=>{var r,o;const l=(o=(r=e.labelPlacement)!=null?r:n)!=null?o:"inside";return l==="inside"&&!e.label?"outside":l},[e.labelPlacement,n,e.label])}function Ue(e){return p.forwardRef(e)}var ln=(e,t,n=!0)=>{if(!t)return[e,{}];const r=t.reduce((o,l)=>l in e?{...o,[l]:e[l]}:o,{});return n?[Object.keys(e).filter(l=>!t.includes(l)).reduce((l,a)=>({...l,[a]:e[a]}),{}),r]:[e,r]};function MS(e){var t,n,r,o;const l=on(),[a,i]=ln(e,ru.variantKeys),{ref:s,as:u,children:c,anchorIcon:d,isExternal:f=!1,showAnchorIcon:y=!1,autoFocus:g=!1,className:b,onPress:S,onPressStart:h,onPressEnd:m,onClick:v,...x}=a,C=u||"a",T=Ze(s),L=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:l==null?void 0:l.disableAnimation)!=null?n:!1,{linkProps:M}=zx({...x,onPress:S,onPressStart:h,onPressEnd:m,onClick:v,isDisabled:e.isDisabled,elementType:`${u}`},T),{isFocused:D,isFocusVisible:I,focusProps:E}=lr({autoFocus:g});f&&(x.rel=(r=x.rel)!=null?r:"noopener noreferrer",x.target=(o=x.target)!=null?o:"_blank");const k=p.useMemo(()=>ru({...i,disableAnimation:L,className:b}),[Wt(i),L,b]),z=p.useCallback(()=>({ref:T,className:k,"data-focus":B(D),"data-disabled":B(e.isDisabled),"data-focus-visible":B(I),...Z(E,M,x)}),[k,D,I,E,M,x]);return{Component:C,children:c,anchorIcon:d,showAnchorIcon:y,getLinkProps:z}}var LS=e=>w.jsx("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,viewBox:"0 0 24 24",width:"1em",...e,children:w.jsx("polyline",{points:"20 6 9 17 4 12"})}),IS=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M16 17.1c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z"}),w.jsx("path",{d:"M8 8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9v4.2c0 3.5-1.4 4.9-4.9 4.9H16"}),w.jsx("path",{d:"M16 12.9C16 9.4 14.6 8 11.1 8"})]}),RS=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",shapeRendering:"geometricPrecision",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"}),w.jsx("path",{d:"M15 3h6v6"}),w.jsx("path",{d:"M10 14L21 3"})]}),jS=e=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:w.jsx("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})}),mg=Ue((e,t)=>{const{Component:n,children:r,showAnchorIcon:o,anchorIcon:l=w.jsx(RS,{className:cw}),getLinkProps:a}=MS({ref:t,...e});return w.jsx(n,{...a(),children:w.jsxs(w.Fragment,{children:[r,o&&l]})})});mg.displayName="HeroUI.Link";var $t=mg,zS=e=>e.replace(/[\u00A0]/g," ");function OS({timeout:e=2e3}={}){const[t,n]=p.useState(null),[r,o]=p.useState(!1),[l,a]=p.useState(null),i=p.useCallback(()=>{l&&clearTimeout(l)},[l]),s=p.useCallback(d=>{i(),a(setTimeout(()=>o(!1),e)),o(d)},[i,e]),u=p.useCallback(d=>{if("clipboard"in navigator){const f=typeof d=="string"?zS(d):d;navigator.clipboard.writeText(f).then(()=>s(!0)).catch(y=>n(y))}else n(new Error("useClipboard: navigator.clipboard is not supported"))},[s]),c=p.useCallback(()=>{o(!1),n(null),i()},[i]);return{copy:u,reset:c,error:t,copied:r}}function FS(e){var t,n,r,o;const l=on(),[a,i]=ln(e,Gi.variantKeys),{ref:s,as:u,children:c,symbol:d="$",classNames:f,timeout:y,copyIcon:g,checkIcon:b,codeString:S,disableCopy:h=!1,disableTooltip:m=!1,hideCopyButton:v=!1,autoFocus:x=!1,hideSymbol:C=!1,onCopy:T,tooltipProps:L={},copyButtonProps:M={},className:D,...I}=a,E=u||"div",k=typeof E=="string",z=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:l==null?void 0:l.disableAnimation)!=null?n:!1,$={offset:15,delay:1e3,content:"Copy to clipboard",color:(o=e==null?void 0:e.color)!=null?o:(r=Gi.defaultVariants)==null?void 0:r.color,isDisabled:a.disableCopy,...L},A=Ze(s),N=p.useRef(null),{copy:O,copied:P}=OS({timeout:y}),R=c&&Array.isArray(c),{isFocusVisible:_,isFocused:V,focusProps:H}=lr({autoFocus:x}),te=p.useMemo(()=>Gi({...i,disableAnimation:z}),[Wt(i),z]),K=p.useMemo(()=>{if(!d||typeof d!="string")return d;const Me=d.trim();return Me?`${Me} `:""},[d]),re=ee(f==null?void 0:f.base,D),G=p.useCallback(()=>({className:te.base({class:re}),...za(I,{enabled:k})}),[te,re,R,I]),we=p.useCallback(()=>{var Me;if(h)return;let nt="";typeof c=="string"?nt=c:Array.isArray(c)&&c.forEach(Ke=>{var St,ct;const U=typeof Ke=="string"?Ke:(ct=(St=Ke==null?void 0:Ke.props)==null?void 0:St.children)==null?void 0:ct.toString();U&&(nt+=U+`
`)});const Ae=S||nt||((Me=N.current)==null?void 0:Me.textContent)||"";O(Ae),T==null||T(Ae)},[O,S,h,T,c]),ut={"aria-label":typeof $.content=="string"?$.content:"Copy to clipboard",size:"sm",variant:"light",isDisabled:h,onPress:we,isIconOnly:!0,...M},xe=p.useCallback(()=>({...ut,"data-copied":B(P),className:te.copyButton({class:ee(f==null?void 0:f.copyButton)})}),[te,_,V,h,f==null?void 0:f.copyButton,ut,H]);return{Component:E,as:u,domRef:A,preRef:N,children:c,slots:te,classNames:f,copied:P,onCopy:we,copyIcon:g,checkIcon:b,symbolBefore:K,isMultiLine:R,isFocusVisible:_,hideCopyButton:v,disableCopy:h,disableTooltip:m,hideSymbol:C,tooltipProps:$,getSnippetProps:G,getCopyButtonProps:xe}}const DS="modulepreload",AS=function(e){return"/"+e},fp={},pi=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));o=Promise.all(n.map(i=>{if(i=AS(i),i in fp)return;fp[i]=!0;const s=i.endsWith(".css"),u=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${u}`))return;const c=document.createElement("link");if(c.rel=s?"stylesheet":DS,s||(c.as="script",c.crossOrigin=""),c.href=i,a&&c.setAttribute("nonce",a),document.head.appendChild(c),s)return new Promise((d,f)=>{c.addEventListener("load",d),c.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})}))}return o.then(()=>t()).catch(l=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l})};function WS(e){let[t,n]=ml(e.isOpen,e.defaultOpen||!1,e.onOpenChange);const r=p.useCallback(()=>{n(!0)},[n]),o=p.useCallback(()=>{n(!1)},[n]),l=p.useCallback(()=>{n(!t)},[n,t]);return{isOpen:t,setOpen:n,open:r,close:o,toggle:l}}const VS=1500,pp=500;let Dn={},BS=0,yo=!1,Ut=null,An=null;function HS(e={}){let{delay:t=VS,closeDelay:n=pp}=e,{isOpen:r,open:o,close:l}=WS(e),a=p.useMemo(()=>`${++BS}`,[]),i=p.useRef(null),s=p.useRef(l),u=()=>{Dn[a]=f},c=()=>{for(let g in Dn)g!==a&&(Dn[g](!0),delete Dn[g])},d=()=>{i.current&&clearTimeout(i.current),i.current=null,c(),u(),yo=!0,o(),Ut&&(clearTimeout(Ut),Ut=null),An&&(clearTimeout(An),An=null)},f=g=>{g||n<=0?(i.current&&clearTimeout(i.current),i.current=null,s.current()):i.current||(i.current=setTimeout(()=>{i.current=null,s.current()},n)),Ut&&(clearTimeout(Ut),Ut=null),yo&&(An&&clearTimeout(An),An=setTimeout(()=>{delete Dn[a],An=null,yo=!1},Math.max(pp,n)))},y=()=>{c(),u(),!r&&!Ut&&!yo?Ut=setTimeout(()=>{Ut=null,yo=!0,d()},t):r||d()};return p.useEffect(()=>{s.current=l},[l]),p.useEffect(()=>()=>{i.current&&clearTimeout(i.current),Dn[a]&&delete Dn[a]},[a]),{isOpen:r,open:g=>{!g&&t>0&&!i.current?y():d()},close:f}}function US(e,t){let n=qr(e,{labelable:!0}),{hoverProps:r}=or({onHoverStart:()=>t==null?void 0:t.open(!0),onHoverEnd:()=>t==null?void 0:t.close()});return{tooltipProps:Z(n,r,{role:"tooltip"})}}function KS(e,t,n){let{isDisabled:r,trigger:o}=e,l=ol(),a=p.useRef(!1),i=p.useRef(!1),s=()=>{(a.current||i.current)&&t.open(i.current)},u=h=>{!a.current&&!i.current&&t.close(h)};p.useEffect(()=>{let h=m=>{n&&n.current&&m.key==="Escape"&&(m.stopPropagation(),t.close(!0))};if(t.isOpen)return document.addEventListener("keydown",h,!0),()=>{document.removeEventListener("keydown",h,!0)}},[n,t]);let c=()=>{o!=="focus"&&(Lc()==="pointer"?a.current=!0:a.current=!1,s())},d=()=>{o!=="focus"&&(i.current=!1,a.current=!1,u())},f=()=>{i.current=!1,a.current=!1,u(!0)},y=()=>{Mc()&&(i.current=!0,s())},g=()=>{i.current=!1,a.current=!1,u(!0)},{hoverProps:b}=or({isDisabled:r,onHoverStart:c,onHoverEnd:d}),{focusableProps:S}=eo({isDisabled:r,onFocus:y,onBlur:g},n);return{triggerProps:{"aria-describedby":t.isOpen?l:void 0,...Z(S,b,{onPointerDown:f,onKeyDown:f,tabIndex:void 0})},tooltipProps:{id:l}}}var GS=e=>{const t={top:{originY:1},bottom:{originY:0},left:{originX:1},right:{originX:0},"top-start":{originX:0,originY:1},"top-end":{originX:1,originY:1},"bottom-start":{originX:0,originY:0},"bottom-end":{originX:1,originY:0},"right-start":{originX:0,originY:0},"right-end":{originX:0,originY:1},"left-start":{originX:1,originY:0},"left-end":{originX:1,originY:1}};return(t==null?void 0:t[e])||{}},QS=e=>({top:"top",bottom:"bottom",left:"left",right:"right","top-start":"top start","top-end":"top end","bottom-start":"bottom start","bottom-end":"bottom end","left-start":"left top","left-end":"left bottom","right-start":"right top","right-end":"right bottom"})[e],mp=(e,t)=>{if(t.includes("-")){const[,n]=t.split("-");return`${e}-${n}`}return e},Wc=globalThis!=null&&globalThis.document?p.useLayoutEffect:p.useEffect;function YS(e){var t,n;const r=on(),[o,l]=ln(e,Ef.variantKeys),{ref:a,as:i,isOpen:s,content:u,children:c,defaultOpen:d,onOpenChange:f,isDisabled:y,trigger:g,shouldFlip:b=!0,containerPadding:S=12,placement:h="top",delay:m=0,closeDelay:v=500,showArrow:x=!1,offset:C=7,crossOffset:T=0,isDismissable:L,shouldCloseOnBlur:M=!0,portalContainer:D,isKeyboardDismissDisabled:I=!1,updatePositionDeps:E=[],shouldCloseOnInteractOutside:k,className:z,onClose:$,motionProps:A,classNames:N,...O}=o,P=i||"div",R=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,_=HS({delay:m,closeDelay:v,isDisabled:y,defaultOpen:d,isOpen:s,onOpenChange:U=>{f==null||f(U),U||$==null||$()}}),V=p.useRef(null),H=p.useRef(null),te=p.useId(),K=_.isOpen&&!y;p.useImperativeHandle(a,()=>Ox(H));const{triggerProps:re,tooltipProps:G}=KS({isDisabled:y,trigger:g},_,V),{tooltipProps:we}=US({isOpen:K,...Z(o,G)},_),{overlayProps:ut,placement:xe,updatePosition:Me}=o2({isOpen:K,targetRef:V,placement:QS(h),overlayRef:H,offset:x?C+3:C,crossOffset:T,shouldFlip:b,containerPadding:S});Wc(()=>{E.length&&Me()},E);const{overlayProps:nt}=i2({isOpen:K,onClose:_.close,isDismissable:L,shouldCloseOnBlur:M,isKeyboardDismissDisabled:I,shouldCloseOnInteractOutside:k},H),Ae=p.useMemo(()=>{var U,Vt,an;return Ef({...l,disableAnimation:R,radius:(U=e==null?void 0:e.radius)!=null?U:"md",size:(Vt=e==null?void 0:e.size)!=null?Vt:"md",shadow:(an=e==null?void 0:e.shadow)!=null?an:"sm"})},[Wt(l),R,e==null?void 0:e.radius,e==null?void 0:e.size,e==null?void 0:e.shadow]),Ke=p.useCallback((U={},Vt=null)=>({...Z(re,U),ref:Ov(Vt,V),"aria-describedby":K?te:void 0}),[re,K,te,_]),St=p.useCallback(()=>({ref:H,"data-slot":"base","data-open":B(K),"data-arrow":B(x),"data-disabled":B(y),"data-placement":mp(xe||"top",h),...Z(we,nt,O),style:Z(ut.style,O.style,o.style),className:Ae.base({class:N==null?void 0:N.base}),id:te}),[Ae,K,x,y,xe,h,we,nt,O,ut,o,te]),ct=p.useCallback(()=>({"data-slot":"content","data-open":B(K),"data-arrow":B(x),"data-disabled":B(y),"data-placement":mp(xe||"top",h),className:Ae.content({class:ee(N==null?void 0:N.content,z)})}),[Ae,K,x,y,xe,h,N]);return{Component:P,content:u,children:c,isOpen:K,triggerRef:V,showArrow:x,portalContainer:D,placement:h,disableAnimation:R,isDisabled:y,motionProps:A,getTooltipContentProps:ct,getTriggerProps:Ke,getTooltipProps:St}}var hp={easeIn:[.4,0,1,1],easeOut:[0,0,.2,1]},XS={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}}},ZS=()=>pi(()=>import("./index-BydXBEuJ.js"),__vite__mapDeps([])).then(e=>e.default),hg=Ue((e,t)=>{var n;const{Component:r,children:o,content:l,isOpen:a,portalContainer:i,placement:s,disableAnimation:u,motionProps:c,getTriggerProps:d,getTooltipProps:f,getTooltipContentProps:y}=YS({...e,ref:t});let g;try{if(p.Children.count(o)!==1)throw new Error;if(!p.isValidElement(o))g=w.jsx("p",{...d(),children:o});else{const C=o,T=(n=C.props.ref)!=null?n:C.ref;g=p.cloneElement(C,d(C.props,T))}}catch{g=w.jsx("span",{}),bw("Tooltip must have only one child node. Please, check your code.")}const{ref:b,id:S,style:h,...m}=f(),v=w.jsx("div",{ref:b,id:S,style:h,children:w.jsx(di.div,{animate:"enter",exit:"exit",initial:"exit",variants:XS.scaleSpring,...Z(c,m),style:{...GS(s)},children:w.jsx(r,{...y(),children:l})},`${S}-tooltip-inner`)},`${S}-tooltip-content`);return w.jsxs(w.Fragment,{children:[g,u?a&&w.jsx(ep,{portalContainer:i,children:w.jsx("div",{ref:b,id:S,style:h,...m,children:w.jsx(r,{...y(),children:l})})}):w.jsx(fi,{features:ZS,children:w.jsx(Ac,{children:a&&w.jsx(ep,{portalContainer:i,children:v})})})]})});hg.displayName="HeroUI.Tooltip";var JS=hg,[pC,qS]=Rc({name:"ButtonGroupContext",strict:!1});function eE(e,t){let{elementType:n="button",isDisabled:r,onPress:o,onPressStart:l,onPressEnd:a,onPressChange:i,preventFocusOnPress:s,allowFocusWhenDisabled:u,onClick:c,href:d,target:f,rel:y,type:g="button",allowTextSelectionOnPress:b}=e,S;n==="button"?S={type:g,disabled:r}:S={role:"button",href:n==="a"&&!r?d:void 0,target:n==="a"?f:void 0,type:n==="input"?g:void 0,disabled:n==="input"?r:void 0,"aria-disabled":!r||n==="input"?void 0:r,rel:n==="a"?y:void 0};let{pressProps:h,isPressed:m}=Kr({onClick:c,onPressStart:l,onPressEnd:a,onPressChange:i,onPress:o,isDisabled:r,preventFocusOnPress:s,allowTextSelectionOnPress:b,ref:t}),{focusableProps:v}=eo(e,t);u&&(v.tabIndex=r?-1:v.tabIndex);let x=Z(v,h,qr(e,{labelable:!0}));return{isPressed:m,buttonProps:Z(S,x,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}var tE=()=>pi(()=>import("./index-BydXBEuJ.js"),__vite__mapDeps([])).then(e=>e.default),vg=e=>{const{ripples:t=[],motionProps:n,color:r="currentColor",style:o,onClear:l}=e;return w.jsx(w.Fragment,{children:t.map(a=>{const i=yw(.01*a.size,.2,a.size>100?.75:.5);return w.jsx(fi,{features:tE,children:w.jsx(Ac,{mode:"popLayout",children:w.jsx(di.span,{animate:{transform:"scale(2)",opacity:0},className:"heroui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:r,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:a.y,left:a.x,width:`${a.size}px`,height:`${a.size}px`,...o},transition:{duration:i},onAnimationComplete:()=>{l(a.key)},...n})})},a.key)})})};vg.displayName="HeroUI.Ripple";var nE=vg;function rE(e={}){const[t,n]=p.useState([]),r=p.useCallback(l=>{const a=l.target,i=Math.max(a.clientWidth,a.clientHeight);n(s=>[...s,{key:gw(s.length.toString()),size:i,x:l.x-i/2,y:l.y-i/2}])},[]),o=p.useCallback(l=>{n(a=>a.filter(i=>i.key!==l))},[]);return{ripples:t,onClear:o,onPress:r,...e}}function oE(e){var t,n,r,o,l,a,i,s,u;const c=qS(),d=on(),f=!!c,{ref:y,as:g,children:b,startContent:S,endContent:h,autoFocus:m,className:v,spinner:x,isLoading:C=!1,disableRipple:T=!1,fullWidth:L=(t=c==null?void 0:c.fullWidth)!=null?t:!1,radius:M=c==null?void 0:c.radius,size:D=(n=c==null?void 0:c.size)!=null?n:"md",color:I=(r=c==null?void 0:c.color)!=null?r:"default",variant:E=(o=c==null?void 0:c.variant)!=null?o:"solid",disableAnimation:k=(a=(l=c==null?void 0:c.disableAnimation)!=null?l:d==null?void 0:d.disableAnimation)!=null?a:!1,isDisabled:z=(i=c==null?void 0:c.isDisabled)!=null?i:!1,isIconOnly:$=(s=c==null?void 0:c.isIconOnly)!=null?s:!1,spinnerPlacement:A="start",onPress:N,onClick:O,...P}=e,R=g||"button",_=typeof R=="string",V=Ze(y),H=(u=T||(d==null?void 0:d.disableRipple))!=null?u:k,{isFocusVisible:te,isFocused:K,focusProps:re}=lr({autoFocus:m}),G=z||C,we=p.useMemo(()=>ou({size:D,color:I,variant:E,radius:M,fullWidth:L,isDisabled:G,isInGroup:f,disableAnimation:k,isIconOnly:$,className:v}),[D,I,E,M,L,G,f,$,k,v]),{onPress:ut,onClear:xe,ripples:Me}=rE(),nt=p.useCallback(dt=>{H||G||k||V.current&&ut(dt)},[H,G,k,V,ut]),{buttonProps:Ae,isPressed:Ke}=eE({elementType:g,isDisabled:G,onPress:At(N,nt),onClick:O,...P},V),{isHovered:St,hoverProps:ct}=or({isDisabled:G}),U=p.useCallback((dt={})=>({"data-disabled":B(G),"data-focus":B(K),"data-pressed":B(Ke),"data-focus-visible":B(te),"data-hover":B(St),"data-loading":B(C),...Z(Ae,re,ct,za(P,{enabled:_}),za(dt)),className:we}),[C,G,K,Ke,_,te,St,Ae,re,ct,P,we]),Vt=dt=>p.isValidElement(dt)?p.cloneElement(dt,{"aria-hidden":!0,focusable:!1}):null,an=Vt(S),mi=Vt(h),hi=p.useMemo(()=>({sm:"sm",md:"sm",lg:"md"})[D],[D]),gl=p.useCallback(()=>({ripples:Me,onClear:xe}),[Me,xe]);return{Component:R,children:b,domRef:V,spinner:x,styles:we,startContent:an,endContent:mi,isLoading:C,spinnerPlacement:A,spinnerSize:hi,disableRipple:H,getButtonProps:U,getRippleProps:gl,isIconOnly:$}}function lE(e){var t,n;const[r,o]=ln(e,xf.variantKeys),l=on(),a=(n=(t=e==null?void 0:e.variant)!=null?t:l==null?void 0:l.spinnerVariant)!=null?n:"default",{children:i,className:s,classNames:u,label:c,...d}=r,f=p.useMemo(()=>xf({...o}),[Wt(o)]),y=ee(u==null?void 0:u.base,s),g=c||i,b=p.useMemo(()=>g&&typeof g=="string"?g:d["aria-label"]?"":"Loading",[i,g,d["aria-label"]]),S=p.useCallback(()=>({"aria-label":b,className:f.base({class:y}),...d}),[b,f,y,d]);return{label:g,slots:f,classNames:u,variant:a,getSpinnerProps:S}}var gg=Ue((e,t)=>{const{slots:n,classNames:r,label:o,variant:l,getSpinnerProps:a}=lE({...e});return l==="wave"||l==="dots"?w.jsxs("div",{ref:t,...a(),children:[w.jsx("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[...new Array(3)].map((i,s)=>w.jsx("i",{className:n.dots({class:r==null?void 0:r.dots}),style:{"--dot-index":s}},`dot-${s}`))}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):l==="simple"?w.jsxs("div",{ref:t,...a(),children:[w.jsxs("svg",{className:n.wrapper({class:r==null?void 0:r.wrapper}),fill:"none",viewBox:"0 0 24 24",children:[w.jsx("circle",{className:n.circle1({class:r==null?void 0:r.circle1}),cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),w.jsx("path",{className:n.circle2({class:r==null?void 0:r.circle2}),d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",fill:"currentColor"})]}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):l==="spinner"?w.jsxs("div",{ref:t,...a(),children:[w.jsx("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[...new Array(12)].map((i,s)=>w.jsx("i",{className:n.spinnerBars({class:r==null?void 0:r.spinnerBars}),style:{"--bar-index":s}},`star-${s}`))}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):w.jsxs("div",{ref:t,...a(),children:[w.jsxs("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[w.jsx("i",{className:n.circle1({class:r==null?void 0:r.circle1})}),w.jsx("i",{className:n.circle2({class:r==null?void 0:r.circle2})})]}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]})});gg.displayName="HeroUI.Spinner";var aE=gg,yg=Ue((e,t)=>{const{Component:n,domRef:r,children:o,spinnerSize:l,spinner:a=w.jsx(aE,{color:"current",size:l}),spinnerPlacement:i,startContent:s,endContent:u,isLoading:c,disableRipple:d,getButtonProps:f,getRippleProps:y,isIconOnly:g}=oE({...e,ref:t});return w.jsxs(n,{ref:r,...f(),children:[s,c&&i==="start"&&a,c&&g?null:o,c&&i==="end"&&a,u,!d&&w.jsx(nE,{...y()})]})});yg.displayName="HeroUI.Button";var bg=yg,wg=Ue((e,t)=>{const{Component:n,domRef:r,preRef:o,children:l,slots:a,classNames:i,copied:s,copyIcon:u=w.jsx(IS,{}),checkIcon:c=w.jsx(LS,{}),symbolBefore:d,disableCopy:f,disableTooltip:y,hideSymbol:g,hideCopyButton:b,tooltipProps:S,isMultiLine:h,onCopy:m,getSnippetProps:v,getCopyButtonProps:x}=FS({...e,ref:t}),C=p.useCallback(({children:M})=>w.jsx(JS,{...S,isDisabled:s||S.isDisabled,children:M}),[Wt(S)]),T=p.useMemo(()=>{if(b)return null;const M=c&&p.cloneElement(c,{className:a.checkIcon()}),D=u&&p.cloneElement(u,{className:a.copyIcon()}),I=w.jsxs(bg,{...x(),children:[M,D]});return y?I:w.jsx(C,{children:I})},[a,i==null?void 0:i.copyButton,s,c,u,m,C,f,y,b]),L=p.useMemo(()=>h&&l&&Array.isArray(l)?w.jsx("div",{className:a.content({class:i==null?void 0:i.content}),children:l.map((M,D)=>w.jsxs("pre",{className:a.pre({class:i==null?void 0:i.pre}),children:[!g&&w.jsx("span",{className:a.symbol({class:i==null?void 0:i.symbol}),children:d}),M]},`${D}-${M}`))}):w.jsxs("pre",{ref:o,className:a.pre({class:i==null?void 0:i.pre}),children:[!g&&w.jsx("span",{className:a.symbol({class:i==null?void 0:i.symbol}),children:d}),l]}),[l,g,h,d,i==null?void 0:i.pre,a]);return w.jsxs(n,{ref:r,...v(),children:[L,T]})});wg.displayName="HeroUI.Snippet";var iE=wg;function sE(e){const[t,n]=ln(e,Pf.variantKeys),{as:r,children:o,className:l,...a}=t,i=r||"code",s=p.useMemo(()=>Pf({...n,className:l}),[Wt(n),l]);return{Component:i,children:o,getCodeProps:()=>({className:s,...a})}}var xg=Ue((e,t)=>{const{Component:n,children:r,getCodeProps:o}=sE({...e});return w.jsx(n,{ref:t,...o(),children:r})});xg.displayName="HeroUI.Code";var uE=xg;const jt={navItems:[{label:"Home",href:"/"},{label:"Docs",href:"/docs"},{label:"Pricing",href:"/pricing"},{label:"Blog",href:"/blog"},{label:"About",href:"/about"}],navMenuItems:[{label:"Profile",href:"/profile"},{label:"Dashboard",href:"/dashboard"},{label:"Projects",href:"/projects"},{label:"Team",href:"/team"},{label:"Calendar",href:"/calendar"},{label:"Settings",href:"/settings"},{label:"Help & Feedback",href:"/help-feedback"},{label:"Logout",href:"/logout"}],links:{github:"https://github.com/frontio-ai/heroui",twitter:"https://twitter.com/hero_ui",docs:"https://heroui.com",discord:"https://discord.gg/9b6yyZKmH4",sponsor:"https://patreon.com/jrgarciadev"}},Xn=xc({base:"tracking-tight inline font-semibold",variants:{color:{violet:"from-[#FF1CF7] to-[#b249f8]",yellow:"from-[#FF705B] to-[#FFB457]",blue:"from-[#5EA2EF] to-[#0072F5]",cyan:"from-[#00b7fa] to-[#01cfea]",green:"from-[#6FEE8D] to-[#17c964]",pink:"from-[#FF72E1] to-[#F54C7A]",foreground:"dark:from-[#FFFFFF] dark:to-[#4B4B4B]"},size:{sm:"text-3xl lg:text-4xl",md:"text-[2.3rem] lg:text-5xl leading-9",lg:"text-4xl lg:text-6xl"},fullWidth:{true:"w-full block"}},defaultVariants:{size:"md"},compoundVariants:[{color:["violet","yellow","blue","cyan","green","pink","foreground"],class:"bg-clip-text text-transparent bg-gradient-to-b"}]}),cE=xc({base:"w-full md:w-1/2 my-2 text-lg lg:text-xl text-default-600 block max-w-full",variants:{fullWidth:{true:"!w-full"}},defaultVariants:{fullWidth:!0}}),dE=({size:e=36,height:t,...n})=>w.jsx("svg",{fill:"none",height:e||t,viewBox:"0 0 32 32",width:e||t,...n,children:w.jsx("path",{clipRule:"evenodd",d:"M17.6482 10.1305L15.8785 7.02583L7.02979 22.5499H10.5278L17.6482 10.1305ZM19.8798 14.0457L18.11 17.1983L19.394 19.4511H16.8453L15.1056 22.5499H24.7272L19.8798 14.0457Z",fill:"currentColor",fillRule:"evenodd"})}),fE=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M14.82 4.26a10.14 10.14 0 0 0-.53 1.1 14.66 14.66 0 0 0-4.58 0 10.14 10.14 0 0 0-.53-1.1 16 16 0 0 0-4.13 1.3 17.33 17.33 0 0 0-3 11.59 16.6 16.6 0 0 0 5.07 2.59A12.89 12.89 0 0 0 8.23 18a9.65 9.65 0 0 1-1.71-.83 3.39 3.39 0 0 0 .42-.33 11.66 11.66 0 0 0 10.12 0q.21.18.42.33a10.84 10.84 0 0 1-1.71.84 12.41 12.41 0 0 0 1.08 1.78 16.44 16.44 0 0 0 5.06-2.59 17.22 17.22 0 0 0-3-11.59 16.09 16.09 0 0 0-4.09-1.35zM8.68 14.81a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.93 1.93 0 0 1 1.8 2 1.93 1.93 0 0 1-1.8 2zm6.64 0a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.92 1.92 0 0 1 1.8 2 1.92 1.92 0 0 1-1.8 2z",fill:"currentColor"})}),pE=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z",fill:"currentColor"})}),xu=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{clipRule:"evenodd",d:"M12.026 2c-5.509 0-9.974 4.465-9.974 9.974 0 4.406 2.857 8.145 6.821 9.465.499.09.679-.217.679-.481 0-.237-.008-.865-.011-1.696-2.775.602-3.361-1.338-3.361-1.338-.452-1.152-1.107-1.459-1.107-1.459-.905-.619.069-.605.069-.605 1.002.07 1.527 1.028 1.527 1.028.89 1.524 2.336 1.084 2.902.829.091-.645.351-1.085.635-1.334-2.214-.251-4.542-1.107-4.542-4.93 0-1.087.389-1.979 1.024-2.675-.101-.253-.446-1.268.099-2.64 0 0 .837-.269 2.742 1.021a9.582 9.582 0 0 1 2.496-.336 9.554 9.554 0 0 1 2.496.336c1.906-1.291 2.742-1.021 2.742-1.021.545 1.372.203 2.387.099 2.64.64.696 1.024 1.587 1.024 2.675 0 3.833-2.33 4.675-4.552 4.922.355.308.675.916.675 1.846 0 1.334-.012 2.41-.012 2.737 0 .267.178.577.687.479C19.146 20.115 22 16.379 22 11.974 22 6.465 17.535 2 12.026 2z",fill:"currentColor",fillRule:"evenodd"})}),mE=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z",fill:"currentColor"})}),hE=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsxs("g",{fill:"currentColor",children:[w.jsx("path",{d:"M19 12a7 7 0 11-7-7 7 7 0 017 7z"}),w.jsx("path",{d:"M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z"})]})}),vE=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M12.62 20.81c-.34.12-.9.12-1.24 0C8.48 19.82 2 15.69 2 8.69 2 5.6 4.49 3.1 7.56 3.1c1.82 0 3.43.88 4.44 2.24a5.53 5.53 0 0 1 4.44-2.24C19.51 3.1 22 5.6 22 8.69c0 7-6.48 11.13-9.38 12.12Z",fill:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5})}),gE=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"}),w.jsx("path",{d:"M22 22L20 20",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"})]});function yE(e){const[t,n]=ln(e,$f.variantKeys),{as:r,children:o,className:l,keys:a,title:i,classNames:s,...u}=t,c=r||"kbd",d=p.useMemo(()=>$f({...n}),[Wt(n)]),f=ee(s==null?void 0:s.base,l),y=typeof a=="string"?[a]:Array.isArray(a)?a:[];return{Component:c,slots:d,classNames:s,title:i,children:o,keysToRender:y,getKbdProps:(b={})=>({...u,...b,className:ee(d.base({class:ee(f,b.className)}))})}}var bE={command:"⌘",shift:"⇧",ctrl:"⌃",option:"⌥",enter:"↵",delete:"⌫",escape:"⎋",tab:"⇥",capslock:"⇪",up:"↑",right:"→",down:"↓",left:"←",pageup:"⇞",pagedown:"⇟",home:"↖",end:"↘",help:"?",space:"␣",fn:"Fn",win:"⌘",alt:"⌥"},wE={command:"Command",shift:"Shift",ctrl:"Control",option:"Option",enter:"Enter",delete:"Delete",escape:"Escape",tab:"Tab",capslock:"Caps Lock",up:"Up",right:"Right",down:"Down",left:"Left",pageup:"Page Up",pagedown:"Page Down",home:"Home",end:"End",help:"Help",space:"Space",fn:"Fn",win:"Win",alt:"Alt"},Sg=Ue((e,t)=>{const{Component:n,children:r,slots:o,classNames:l,keysToRender:a,getKbdProps:i}=yE({...e}),s=p.useMemo(()=>a.map(u=>w.jsx("abbr",{className:o.abbr({class:l==null?void 0:l.abbr}),title:wE[u],children:bE[u]},u)),[a]);return w.jsxs(n,{ref:t,...i(),children:[s,r&&w.jsx("span",{className:o.content({class:l==null?void 0:l.content}),children:r})]})});Sg.displayName="HeroUI.Kbd";var xE=Sg;function SE(e){let{id:t,label:n,"aria-labelledby":r,"aria-label":o,labelElementType:l="label"}=e;t=ol(t);let a=ol(),i={};n&&(r=r?`${a} ${r}`:a,i={id:a,htmlFor:l==="label"?t:void 0});let s=Xw({id:t,"aria-label":o,"aria-labelledby":r});return{labelProps:i,fieldProps:s}}function EE(e){let{description:t,errorMessage:n,isInvalid:r,validationState:o}=e,{labelProps:l,fieldProps:a}=SE(e),i=Nf([!!t,!!n,r,o]),s=Nf([!!t,!!n,r,o]);return a=Z(a,{"aria-describedby":[i,s,e["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:l,fieldProps:a,descriptionProps:{id:i},errorMessageProps:{id:s}}}function CE(e,t,n){let{validationBehavior:r,focus:o}=e;be(()=>{if(r==="native"&&(n!=null&&n.current)&&!n.current.disabled){let s=t.realtimeValidation.isInvalid?t.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";n.current.setCustomValidity(s),n.current.hasAttribute("title")||(n.current.title=""),t.realtimeValidation.isInvalid||t.updateValidation(kE(n.current))}});let l=Oe(()=>{t.resetValidation()}),a=Oe(s=>{var u;t.displayValidation.isInvalid||t.commitValidation();let c=n==null||(u=n.current)===null||u===void 0?void 0:u.form;if(!s.defaultPrevented&&n&&c&&PE(c)===n.current){var d;o?o():(d=n.current)===null||d===void 0||d.focus(),yx("keyboard")}s.preventDefault()}),i=Oe(()=>{t.commitValidation()});p.useEffect(()=>{let s=n==null?void 0:n.current;if(!s)return;let u=s.form;return s.addEventListener("invalid",a),s.addEventListener("change",i),u==null||u.addEventListener("reset",l),()=>{s.removeEventListener("invalid",a),s.removeEventListener("change",i),u==null||u.removeEventListener("reset",l)}},[n,a,i,l,r])}function $E(e){let t=e.validity;return{badInput:t.badInput,customError:t.customError,patternMismatch:t.patternMismatch,rangeOverflow:t.rangeOverflow,rangeUnderflow:t.rangeUnderflow,stepMismatch:t.stepMismatch,tooLong:t.tooLong,tooShort:t.tooShort,typeMismatch:t.typeMismatch,valueMissing:t.valueMissing,valid:t.valid}}function kE(e){return{isInvalid:!e.validity.valid,validationDetails:$E(e),validationErrors:e.validationMessage?[e.validationMessage]:[]}}function PE(e){for(let t=0;t<e.elements.length;t++){let n=e.elements[t];if(!n.validity.valid)return n}return null}const Eg={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},Cg={...Eg,customError:!0,valid:!1},bo={isInvalid:!1,validationDetails:Eg,validationErrors:[]},$g=p.createContext({}),vp="__formValidationState"+Date.now();function TE(e){if(e[vp]){let{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}=e[vp];return{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}}return NE(e)}function NE(e){let{isInvalid:t,validationState:n,name:r,value:o,builtinValidation:l,validate:a,validationBehavior:i="aria"}=e;n&&(t||(t=n==="invalid"));let s=t!==void 0?{isInvalid:t,validationErrors:[],validationDetails:Cg}:null,u=p.useMemo(()=>{if(!a||o==null)return null;let I=_E(a,o);return gp(I)},[a,o]);l!=null&&l.validationDetails.valid&&(l=void 0);let c=p.useContext($g),d=p.useMemo(()=>r?Array.isArray(r)?r.flatMap(I=>Su(c[I])):Su(c[r]):[],[c,r]),[f,y]=p.useState(c),[g,b]=p.useState(!1);c!==f&&(y(c),b(!1));let S=p.useMemo(()=>gp(g?[]:d),[g,d]),h=p.useRef(bo),[m,v]=p.useState(bo),x=p.useRef(bo),C=()=>{if(!T)return;L(!1);let I=u||l||h.current;ns(I,x.current)||(x.current=I,v(I))},[T,L]=p.useState(!1);return p.useEffect(C),{realtimeValidation:s||S||u||l||bo,displayValidation:i==="native"?s||S||m:s||S||u||l||m,updateValidation(I){i==="aria"&&!ns(m,I)?v(I):h.current=I},resetValidation(){let I=bo;ns(I,x.current)||(x.current=I,v(I)),i==="native"&&L(!1),b(!0)},commitValidation(){i==="native"&&L(!0),b(!0)}}}function Su(e){return e?Array.isArray(e)?e:[e]:[]}function _E(e,t){if(typeof e=="function"){let n=e(t);if(n&&typeof n!="boolean")return Su(n)}return[]}function gp(e){return e.length?{isInvalid:!0,validationErrors:e,validationDetails:Cg}:null}function ns(e,t){return e===t?!0:!!e&&!!t&&e.isInvalid===t.isInvalid&&e.validationErrors.length===t.validationErrors.length&&e.validationErrors.every((n,r)=>n===t.validationErrors[r])&&Object.entries(e.validationDetails).every(([n,r])=>t.validationDetails[n]===r)}function ME(e,t){let{inputElementType:n="input",isDisabled:r=!1,isRequired:o=!1,isReadOnly:l=!1,type:a="text",validationBehavior:i="aria"}=e,[s,u]=ml(e.value,e.defaultValue||"",e.onChange),{focusableProps:c}=eo(e,t),d=TE({...e,value:s}),{isInvalid:f,validationErrors:y,validationDetails:g}=d.displayValidation,{labelProps:b,fieldProps:S,descriptionProps:h,errorMessageProps:m}=EE({...e,isInvalid:f,errorMessage:e.errorMessage||y}),v=qr(e,{labelable:!0});const x={type:a,pattern:e.pattern};return Sv(t,s,u),CE(e,d,t),p.useEffect(()=>{if(t.current instanceof vt(t.current).HTMLTextAreaElement){let C=t.current;Object.defineProperty(C,"defaultValue",{get:()=>C.value,set:()=>{},configurable:!0})}},[t]),{labelProps:b,inputProps:Z(v,n==="input"?x:void 0,{disabled:r,readOnly:l,required:o&&i==="native","aria-required":o&&i==="aria"||void 0,"aria-invalid":f||void 0,"aria-errormessage":e["aria-errormessage"],"aria-activedescendant":e["aria-activedescendant"],"aria-autocomplete":e["aria-autocomplete"],"aria-haspopup":e["aria-haspopup"],"aria-controls":e["aria-controls"],value:s,onChange:C=>u(C.target.value),autoComplete:e.autoComplete,autoCapitalize:e.autoCapitalize,maxLength:e.maxLength,minLength:e.minLength,name:e.name,placeholder:e.placeholder,inputMode:e.inputMode,autoCorrect:e.autoCorrect,spellCheck:e.spellCheck,[parseInt(q.version,10)>=17?"enterKeyHint":"enterkeyhint"]:e.enterKeyHint,onCopy:e.onCopy,onCut:e.onCut,onPaste:e.onPaste,onCompositionEnd:e.onCompositionEnd,onCompositionStart:e.onCompositionStart,onCompositionUpdate:e.onCompositionUpdate,onSelect:e.onSelect,onBeforeInput:e.onBeforeInput,onInput:e.onInput,...c,...S}),descriptionProps:h,errorMessageProps:m,isInvalid:f,validationErrors:y,validationDetails:g}}var yp=Symbol("default");function kg(e,t){let n=p.useContext(e);if(t===null)return null;if(n&&typeof n=="object"&&"slots"in n&&n.slots){let r=new Intl.ListFormat().format(Object.keys(n.slots).map(l=>`"${l}"`));if(!t&&!n.slots[yp])throw new Error(`A slot prop is required. Valid slot names are ${r}.`);let o=t||yp;if(!n.slots[o])throw new Error(`Invalid slot "${t}". Valid slot names are ${r}.`);return n.slots[o]}return n}function LE(e,t,n){let r=kg(n,e.slot)||{},{ref:o,...l}=r,a=Zw(p.useMemo(()=>zw(t,o),[t,o])),i=Z(l,e);return"style"in l&&l.style&&"style"in e&&e.style&&(typeof l.style=="function"||typeof e.style=="function"?i.style=s=>{let u=typeof l.style=="function"?l.style(s):l.style,c={...s.defaultStyle,...u},d=typeof e.style=="function"?e.style({...s,defaultStyle:c}):e.style;return{...c,...d}}:i.style={...l.style,...e.style}),[i,a]}var Eu=p.createContext(null);p.forwardRef(function(t,n){[t,n]=LE(t,n,Eu);let{validationErrors:r,validationBehavior:o="native",children:l,className:a,...i}=t;const s=p.useMemo(()=>dw({className:a}),[a]);return w.jsx("form",{noValidate:o!=="native",...i,ref:n,className:s,children:w.jsx(Eu.Provider,{value:{...t,validationBehavior:o},children:w.jsx($g.Provider,{value:r??{},children:l})})})});function IE(e){var t,n,r,o,l,a,i;const s=on(),{validationBehavior:u}=kg(Eu)||{},[c,d]=ln(e,Cf.variantKeys),{ref:f,as:y,type:g,label:b,baseRef:S,wrapperRef:h,description:m,className:v,classNames:x,autoFocus:C,startContent:T,endContent:L,onClear:M,onChange:D,validationState:I,validationBehavior:E=(t=u??(s==null?void 0:s.validationBehavior))!=null?t:"native",innerWrapperRef:k,onValueChange:z=()=>{},...$}=c,A=p.useCallback(Y=>{z(Y??"")},[z]),[N,O]=p.useState(!1),P=y||"div",R=(r=(n=e.disableAnimation)!=null?n:s==null?void 0:s.disableAnimation)!=null?r:!1,_=Ze(f),V=Ze(S),H=Ze(h),te=Ze(k),[K,re]=ml(c.value,(o=c.defaultValue)!=null?o:"",A),G=g==="file",we=((i=(a=(l=_==null?void 0:_.current)==null?void 0:l.files)==null?void 0:a.length)!=null?i:0)>0,ut=["date","time","month","week","range"].includes(g),xe=!mw(K)||ut||we,Me=xe||N,nt=g==="hidden",Ae=e.isMultiline,Ke=ee(x==null?void 0:x.base,v,xe?"is-filled":""),St=p.useCallback(()=>{var Y;G?_.current.value="":re(""),M==null||M(),(Y=_.current)==null||Y.focus()},[re,M,G]);Wc(()=>{_.current&&re(_.current.value)},[_.current]);const{labelProps:ct,inputProps:U,isInvalid:Vt,validationErrors:an,validationDetails:mi,descriptionProps:hi,errorMessageProps:gl}=ME({...e,validationBehavior:E,autoCapitalize:e.autoCapitalize,value:K,"aria-label":vw(e["aria-label"],e.label,e.placeholder),inputElementType:Ae?"textarea":"input",onChange:re},_);G&&(delete U.value,delete U.onChange);const{isFocusVisible:dt,isFocused:yl,focusProps:Vc}=lr({autoFocus:C,isTextInput:!0}),{isHovered:bl,hoverProps:Og}=or({isDisabled:!!(e!=null&&e.isDisabled)}),{isHovered:no,hoverProps:Fg}=or({isDisabled:!!(e!=null&&e.isDisabled)}),{focusProps:Bc,isFocusVisible:Hc}=lr(),{focusWithinProps:Uc}=ii({onFocusWithinChange:O}),{pressProps:Kc}=Kr({isDisabled:!!(e!=null&&e.isDisabled)||!!(e!=null&&e.isReadOnly),onPress:St}),dr=I==="invalid"||Vt,Mt=_S({labelPlacement:e.labelPlacement,label:b}),vi=typeof c.errorMessage=="function"?c.errorMessage({isInvalid:dr,validationErrors:an,validationDetails:mi}):c.errorMessage||(an==null?void 0:an.join(" ")),ro=!!M||e.isClearable,Gc=!!b||!!m||!!vi,zn=!!c.placeholder,Qc=!!b,gi=!!m||!!vi,Yc=Mt==="outside"||Mt==="outside-left",Dg=Mt==="inside",wl=_.current?(!_.current.value||_.current.value===""||!K||K==="")&&zn:!1,Ag=Mt==="outside-left",Bt=!!T,Wg=Yc?Mt==="outside-left"||zn||Mt==="outside"&&Bt:!1,Vg=Mt==="outside"&&!zn&&!Bt,Se=p.useMemo(()=>Cf({...d,isInvalid:dr,labelPlacement:Mt,isClearable:ro,disableAnimation:R}),[Wt(d),dr,Mt,ro,Bt,R]),Bg=p.useCallback((Y={})=>({ref:V,className:Se.base({class:Ke}),"data-slot":"base","data-filled":B(xe||zn||Bt||wl||G),"data-filled-within":B(Me||zn||Bt||wl||G),"data-focus-within":B(N),"data-focus-visible":B(dt),"data-readonly":B(e.isReadOnly),"data-focus":B(yl),"data-hover":B(bl||no),"data-required":B(e.isRequired),"data-invalid":B(dr),"data-disabled":B(e.isDisabled),"data-has-elements":B(Gc),"data-has-helper":B(gi),"data-has-label":B(Qc),"data-has-value":B(!wl),"data-hidden":B(nt),...Uc,...Y}),[Se,Ke,xe,yl,bl,no,dr,gi,Qc,Gc,wl,Bt,N,dt,Me,zn,Uc,nt,e.isReadOnly,e.isRequired,e.isDisabled]),Hg=p.useCallback((Y={})=>({"data-slot":"label",className:Se.label({class:x==null?void 0:x.label}),...Z(ct,Fg,Y)}),[Se,no,ct,x==null?void 0:x.label]),Xc=p.useCallback(Y=>{Y.key==="Escape"&&K&&(ro||M)&&!e.isReadOnly&&(re(""),M==null||M())},[K,re,M,ro,e.isReadOnly]),Ug=p.useCallback((Y={})=>({"data-slot":"input","data-filled":B(xe),"data-filled-within":B(Me),"data-has-start-content":B(Bt),"data-has-end-content":B(!!L),"data-type":g,className:Se.input({class:ee(x==null?void 0:x.input,xe?"is-filled":"",Ae?"pe-0":"",g==="password"?"[&::-ms-reveal]:hidden":"")}),...Z(Vc,U,za($,{enabled:!0,labelable:!0,omitEventNames:new Set(Object.keys(U))}),Y),"aria-readonly":B(e.isReadOnly),onChange:At(U.onChange,D),onKeyDown:At(U.onKeyDown,Y.onKeyDown,Xc),ref:_}),[Se,K,Vc,U,$,xe,Me,Bt,L,x==null?void 0:x.input,e.isReadOnly,e.isRequired,D,Xc]),Kg=p.useCallback((Y={})=>({ref:H,"data-slot":"input-wrapper","data-hover":B(bl||no),"data-focus-visible":B(dt),"data-focus":B(yl),className:Se.inputWrapper({class:ee(x==null?void 0:x.inputWrapper,xe?"is-filled":"")}),...Z(Y,Og),onClick:oo=>{_.current&&oo.currentTarget===oo.target&&_.current.focus()},style:{cursor:"text",...Y.style}}),[Se,bl,no,dt,yl,K,x==null?void 0:x.inputWrapper]),Gg=p.useCallback((Y={})=>({...Y,ref:te,"data-slot":"inner-wrapper",onClick:oo=>{_.current&&oo.currentTarget===oo.target&&_.current.focus()},className:Se.innerWrapper({class:ee(x==null?void 0:x.innerWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.innerWrapper]),Qg=p.useCallback((Y={})=>({...Y,"data-slot":"main-wrapper",className:Se.mainWrapper({class:ee(x==null?void 0:x.mainWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.mainWrapper]),Yg=p.useCallback((Y={})=>({...Y,"data-slot":"helper-wrapper",className:Se.helperWrapper({class:ee(x==null?void 0:x.helperWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.helperWrapper]),Xg=p.useCallback((Y={})=>({...Y,...hi,"data-slot":"description",className:Se.description({class:ee(x==null?void 0:x.description,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.description]),Zg=p.useCallback((Y={})=>({...Y,...gl,"data-slot":"error-message",className:Se.errorMessage({class:ee(x==null?void 0:x.errorMessage,Y==null?void 0:Y.className)})}),[Se,gl,x==null?void 0:x.errorMessage]),Jg=p.useCallback((Y={})=>({...Y,type:"button",tabIndex:-1,disabled:e.isDisabled,"aria-label":"clear input","data-slot":"clear-button","data-focus-visible":B(Hc),className:Se.clearButton({class:ee(x==null?void 0:x.clearButton,Y==null?void 0:Y.className)}),...Z(Kc,Bc)}),[Se,Hc,Kc,Bc,x==null?void 0:x.clearButton]);return{Component:P,classNames:x,domRef:_,label:b,description:m,startContent:T,endContent:L,labelPlacement:Mt,isClearable:ro,hasHelper:gi,hasStartContent:Bt,isLabelOutside:Wg,isOutsideLeft:Ag,isLabelOutsideAsPlaceholder:Vg,shouldLabelBeOutside:Yc,shouldLabelBeInside:Dg,hasPlaceholder:zn,isInvalid:dr,errorMessage:vi,getBaseProps:Bg,getLabelProps:Hg,getInputProps:Ug,getMainWrapperProps:Qg,getInputWrapperProps:Kg,getInnerWrapperProps:Gg,getHelperWrapperProps:Yg,getDescriptionProps:Xg,getErrorMessageProps:Zg,getClearButtonProps:Jg}}var Pg=Ue((e,t)=>{const{Component:n,label:r,description:o,isClearable:l,startContent:a,endContent:i,labelPlacement:s,hasHelper:u,isOutsideLeft:c,shouldLabelBeOutside:d,errorMessage:f,isInvalid:y,getBaseProps:g,getLabelProps:b,getInputProps:S,getInnerWrapperProps:h,getInputWrapperProps:m,getMainWrapperProps:v,getHelperWrapperProps:x,getDescriptionProps:C,getErrorMessageProps:T,getClearButtonProps:L}=IE({...e,ref:t}),M=r?w.jsx("label",{...b(),children:r}):null,D=p.useMemo(()=>l?w.jsx("button",{...L(),children:i||w.jsx(jS,{})}):i,[l,L]),I=p.useMemo(()=>{const z=y&&f;return!u||!(z||o)?null:w.jsx("div",{...x(),children:z?w.jsx("div",{...T(),children:f}):w.jsx("div",{...C(),children:o})})},[u,y,f,o,x,T,C]),E=p.useMemo(()=>w.jsxs("div",{...h(),children:[a,w.jsx("input",{...S()}),D]}),[a,D,S,h]),k=p.useMemo(()=>d?w.jsxs("div",{...v(),children:[w.jsxs("div",{...m(),children:[c?null:M,E]}),I]}):w.jsxs(w.Fragment,{children:[w.jsxs("div",{...m(),children:[M,E]}),I]}),[s,I,d,M,E,f,o,v,m,T,C]);return w.jsxs(n,{...g(),children:[c?M:null,k]})});Pg.displayName="HeroUI.Input";var RE=Pg,[jE,to]=Rc({name:"NavbarContext",strict:!0,errorMessage:"useNavbarContext: `context` is undefined. Seems you forgot to wrap component within <Navbar />"}),zE={enter:{height:"calc(100vh - var(--navbar-height))",transition:{duration:.3,easings:"easeOut"}},exit:{height:0,transition:{duration:.25,easings:"easeIn"}}},OE=()=>pi(()=>import("./index-BydXBEuJ.js"),__vite__mapDeps([])).then(e=>e.default),Tg=Ue((e,t)=>{var n,r;const{className:o,children:l,portalContainer:a,motionProps:i,style:s,...u}=e,c=Ze(t),{slots:d,isMenuOpen:f,height:y,disableAnimation:g,classNames:b}=to(),S=ee(b==null?void 0:b.menu,o);return g?f?w.jsx(np,{portalContainer:a,children:w.jsx("ul",{ref:c,className:(n=d.menu)==null?void 0:n.call(d,{class:S}),"data-open":B(f),style:{"--navbar-height":typeof y=="number"?`${y}px`:y},...u,children:l})}):null:w.jsx(Ac,{mode:"wait",children:f?w.jsx(np,{portalContainer:a,children:w.jsx(fi,{features:OE,children:w.jsx(di.ul,{ref:c,layoutScroll:!0,animate:"enter",className:(r=d.menu)==null?void 0:r.call(d,{class:S}),"data-open":B(f),exit:"exit",initial:"exit",style:{"--navbar-height":typeof y=="number"?`${y}px`:y,...s},variants:zE,...Z(i,u),children:l})})}):null})});Tg.displayName="HeroUI.NavbarMenu";var Ng=Tg,FE={visible:{y:0,transition:{ease:hp.easeOut}},hidden:{y:"-100%",transition:{ease:hp.easeIn}}},DE=typeof window<"u";function bp(e){return DE?e?{x:e.scrollLeft,y:e.scrollTop}:{x:window.scrollX,y:window.scrollY}:{x:0,y:0}}var AE=e=>{const{elementRef:t,delay:n=30,callback:r,isEnabled:o}=e,l=p.useRef(o?bp(t==null?void 0:t.current):{x:0,y:0}),a=p.useRef(null),i=p.useCallback(()=>{const s=bp(t==null?void 0:t.current);typeof r=="function"&&r({prevPos:l.current,currPos:s}),l.current=s,a.current=null},[r,t]);return p.useEffect(()=>{if(!o)return;const s=()=>{n?(a.current&&clearTimeout(a.current),a.current=setTimeout(i,n)):i()},u=(t==null?void 0:t.current)||window;return u.addEventListener("scroll",s),()=>{u.removeEventListener("scroll",s),a.current&&(clearTimeout(a.current),a.current=null)}},[t==null?void 0:t.current,n,i,o]),l.current};function WE(e){var t,n;const r=on(),[o,l]=ln(e,kf.variantKeys),{ref:a,as:i,parentRef:s,height:u="4rem",shouldHideOnScroll:c=!1,disableScrollHandler:d=!1,shouldBlockScroll:f=!0,onScrollPositionChange:y,isMenuOpen:g,isMenuDefaultOpen:b,onMenuOpenChange:S=()=>{},motionProps:h,className:m,classNames:v,...x}=o,C=i||"nav",T=(n=(t=e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,L=Ze(a),M=p.useRef(0),D=p.useRef(0),[I,E]=p.useState(!1),k=p.useCallback(_=>{S(_||!1)},[S]),[z,$]=ml(g,b??!1,k),A=()=>{if(L.current){const _=L.current.offsetWidth;_!==M.current&&(M.current=_)}};u2({isDisabled:!(f&&z)}),au({ref:L,onResize:()=>{var _;const V=(_=L.current)==null?void 0:_.offsetWidth,H=window.innerWidth-document.documentElement.clientWidth;V&&V+H==M.current||V!==M.current&&(A(),$(!1))}}),p.useEffect(()=>{var _;A(),D.current=((_=L.current)==null?void 0:_.offsetHeight)||0},[]);const N=p.useMemo(()=>kf({...l,disableAnimation:T,hideOnScroll:c}),[Wt(l),T,c]),O=ee(v==null?void 0:v.base,m);return AE({elementRef:s,isEnabled:c||!d,callback:({prevPos:_,currPos:V})=>{y==null||y(V.y),c&&E(H=>{const te=V.y>_.y&&V.y>D.current;return te!==H?te:H})}}),{Component:C,slots:N,domRef:L,height:u,isHidden:I,disableAnimation:T,shouldHideOnScroll:c,isMenuOpen:z,classNames:v,setIsMenuOpen:$,motionProps:h,getBaseProps:(_={})=>({...Z(x,_),"data-hidden":B(I),"data-menu-open":B(z),ref:L,className:N.base({class:ee(O,_==null?void 0:_.className)}),style:{"--navbar-height":typeof u=="number"?`${u}px`:u,...x==null?void 0:x.style,..._==null?void 0:_.style}}),getWrapperProps:(_={})=>({..._,"data-menu-open":B(z),className:N.wrapper({class:ee(v==null?void 0:v.wrapper,_==null?void 0:_.className)})})}}var VE=()=>pi(()=>import("./index-BydXBEuJ.js"),__vite__mapDeps([])).then(e=>e.default),_g=Ue((e,t)=>{const{children:n,...r}=e,o=WE({...r,ref:t}),l=o.Component,[a,i]=Dx(n,Ng),s=w.jsxs(w.Fragment,{children:[w.jsx("header",{...o.getWrapperProps(),children:a}),i]});return w.jsx(jE,{value:o,children:o.shouldHideOnScroll?w.jsx(fi,{features:VE,children:w.jsx(di.nav,{animate:o.isHidden?"hidden":"visible",initial:!1,variants:FE,...Z(o.getBaseProps(),o.motionProps),children:s})}):w.jsx(l,{...o.getBaseProps(),children:s})})});_g.displayName="HeroUI.Navbar";var BE=_g,Mg=Ue((e,t)=>{var n;const{as:r,className:o,children:l,...a}=e,i=r||"div",s=Ze(t),{slots:u,classNames:c}=to(),d=ee(c==null?void 0:c.brand,o);return w.jsx(i,{ref:s,className:(n=u.brand)==null?void 0:n.call(u,{class:d}),...a,children:l})});Mg.displayName="HeroUI.NavbarBrand";var HE=Mg,Lg=Ue((e,t)=>{var n;const{as:r,className:o,children:l,justify:a="start",...i}=e,s=r||"ul",u=Ze(t),{slots:c,classNames:d}=to(),f=ee(d==null?void 0:d.content,o);return w.jsx(s,{ref:u,className:(n=c.content)==null?void 0:n.call(c,{class:f}),"data-justify":a,...i,children:l})});Lg.displayName="HeroUI.NavbarContent";var rs=Lg,Ig=Ue((e,t)=>{var n;const{as:r,className:o,children:l,isActive:a,...i}=e,s=r||"li",u=Ze(t),{slots:c,classNames:d}=to(),f=ee(d==null?void 0:d.item,o);return w.jsx(s,{ref:u,className:(n=c.item)==null?void 0:n.call(c,{class:f}),"data-active":B(a),...i,children:l})});Ig.displayName="HeroUI.NavbarItem";var Ql=Ig,Rg=Ue((e,t)=>{var n;const{className:r,children:o,isActive:l,...a}=e,i=Ze(t),{slots:s,isMenuOpen:u,classNames:c}=to(),d=ee(c==null?void 0:c.menuItem,r);return w.jsx("li",{ref:i,className:(n=s.menuItem)==null?void 0:n.call(s,{class:d}),"data-active":B(l),"data-open":B(u),...a,children:o})});Rg.displayName="HeroUI.NavbarMenuItem";var UE=Rg;function KE(e,t){let{elementType:n="button",isDisabled:r,onPress:o,onPressStart:l,onPressEnd:a,onPressUp:i,onPressChange:s,preventFocusOnPress:u,allowFocusWhenDisabled:c,onClick:d,href:f,target:y,rel:g,type:b="button"}=e,S;n==="button"?S={type:b,disabled:r}:S={role:"button",href:n==="a"&&!r?f:void 0,target:n==="a"?y:void 0,type:n==="input"?b:void 0,disabled:n==="input"?r:void 0,"aria-disabled":!r||n==="input"?void 0:r,rel:n==="a"?g:void 0};let{pressProps:h,isPressed:m}=Kr({onPressStart:l,onPressEnd:a,onPressChange:s,onPress:o,onPressUp:i,onClick:d,isDisabled:r,preventFocusOnPress:u,ref:t}),{focusableProps:v}=eo(e,t);c&&(v.tabIndex=r?-1:v.tabIndex);let x=Z(v,h,qr(e,{labelable:!0}));return{isPressed:m,buttonProps:Z(S,x,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}function GE(e,t,n){const{isSelected:r}=t,{isPressed:o,buttonProps:l}=KE({...e,onPress:At(t.toggle,e.onPress)},n);return{isPressed:o,isSelected:r,isDisabled:e.isDisabled||!1,buttonProps:Z(l,{"aria-pressed":r})}}function jg(e={}){let{isReadOnly:t}=e,[n,r]=ml(e.isSelected,e.defaultSelected||!1,e.onChange);function o(a){t||r(a)}function l(){t||r(!n)}return{isSelected:n,setSelected:o,toggle:l}}var zg=Ue((e,t)=>{var n;const{as:r,icon:o,className:l,onChange:a,autoFocus:i,srOnlyText:s,...u}=e,c=r||"button",d=Ze(t),{slots:f,classNames:y,isMenuOpen:g,setIsMenuOpen:b}=to(),h=jg({...u,isSelected:g,onChange:E=>{a==null||a(E),b(E)}}),{buttonProps:m,isPressed:v}=GE(e,h,d),{isFocusVisible:x,focusProps:C}=lr({autoFocus:i}),{isHovered:T,hoverProps:L}=or({}),M=ee(y==null?void 0:y.toggle,l),D=p.useMemo(()=>typeof o=="function"?o(g??!1):o||w.jsx("span",{className:f.toggleIcon({class:y==null?void 0:y.toggleIcon})}),[o,g,f.toggleIcon,y==null?void 0:y.toggleIcon]),I=p.useMemo(()=>s||(h.isSelected?"close navigation menu":"open navigation menu"),[s,g]);return w.jsxs(c,{ref:d,className:(n=f.toggle)==null?void 0:n.call(f,{class:M}),"data-focus-visible":B(x),"data-hover":B(T),"data-open":B(g),"data-pressed":B(v),...Z(m,C,L,u),children:[w.jsx("span",{className:f.srOnly(),children:I}),D]})});zg.displayName="HeroUI.NavbarMenuToggle";var QE=zg;function YE(e,t,n){let{isDisabled:r=!1,isReadOnly:o=!1,value:l,name:a,children:i,"aria-label":s,"aria-labelledby":u,validationState:c="valid",isInvalid:d}=e,f=x=>{x.stopPropagation(),t.setSelected(x.target.checked)},{pressProps:y,isPressed:g}=Kr({isDisabled:r}),{pressProps:b,isPressed:S}=Kr({onPress(){var x;t.toggle(),(x=n.current)===null||x===void 0||x.focus()},isDisabled:r||o}),{focusableProps:h}=eo(e,n),m=Z(y,h),v=qr(e,{labelable:!0});return Sv(n,t.isSelected,t.setSelected),{labelProps:Z(b,{onClick:x=>x.preventDefault()}),inputProps:Z(v,{"aria-invalid":d||c==="invalid"||void 0,"aria-errormessage":e["aria-errormessage"],"aria-controls":e["aria-controls"],"aria-readonly":o||void 0,onChange:f,disabled:r,...l==null?{}:{value:l},name:a,type:"checkbox",...m}),isSelected:t.isSelected,isPressed:g||S,isDisabled:r,isReadOnly:o,isInvalid:d||c==="invalid"}}function XE(e,t,n){let{labelProps:r,inputProps:o,isSelected:l,isPressed:a,isDisabled:i,isReadOnly:s}=YE(e,t,n);return{labelProps:r,inputProps:{...o,role:"switch",checked:l},isSelected:l,isPressed:a,isDisabled:i,isReadOnly:s}}function ZE(e={}){var t,n;const r=on(),[o,l]=ln(e,Sf.variantKeys),{ref:a,as:i,name:s,value:u="",isReadOnly:c=!1,autoFocus:d=!1,startContent:f,endContent:y,defaultSelected:g,isSelected:b,children:S,thumbIcon:h,className:m,classNames:v,onChange:x,onValueChange:C,...T}=o,L=i||"label",M=p.useRef(null),D=p.useRef(null),I=(n=(t=e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,E=p.useId(),k=p.useMemo(()=>{const U=T["aria-label"]||typeof S=="string"?S:void 0;return{name:s,value:u,children:S,autoFocus:d,defaultSelected:g,isSelected:b,isDisabled:!!e.isDisabled,isReadOnly:c,"aria-label":U,"aria-labelledby":T["aria-labelledby"]||E,onChange:C}},[u,s,E,S,d,c,b,g,e.isDisabled,T["aria-label"],T["aria-labelledby"],C]),z=jg(k);Wc(()=>{if(!D.current)return;const U=!!D.current.checked;z.setSelected(U)},[D.current]);const{inputProps:$,isPressed:A,isReadOnly:N}=XE(k,z,D),{focusProps:O,isFocused:P,isFocusVisible:R}=lr({autoFocus:$.autoFocus}),{hoverProps:_,isHovered:V}=or({isDisabled:$.disabled}),te=k.isDisabled||N?!1:A,K=$.checked,re=$.disabled,G=p.useMemo(()=>Sf({...l,disableAnimation:I}),[Wt(l),I]),we=ee(v==null?void 0:v.base,m),ut=U=>({...Z(_,T,U),ref:M,className:G.base({class:ee(we,U==null?void 0:U.className)}),"data-disabled":B(re),"data-selected":B(K),"data-readonly":B(N),"data-focus":B(P),"data-focus-visible":B(R),"data-hover":B(V),"data-pressed":B(te)}),xe=p.useCallback((U={})=>({...U,"aria-hidden":!0,className:ee(G.wrapper({class:ee(v==null?void 0:v.wrapper,U==null?void 0:U.className)}))}),[G,v==null?void 0:v.wrapper]),Me=(U={})=>({...Z($,O,U),ref:Ov(D,a),id:$.id,className:G.hiddenInput({class:v==null?void 0:v.hiddenInput}),onChange:At(x,$.onChange)}),nt=p.useCallback((U={})=>({...U,className:G.thumb({class:ee(v==null?void 0:v.thumb,U==null?void 0:U.className)})}),[G,v==null?void 0:v.thumb]),Ae=p.useCallback((U={})=>({...U,id:E,className:G.label({class:ee(v==null?void 0:v.label,U==null?void 0:U.className)})}),[G,v==null?void 0:v.label,re,K]),Ke=p.useCallback((U={includeStateProps:!1})=>Z({width:"1em",height:"1em",className:G.thumbIcon({class:ee(v==null?void 0:v.thumbIcon)})},U.includeStateProps?{isSelected:K}:{}),[G,v==null?void 0:v.thumbIcon,K]),St=p.useCallback((U={})=>({width:"1em",height:"1em",...U,className:G.startContent({class:ee(v==null?void 0:v.startContent,U==null?void 0:U.className)})}),[G,v==null?void 0:v.startContent,K]),ct=p.useCallback((U={})=>({width:"1em",height:"1em",...U,className:G.endContent({class:ee(v==null?void 0:v.endContent,U==null?void 0:U.className)})}),[G,v==null?void 0:v.endContent,K]);return{Component:L,slots:G,classNames:v,domRef:M,children:S,thumbIcon:h,startContent:f,endContent:y,isHovered:V,isSelected:K,isPressed:te,isFocused:P,isFocusVisible:R,isDisabled:re,getBaseProps:ut,getWrapperProps:xe,getInputProps:Me,getLabelProps:Ae,getThumbProps:nt,getThumbIconProps:Ke,getStartContentProps:St,getEndContentProps:ct}}var ft={KEY:"heroui-theme",LIGHT:"light",DARK:"dark",SYSTEM:"system"};function JE(e=ft.SYSTEM){const t="(prefers-color-scheme: dark)",[n,r]=p.useState(()=>{var a;const i=localStorage.getItem(ft.KEY);return i||(e===ft.SYSTEM?(a=window.matchMedia)!=null&&a.call(window,t).matches?ft.DARK:ft.LIGHT:e)}),o=p.useCallback(a=>{var i;const s=a===ft.SYSTEM?(i=window.matchMedia)!=null&&i.call(window,t).matches?ft.DARK:ft.LIGHT:a;localStorage.setItem(ft.KEY,a),document.documentElement.classList.remove(n),document.documentElement.classList.add(s),r(a)},[n]),l=p.useCallback(a=>{e===ft.SYSTEM&&o(a.matches?ft.DARK:ft.LIGHT)},[o]);return p.useEffect(()=>o(n),[n,o]),p.useEffect(()=>{const a=window.matchMedia(t);return a.addEventListener("change",l),()=>a.removeEventListener("change",l)},[l]),{theme:n,setTheme:o}}const wp=({className:e,classNames:t})=>{const[n,r]=p.useState(!1),{theme:o,setTheme:l}=JE(),{Component:a,slots:i,isSelected:s,getBaseProps:u,getInputProps:c,getWrapperProps:d}=ZE({isSelected:o==="light",onChange:()=>l(o==="light"?"dark":"light")});return p.useEffect(()=>{r(!0)},[n]),n?w.jsxs(a,{"aria-label":s?"Switch to dark mode":"Switch to light mode",...u({className:La("px-px transition-opacity hover:opacity-80 cursor-pointer",e,t==null?void 0:t.base)}),children:[w.jsx(y2,{children:w.jsx("input",{...c()})}),w.jsx("div",{...d(),className:i.wrapper({class:La(["w-auto h-auto","bg-transparent","rounded-lg","flex items-center justify-center","group-data-[selected=true]:bg-transparent","!text-default-500","pt-px","px-0","mx-0"],t==null?void 0:t.wrapper)}),children:s?w.jsx(mE,{size:22}):w.jsx(hE,{size:22})})]}):w.jsx("div",{className:"w-6 h-6"})},qE=()=>{const e=w.jsx(RE,{"aria-label":"Search",classNames:{inputWrapper:"bg-default-100",input:"text-sm"},endContent:w.jsx(xE,{className:"hidden lg:inline-block",keys:["command"],children:"K"}),labelPlacement:"outside",placeholder:"Search...",startContent:w.jsx(gE,{className:"text-base text-default-400 pointer-events-none flex-shrink-0"}),type:"search"});return w.jsxs(BE,{maxWidth:"xl",position:"sticky",children:[w.jsxs(rs,{className:"basis-1/5 sm:basis-full",justify:"start",children:[w.jsx(HE,{className:"gap-3 max-w-fit",children:w.jsxs($t,{className:"flex justify-start items-center gap-1",color:"foreground",href:"/",children:[w.jsx(dE,{}),w.jsx("p",{className:"font-bold text-inherit",children:"ACME"})]})}),w.jsx("div",{className:"hidden lg:flex gap-4 justify-start ml-2",children:jt.navItems.map(t=>w.jsx(Ql,{children:w.jsx($t,{className:La(ru({color:"foreground"}),"data-[active=true]:text-primary data-[active=true]:font-medium"),color:"foreground",href:t.href,children:t.label})},t.href))})]}),w.jsxs(rs,{className:"hidden sm:flex basis-1/5 sm:basis-full",justify:"end",children:[w.jsxs(Ql,{className:"hidden sm:flex gap-2",children:[w.jsx($t,{isExternal:!0,href:jt.links.twitter,title:"Twitter",children:w.jsx(pE,{className:"text-default-500"})}),w.jsx($t,{isExternal:!0,href:jt.links.discord,title:"Discord",children:w.jsx(fE,{className:"text-default-500"})}),w.jsx($t,{isExternal:!0,href:jt.links.github,title:"GitHub",children:w.jsx(xu,{className:"text-default-500"})}),w.jsx(wp,{})]}),w.jsx(Ql,{className:"hidden lg:flex",children:e}),w.jsx(Ql,{className:"hidden md:flex",children:w.jsx(bg,{isExternal:!0,as:$t,className:"text-sm font-normal text-default-600 bg-default-100",href:jt.links.sponsor,startContent:w.jsx(vE,{className:"text-danger"}),variant:"flat",children:"Sponsor"})})]}),w.jsxs(rs,{className:"sm:hidden basis-1 pl-4",justify:"end",children:[w.jsx($t,{isExternal:!0,href:jt.links.github,children:w.jsx(xu,{className:"text-default-500"})}),w.jsx(wp,{}),w.jsx(QE,{})]}),w.jsxs(Ng,{children:[e,w.jsx("div",{className:"mx-4 mt-2 flex flex-col gap-2",children:jt.navMenuItems.map((t,n)=>w.jsx(UE,{children:w.jsx($t,{color:n===2?"primary":n===jt.navMenuItems.length-1?"danger":"foreground",href:"#",size:"lg",children:t.label})},`${t}-${n}`))})]})]})};function vl({children:e}){return w.jsxs("div",{className:"relative flex flex-col h-screen",children:[w.jsx(qE,{}),w.jsx("main",{className:"container mx-auto max-w-7xl px-6 flex-grow pt-16",children:e}),w.jsx("footer",{className:"w-full flex items-center justify-center py-3",children:w.jsxs($t,{isExternal:!0,className:"flex items-center gap-1 text-current",href:"https://heroui.com",title:"heroui.com homepage",children:[w.jsx("span",{className:"text-default-600",children:"Powered by"}),w.jsx("p",{className:"text-primary",children:"HeroUI"})]})})]})}function eC(){return w.jsx(vl,{children:w.jsxs("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:[w.jsxs("div",{className:"inline-block max-w-lg text-center justify-center",children:[w.jsx("span",{className:Xn(),children:"Make "}),w.jsx("span",{className:Xn({color:"violet"}),children:"beautiful "}),w.jsx("br",{}),w.jsx("span",{className:Xn(),children:"websites regardless of your design experience."}),w.jsx("div",{className:cE({class:"mt-4"}),children:"Beautiful, fast and modern React UI library."})]}),w.jsxs("div",{className:"flex gap-3",children:[w.jsx($t,{isExternal:!0,className:ou({color:"primary",radius:"full",variant:"shadow"}),href:jt.links.docs,children:"Documentation"}),w.jsxs($t,{isExternal:!0,className:ou({variant:"bordered",radius:"full"}),href:jt.links.github,children:[w.jsx(xu,{size:20}),"GitHub"]})]}),w.jsx("div",{className:"mt-8",children:w.jsx(iE,{hideCopyButton:!0,hideSymbol:!0,variant:"bordered",children:w.jsxs("span",{children:["Get started by editing"," ",w.jsx(uE,{color:"primary",children:"pages/index.tsx"})]})})})]})})}function tC(){return w.jsx(vl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Xn(),children:"Docs"})})})})}function nC(){return w.jsx(vl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Xn(),children:"Pricing"})})})})}function rC(){return w.jsx(vl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Xn(),children:"Blog"})})})})}function oC(){return w.jsx(vl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Xn(),children:"About"})})})})}function lC(){return w.jsxs(vb,{children:[w.jsx(mr,{element:w.jsx(eC,{}),path:"/"}),w.jsx(mr,{element:w.jsx(tC,{}),path:"/docs"}),w.jsx(mr,{element:w.jsx(nC,{}),path:"/pricing"}),w.jsx(mr,{element:w.jsx(rC,{}),path:"/blog"}),w.jsx(mr,{element:w.jsx(oC,{}),path:"/about"})]})}function aC({children:e}){const t=q1();return w.jsx(NS,{navigate:t,useHref:J1,children:e})}os.createRoot(document.getElementById("root")).render(w.jsx(q.StrictMode,{children:w.jsx(bb,{children:w.jsx(aC,{children:w.jsx(lC,{})})})}));export{Fc as A,U2 as B,Fa as C,eS as D,N2 as E,sg as F,og as G,Xv as H,tS as I,rS as J,ug as K,nS as L,$2 as M,cg as N,ng as O,p as P,jc as a,si as b,es as c,Fn as d,cC as e,op as f,wu as g,L2 as h,uC as i,Yv as j,sC as k,iC as l,fC as m,k2 as n,O2 as o,Q as p,ar as q,S2 as r,bu as s,Kv as t,C2 as u,dC as v,Uv as w,x2 as x,E2 as y,qv as z};

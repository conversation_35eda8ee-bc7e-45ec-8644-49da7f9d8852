import{r as yt,t as X,n as G,i as De,a as Ie,p as bt,b as bn,c as ve,d as Tn,v as Vn,e as An,f as D,g as Tt,h as U,j as Sn,k as Q,M as Vt,l as ye,m as wn,o as xn,q as E,s as At,u as St,w as wt,x as qe,y as xt,z as Pn,A as Mn,B as Cn,C as He,D as Fn,E as Dn,F as In,G as En,H as Rn,I as On,J as Kn,K as Nn,L as Bn,N as Ln,O as kn,P as _n}from"./index-BIhQ6av6.js";function Pt(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function re(t,e,n){const s=t.getProps();return yt(s,e,n!==void 0?n:s.custom,t)}const R=t=>t*1e3,O=t=>t/1e3,Gn={type:"spring",stiffness:500,damping:25,restSpeed:10},Un=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),jn={type:"keyframes",duration:.8},Wn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},zn=(t,{keyframes:e})=>e.length>2?jn:X.has(t)?t.startsWith("scale")?Un(e[1]):Gn:Wn;function Mt(t,e){return t?t[e]||t.default||t:void 0}const qn=t=>t!==null;function oe(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(qn),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return!r||s===void 0?i[r]:s}let Ct=G;const Ft=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Hn=1e-7,$n=12;function Yn(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Ft(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>Hn&&++a<$n);return o}function Z(t,e,n,s){if(t===e&&n===s)return G;const i=r=>Yn(r,0,1,t,n);return r=>r===0||r===1?r:Ft(i(r),e,s)}const Dt=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,It=t=>e=>1-t(1-e),Et=Z(.33,1.53,.69,.99),Ee=It(Et),Rt=Dt(Ee),Ot=t=>(t*=2)<1?.5*Ee(t):.5*(2-Math.pow(2,-10*(t-1))),Re=t=>1-Math.sin(Math.acos(t)),Xn=It(Re),Kt=Dt(Re),Nt=t=>/^0[^.\s]+$/u.test(t);function Zn(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Nt(t):!0}const Bt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Jn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Qn(t){const e=Jn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Lt(t,e,n=1){const[s,i]=Qn(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return Bt(o)?parseFloat(o):o}return De(i)?Lt(i,e,n+1):i}const es=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),$e=t=>t===Ie||t===bt,Ye=(t,e)=>parseFloat(t.split(", ")[e]),Xe=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/u);if(i)return Ye(i[1],e);{const r=s.match(/^matrix\((.+)\)$/u);return r?Ye(r[1],t):0}},ts=new Set(["x","y","z"]),ns=bn.filter(t=>!ts.has(t));function ss(t){const e=[];return ns.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const j={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Xe(4,13),y:Xe(5,14)};j.translateX=j.x;j.translateY=j.y;const kt=t=>e=>e.test(t),is={test:t=>t==="auto",parse:t=>t},_t=[Ie,bt,ve,Tn,Vn,An,is],Ze=t=>_t.find(kt(t)),L=new Set;let be=!1,Te=!1;function Gt(){if(Te){const t=Array.from(L).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=ss(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,o])=>{var a;(a=s.getValue(r))===null||a===void 0||a.set(o)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Te=!1,be=!1,L.forEach(t=>t.complete()),L.clear()}function Ut(){L.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Te=!0)})}function rs(){Ut(),Gt()}class Oe{constructor(e,n,s,i,r,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(L.add(this),be||(be=!0,D.read(Ut),D.resolveKeyframes(Gt))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let r=0;r<e.length;r++)if(e[r]===null)if(r===0){const o=i==null?void 0:i.get(),a=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const l=s.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),i&&o===void 0&&i.set(e[0])}else e[r]=e[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),L.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,L.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const $=t=>Math.round(t*1e5)/1e5,Ke=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function os(t){return t==null}const as=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ne=(t,e)=>n=>!!(typeof n=="string"&&as.test(n)&&n.startsWith(t)||e&&!os(n)&&Object.prototype.hasOwnProperty.call(n,e)),jt=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,o,a]=s.match(Ke);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},ls=t=>U(0,255,t),he={...Ie,transform:t=>Math.round(ls(t))},B={test:Ne("rgb","red"),parse:jt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+he.transform(t)+", "+he.transform(e)+", "+he.transform(n)+", "+$(Tt.transform(s))+")"};function us(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ve={test:Ne("#"),parse:us,transform:B.transform},_={test:Ne("hsl","hue"),parse:jt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ve.transform($(e))+", "+ve.transform($(n))+", "+$(Tt.transform(s))+")"},x={test:t=>B.test(t)||Ve.test(t)||_.test(t),parse:t=>B.test(t)?B.parse(t):_.test(t)?_.parse(t):Ve.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?B.transform(t):_.transform(t)},cs=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function hs(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Ke))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(cs))===null||n===void 0?void 0:n.length)||0)>0}const Wt="number",zt="color",ds="var",fs="var(",Je="${}",ps=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Y(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(ps,l=>(x.test(l)?(s.color.push(r),i.push(zt),n.push(x.parse(l))):l.startsWith(fs)?(s.var.push(r),i.push(ds),n.push(l)):(s.number.push(r),i.push(Wt),n.push(parseFloat(l))),++r,Je)).split(Je);return{values:n,split:a,indexes:s,types:i}}function qt(t){return Y(t).values}function Ht(t){const{split:e,types:n}=Y(t),s=e.length;return i=>{let r="";for(let o=0;o<s;o++)if(r+=e[o],i[o]!==void 0){const a=n[o];a===Wt?r+=$(i[o]):a===zt?r+=x.transform(i[o]):r+=i[o]}return r}}const ms=t=>typeof t=="number"?0:t;function gs(t){const e=qt(t);return Ht(t)(e.map(ms))}const W={test:hs,parse:qt,createTransformer:Ht,getAnimatableNone:gs},vs=new Set(["brightness","contrast","saturate","opacity"]);function ys(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Ke)||[];if(!s)return t;const i=n.replace(s,"");let r=vs.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const bs=/\b([a-z-]*)\(.*?\)/gu,Ae={...W,getAnimatableNone:t=>{const e=t.match(bs);return e?e.map(ys).join(" "):t}},Ts={...Sn,color:x,backgroundColor:x,outlineColor:x,fill:x,stroke:x,borderColor:x,borderTopColor:x,borderRightColor:x,borderBottomColor:x,borderLeftColor:x,filter:Ae,WebkitFilter:Ae},Be=t=>Ts[t];function $t(t,e){let n=Be(t);return n!==Ae&&(n=W),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Vs=new Set(["auto","none","0"]);function As(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!Vs.has(r)&&Y(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=$t(n,i)}class Yt extends Oe{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),De(u))){const c=Lt(u,n.current);c!==void 0&&(e[l]=c),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!es.has(s)||e.length!==2)return;const[i,r]=e,o=Ze(i),a=Ze(r);if(o!==a)if($e(o)&&$e(a))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)Zn(e[i])&&s.push(i);s.length&&As(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=j[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const r=n.getValue(s);r&&r.jump(this.measuredOrigin,!1);const o=i.length-1,a=i[o];i[o]=j[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function Le(t){return typeof t=="function"}let J;function Ss(){J=void 0}const K={now:()=>(J===void 0&&K.set(Q.isProcessing||Vt.useManualTiming?Q.timestamp:performance.now()),J),set:t=>{J=t,queueMicrotask(Ss)}},Qe=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(W.test(t)||t==="0")&&!t.startsWith("url("));function ws(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function xs(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],o=Qe(i,e),a=Qe(r,e);return!o||!a?!1:ws(t)||(n==="spring"||Le(n))&&s}const Ps=40;class Xt{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=K.now(),this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Ps?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&rs(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=K.now(),this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:r,delay:o,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!xs(e,s,i,r))if(o)this.options.duration=0;else{l==null||l(oe(e,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const c=this.initPlayback(e,n);c!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const ke=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},Zt=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=t(ke(0,i-1,r))+", ";return`linear(${s.substring(0,s.length-2)})`};function Jt(t,e){return e?t*(1e3/e):0}const Ms=5;function Qt(t,e,n){const s=Math.max(e-Ms,0);return Jt(n-t(s),e-s)}const S={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},de=.001;function Cs({duration:t=S.duration,bounce:e=S.bounce,velocity:n=S.velocity,mass:s=S.mass}){let i,r,o=1-e;o=U(S.minDamping,S.maxDamping,o),t=U(S.minDuration,S.maxDuration,O(t)),o<1?(i=u=>{const c=u*o,h=c*t,d=c-n,f=Se(u,o),T=Math.exp(-h);return de-d/f*T},r=u=>{const h=u*o*t,d=h*n+n,f=Math.pow(o,2)*Math.pow(u,2)*t,T=Math.exp(-h),v=Se(Math.pow(u,2),o);return(-i(u)+de>0?-1:1)*((d-f)*T)/v}):(i=u=>{const c=Math.exp(-u*t),h=(u-n)*t+1;return-de+c*h},r=u=>{const c=Math.exp(-u*t),h=(n-u)*(t*t);return c*h});const a=5/t,l=Ds(i,r,a);if(t=R(t),isNaN(l))return{stiffness:S.stiffness,damping:S.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:o*2*Math.sqrt(s*u),duration:t}}}const Fs=12;function Ds(t,e,n){let s=n;for(let i=1;i<Fs;i++)s=s-t(s)/e(s);return s}function Se(t,e){return t*Math.sqrt(1-e*e)}const we=2e4;function en(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<we;)e+=n,s=t.next(e);return e>=we?1/0:e}const Is=["duration","bounce"],Es=["stiffness","damping","mass"];function et(t,e){return e.some(n=>t[n]!==void 0)}function Rs(t){let e={velocity:S.velocity,stiffness:S.stiffness,damping:S.damping,mass:S.mass,isResolvedFromDuration:!1,...t};if(!et(t,Es)&&et(t,Is))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*U(.05,1,1-t.bounce)*Math.sqrt(i);e={...e,mass:S.mass,stiffness:i,damping:r}}else{const n=Cs(t);e={...e,...n,mass:S.mass},e.isResolvedFromDuration=!0}return e}function tn(t=S.visualDuration,e=S.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:r},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:f}=Rs({...n,velocity:-O(n.velocity||0)}),T=d||0,v=u/(2*Math.sqrt(l*c)),y=o-r,p=O(Math.sqrt(l/c)),b=Math.abs(y)<5;s||(s=b?S.restSpeed.granular:S.restSpeed.default),i||(i=b?S.restDelta.granular:S.restDelta.default);let V;if(v<1){const g=Se(p,v);V=A=>{const w=Math.exp(-v*p*A);return o-w*((T+v*p*y)/g*Math.sin(g*A)+y*Math.cos(g*A))}}else if(v===1)V=g=>o-Math.exp(-p*g)*(y+(T+p*y)*g);else{const g=p*Math.sqrt(v*v-1);V=A=>{const w=Math.exp(-v*p*A),m=Math.min(g*A,300);return o-w*((T+v*p*y)*Math.sinh(m)+g*y*Math.cosh(m))/g}}const C={calculatedDuration:f&&h||null,next:g=>{const A=V(g);if(f)a.done=g>=h;else{let w=0;v<1&&(w=g===0?R(T):Qt(V,g,A));const m=Math.abs(w)<=s,M=Math.abs(o-A)<=i;a.done=m&&M}return a.value=a.done?o:A,a},toString:()=>{const g=Math.min(en(C),we),A=Zt(w=>C.next(g*w).value,g,30);return g+"ms "+A}};return C}function tt({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},f=m=>a!==void 0&&m<a||l!==void 0&&m>l,T=m=>a===void 0?l:l===void 0||Math.abs(a-m)<Math.abs(l-m)?a:l;let v=n*e;const y=h+v,p=o===void 0?y:o(y);p!==y&&(v=p-h);const b=m=>-v*Math.exp(-m/s),V=m=>p+b(m),C=m=>{const M=b(m),F=V(m);d.done=Math.abs(M)<=u,d.value=d.done?p:F};let g,A;const w=m=>{f(d.value)&&(g=m,A=tn({keyframes:[d.value,T(d.value)],velocity:Qt(V,m,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:m=>{let M=!1;return!A&&g===void 0&&(M=!0,C(m),w(m)),g!==void 0&&m>=g?A.next(m-g):(!M&&C(m),d)}}}const Os=Z(.42,0,1,1),Ks=Z(0,0,.58,1),nn=Z(.42,0,.58,1),Ns=t=>Array.isArray(t)&&typeof t[0]!="number",_e=t=>Array.isArray(t)&&typeof t[0]=="number",Bs={linear:G,easeIn:Os,easeInOut:nn,easeOut:Ks,circIn:Re,circInOut:Kt,circOut:Xn,backIn:Ee,backInOut:Rt,backOut:Et,anticipate:Ot},nt=t=>{if(_e(t)){Ct(t.length===4);const[e,n,s,i]=t;return Z(e,n,s,i)}else if(typeof t=="string")return Bs[t];return t},Ls=(t,e)=>n=>e(t(n)),ae=(...t)=>t.reduce(Ls),le=(t,e,n)=>t+(e-t)*n;function fe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function ks({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=fe(l,a,t+1/3),r=fe(l,a,t),o=fe(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}function ee(t,e){return n=>n>0?e:t}const pe=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},_s=[Ve,B,_],Gs=t=>_s.find(e=>e.test(t));function st(t){const e=Gs(t);if(!e)return!1;let n=e.parse(t);return e===_&&(n=ks(n)),n}const it=(t,e)=>{const n=st(t),s=st(e);if(!n||!s)return ee(t,e);const i={...n};return r=>(i.red=pe(n.red,s.red,r),i.green=pe(n.green,s.green,r),i.blue=pe(n.blue,s.blue,r),i.alpha=le(n.alpha,s.alpha,r),B.transform(i))},xe=new Set(["none","hidden"]);function Us(t,e){return xe.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function js(t,e){return n=>le(t,e,n)}function Ge(t){return typeof t=="number"?js:typeof t=="string"?De(t)?ee:x.test(t)?it:qs:Array.isArray(t)?sn:typeof t=="object"?x.test(t)?it:Ws:ee}function sn(t,e){const n=[...t],s=n.length,i=t.map((r,o)=>Ge(r)(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}}function Ws(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ge(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function zs(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const o=e.types[r],a=t.indexes[o][i[o]],l=(n=t.values[a])!==null&&n!==void 0?n:0;s[r]=l,i[o]++}return s}const qs=(t,e)=>{const n=W.createTransformer(e),s=Y(t),i=Y(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?xe.has(t)&&!i.values.length||xe.has(e)&&!s.values.length?Us(t,e):ae(sn(zs(s,i),i.values),n):ee(t,e)};function rn(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?le(t,e,n):Ge(t)(t,e)}function Hs(t,e,n){const s=[],i=n||rn,r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const l=Array.isArray(e)?e[o]||G:e;a=ae(l,a)}s.push(a)}return s}function $s(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(Ct(r===e.length),r===1)return()=>e[0];if(r===2&&t[0]===t[1])return()=>e[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=Hs(e,s,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<t.length-2&&!(u<t[c+1]);c++);const h=ke(t[c],t[c+1],u);return o[c](h)};return n?u=>l(U(t[0],t[r-1],u)):l}function Ys(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=ke(0,e,s);t.push(le(n,1,i))}}function Xs(t){const e=[0];return Ys(e,t.length-1),e}function Zs(t,e){return t.map(n=>n*e)}function Js(t,e){return t.map(()=>e||nn).splice(0,t.length-1)}function te({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Ns(s)?s.map(nt):nt(s),r={done:!1,value:e[0]},o=Zs(n&&n.length===e.length?n:Xs(e),t),a=$s(o,e,{ease:Array.isArray(i)?i:Js(e,i)});return{calculatedDuration:t,next:l=>(r.value=a(l),r.done=l>=t,r)}}const Qs=t=>{const e=({timestamp:n})=>t(n);return{start:()=>D.update(e,!0),stop:()=>ye(e),now:()=>Q.isProcessing?Q.timestamp:K.now()}},ei={decay:tt,inertia:tt,tween:te,keyframes:te,spring:tn},ti=t=>t/100;class Ue extends Xt{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:s,element:i,keyframes:r}=this.options,o=(i==null?void 0:i.KeyframeResolver)||Oe,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new o(r,a,n,s,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:o=0}=this.options,a=Le(n)?n:ei[n]||te;let l,u;a!==te&&typeof e[0]!="number"&&(l=ae(ti,rn(e[0],e[1])),e=[0,100]);const c=a({...this.options,keyframes:e});r==="mirror"&&(u=a({...this.options,keyframes:[...e].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=en(c));const{calculatedDuration:h}=c,d=h+i,f=d*(s+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:d,totalDuration:f}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:m}=this.options;return{done:!0,value:m[m.length-1]}}const{finalKeyframe:i,generator:r,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:h}=s;if(this.startTime===null)return r.next(0);const{delay:d,repeat:f,repeatType:T,repeatDelay:v,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-c/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const p=this.currentTime-d*(this.speed>=0?1:-1),b=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let V=this.currentTime,C=r;if(f){const m=Math.min(this.currentTime,c)/h;let M=Math.floor(m),F=m%1;!F&&m>=1&&(F=1),F===1&&M--,M=Math.min(M,f+1),!!(M%2)&&(T==="reverse"?(F=1-F,v&&(F-=v/h)):T==="mirror"&&(C=o)),V=U(0,1,F)*h}const g=b?{done:!1,value:l[0]}:C.next(V);a&&(g.value=a(g.value));let{done:A}=g;!b&&u!==null&&(A=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const w=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return w&&i!==void 0&&(g.value=oe(l,this.options,i)),y&&y(g.value),w&&this.finish(),g}get duration(){const{resolved:e}=this;return e?O(e.calculatedDuration):0}get time(){return O(this.currentTime)}set time(e){e=R(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=O(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=Qs,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const ni=new Set(["opacity","clipPath","filter","transform"]);function je(t){let e;return()=>(e===void 0&&(e=t()),e)}const si={linearEasing:void 0};function ii(t,e){const n=je(t);return()=>{var s;return(s=si[e])!==null&&s!==void 0?s:n()}}const ne=ii(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function on(t){return!!(typeof t=="function"&&ne()||!t||typeof t=="string"&&(t in Pe||ne())||_e(t)||Array.isArray(t)&&t.every(on))}const q=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Pe={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:q([0,.65,.55,1]),circOut:q([.55,0,1,.45]),backIn:q([.31,.01,.66,-.59]),backOut:q([.33,1.53,.69,.99])};function an(t,e){if(t)return typeof t=="function"&&ne()?Zt(t,e):_e(t)?q(t):Array.isArray(t)?t.map(n=>an(n,e)||Pe.easeOut):Pe[t]}function ri(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=an(a,i);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}function rt(t,e){t.timeline=e,t.onfinish=null}const oi=je(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),se=10,ai=2e4;function li(t){return Le(t.type)||t.type==="spring"||!on(t.ease)}function ui(t,e){const n=new Ue({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let r=0;for(;!s.done&&r<ai;)s=n.sample(r),i.push(s.value),r+=se;return{times:void 0,keyframes:i,duration:r-se,ease:"linear"}}const ln={anticipate:Ot,backInOut:Rt,circInOut:Kt};function ci(t){return t in ln}class ot extends Xt{constructor(e){super(e);const{name:n,motionValue:s,element:i,keyframes:r}=this.options;this.resolver=new Yt(r,(o,a)=>this.onKeyframesResolved(o,a),n,s,i),this.resolver.scheduleResolve()}initPlayback(e,n){var s;let{duration:i=300,times:r,ease:o,type:a,motionValue:l,name:u,startTime:c}=this.options;if(!(!((s=l.owner)===null||s===void 0)&&s.current))return!1;if(typeof o=="string"&&ne()&&ci(o)&&(o=ln[o]),li(this.options)){const{onComplete:d,onUpdate:f,motionValue:T,element:v,...y}=this.options,p=ui(e,y);e=p.keyframes,e.length===1&&(e[1]=e[0]),i=p.duration,r=p.times,o=p.ease,a="keyframes"}const h=ri(l.owner.current,u,e,{...this.options,duration:i,times:r,ease:o});return h.startTime=c??this.calcStartTime(),this.pendingTimeline?(rt(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:d}=this.options;l.set(oe(e,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:i,times:r,type:a,ease:o,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return O(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return O(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=R(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return G;const{animation:s}=n;rt(s,e)}return G}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:r,ease:o,times:a}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:h,element:d,...f}=this.options,T=new Ue({...f,keyframes:s,duration:i,type:r,ease:o,times:a,isGenerator:!0}),v=R(this.time);u.setWithVelocity(T.sample(v-se).value,T.sample(v).value,se)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:r,damping:o,type:a}=e;return oi()&&s&&ni.has(s)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&r!=="mirror"&&o!==0&&a!=="inertia"}}const hi=je(()=>window.ScrollTimeline!==void 0);class di{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}then(e,n){return Promise.all(this.animations).then(e).catch(n)}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e,n){const s=this.animations.map(i=>hi()&&i.attachTimeline?i.attachTimeline(e):n(i));return()=>{s.forEach((i,r)=>{i&&i(),this.animations[r].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function fi({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const pi=(t,e,n,s={},i,r)=>o=>{const a=Mt(s,t)||{},l=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-R(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};fi(a)||(c={...c,...zn(t,c)}),c.duration&&(c.duration=R(c.duration)),c.repeatDelay&&(c.repeatDelay=R(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),Vt.skipAnimations&&(h=!0,c.duration=0,c.delay=0),h&&!r&&e.get()!==void 0){const d=oe(c.keyframes,a);if(d!==void 0)return D.update(()=>{c.onUpdate(d),c.onComplete()}),new di([])}return!r&&ot.supports(c)?new ot(c):new Ue(c)};function mi(t,e){t.indexOf(e)===-1&&t.push(e)}function gi(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class un{constructor(){this.subscriptions=[]}add(e){return mi(this.subscriptions,e),()=>gi(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const at=30,vi=t=>!isNaN(parseFloat(t));class yi{constructor(e,n={}){this.version="11.15.0",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const r=K.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=K.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=vi(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new un);const s=this.events[e].add(n);return e==="change"?()=>{s(),D.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=K.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>at)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,at);return Jt(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ie(t,e){return new yi(t,e)}function bi(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ie(n))}function Ti(t,e){const n=re(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const o in r){const a=wn(r[o]);bi(t,o,a)}}function Vi(t){return t.props[xn]}function Ai(t){return!!(E(t)&&t.add)}function Si(t,e){const n=t.getValue("willChange");if(Ai(n))return n.add(e)}function wi({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function cn(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);const u=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const h in l){const d=t.getValue(h,(r=t.latestValues[h])!==null&&r!==void 0?r:null),f=l[h];if(f===void 0||c&&wi(c,h))continue;const T={delay:n,...Mt(o||{},h)};let v=!1;if(window.MotionHandoffAnimation){const p=Vi(t);if(p){const b=window.MotionHandoffAnimation(p,h,D);b!==null&&(T.startTime=b,v=!0)}}Si(t,h),d.start(pi(h,d,f,t.shouldReduceMotion&&X.has(h)?{type:!1}:T,t,v));const y=d.animation;y&&u.push(y)}return a&&Promise.all(u).then(()=>{D.update(()=>{a&&Ti(t,a)})}),u}function Me(t,e,n={}){var s;const i=re(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);const o=i?()=>Promise.all(cn(t,i,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:d}=r;return xi(t,e,c+u,h,d,n)}:()=>Promise.resolve(),{when:l}=r;if(l){const[u,c]=l==="beforeChildren"?[o,a]:[a,o];return u().then(()=>c())}else return Promise.all([o(),a(n.delay)])}function xi(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,l=i===1?(u=0)=>u*s:(u=0)=>a-u*s;return Array.from(t.variantChildren).sort(Pi).forEach((u,c)=>{u.notify("AnimationStart",e),o.push(Me(u,e,{...r,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(o)}function Pi(t,e){return t.sortNodePosition(e)}function Mi(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Me(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Me(t,e,n);else{const i=typeof e=="function"?re(t,e,n.custom):e;s=Promise.all(cn(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}const Ci=St.length;function hn(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?hn(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Ci;n++){const s=St[n],i=t.props[s];(At(i)||i===!1)&&(e[s]=i)}return e}const Fi=[...xt].reverse(),Di=xt.length;function Ii(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Mi(t,n,s)))}function Ei(t){let e=Ii(t),n=lt(),s=!0;const i=l=>(u,c)=>{var h;const d=re(t,c,l==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(d){const{transition:f,transitionEnd:T,...v}=d;u={...u,...v,...T}}return u};function r(l){e=l(t)}function o(l){const{props:u}=t,c=hn(t.parent)||{},h=[],d=new Set;let f={},T=1/0;for(let y=0;y<Di;y++){const p=Fi[y],b=n[p],V=u[p]!==void 0?u[p]:c[p],C=At(V),g=p===l?b.isActive:null;g===!1&&(T=y);let A=V===c[p]&&V!==u[p]&&C;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),b.protectedKeys={...f},!b.isActive&&g===null||!V&&!b.prevProp||wt(V)||typeof V=="boolean")continue;const w=Ri(b.prevProp,V);let m=w||p===l&&b.isActive&&!A&&C||y>T&&C,M=!1;const F=Array.isArray(V)?V:[V];let k=F.reduce(i(p),{});g===!1&&(k={});const{prevResolvedValues:We={}}=b,yn={...We,...k},ze=P=>{m=!0,d.has(P)&&(M=!0,d.delete(P)),b.needsAnimating[P]=!0;const I=t.getValue(P);I&&(I.liveStyle=!1)};for(const P in yn){const I=k[P],ue=We[P];if(f.hasOwnProperty(P))continue;let ce=!1;qe(I)&&qe(ue)?ce=!Pt(I,ue):ce=I!==ue,ce?I!=null?ze(P):d.add(P):I!==void 0&&d.has(P)?ze(P):b.protectedKeys[P]=!0}b.prevProp=V,b.prevResolvedValues=k,b.isActive&&(f={...f,...k}),s&&t.blockInitialAnimation&&(m=!1),m&&(!(A&&w)||M)&&h.push(...F.map(P=>({animation:P,options:{type:p}})))}if(d.size){const y={};d.forEach(p=>{const b=t.getBaseTarget(p),V=t.getValue(p);V&&(V.liveStyle=!0),y[p]=b??null}),h.push({animation:y})}let v=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(v=!1),s=!1,v?e(h):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=t.variantChildren)===null||c===void 0||c.forEach(d=>{var f;return(f=d.animationState)===null||f===void 0?void 0:f.setActive(l,u)}),n[l].isActive=u;const h=o(l);for(const d in n)n[d].protectedKeys={};return h}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n,reset:()=>{n=lt(),s=!0}}}function Ri(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Pt(e,t):!1}function N(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function lt(){return{animate:N(!0),whileInView:N(),whileHover:N(),whileTap:N(),whileDrag:N(),whileFocus:N(),exit:N()}}class z{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Oi extends z{constructor(e){super(e),e.animationState||(e.animationState=Ei(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();wt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let Ki=0;class Ni extends z{constructor(){super(...arguments),this.id=Ki++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const Bi={animation:{Feature:Oi},exit:{Feature:Ni}},Li={y:!1};function ki(){return Li.y}function _i(t,e,n){var s;if(t instanceof Element)return[t];if(typeof t=="string"){let i=document;const r=(s=void 0)!==null&&s!==void 0?s:i.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}function dn(t,e){const n=_i(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function ut(t){return e=>{e.pointerType==="touch"||ki()||t(e)}}function Gi(t,e,n={}){const[s,i,r]=dn(t,n),o=ut(a=>{const{target:l}=a,u=e(a);if(typeof u!="function"||!l)return;const c=ut(h=>{u(h),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,i)});return s.forEach(a=>{a.addEventListener("pointerenter",o,i)}),r}const fn=(t,e)=>e?t===e?!0:fn(t,e.parentElement):!1,Ui=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,ji=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Wi(t){return ji.has(t.tagName)||t.tabIndex!==-1}const H=new WeakSet;function ct(t){return e=>{e.key==="Enter"&&t(e)}}function me(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const zi=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=ct(()=>{if(H.has(n))return;me(n,"down");const i=ct(()=>{me(n,"up")}),r=()=>me(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function ht(t){return Ui(t)&&!0}function qi(t,e,n={}){const[s,i,r]=dn(t,n),o=a=>{const l=a.currentTarget;if(!ht(a)||H.has(l))return;H.add(l);const u=e(a),c=(f,T)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",d),!(!ht(f)||!H.has(l))&&(H.delete(l),typeof u=="function"&&u(f,{success:T}))},h=f=>{c(f,n.useGlobalTarget||fn(l,f.target))},d=f=>{c(f,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",d,i)};return s.forEach(a=>{!Wi(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,i),a.addEventListener("focus",u=>zi(u,i),i)}),r}function pn(t){return{point:{x:t.pageX,y:t.pageY}}}function dt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const ft=()=>({min:0,max:0}),mn=()=>({x:ft(),y:ft()});function Hi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function $i(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function Yi(t,e){return Hi($i(t.getBoundingClientRect(),e))}function pt(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&D.postRender(()=>r(e,pn(e)))}class Xi extends z{mount(){const{current:e}=this.node;e&&(this.unmount=Gi(e,n=>(pt(this.node,n,"Start"),s=>pt(this.node,s,"End"))))}unmount(){}}class Zi extends z{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ae(dt(this.node.current,"focus",()=>this.onFocus()),dt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function mt(t,e,n){const{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&D.postRender(()=>r(e,pn(e)))}class Ji extends z{mount(){const{current:e}=this.node;e&&(this.unmount=qi(e,n=>(mt(this.node,n,"Start"),(s,{success:i})=>mt(this.node,s,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ce=new WeakMap,ge=new WeakMap,Qi=t=>{const e=Ce.get(t.target);e&&e(t)},er=t=>{t.forEach(Qi)};function tr({root:t,...e}){const n=t||document;ge.has(n)||ge.set(n,{});const s=ge.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(er,{root:t,...e})),s[i]}function nr(t,e,n){const s=tr(e);return Ce.set(t,n),s.observe(t),()=>{Ce.delete(t),s.unobserve(t)}}const sr={some:0,all:1};class ir extends z{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:sr[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,r&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),d=u?c:h;d&&d(l)};return nr(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(rr(e,n))&&this.startObserver()}unmount(){}}function rr({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const or={inView:{Feature:ir},tap:{Feature:Ji},focus:{Feature:Zi},hover:{Feature:Xi}},Fe={current:null},gn={current:!1};function ar(){if(gn.current=!0,!!Pn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Fe.current=t.matches;t.addListener(e),e()}else Fe.current=!1}function lr(t,e,n){for(const s in e){const i=e[s],r=n[s];if(E(i))t.addValue(s,i);else if(E(r))t.addValue(s,ie(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const o=t.getValue(s);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=t.getStaticValue(s);t.addValue(s,ie(o!==void 0?o:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const gt=new WeakMap,ur=[..._t,x,W],cr=t=>ur.find(kt(t)),vt=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class hr{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Oe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=K.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,D.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Mn(n),this.isVariantNode=Cn(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const f=h[d];l[d]!==void 0&&E(f)&&f.set(l[d],!1)}}mount(e){this.current=e,gt.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),gn.current||ar(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Fe.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){gt.delete(this.current),this.projection&&this.projection.unmount(),ye(this.notifyUpdate),ye(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=X.has(e),i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&D.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r(),o&&o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in He){const n=He[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):mn()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<vt.length;s++){const i=vt[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,o=e[r];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=lr(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ie(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Bt(i)||Nt(i))?i=parseFloat(i):!cr(i)&&W.test(n)&&(i=$t(e,n)),this.setBaseTarget(e,E(i)?i.get():i)),E(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const o=yt(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[e])}if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!E(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new un),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class vn extends hr{constructor(){super(...arguments),this.KeyframeResolver=Yt}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;E(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function dr(t){return window.getComputedStyle(t)}class fr extends vn{constructor(){super(...arguments),this.type="html",this.renderInstance=Fn}readValueFromInstance(e,n){if(X.has(n)){const s=Be(n);return s&&s.default||0}else{const s=dr(e),i=(Dn(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Yi(e,n)}build(e,n,s){In(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return En(e,n,s)}}class pr extends vn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=mn}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(X.has(n)){const s=Be(n);return s&&s.default||0}return n=On.has(n)?n:Rn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Kn(e,n,s)}build(e,n,s){Nn(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){Bn(e,n,s,i)}mount(e){this.isSVGTag=Ln(e.tagName),super.mount(e)}}const mr=(t,e)=>kn(t)?new pr(e):new fr(e,{allowProjection:t!==_n.Fragment}),gr={renderer:mr,...Bi,...or};var Tr=gr;export{Tr as default};

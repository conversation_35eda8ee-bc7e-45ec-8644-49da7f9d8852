function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
function t0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const l=Object.getOwnPropertyDescriptor(r,o);l&&Object.defineProperty(e,o,l.get?l:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const a of l.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerPolicy&&(l.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?l.credentials="include":o.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();function Cp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ep={exports:{}},Aa={},$p={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var al=Symbol.for("react.element"),n0=Symbol.for("react.portal"),r0=Symbol.for("react.fragment"),o0=Symbol.for("react.strict_mode"),l0=Symbol.for("react.profiler"),a0=Symbol.for("react.provider"),i0=Symbol.for("react.context"),s0=Symbol.for("react.forward_ref"),u0=Symbol.for("react.suspense"),c0=Symbol.for("react.memo"),d0=Symbol.for("react.lazy"),Zc=Symbol.iterator;function f0(e){return e===null||typeof e!="object"?null:(e=Zc&&e[Zc]||e["@@iterator"],typeof e=="function"?e:null)}var kp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Pp=Object.assign,Tp={};function Kr(e,t,n){this.props=e,this.context=t,this.refs=Tp,this.updater=n||kp}Kr.prototype.isReactComponent={};Kr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Kr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Np(){}Np.prototype=Kr.prototype;function Cu(e,t,n){this.props=e,this.context=t,this.refs=Tp,this.updater=n||kp}var Eu=Cu.prototype=new Np;Eu.constructor=Cu;Pp(Eu,Kr.prototype);Eu.isPureReactComponent=!0;var Jc=Array.isArray,_p=Object.prototype.hasOwnProperty,$u={current:null},Mp={key:!0,ref:!0,__self:!0,__source:!0};function Lp(e,t,n){var r,o={},l=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(l=""+t.key),t)_p.call(t,r)&&!Mp.hasOwnProperty(r)&&(o[r]=t[r]);var i=arguments.length-2;if(i===1)o.children=n;else if(1<i){for(var s=Array(i),u=0;u<i;u++)s[u]=arguments[u+2];o.children=s}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)o[r]===void 0&&(o[r]=i[r]);return{$$typeof:al,type:e,key:l,ref:a,props:o,_owner:$u.current}}function p0(e,t){return{$$typeof:al,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ku(e){return typeof e=="object"&&e!==null&&e.$$typeof===al}function m0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var qc=/\/+/g;function gi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?m0(""+e.key):t.toString(36)}function Ql(e,t,n,r,o){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(l){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case al:case n0:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+gi(a,0):r,Jc(o)?(n="",e!=null&&(n=e.replace(qc,"$&/")+"/"),Ql(o,t,n,"",function(u){return u})):o!=null&&(ku(o)&&(o=p0(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(qc,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",Jc(e))for(var i=0;i<e.length;i++){l=e[i];var s=r+gi(l,i);a+=Ql(l,t,n,s,o)}else if(s=f0(e),typeof s=="function")for(e=s.call(e),i=0;!(l=e.next()).done;)l=l.value,s=r+gi(l,i++),a+=Ql(l,t,n,s,o);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function wl(e,t,n){if(e==null)return e;var r=[],o=0;return Ql(e,r,"","",function(l){return t.call(n,l,o++)}),r}function h0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Be={current:null},Yl={transition:null},v0={ReactCurrentDispatcher:Be,ReactCurrentBatchConfig:Yl,ReactCurrentOwner:$u};function Ip(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:wl,forEach:function(e,t,n){wl(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return wl(e,function(){t++}),t},toArray:function(e){return wl(e,function(t){return t})||[]},only:function(e){if(!ku(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=Kr;J.Fragment=r0;J.Profiler=l0;J.PureComponent=Cu;J.StrictMode=o0;J.Suspense=u0;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=v0;J.act=Ip;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Pp({},e.props),o=e.key,l=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,a=$u.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(s in t)_p.call(t,s)&&!Mp.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&i!==void 0?i[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){i=Array(s);for(var u=0;u<s;u++)i[u]=arguments[u+2];r.children=i}return{$$typeof:al,type:e.type,key:o,ref:l,props:r,_owner:a}};J.createContext=function(e){return e={$$typeof:i0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:a0,_context:e},e.Consumer=e};J.createElement=Lp;J.createFactory=function(e){var t=Lp.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:s0,render:e}};J.isValidElement=ku;J.lazy=function(e){return{$$typeof:d0,_payload:{_status:-1,_result:e},_init:h0}};J.memo=function(e,t){return{$$typeof:c0,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Yl.transition;Yl.transition={};try{e()}finally{Yl.transition=t}};J.unstable_act=Ip;J.useCallback=function(e,t){return Be.current.useCallback(e,t)};J.useContext=function(e){return Be.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return Be.current.useDeferredValue(e)};J.useEffect=function(e,t){return Be.current.useEffect(e,t)};J.useId=function(){return Be.current.useId()};J.useImperativeHandle=function(e,t,n){return Be.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return Be.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return Be.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return Be.current.useMemo(e,t)};J.useReducer=function(e,t,n){return Be.current.useReducer(e,t,n)};J.useRef=function(e){return Be.current.useRef(e)};J.useState=function(e){return Be.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return Be.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return Be.current.useTransition()};J.version="18.3.1";$p.exports=J;var p=$p.exports;const q=Cp(p),g0=t0({__proto__:null,default:q},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var y0=p,b0=Symbol.for("react.element"),w0=Symbol.for("react.fragment"),x0=Object.prototype.hasOwnProperty,S0=y0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,C0={key:!0,ref:!0,__self:!0,__source:!0};function Rp(e,t,n){var r,o={},l=null,a=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)x0.call(t,r)&&!C0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:b0,type:e,key:l,ref:a,props:o,_owner:S0.current}}Aa.Fragment=w0;Aa.jsx=Rp;Aa.jsxs=Rp;Ep.exports=Aa;var w=Ep.exports,os={},jp={exports:{}},it={},zp={exports:{}},Op={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,R){var L=T.length;T.push(R);e:for(;0<L;){var V=L-1>>>1,H=T[V];if(0<o(H,R))T[V]=R,T[L]=H,L=V;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var R=T[0],L=T.pop();if(L!==R){T[0]=L;e:for(var V=0,H=T.length,te=H>>>1;V<te;){var K=2*(V+1)-1,re=T[K],G=K+1,we=T[G];if(0>o(re,L))G<H&&0>o(we,re)?(T[V]=we,T[G]=L,V=G):(T[V]=re,T[K]=L,V=K);else if(G<H&&0>o(we,L))T[V]=we,T[G]=L,V=G;else break e}}return R}function o(T,R){var L=T.sortIndex-R.sortIndex;return L!==0?L:T.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var a=Date,i=a.now();e.unstable_now=function(){return a.now()-i}}var s=[],u=[],c=1,d=null,f=3,v=!1,y=!1,b=!1,S=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=T)r(u),R.sortIndex=R.expirationTime,t(s,R);else break;R=n(u)}}function x(T){if(b=!1,g(T),!y)if(n(s)!==null)y=!0,N(E);else{var R=n(u);R!==null&&F(x,R.startTime-T)}}function E(T,R){y=!1,b&&(b=!1,h(M),M=-1),v=!0;var L=f;try{for(g(R),d=n(s);d!==null&&(!(d.expirationTime>R)||T&&!C());){var V=d.callback;if(typeof V=="function"){d.callback=null,f=d.priorityLevel;var H=V(d.expirationTime<=R);R=e.unstable_now(),typeof H=="function"?d.callback=H:d===n(s)&&r(s),g(R)}else r(s);d=n(s)}if(d!==null)var te=!0;else{var K=n(u);K!==null&&F(x,K.startTime-R),te=!1}return te}finally{d=null,f=L,v=!1}}var $=!1,_=null,M=-1,O=5,I=-1;function C(){return!(e.unstable_now()-I<O)}function P(){if(_!==null){var T=e.unstable_now();I=T;var R=!0;try{R=_(!0,T)}finally{R?z():($=!1,_=null)}}else $=!1}var z;if(typeof m=="function")z=function(){m(P)};else if(typeof MessageChannel<"u"){var k=new MessageChannel,D=k.port2;k.port1.onmessage=P,z=function(){D.postMessage(null)}}else z=function(){S(P,0)};function N(T){_=T,$||($=!0,z())}function F(T,R){M=S(function(){T(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){y||v||(y=!0,N(E))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(T){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var L=f;f=R;try{return T()}finally{f=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,R){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var L=f;f=T;try{return R()}finally{f=L}},e.unstable_scheduleCallback=function(T,R,L){var V=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?V+L:V):L=V,T){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=L+H,T={id:c++,callback:R,priorityLevel:T,startTime:L,expirationTime:H,sortIndex:-1},L>V?(T.sortIndex=L,t(u,T),n(s)===null&&T===n(u)&&(b?(h(M),M=-1):b=!0,F(x,L-V))):(T.sortIndex=H,t(s,T),y||v||(y=!0,N(E))),T},e.unstable_shouldYield=C,e.unstable_wrapCallback=function(T){var R=f;return function(){var L=f;f=R;try{return T.apply(this,arguments)}finally{f=L}}}})(Op);zp.exports=Op;var E0=zp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $0=p,at=E0;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Fp=new Set,Oo={};function ar(e,t){Or(e,t),Or(e+"Capture",t)}function Or(e,t){for(Oo[e]=t,e=0;e<t.length;e++)Fp.add(t[e])}var Jt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ls=Object.prototype.hasOwnProperty,k0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ed={},td={};function P0(e){return ls.call(td,e)?!0:ls.call(ed,e)?!1:k0.test(e)?td[e]=!0:(ed[e]=!0,!1)}function T0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function N0(e,t,n,r){if(t===null||typeof t>"u"||T0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,o,l,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=a}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Pu=/[\-:]([a-z])/g;function Tu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Pu,Tu);Re[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Pu,Tu);Re[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Pu,Tu);Re[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function Nu(e,t,n,r){var o=Re.hasOwnProperty(t)?Re[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(N0(t,n,o,r)&&(n=null),r||o===null?P0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var nn=$0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,xl=Symbol.for("react.element"),mr=Symbol.for("react.portal"),hr=Symbol.for("react.fragment"),_u=Symbol.for("react.strict_mode"),as=Symbol.for("react.profiler"),Ap=Symbol.for("react.provider"),Dp=Symbol.for("react.context"),Mu=Symbol.for("react.forward_ref"),is=Symbol.for("react.suspense"),ss=Symbol.for("react.suspense_list"),Lu=Symbol.for("react.memo"),cn=Symbol.for("react.lazy"),Wp=Symbol.for("react.offscreen"),nd=Symbol.iterator;function oo(e){return e===null||typeof e!="object"?null:(e=nd&&e[nd]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,yi;function bo(e){if(yi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);yi=t&&t[1]||""}return`
`+yi+e}var bi=!1;function wi(e,t){if(!e||bi)return"";bi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),l=r.stack.split(`
`),a=o.length-1,i=l.length-1;1<=a&&0<=i&&o[a]!==l[i];)i--;for(;1<=a&&0<=i;a--,i--)if(o[a]!==l[i]){if(a!==1||i!==1)do if(a--,i--,0>i||o[a]!==l[i]){var s=`
`+o[a].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=a&&0<=i);break}}}finally{bi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?bo(e):""}function _0(e){switch(e.tag){case 5:return bo(e.type);case 16:return bo("Lazy");case 13:return bo("Suspense");case 19:return bo("SuspenseList");case 0:case 2:case 15:return e=wi(e.type,!1),e;case 11:return e=wi(e.type.render,!1),e;case 1:return e=wi(e.type,!0),e;default:return""}}function us(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case hr:return"Fragment";case mr:return"Portal";case as:return"Profiler";case _u:return"StrictMode";case is:return"Suspense";case ss:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dp:return(e.displayName||"Context")+".Consumer";case Ap:return(e._context.displayName||"Context")+".Provider";case Mu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Lu:return t=e.displayName||null,t!==null?t:us(e.type)||"Memo";case cn:t=e._payload,e=e._init;try{return us(e(t))}catch{}}return null}function M0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return us(t);case 8:return t===_u?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Nn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Vp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function L0(e){var t=Vp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,l.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Sl(e){e._valueTracker||(e._valueTracker=L0(e))}function Bp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Vp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ia(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function cs(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function rd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Nn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Hp(e,t){t=t.checked,t!=null&&Nu(e,"checked",t,!1)}function ds(e,t){Hp(e,t);var n=Nn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?fs(e,t.type,n):t.hasOwnProperty("defaultValue")&&fs(e,t.type,Nn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function od(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function fs(e,t,n){(t!=="number"||ia(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var wo=Array.isArray;function Pr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Nn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ps(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ld(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(wo(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Nn(n)}}function Up(e,t){var n=Nn(t.value),r=Nn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ad(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Kp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ms(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Kp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Cl,Gp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Cl=Cl||document.createElement("div"),Cl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Cl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Fo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Po={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},I0=["Webkit","ms","Moz","O"];Object.keys(Po).forEach(function(e){I0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Po[t]=Po[e]})});function Qp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Po.hasOwnProperty(e)&&Po[e]?(""+t).trim():t+"px"}function Yp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Qp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var R0=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function hs(e,t){if(t){if(R0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function vs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gs=null;function Iu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ys=null,Tr=null,Nr=null;function id(e){if(e=ul(e)){if(typeof ys!="function")throw Error(j(280));var t=e.stateNode;t&&(t=Ha(t),ys(e.stateNode,e.type,t))}}function Xp(e){Tr?Nr?Nr.push(e):Nr=[e]:Tr=e}function Zp(){if(Tr){var e=Tr,t=Nr;if(Nr=Tr=null,id(e),t)for(e=0;e<t.length;e++)id(t[e])}}function Jp(e,t){return e(t)}function qp(){}var xi=!1;function em(e,t,n){if(xi)return e(t,n);xi=!0;try{return Jp(e,t,n)}finally{xi=!1,(Tr!==null||Nr!==null)&&(qp(),Zp())}}function Ao(e,t){var n=e.stateNode;if(n===null)return null;var r=Ha(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var bs=!1;if(Jt)try{var lo={};Object.defineProperty(lo,"passive",{get:function(){bs=!0}}),window.addEventListener("test",lo,lo),window.removeEventListener("test",lo,lo)}catch{bs=!1}function j0(e,t,n,r,o,l,a,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var To=!1,sa=null,ua=!1,ws=null,z0={onError:function(e){To=!0,sa=e}};function O0(e,t,n,r,o,l,a,i,s){To=!1,sa=null,j0.apply(z0,arguments)}function F0(e,t,n,r,o,l,a,i,s){if(O0.apply(this,arguments),To){if(To){var u=sa;To=!1,sa=null}else throw Error(j(198));ua||(ua=!0,ws=u)}}function ir(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function tm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function sd(e){if(ir(e)!==e)throw Error(j(188))}function A0(e){var t=e.alternate;if(!t){if(t=ir(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var l=o.alternate;if(l===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===l.child){for(l=o.child;l;){if(l===n)return sd(o),e;if(l===r)return sd(o),t;l=l.sibling}throw Error(j(188))}if(n.return!==r.return)n=o,r=l;else{for(var a=!1,i=o.child;i;){if(i===n){a=!0,n=o,r=l;break}if(i===r){a=!0,r=o,n=l;break}i=i.sibling}if(!a){for(i=l.child;i;){if(i===n){a=!0,n=l,r=o;break}if(i===r){a=!0,r=l,n=o;break}i=i.sibling}if(!a)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function nm(e){return e=A0(e),e!==null?rm(e):null}function rm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=rm(e);if(t!==null)return t;e=e.sibling}return null}var om=at.unstable_scheduleCallback,ud=at.unstable_cancelCallback,D0=at.unstable_shouldYield,W0=at.unstable_requestPaint,ye=at.unstable_now,V0=at.unstable_getCurrentPriorityLevel,Ru=at.unstable_ImmediatePriority,lm=at.unstable_UserBlockingPriority,ca=at.unstable_NormalPriority,B0=at.unstable_LowPriority,am=at.unstable_IdlePriority,Da=null,Ot=null;function H0(e){if(Ot&&typeof Ot.onCommitFiberRoot=="function")try{Ot.onCommitFiberRoot(Da,e,void 0,(e.current.flags&128)===128)}catch{}}var Pt=Math.clz32?Math.clz32:G0,U0=Math.log,K0=Math.LN2;function G0(e){return e>>>=0,e===0?32:31-(U0(e)/K0|0)|0}var El=64,$l=4194304;function xo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function da(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,l=e.pingedLanes,a=n&268435455;if(a!==0){var i=a&~o;i!==0?r=xo(i):(l&=a,l!==0&&(r=xo(l)))}else a=n&~o,a!==0?r=xo(a):l!==0&&(r=xo(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,l=t&-t,o>=l||o===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Pt(t),o=1<<n,r|=e[n],t&=~o;return r}function Q0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Y0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,l=e.pendingLanes;0<l;){var a=31-Pt(l),i=1<<a,s=o[a];s===-1?(!(i&n)||i&r)&&(o[a]=Q0(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}function xs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function im(){var e=El;return El<<=1,!(El&4194240)&&(El=64),e}function Si(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function il(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Pt(t),e[t]=n}function X0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Pt(n),l=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~l}}function ju(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Pt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var oe=0;function sm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var um,zu,cm,dm,fm,Ss=!1,kl=[],wn=null,xn=null,Sn=null,Do=new Map,Wo=new Map,fn=[],Z0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cd(e,t){switch(e){case"focusin":case"focusout":wn=null;break;case"dragenter":case"dragleave":xn=null;break;case"mouseover":case"mouseout":Sn=null;break;case"pointerover":case"pointerout":Do.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wo.delete(t.pointerId)}}function ao(e,t,n,r,o,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[o]},t!==null&&(t=ul(t),t!==null&&zu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function J0(e,t,n,r,o){switch(t){case"focusin":return wn=ao(wn,e,t,n,r,o),!0;case"dragenter":return xn=ao(xn,e,t,n,r,o),!0;case"mouseover":return Sn=ao(Sn,e,t,n,r,o),!0;case"pointerover":var l=o.pointerId;return Do.set(l,ao(Do.get(l)||null,e,t,n,r,o)),!0;case"gotpointercapture":return l=o.pointerId,Wo.set(l,ao(Wo.get(l)||null,e,t,n,r,o)),!0}return!1}function pm(e){var t=Vn(e.target);if(t!==null){var n=ir(t);if(n!==null){if(t=n.tag,t===13){if(t=tm(n),t!==null){e.blockedOn=t,fm(e.priority,function(){cm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Cs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);gs=r,n.target.dispatchEvent(r),gs=null}else return t=ul(n),t!==null&&zu(t),e.blockedOn=n,!1;t.shift()}return!0}function dd(e,t,n){Xl(e)&&n.delete(t)}function q0(){Ss=!1,wn!==null&&Xl(wn)&&(wn=null),xn!==null&&Xl(xn)&&(xn=null),Sn!==null&&Xl(Sn)&&(Sn=null),Do.forEach(dd),Wo.forEach(dd)}function io(e,t){e.blockedOn===t&&(e.blockedOn=null,Ss||(Ss=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,q0)))}function Vo(e){function t(o){return io(o,e)}if(0<kl.length){io(kl[0],e);for(var n=1;n<kl.length;n++){var r=kl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(wn!==null&&io(wn,e),xn!==null&&io(xn,e),Sn!==null&&io(Sn,e),Do.forEach(t),Wo.forEach(t),n=0;n<fn.length;n++)r=fn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<fn.length&&(n=fn[0],n.blockedOn===null);)pm(n),n.blockedOn===null&&fn.shift()}var _r=nn.ReactCurrentBatchConfig,fa=!0;function ey(e,t,n,r){var o=oe,l=_r.transition;_r.transition=null;try{oe=1,Ou(e,t,n,r)}finally{oe=o,_r.transition=l}}function ty(e,t,n,r){var o=oe,l=_r.transition;_r.transition=null;try{oe=4,Ou(e,t,n,r)}finally{oe=o,_r.transition=l}}function Ou(e,t,n,r){if(fa){var o=Cs(e,t,n,r);if(o===null)Li(e,t,r,pa,n),cd(e,r);else if(J0(o,e,t,n,r))r.stopPropagation();else if(cd(e,r),t&4&&-1<Z0.indexOf(e)){for(;o!==null;){var l=ul(o);if(l!==null&&um(l),l=Cs(e,t,n,r),l===null&&Li(e,t,r,pa,n),l===o)break;o=l}o!==null&&r.stopPropagation()}else Li(e,t,r,null,n)}}var pa=null;function Cs(e,t,n,r){if(pa=null,e=Iu(r),e=Vn(e),e!==null)if(t=ir(e),t===null)e=null;else if(n=t.tag,n===13){if(e=tm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return pa=e,null}function mm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(V0()){case Ru:return 1;case lm:return 4;case ca:case B0:return 16;case am:return 536870912;default:return 16}default:return 16}}var hn=null,Fu=null,Zl=null;function hm(){if(Zl)return Zl;var e,t=Fu,n=t.length,r,o="value"in hn?hn.value:hn.textContent,l=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[l-r];r++);return Zl=o.slice(e,1<r?1-r:void 0)}function Jl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Pl(){return!0}function fd(){return!1}function st(e){function t(n,r,o,l,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=l,this.target=a,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(l):l[i]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Pl:fd,this.isPropagationStopped=fd,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Pl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Pl)},persist:function(){},isPersistent:Pl}),t}var Gr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Au=st(Gr),sl=me({},Gr,{view:0,detail:0}),ny=st(sl),Ci,Ei,so,Wa=me({},sl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Du,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==so&&(so&&e.type==="mousemove"?(Ci=e.screenX-so.screenX,Ei=e.screenY-so.screenY):Ei=Ci=0,so=e),Ci)},movementY:function(e){return"movementY"in e?e.movementY:Ei}}),pd=st(Wa),ry=me({},Wa,{dataTransfer:0}),oy=st(ry),ly=me({},sl,{relatedTarget:0}),$i=st(ly),ay=me({},Gr,{animationName:0,elapsedTime:0,pseudoElement:0}),iy=st(ay),sy=me({},Gr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),uy=st(sy),cy=me({},Gr,{data:0}),md=st(cy),dy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},py={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function my(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=py[e])?!!t[e]:!1}function Du(){return my}var hy=me({},sl,{key:function(e){if(e.key){var t=dy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Jl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?fy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Du,charCode:function(e){return e.type==="keypress"?Jl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Jl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),vy=st(hy),gy=me({},Wa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hd=st(gy),yy=me({},sl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Du}),by=st(yy),wy=me({},Gr,{propertyName:0,elapsedTime:0,pseudoElement:0}),xy=st(wy),Sy=me({},Wa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Cy=st(Sy),Ey=[9,13,27,32],Wu=Jt&&"CompositionEvent"in window,No=null;Jt&&"documentMode"in document&&(No=document.documentMode);var $y=Jt&&"TextEvent"in window&&!No,vm=Jt&&(!Wu||No&&8<No&&11>=No),vd=" ",gd=!1;function gm(e,t){switch(e){case"keyup":return Ey.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ym(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var vr=!1;function ky(e,t){switch(e){case"compositionend":return ym(t);case"keypress":return t.which!==32?null:(gd=!0,vd);case"textInput":return e=t.data,e===vd&&gd?null:e;default:return null}}function Py(e,t){if(vr)return e==="compositionend"||!Wu&&gm(e,t)?(e=hm(),Zl=Fu=hn=null,vr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return vm&&t.locale!=="ko"?null:t.data;default:return null}}var Ty={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ty[e.type]:t==="textarea"}function bm(e,t,n,r){Xp(r),t=ma(t,"onChange"),0<t.length&&(n=new Au("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var _o=null,Bo=null;function Ny(e){_m(e,0)}function Va(e){var t=br(e);if(Bp(t))return e}function _y(e,t){if(e==="change")return t}var wm=!1;if(Jt){var ki;if(Jt){var Pi="oninput"in document;if(!Pi){var bd=document.createElement("div");bd.setAttribute("oninput","return;"),Pi=typeof bd.oninput=="function"}ki=Pi}else ki=!1;wm=ki&&(!document.documentMode||9<document.documentMode)}function wd(){_o&&(_o.detachEvent("onpropertychange",xm),Bo=_o=null)}function xm(e){if(e.propertyName==="value"&&Va(Bo)){var t=[];bm(t,Bo,e,Iu(e)),em(Ny,t)}}function My(e,t,n){e==="focusin"?(wd(),_o=t,Bo=n,_o.attachEvent("onpropertychange",xm)):e==="focusout"&&wd()}function Ly(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Va(Bo)}function Iy(e,t){if(e==="click")return Va(t)}function Ry(e,t){if(e==="input"||e==="change")return Va(t)}function jy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Nt=typeof Object.is=="function"?Object.is:jy;function Ho(e,t){if(Nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ls.call(t,o)||!Nt(e[o],t[o]))return!1}return!0}function xd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sd(e,t){var n=xd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xd(n)}}function Sm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Sm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Cm(){for(var e=window,t=ia();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ia(e.document)}return t}function Vu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function zy(e){var t=Cm(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Sm(n.ownerDocument.documentElement,n)){if(r!==null&&Vu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,l=Math.min(r.start,o);r=r.end===void 0?l:Math.min(r.end,o),!e.extend&&l>r&&(o=r,r=l,l=o),o=Sd(n,l);var a=Sd(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Oy=Jt&&"documentMode"in document&&11>=document.documentMode,gr=null,Es=null,Mo=null,$s=!1;function Cd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;$s||gr==null||gr!==ia(r)||(r=gr,"selectionStart"in r&&Vu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Mo&&Ho(Mo,r)||(Mo=r,r=ma(Es,"onSelect"),0<r.length&&(t=new Au("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function Tl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var yr={animationend:Tl("Animation","AnimationEnd"),animationiteration:Tl("Animation","AnimationIteration"),animationstart:Tl("Animation","AnimationStart"),transitionend:Tl("Transition","TransitionEnd")},Ti={},Em={};Jt&&(Em=document.createElement("div").style,"AnimationEvent"in window||(delete yr.animationend.animation,delete yr.animationiteration.animation,delete yr.animationstart.animation),"TransitionEvent"in window||delete yr.transitionend.transition);function Ba(e){if(Ti[e])return Ti[e];if(!yr[e])return e;var t=yr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Em)return Ti[e]=t[n];return e}var $m=Ba("animationend"),km=Ba("animationiteration"),Pm=Ba("animationstart"),Tm=Ba("transitionend"),Nm=new Map,Ed="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mn(e,t){Nm.set(e,t),ar(t,[e])}for(var Ni=0;Ni<Ed.length;Ni++){var _i=Ed[Ni],Fy=_i.toLowerCase(),Ay=_i[0].toUpperCase()+_i.slice(1);Mn(Fy,"on"+Ay)}Mn($m,"onAnimationEnd");Mn(km,"onAnimationIteration");Mn(Pm,"onAnimationStart");Mn("dblclick","onDoubleClick");Mn("focusin","onFocus");Mn("focusout","onBlur");Mn(Tm,"onTransitionEnd");Or("onMouseEnter",["mouseout","mouseover"]);Or("onMouseLeave",["mouseout","mouseover"]);Or("onPointerEnter",["pointerout","pointerover"]);Or("onPointerLeave",["pointerout","pointerover"]);ar("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ar("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ar("onBeforeInput",["compositionend","keypress","textInput","paste"]);ar("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var So="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dy=new Set("cancel close invalid load scroll toggle".split(" ").concat(So));function $d(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,F0(r,t,void 0,e),e.currentTarget=null}function _m(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var a=r.length-1;0<=a;a--){var i=r[a],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&o.isPropagationStopped())break e;$d(o,i,u),l=s}else for(a=0;a<r.length;a++){if(i=r[a],s=i.instance,u=i.currentTarget,i=i.listener,s!==l&&o.isPropagationStopped())break e;$d(o,i,u),l=s}}}if(ua)throw e=ws,ua=!1,ws=null,e}function ue(e,t){var n=t[_s];n===void 0&&(n=t[_s]=new Set);var r=e+"__bubble";n.has(r)||(Mm(t,e,2,!1),n.add(r))}function Mi(e,t,n){var r=0;t&&(r|=4),Mm(n,e,r,t)}var Nl="_reactListening"+Math.random().toString(36).slice(2);function Uo(e){if(!e[Nl]){e[Nl]=!0,Fp.forEach(function(n){n!=="selectionchange"&&(Dy.has(n)||Mi(n,!1,e),Mi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Nl]||(t[Nl]=!0,Mi("selectionchange",!1,t))}}function Mm(e,t,n,r){switch(mm(t)){case 1:var o=ey;break;case 4:o=ty;break;default:o=Ou}n=o.bind(null,t,n,e),o=void 0,!bs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Li(e,t,n,r,o){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var i=r.stateNode.containerInfo;if(i===o||i.nodeType===8&&i.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var s=a.tag;if((s===3||s===4)&&(s=a.stateNode.containerInfo,s===o||s.nodeType===8&&s.parentNode===o))return;a=a.return}for(;i!==null;){if(a=Vn(i),a===null)return;if(s=a.tag,s===5||s===6){r=l=a;continue e}i=i.parentNode}}r=r.return}em(function(){var u=l,c=Iu(n),d=[];e:{var f=Nm.get(e);if(f!==void 0){var v=Au,y=e;switch(e){case"keypress":if(Jl(n)===0)break e;case"keydown":case"keyup":v=vy;break;case"focusin":y="focus",v=$i;break;case"focusout":y="blur",v=$i;break;case"beforeblur":case"afterblur":v=$i;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=pd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=oy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=by;break;case $m:case km:case Pm:v=iy;break;case Tm:v=xy;break;case"scroll":v=ny;break;case"wheel":v=Cy;break;case"copy":case"cut":case"paste":v=uy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=hd}var b=(t&4)!==0,S=!b&&e==="scroll",h=b?f!==null?f+"Capture":null:f;b=[];for(var m=u,g;m!==null;){g=m;var x=g.stateNode;if(g.tag===5&&x!==null&&(g=x,h!==null&&(x=Ao(m,h),x!=null&&b.push(Ko(m,x,g)))),S)break;m=m.return}0<b.length&&(f=new v(f,y,null,n,c),d.push({event:f,listeners:b}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",f&&n!==gs&&(y=n.relatedTarget||n.fromElement)&&(Vn(y)||y[qt]))break e;if((v||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,v?(y=n.relatedTarget||n.toElement,v=u,y=y?Vn(y):null,y!==null&&(S=ir(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(v=null,y=u),v!==y)){if(b=pd,x="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(b=hd,x="onPointerLeave",h="onPointerEnter",m="pointer"),S=v==null?f:br(v),g=y==null?f:br(y),f=new b(x,m+"leave",v,n,c),f.target=S,f.relatedTarget=g,x=null,Vn(c)===u&&(b=new b(h,m+"enter",y,n,c),b.target=g,b.relatedTarget=S,x=b),S=x,v&&y)t:{for(b=v,h=y,m=0,g=b;g;g=dr(g))m++;for(g=0,x=h;x;x=dr(x))g++;for(;0<m-g;)b=dr(b),m--;for(;0<g-m;)h=dr(h),g--;for(;m--;){if(b===h||h!==null&&b===h.alternate)break t;b=dr(b),h=dr(h)}b=null}else b=null;v!==null&&kd(d,f,v,b,!1),y!==null&&S!==null&&kd(d,S,y,b,!0)}}e:{if(f=u?br(u):window,v=f.nodeName&&f.nodeName.toLowerCase(),v==="select"||v==="input"&&f.type==="file")var E=_y;else if(yd(f))if(wm)E=Ry;else{E=Ly;var $=My}else(v=f.nodeName)&&v.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(E=Iy);if(E&&(E=E(e,u))){bm(d,E,n,c);break e}$&&$(e,f,u),e==="focusout"&&($=f._wrapperState)&&$.controlled&&f.type==="number"&&fs(f,"number",f.value)}switch($=u?br(u):window,e){case"focusin":(yd($)||$.contentEditable==="true")&&(gr=$,Es=u,Mo=null);break;case"focusout":Mo=Es=gr=null;break;case"mousedown":$s=!0;break;case"contextmenu":case"mouseup":case"dragend":$s=!1,Cd(d,n,c);break;case"selectionchange":if(Oy)break;case"keydown":case"keyup":Cd(d,n,c)}var _;if(Wu)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else vr?gm(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(vm&&n.locale!=="ko"&&(vr||M!=="onCompositionStart"?M==="onCompositionEnd"&&vr&&(_=hm()):(hn=c,Fu="value"in hn?hn.value:hn.textContent,vr=!0)),$=ma(u,M),0<$.length&&(M=new md(M,e,null,n,c),d.push({event:M,listeners:$}),_?M.data=_:(_=ym(n),_!==null&&(M.data=_)))),(_=$y?ky(e,n):Py(e,n))&&(u=ma(u,"onBeforeInput"),0<u.length&&(c=new md("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=_))}_m(d,t)})}function Ko(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ma(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,l=o.stateNode;o.tag===5&&l!==null&&(o=l,l=Ao(e,n),l!=null&&r.unshift(Ko(e,l,o)),l=Ao(e,t),l!=null&&r.push(Ko(e,l,o))),e=e.return}return r}function dr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function kd(e,t,n,r,o){for(var l=t._reactName,a=[];n!==null&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(s!==null&&s===r)break;i.tag===5&&u!==null&&(i=u,o?(s=Ao(n,l),s!=null&&a.unshift(Ko(n,s,i))):o||(s=Ao(n,l),s!=null&&a.push(Ko(n,s,i)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Wy=/\r\n?/g,Vy=/\u0000|\uFFFD/g;function Pd(e){return(typeof e=="string"?e:""+e).replace(Wy,`
`).replace(Vy,"")}function _l(e,t,n){if(t=Pd(t),Pd(e)!==t&&n)throw Error(j(425))}function ha(){}var ks=null,Ps=null;function Ts(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ns=typeof setTimeout=="function"?setTimeout:void 0,By=typeof clearTimeout=="function"?clearTimeout:void 0,Td=typeof Promise=="function"?Promise:void 0,Hy=typeof queueMicrotask=="function"?queueMicrotask:typeof Td<"u"?function(e){return Td.resolve(null).then(e).catch(Uy)}:Ns;function Uy(e){setTimeout(function(){throw e})}function Ii(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Vo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Vo(t)}function Cn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Nd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Qr=Math.random().toString(36).slice(2),jt="__reactFiber$"+Qr,Go="__reactProps$"+Qr,qt="__reactContainer$"+Qr,_s="__reactEvents$"+Qr,Ky="__reactListeners$"+Qr,Gy="__reactHandles$"+Qr;function Vn(e){var t=e[jt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[qt]||n[jt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Nd(e);e!==null;){if(n=e[jt])return n;e=Nd(e)}return t}e=n,n=e.parentNode}return null}function ul(e){return e=e[jt]||e[qt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function br(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function Ha(e){return e[Go]||null}var Ms=[],wr=-1;function Ln(e){return{current:e}}function ce(e){0>wr||(e.current=Ms[wr],Ms[wr]=null,wr--)}function ie(e,t){wr++,Ms[wr]=e.current,e.current=t}var _n={},Ae=Ln(_n),Je=Ln(!1),Xn=_n;function Fr(e,t){var n=e.type.contextTypes;if(!n)return _n;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},l;for(l in n)o[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function qe(e){return e=e.childContextTypes,e!=null}function va(){ce(Je),ce(Ae)}function _d(e,t,n){if(Ae.current!==_n)throw Error(j(168));ie(Ae,t),ie(Je,n)}function Lm(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(j(108,M0(e)||"Unknown",o));return me({},n,r)}function ga(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_n,Xn=Ae.current,ie(Ae,e),ie(Je,Je.current),!0}function Md(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=Lm(e,t,Xn),r.__reactInternalMemoizedMergedChildContext=e,ce(Je),ce(Ae),ie(Ae,e)):ce(Je),ie(Je,n)}var Gt=null,Ua=!1,Ri=!1;function Im(e){Gt===null?Gt=[e]:Gt.push(e)}function Qy(e){Ua=!0,Im(e)}function In(){if(!Ri&&Gt!==null){Ri=!0;var e=0,t=oe;try{var n=Gt;for(oe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Gt=null,Ua=!1}catch(o){throw Gt!==null&&(Gt=Gt.slice(e+1)),om(Ru,In),o}finally{oe=t,Ri=!1}}return null}var xr=[],Sr=0,ya=null,ba=0,ft=[],pt=0,Zn=null,Qt=1,Yt="";function Dn(e,t){xr[Sr++]=ba,xr[Sr++]=ya,ya=e,ba=t}function Rm(e,t,n){ft[pt++]=Qt,ft[pt++]=Yt,ft[pt++]=Zn,Zn=e;var r=Qt;e=Yt;var o=32-Pt(r)-1;r&=~(1<<o),n+=1;var l=32-Pt(t)+o;if(30<l){var a=o-o%5;l=(r&(1<<a)-1).toString(32),r>>=a,o-=a,Qt=1<<32-Pt(t)+o|n<<o|r,Yt=l+e}else Qt=1<<l|n<<o|r,Yt=e}function Bu(e){e.return!==null&&(Dn(e,1),Rm(e,1,0))}function Hu(e){for(;e===ya;)ya=xr[--Sr],xr[Sr]=null,ba=xr[--Sr],xr[Sr]=null;for(;e===Zn;)Zn=ft[--pt],ft[pt]=null,Yt=ft[--pt],ft[pt]=null,Qt=ft[--pt],ft[pt]=null}var lt=null,ot=null,de=!1,kt=null;function jm(e,t){var n=mt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ld(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,lt=e,ot=Cn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,lt=e,ot=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Zn!==null?{id:Qt,overflow:Yt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=mt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,lt=e,ot=null,!0):!1;default:return!1}}function Ls(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Is(e){if(de){var t=ot;if(t){var n=t;if(!Ld(e,t)){if(Ls(e))throw Error(j(418));t=Cn(n.nextSibling);var r=lt;t&&Ld(e,t)?jm(r,n):(e.flags=e.flags&-4097|2,de=!1,lt=e)}}else{if(Ls(e))throw Error(j(418));e.flags=e.flags&-4097|2,de=!1,lt=e}}}function Id(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;lt=e}function Ml(e){if(e!==lt)return!1;if(!de)return Id(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ts(e.type,e.memoizedProps)),t&&(t=ot)){if(Ls(e))throw zm(),Error(j(418));for(;t;)jm(e,t),t=Cn(t.nextSibling)}if(Id(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ot=Cn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ot=null}}else ot=lt?Cn(e.stateNode.nextSibling):null;return!0}function zm(){for(var e=ot;e;)e=Cn(e.nextSibling)}function Ar(){ot=lt=null,de=!1}function Uu(e){kt===null?kt=[e]:kt.push(e)}var Yy=nn.ReactCurrentBatchConfig;function uo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var o=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(a){var i=o.refs;a===null?delete i[l]:i[l]=a},t._stringRef=l,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function Ll(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Rd(e){var t=e._init;return t(e._payload)}function Om(e){function t(h,m){if(e){var g=h.deletions;g===null?(h.deletions=[m],h.flags|=16):g.push(m)}}function n(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function o(h,m){return h=Pn(h,m),h.index=0,h.sibling=null,h}function l(h,m,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<m?(h.flags|=2,m):g):(h.flags|=2,m)):(h.flags|=1048576,m)}function a(h){return e&&h.alternate===null&&(h.flags|=2),h}function i(h,m,g,x){return m===null||m.tag!==6?(m=Wi(g,h.mode,x),m.return=h,m):(m=o(m,g),m.return=h,m)}function s(h,m,g,x){var E=g.type;return E===hr?c(h,m,g.props.children,x,g.key):m!==null&&(m.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===cn&&Rd(E)===m.type)?(x=o(m,g.props),x.ref=uo(h,m,g),x.return=h,x):(x=la(g.type,g.key,g.props,null,h.mode,x),x.ref=uo(h,m,g),x.return=h,x)}function u(h,m,g,x){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=Vi(g,h.mode,x),m.return=h,m):(m=o(m,g.children||[]),m.return=h,m)}function c(h,m,g,x,E){return m===null||m.tag!==7?(m=Qn(g,h.mode,x,E),m.return=h,m):(m=o(m,g),m.return=h,m)}function d(h,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Wi(""+m,h.mode,g),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case xl:return g=la(m.type,m.key,m.props,null,h.mode,g),g.ref=uo(h,null,m),g.return=h,g;case mr:return m=Vi(m,h.mode,g),m.return=h,m;case cn:var x=m._init;return d(h,x(m._payload),g)}if(wo(m)||oo(m))return m=Qn(m,h.mode,g,null),m.return=h,m;Ll(h,m)}return null}function f(h,m,g,x){var E=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return E!==null?null:i(h,m,""+g,x);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case xl:return g.key===E?s(h,m,g,x):null;case mr:return g.key===E?u(h,m,g,x):null;case cn:return E=g._init,f(h,m,E(g._payload),x)}if(wo(g)||oo(g))return E!==null?null:c(h,m,g,x,null);Ll(h,g)}return null}function v(h,m,g,x,E){if(typeof x=="string"&&x!==""||typeof x=="number")return h=h.get(g)||null,i(m,h,""+x,E);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case xl:return h=h.get(x.key===null?g:x.key)||null,s(m,h,x,E);case mr:return h=h.get(x.key===null?g:x.key)||null,u(m,h,x,E);case cn:var $=x._init;return v(h,m,g,$(x._payload),E)}if(wo(x)||oo(x))return h=h.get(g)||null,c(m,h,x,E,null);Ll(m,x)}return null}function y(h,m,g,x){for(var E=null,$=null,_=m,M=m=0,O=null;_!==null&&M<g.length;M++){_.index>M?(O=_,_=null):O=_.sibling;var I=f(h,_,g[M],x);if(I===null){_===null&&(_=O);break}e&&_&&I.alternate===null&&t(h,_),m=l(I,m,M),$===null?E=I:$.sibling=I,$=I,_=O}if(M===g.length)return n(h,_),de&&Dn(h,M),E;if(_===null){for(;M<g.length;M++)_=d(h,g[M],x),_!==null&&(m=l(_,m,M),$===null?E=_:$.sibling=_,$=_);return de&&Dn(h,M),E}for(_=r(h,_);M<g.length;M++)O=v(_,h,M,g[M],x),O!==null&&(e&&O.alternate!==null&&_.delete(O.key===null?M:O.key),m=l(O,m,M),$===null?E=O:$.sibling=O,$=O);return e&&_.forEach(function(C){return t(h,C)}),de&&Dn(h,M),E}function b(h,m,g,x){var E=oo(g);if(typeof E!="function")throw Error(j(150));if(g=E.call(g),g==null)throw Error(j(151));for(var $=E=null,_=m,M=m=0,O=null,I=g.next();_!==null&&!I.done;M++,I=g.next()){_.index>M?(O=_,_=null):O=_.sibling;var C=f(h,_,I.value,x);if(C===null){_===null&&(_=O);break}e&&_&&C.alternate===null&&t(h,_),m=l(C,m,M),$===null?E=C:$.sibling=C,$=C,_=O}if(I.done)return n(h,_),de&&Dn(h,M),E;if(_===null){for(;!I.done;M++,I=g.next())I=d(h,I.value,x),I!==null&&(m=l(I,m,M),$===null?E=I:$.sibling=I,$=I);return de&&Dn(h,M),E}for(_=r(h,_);!I.done;M++,I=g.next())I=v(_,h,M,I.value,x),I!==null&&(e&&I.alternate!==null&&_.delete(I.key===null?M:I.key),m=l(I,m,M),$===null?E=I:$.sibling=I,$=I);return e&&_.forEach(function(P){return t(h,P)}),de&&Dn(h,M),E}function S(h,m,g,x){if(typeof g=="object"&&g!==null&&g.type===hr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case xl:e:{for(var E=g.key,$=m;$!==null;){if($.key===E){if(E=g.type,E===hr){if($.tag===7){n(h,$.sibling),m=o($,g.props.children),m.return=h,h=m;break e}}else if($.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===cn&&Rd(E)===$.type){n(h,$.sibling),m=o($,g.props),m.ref=uo(h,$,g),m.return=h,h=m;break e}n(h,$);break}else t(h,$);$=$.sibling}g.type===hr?(m=Qn(g.props.children,h.mode,x,g.key),m.return=h,h=m):(x=la(g.type,g.key,g.props,null,h.mode,x),x.ref=uo(h,m,g),x.return=h,h=x)}return a(h);case mr:e:{for($=g.key;m!==null;){if(m.key===$)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){n(h,m.sibling),m=o(m,g.children||[]),m.return=h,h=m;break e}else{n(h,m);break}else t(h,m);m=m.sibling}m=Vi(g,h.mode,x),m.return=h,h=m}return a(h);case cn:return $=g._init,S(h,m,$(g._payload),x)}if(wo(g))return y(h,m,g,x);if(oo(g))return b(h,m,g,x);Ll(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(n(h,m.sibling),m=o(m,g),m.return=h,h=m):(n(h,m),m=Wi(g,h.mode,x),m.return=h,h=m),a(h)):n(h,m)}return S}var Dr=Om(!0),Fm=Om(!1),wa=Ln(null),xa=null,Cr=null,Ku=null;function Gu(){Ku=Cr=xa=null}function Qu(e){var t=wa.current;ce(wa),e._currentValue=t}function Rs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mr(e,t){xa=e,Ku=Cr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Xe=!0),e.firstContext=null)}function gt(e){var t=e._currentValue;if(Ku!==e)if(e={context:e,memoizedValue:t,next:null},Cr===null){if(xa===null)throw Error(j(308));Cr=e,xa.dependencies={lanes:0,firstContext:e}}else Cr=Cr.next=e;return t}var Bn=null;function Yu(e){Bn===null?Bn=[e]:Bn.push(e)}function Am(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Yu(t)):(n.next=o.next,o.next=n),t.interleaved=n,en(e,r)}function en(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var dn=!1;function Xu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Dm(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function En(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ne&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,en(e,n)}return o=r.interleaved,o===null?(t.next=t,Yu(r)):(t.next=o.next,o.next=t),r.interleaved=t,en(e,n)}function ql(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ju(e,n)}}function jd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?o=l=a:l=l.next=a,n=n.next}while(n!==null);l===null?o=l=t:l=l.next=t}else o=l=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Sa(e,t,n,r){var o=e.updateQueue;dn=!1;var l=o.firstBaseUpdate,a=o.lastBaseUpdate,i=o.shared.pending;if(i!==null){o.shared.pending=null;var s=i,u=s.next;s.next=null,a===null?l=u:a.next=u,a=s;var c=e.alternate;c!==null&&(c=c.updateQueue,i=c.lastBaseUpdate,i!==a&&(i===null?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(l!==null){var d=o.baseState;a=0,c=u=s=null,i=l;do{var f=i.lane,v=i.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:v,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var y=e,b=i;switch(f=t,v=n,b.tag){case 1:if(y=b.payload,typeof y=="function"){d=y.call(v,d,f);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=b.payload,f=typeof y=="function"?y.call(v,d,f):y,f==null)break e;d=me({},d,f);break e;case 2:dn=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[i]:f.push(i))}else v={eventTime:v,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},c===null?(u=c=v,s=d):c=c.next=v,a|=f;if(i=i.next,i===null){if(i=o.shared.pending,i===null)break;f=i,i=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(c===null&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else l===null&&(o.shared.lanes=0);qn|=a,e.lanes=a,e.memoizedState=d}}function zd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(j(191,o));o.call(r)}}}var cl={},Ft=Ln(cl),Qo=Ln(cl),Yo=Ln(cl);function Hn(e){if(e===cl)throw Error(j(174));return e}function Zu(e,t){switch(ie(Yo,t),ie(Qo,e),ie(Ft,cl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ms(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ms(t,e)}ce(Ft),ie(Ft,t)}function Wr(){ce(Ft),ce(Qo),ce(Yo)}function Wm(e){Hn(Yo.current);var t=Hn(Ft.current),n=ms(t,e.type);t!==n&&(ie(Qo,e),ie(Ft,n))}function Ju(e){Qo.current===e&&(ce(Ft),ce(Qo))}var fe=Ln(0);function Ca(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ji=[];function qu(){for(var e=0;e<ji.length;e++)ji[e]._workInProgressVersionPrimary=null;ji.length=0}var ea=nn.ReactCurrentDispatcher,zi=nn.ReactCurrentBatchConfig,Jn=0,pe=null,ke=null,Ne=null,Ea=!1,Lo=!1,Xo=0,Xy=0;function je(){throw Error(j(321))}function ec(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Nt(e[n],t[n]))return!1;return!0}function tc(e,t,n,r,o,l){if(Jn=l,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ea.current=e===null||e.memoizedState===null?e1:t1,e=n(r,o),Lo){l=0;do{if(Lo=!1,Xo=0,25<=l)throw Error(j(301));l+=1,Ne=ke=null,t.updateQueue=null,ea.current=n1,e=n(r,o)}while(Lo)}if(ea.current=$a,t=ke!==null&&ke.next!==null,Jn=0,Ne=ke=pe=null,Ea=!1,t)throw Error(j(300));return e}function nc(){var e=Xo!==0;return Xo=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e,Ne}function yt(){if(ke===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=ke.next;var t=Ne===null?pe.memoizedState:Ne.next;if(t!==null)Ne=t,ke=e;else{if(e===null)throw Error(j(310));ke=e,e={memoizedState:ke.memoizedState,baseState:ke.baseState,baseQueue:ke.baseQueue,queue:ke.queue,next:null},Ne===null?pe.memoizedState=Ne=e:Ne=Ne.next=e}return Ne}function Zo(e,t){return typeof t=="function"?t(e):t}function Oi(e){var t=yt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=ke,o=r.baseQueue,l=n.pending;if(l!==null){if(o!==null){var a=o.next;o.next=l.next,l.next=a}r.baseQueue=o=l,n.pending=null}if(o!==null){l=o.next,r=r.baseState;var i=a=null,s=null,u=l;do{var c=u.lane;if((Jn&c)===c)s!==null&&(s=s.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};s===null?(i=s=d,a=r):s=s.next=d,pe.lanes|=c,qn|=c}u=u.next}while(u!==null&&u!==l);s===null?a=r:s.next=i,Nt(r,t.memoizedState)||(Xe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do l=o.lane,pe.lanes|=l,qn|=l,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Fi(e){var t=yt(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,l=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do l=e(l,a.action),a=a.next;while(a!==o);Nt(l,t.memoizedState)||(Xe=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Vm(){}function Bm(e,t){var n=pe,r=yt(),o=t(),l=!Nt(r.memoizedState,o);if(l&&(r.memoizedState=o,Xe=!0),r=r.queue,rc(Km.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||Ne!==null&&Ne.memoizedState.tag&1){if(n.flags|=2048,Jo(9,Um.bind(null,n,r,o,t),void 0,null),_e===null)throw Error(j(349));Jn&30||Hm(n,t,o)}return o}function Hm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Um(e,t,n,r){t.value=n,t.getSnapshot=r,Gm(t)&&Qm(e)}function Km(e,t,n){return n(function(){Gm(t)&&Qm(e)})}function Gm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Nt(e,n)}catch{return!0}}function Qm(e){var t=en(e,1);t!==null&&Tt(t,e,1,-1)}function Od(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Zo,lastRenderedState:e},t.queue=e,e=e.dispatch=qy.bind(null,pe,e),[t.memoizedState,e]}function Jo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ym(){return yt().memoizedState}function ta(e,t,n,r){var o=It();pe.flags|=e,o.memoizedState=Jo(1|t,n,void 0,r===void 0?null:r)}function Ka(e,t,n,r){var o=yt();r=r===void 0?null:r;var l=void 0;if(ke!==null){var a=ke.memoizedState;if(l=a.destroy,r!==null&&ec(r,a.deps)){o.memoizedState=Jo(t,n,l,r);return}}pe.flags|=e,o.memoizedState=Jo(1|t,n,l,r)}function Fd(e,t){return ta(8390656,8,e,t)}function rc(e,t){return Ka(2048,8,e,t)}function Xm(e,t){return Ka(4,2,e,t)}function Zm(e,t){return Ka(4,4,e,t)}function Jm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qm(e,t,n){return n=n!=null?n.concat([e]):null,Ka(4,4,Jm.bind(null,t,e),n)}function oc(){}function eh(e,t){var n=yt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ec(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function th(e,t){var n=yt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ec(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function nh(e,t,n){return Jn&21?(Nt(n,t)||(n=im(),pe.lanes|=n,qn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Xe=!0),e.memoizedState=n)}function Zy(e,t){var n=oe;oe=n!==0&&4>n?n:4,e(!0);var r=zi.transition;zi.transition={};try{e(!1),t()}finally{oe=n,zi.transition=r}}function rh(){return yt().memoizedState}function Jy(e,t,n){var r=kn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},oh(e))lh(t,n);else if(n=Am(e,t,n,r),n!==null){var o=Ve();Tt(n,e,r,o),ah(n,t,r)}}function qy(e,t,n){var r=kn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(oh(e))lh(t,o);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var a=t.lastRenderedState,i=l(a,n);if(o.hasEagerState=!0,o.eagerState=i,Nt(i,a)){var s=t.interleaved;s===null?(o.next=o,Yu(t)):(o.next=s.next,s.next=o),t.interleaved=o;return}}catch{}finally{}n=Am(e,t,o,r),n!==null&&(o=Ve(),Tt(n,e,r,o),ah(n,t,r))}}function oh(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function lh(e,t){Lo=Ea=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ah(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ju(e,n)}}var $a={readContext:gt,useCallback:je,useContext:je,useEffect:je,useImperativeHandle:je,useInsertionEffect:je,useLayoutEffect:je,useMemo:je,useReducer:je,useRef:je,useState:je,useDebugValue:je,useDeferredValue:je,useTransition:je,useMutableSource:je,useSyncExternalStore:je,useId:je,unstable_isNewReconciler:!1},e1={readContext:gt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:gt,useEffect:Fd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ta(4194308,4,Jm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ta(4194308,4,e,t)},useInsertionEffect:function(e,t){return ta(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=It();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Jy.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:Od,useDebugValue:oc,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=Od(!1),t=e[0];return e=Zy.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,o=It();if(de){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),_e===null)throw Error(j(349));Jn&30||Hm(r,t,n)}o.memoizedState=n;var l={value:n,getSnapshot:t};return o.queue=l,Fd(Km.bind(null,r,l,e),[e]),r.flags|=2048,Jo(9,Um.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=It(),t=_e.identifierPrefix;if(de){var n=Yt,r=Qt;n=(r&~(1<<32-Pt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Xo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},t1={readContext:gt,useCallback:eh,useContext:gt,useEffect:rc,useImperativeHandle:qm,useInsertionEffect:Xm,useLayoutEffect:Zm,useMemo:th,useReducer:Oi,useRef:Ym,useState:function(){return Oi(Zo)},useDebugValue:oc,useDeferredValue:function(e){var t=yt();return nh(t,ke.memoizedState,e)},useTransition:function(){var e=Oi(Zo)[0],t=yt().memoizedState;return[e,t]},useMutableSource:Vm,useSyncExternalStore:Bm,useId:rh,unstable_isNewReconciler:!1},n1={readContext:gt,useCallback:eh,useContext:gt,useEffect:rc,useImperativeHandle:qm,useInsertionEffect:Xm,useLayoutEffect:Zm,useMemo:th,useReducer:Fi,useRef:Ym,useState:function(){return Fi(Zo)},useDebugValue:oc,useDeferredValue:function(e){var t=yt();return ke===null?t.memoizedState=e:nh(t,ke.memoizedState,e)},useTransition:function(){var e=Fi(Zo)[0],t=yt().memoizedState;return[e,t]},useMutableSource:Vm,useSyncExternalStore:Bm,useId:rh,unstable_isNewReconciler:!1};function Ct(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function js(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ga={isMounted:function(e){return(e=e._reactInternals)?ir(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=kn(e),l=Zt(r,o);l.payload=t,n!=null&&(l.callback=n),t=En(e,l,o),t!==null&&(Tt(t,e,o,r),ql(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ve(),o=kn(e),l=Zt(r,o);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=En(e,l,o),t!==null&&(Tt(t,e,o,r),ql(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ve(),r=kn(e),o=Zt(n,r);o.tag=2,t!=null&&(o.callback=t),t=En(e,o,r),t!==null&&(Tt(t,e,r,n),ql(t,e,r))}};function Ad(e,t,n,r,o,l,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,a):t.prototype&&t.prototype.isPureReactComponent?!Ho(n,r)||!Ho(o,l):!0}function ih(e,t,n){var r=!1,o=_n,l=t.contextType;return typeof l=="object"&&l!==null?l=gt(l):(o=qe(t)?Xn:Ae.current,r=t.contextTypes,l=(r=r!=null)?Fr(e,o):_n),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ga,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=l),t}function Dd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ga.enqueueReplaceState(t,t.state,null)}function zs(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Xu(e);var l=t.contextType;typeof l=="object"&&l!==null?o.context=gt(l):(l=qe(t)?Xn:Ae.current,o.context=Fr(e,l)),o.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(js(e,t,l,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ga.enqueueReplaceState(o,o.state,null),Sa(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Vr(e,t){try{var n="",r=t;do n+=_0(r),r=r.return;while(r);var o=n}catch(l){o=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:o,digest:null}}function Ai(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Os(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var r1=typeof WeakMap=="function"?WeakMap:Map;function sh(e,t,n){n=Zt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Pa||(Pa=!0,Gs=r),Os(e,t)},n}function uh(e,t,n){n=Zt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Os(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Os(e,t),typeof r!="function"&&($n===null?$n=new Set([this]):$n.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Wd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new r1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=g1.bind(null,e,t,n),t.then(e,e))}function Vd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Zt(-1,1),t.tag=2,En(n,t,1))),n.lanes|=1),e)}var o1=nn.ReactCurrentOwner,Xe=!1;function We(e,t,n,r){t.child=e===null?Fm(t,null,n,r):Dr(t,e.child,n,r)}function Hd(e,t,n,r,o){n=n.render;var l=t.ref;return Mr(t,o),r=tc(e,t,n,r,l,o),n=nc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,tn(e,t,o)):(de&&n&&Bu(t),t.flags|=1,We(e,t,r,o),t.child)}function Ud(e,t,n,r,o){if(e===null){var l=n.type;return typeof l=="function"&&!fc(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,ch(e,t,l,r,o)):(e=la(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&o)){var a=l.memoizedProps;if(n=n.compare,n=n!==null?n:Ho,n(a,r)&&e.ref===t.ref)return tn(e,t,o)}return t.flags|=1,e=Pn(l,r),e.ref=t.ref,e.return=t,t.child=e}function ch(e,t,n,r,o){if(e!==null){var l=e.memoizedProps;if(Ho(l,r)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=r=l,(e.lanes&o)!==0)e.flags&131072&&(Xe=!0);else return t.lanes=e.lanes,tn(e,t,o)}return Fs(e,t,n,r,o)}function dh(e,t,n){var r=t.pendingProps,o=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ie($r,rt),rt|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ie($r,rt),rt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,ie($r,rt),rt|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,ie($r,rt),rt|=r;return We(e,t,o,n),t.child}function fh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Fs(e,t,n,r,o){var l=qe(n)?Xn:Ae.current;return l=Fr(t,l),Mr(t,o),n=tc(e,t,n,r,l,o),r=nc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,tn(e,t,o)):(de&&r&&Bu(t),t.flags|=1,We(e,t,n,o),t.child)}function Kd(e,t,n,r,o){if(qe(n)){var l=!0;ga(t)}else l=!1;if(Mr(t,o),t.stateNode===null)na(e,t),ih(t,n,r),zs(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,i=t.memoizedProps;a.props=i;var s=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=gt(u):(u=qe(n)?Xn:Ae.current,u=Fr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(i!==r||s!==u)&&Dd(t,a,r,u),dn=!1;var f=t.memoizedState;a.state=f,Sa(t,r,a,o),s=t.memoizedState,i!==r||f!==s||Je.current||dn?(typeof c=="function"&&(js(t,n,c,r),s=t.memoizedState),(i=dn||Ad(t,n,i,r,f,s,u))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=u,r=i):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Dm(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:Ct(t.type,i),a.props=u,d=t.pendingProps,f=a.context,s=n.contextType,typeof s=="object"&&s!==null?s=gt(s):(s=qe(n)?Xn:Ae.current,s=Fr(t,s));var v=n.getDerivedStateFromProps;(c=typeof v=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(i!==d||f!==s)&&Dd(t,a,r,s),dn=!1,f=t.memoizedState,a.state=f,Sa(t,r,a,o);var y=t.memoizedState;i!==d||f!==y||Je.current||dn?(typeof v=="function"&&(js(t,n,v,r),y=t.memoizedState),(u=dn||Ad(t,n,u,r,f,y,s)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,y,s),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,y,s)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),a.props=r,a.state=y,a.context=s,r=u):(typeof a.componentDidUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return As(e,t,n,r,l,o)}function As(e,t,n,r,o,l){fh(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&Md(t,n,!1),tn(e,t,l);r=t.stateNode,o1.current=t;var i=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Dr(t,e.child,null,l),t.child=Dr(t,null,i,l)):We(e,t,i,l),t.memoizedState=r.state,o&&Md(t,n,!0),t.child}function ph(e){var t=e.stateNode;t.pendingContext?_d(e,t.pendingContext,t.pendingContext!==t.context):t.context&&_d(e,t.context,!1),Zu(e,t.containerInfo)}function Gd(e,t,n,r,o){return Ar(),Uu(o),t.flags|=256,We(e,t,n,r),t.child}var Ds={dehydrated:null,treeContext:null,retryLane:0};function Ws(e){return{baseLanes:e,cachePool:null,transitions:null}}function mh(e,t,n){var r=t.pendingProps,o=fe.current,l=!1,a=(t.flags&128)!==0,i;if((i=a)||(i=e!==null&&e.memoizedState===null?!1:(o&2)!==0),i?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ie(fe,o&1),e===null)return Is(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,l?(r=t.mode,l=t.child,a={mode:"hidden",children:a},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=a):l=Xa(a,r,0,null),e=Qn(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Ws(n),t.memoizedState=Ds,e):lc(t,a));if(o=e.memoizedState,o!==null&&(i=o.dehydrated,i!==null))return l1(e,t,a,r,i,o,n);if(l){l=r.fallback,a=t.mode,o=e.child,i=o.sibling;var s={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=Pn(o,s),r.subtreeFlags=o.subtreeFlags&14680064),i!==null?l=Pn(i,l):(l=Qn(l,a,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,a=e.child.memoizedState,a=a===null?Ws(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},l.memoizedState=a,l.childLanes=e.childLanes&~n,t.memoizedState=Ds,r}return l=e.child,e=l.sibling,r=Pn(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function lc(e,t){return t=Xa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Il(e,t,n,r){return r!==null&&Uu(r),Dr(t,e.child,null,n),e=lc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function l1(e,t,n,r,o,l,a){if(n)return t.flags&256?(t.flags&=-257,r=Ai(Error(j(422))),Il(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,o=t.mode,r=Xa({mode:"visible",children:r.children},o,0,null),l=Qn(l,o,a,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Dr(t,e.child,null,a),t.child.memoizedState=Ws(a),t.memoizedState=Ds,l);if(!(t.mode&1))return Il(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var i=r.dgst;return r=i,l=Error(j(419)),r=Ai(l,r,void 0),Il(e,t,a,r)}if(i=(a&e.childLanes)!==0,Xe||i){if(r=_e,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==l.retryLane&&(l.retryLane=o,en(e,o),Tt(r,e,o,-1))}return dc(),r=Ai(Error(j(421))),Il(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=y1.bind(null,e),o._reactRetry=t,null):(e=l.treeContext,ot=Cn(o.nextSibling),lt=t,de=!0,kt=null,e!==null&&(ft[pt++]=Qt,ft[pt++]=Yt,ft[pt++]=Zn,Qt=e.id,Yt=e.overflow,Zn=t),t=lc(t,r.children),t.flags|=4096,t)}function Qd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Rs(e.return,t,n)}function Di(e,t,n,r,o){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=o)}function hh(e,t,n){var r=t.pendingProps,o=r.revealOrder,l=r.tail;if(We(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Qd(e,n,t);else if(e.tag===19)Qd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ie(fe,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ca(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Di(t,!1,o,n,l);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ca(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Di(t,!0,n,null,l);break;case"together":Di(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function na(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function tn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),qn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=Pn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Pn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function a1(e,t,n){switch(t.tag){case 3:ph(t),Ar();break;case 5:Wm(t);break;case 1:qe(t.type)&&ga(t);break;case 4:Zu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ie(wa,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ie(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?mh(e,t,n):(ie(fe,fe.current&1),e=tn(e,t,n),e!==null?e.sibling:null);ie(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return hh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ie(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,dh(e,t,n)}return tn(e,t,n)}var vh,Vs,gh,yh;vh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Vs=function(){};gh=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Hn(Ft.current);var l=null;switch(n){case"input":o=cs(e,o),r=cs(e,r),l=[];break;case"select":o=me({},o,{value:void 0}),r=me({},r,{value:void 0}),l=[];break;case"textarea":o=ps(e,o),r=ps(e,r),l=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ha)}hs(n,r);var a;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var i=o[u];for(a in i)i.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Oo.hasOwnProperty(u)?l||(l=[]):(l=l||[]).push(u,null));for(u in r){var s=r[u];if(i=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&s!==i&&(s!=null||i!=null))if(u==="style")if(i){for(a in i)!i.hasOwnProperty(a)||s&&s.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in s)s.hasOwnProperty(a)&&i[a]!==s[a]&&(n||(n={}),n[a]=s[a])}else n||(l||(l=[]),l.push(u,n)),n=s;else u==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,i=i?i.__html:void 0,s!=null&&i!==s&&(l=l||[]).push(u,s)):u==="children"?typeof s!="string"&&typeof s!="number"||(l=l||[]).push(u,""+s):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Oo.hasOwnProperty(u)?(s!=null&&u==="onScroll"&&ue("scroll",e),l||i===s||(l=[])):(l=l||[]).push(u,s))}n&&(l=l||[]).push("style",n);var u=l;(t.updateQueue=u)&&(t.flags|=4)}};yh=function(e,t,n,r){n!==r&&(t.flags|=4)};function co(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function i1(e,t,n){var r=t.pendingProps;switch(Hu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return qe(t.type)&&va(),ze(t),null;case 3:return r=t.stateNode,Wr(),ce(Je),ce(Ae),qu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ml(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,kt!==null&&(Xs(kt),kt=null))),Vs(e,t),ze(t),null;case 5:Ju(t);var o=Hn(Yo.current);if(n=t.type,e!==null&&t.stateNode!=null)gh(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return ze(t),null}if(e=Hn(Ft.current),Ml(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[jt]=t,r[Go]=l,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(o=0;o<So.length;o++)ue(So[o],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":rd(r,l),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},ue("invalid",r);break;case"textarea":ld(r,l),ue("invalid",r)}hs(n,l),o=null;for(var a in l)if(l.hasOwnProperty(a)){var i=l[a];a==="children"?typeof i=="string"?r.textContent!==i&&(l.suppressHydrationWarning!==!0&&_l(r.textContent,i,e),o=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(l.suppressHydrationWarning!==!0&&_l(r.textContent,i,e),o=["children",""+i]):Oo.hasOwnProperty(a)&&i!=null&&a==="onScroll"&&ue("scroll",r)}switch(n){case"input":Sl(r),od(r,l,!0);break;case"textarea":Sl(r),ad(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=ha)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Kp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[jt]=t,e[Go]=r,vh(e,t,!1,!1),t.stateNode=e;e:{switch(a=vs(n,r),n){case"dialog":ue("cancel",e),ue("close",e),o=r;break;case"iframe":case"object":case"embed":ue("load",e),o=r;break;case"video":case"audio":for(o=0;o<So.length;o++)ue(So[o],e);o=r;break;case"source":ue("error",e),o=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),o=r;break;case"details":ue("toggle",e),o=r;break;case"input":rd(e,r),o=cs(e,r),ue("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=me({},r,{value:void 0}),ue("invalid",e);break;case"textarea":ld(e,r),o=ps(e,r),ue("invalid",e);break;default:o=r}hs(n,o),i=o;for(l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="style"?Yp(e,s):l==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&Gp(e,s)):l==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&Fo(e,s):typeof s=="number"&&Fo(e,""+s):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Oo.hasOwnProperty(l)?s!=null&&l==="onScroll"&&ue("scroll",e):s!=null&&Nu(e,l,s,a))}switch(n){case"input":Sl(e),od(e,r,!1);break;case"textarea":Sl(e),ad(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Nn(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?Pr(e,!!r.multiple,l,!1):r.defaultValue!=null&&Pr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=ha)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ze(t),null;case 6:if(e&&t.stateNode!=null)yh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=Hn(Yo.current),Hn(Ft.current),Ml(t)){if(r=t.stateNode,n=t.memoizedProps,r[jt]=t,(l=r.nodeValue!==n)&&(e=lt,e!==null))switch(e.tag){case 3:_l(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&_l(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[jt]=t,t.stateNode=r}return ze(t),null;case 13:if(ce(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&ot!==null&&t.mode&1&&!(t.flags&128))zm(),Ar(),t.flags|=98560,l=!1;else if(l=Ml(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(j(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(j(317));l[jt]=t}else Ar(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ze(t),l=!1}else kt!==null&&(Xs(kt),kt=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Pe===0&&(Pe=3):dc())),t.updateQueue!==null&&(t.flags|=4),ze(t),null);case 4:return Wr(),Vs(e,t),e===null&&Uo(t.stateNode.containerInfo),ze(t),null;case 10:return Qu(t.type._context),ze(t),null;case 17:return qe(t.type)&&va(),ze(t),null;case 19:if(ce(fe),l=t.memoizedState,l===null)return ze(t),null;if(r=(t.flags&128)!==0,a=l.rendering,a===null)if(r)co(l,!1);else{if(Pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Ca(e),a!==null){for(t.flags|=128,co(l,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,a=l.alternate,a===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=a.childLanes,l.lanes=a.lanes,l.child=a.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=a.memoizedProps,l.memoizedState=a.memoizedState,l.updateQueue=a.updateQueue,l.type=a.type,e=a.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ie(fe,fe.current&1|2),t.child}e=e.sibling}l.tail!==null&&ye()>Br&&(t.flags|=128,r=!0,co(l,!1),t.lanes=4194304)}else{if(!r)if(e=Ca(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),co(l,!0),l.tail===null&&l.tailMode==="hidden"&&!a.alternate&&!de)return ze(t),null}else 2*ye()-l.renderingStartTime>Br&&n!==1073741824&&(t.flags|=128,r=!0,co(l,!1),t.lanes=4194304);l.isBackwards?(a.sibling=t.child,t.child=a):(n=l.last,n!==null?n.sibling=a:t.child=a,l.last=a)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=ye(),t.sibling=null,n=fe.current,ie(fe,r?n&1|2:n&1),t):(ze(t),null);case 22:case 23:return cc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?rt&1073741824&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function s1(e,t){switch(Hu(t),t.tag){case 1:return qe(t.type)&&va(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Wr(),ce(Je),ce(Ae),qu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ju(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));Ar()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return Wr(),null;case 10:return Qu(t.type._context),null;case 22:case 23:return cc(),null;case 24:return null;default:return null}}var Rl=!1,Fe=!1,u1=typeof WeakSet=="function"?WeakSet:Set,W=null;function Er(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function Bs(e,t,n){try{n()}catch(r){he(e,t,r)}}var Yd=!1;function c1(e,t){if(ks=fa,e=Cm(),Vu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var a=0,i=-1,s=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var v;d!==n||o!==0&&d.nodeType!==3||(i=a+o),d!==l||r!==0&&d.nodeType!==3||(s=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(v=d.firstChild)!==null;)f=d,d=v;for(;;){if(d===e)break t;if(f===n&&++u===o&&(i=a),f===l&&++c===r&&(s=a),(v=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=v}n=i===-1||s===-1?null:{start:i,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ps={focusedElem:e,selectionRange:n},fa=!1,W=t;W!==null;)if(t=W,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,W=e;else for(;W!==null;){t=W;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var b=y.memoizedProps,S=y.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?b:Ct(t.type,b),S);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(x){he(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,W=e;break}W=t.return}return y=Yd,Yd=!1,y}function Io(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var l=o.destroy;o.destroy=void 0,l!==void 0&&Bs(t,n,l)}o=o.next}while(o!==r)}}function Qa(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Hs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function bh(e){var t=e.alternate;t!==null&&(e.alternate=null,bh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[jt],delete t[Go],delete t[_s],delete t[Ky],delete t[Gy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function wh(e){return e.tag===5||e.tag===3||e.tag===4}function Xd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Us(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ha));else if(r!==4&&(e=e.child,e!==null))for(Us(e,t,n),e=e.sibling;e!==null;)Us(e,t,n),e=e.sibling}function Ks(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ks(e,t,n),e=e.sibling;e!==null;)Ks(e,t,n),e=e.sibling}var Le=null,$t=!1;function an(e,t,n){for(n=n.child;n!==null;)xh(e,t,n),n=n.sibling}function xh(e,t,n){if(Ot&&typeof Ot.onCommitFiberUnmount=="function")try{Ot.onCommitFiberUnmount(Da,n)}catch{}switch(n.tag){case 5:Fe||Er(n,t);case 6:var r=Le,o=$t;Le=null,an(e,t,n),Le=r,$t=o,Le!==null&&($t?(e=Le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Le.removeChild(n.stateNode));break;case 18:Le!==null&&($t?(e=Le,n=n.stateNode,e.nodeType===8?Ii(e.parentNode,n):e.nodeType===1&&Ii(e,n),Vo(e)):Ii(Le,n.stateNode));break;case 4:r=Le,o=$t,Le=n.stateNode.containerInfo,$t=!0,an(e,t,n),Le=r,$t=o;break;case 0:case 11:case 14:case 15:if(!Fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var l=o,a=l.destroy;l=l.tag,a!==void 0&&(l&2||l&4)&&Bs(n,t,a),o=o.next}while(o!==r)}an(e,t,n);break;case 1:if(!Fe&&(Er(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){he(n,t,i)}an(e,t,n);break;case 21:an(e,t,n);break;case 22:n.mode&1?(Fe=(r=Fe)||n.memoizedState!==null,an(e,t,n),Fe=r):an(e,t,n);break;default:an(e,t,n)}}function Zd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new u1),t.forEach(function(r){var o=b1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function St(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var l=e,a=t,i=a;e:for(;i!==null;){switch(i.tag){case 5:Le=i.stateNode,$t=!1;break e;case 3:Le=i.stateNode.containerInfo,$t=!0;break e;case 4:Le=i.stateNode.containerInfo,$t=!0;break e}i=i.return}if(Le===null)throw Error(j(160));xh(l,a,o),Le=null,$t=!1;var s=o.alternate;s!==null&&(s.return=null),o.return=null}catch(u){he(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Sh(t,e),t=t.sibling}function Sh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Mt(e),r&4){try{Io(3,e,e.return),Qa(3,e)}catch(b){he(e,e.return,b)}try{Io(5,e,e.return)}catch(b){he(e,e.return,b)}}break;case 1:St(t,e),Mt(e),r&512&&n!==null&&Er(n,n.return);break;case 5:if(St(t,e),Mt(e),r&512&&n!==null&&Er(n,n.return),e.flags&32){var o=e.stateNode;try{Fo(o,"")}catch(b){he(e,e.return,b)}}if(r&4&&(o=e.stateNode,o!=null)){var l=e.memoizedProps,a=n!==null?n.memoizedProps:l,i=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{i==="input"&&l.type==="radio"&&l.name!=null&&Hp(o,l),vs(i,a);var u=vs(i,l);for(a=0;a<s.length;a+=2){var c=s[a],d=s[a+1];c==="style"?Yp(o,d):c==="dangerouslySetInnerHTML"?Gp(o,d):c==="children"?Fo(o,d):Nu(o,c,d,u)}switch(i){case"input":ds(o,l);break;case"textarea":Up(o,l);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!l.multiple;var v=l.value;v!=null?Pr(o,!!l.multiple,v,!1):f!==!!l.multiple&&(l.defaultValue!=null?Pr(o,!!l.multiple,l.defaultValue,!0):Pr(o,!!l.multiple,l.multiple?[]:"",!1))}o[Go]=l}catch(b){he(e,e.return,b)}}break;case 6:if(St(t,e),Mt(e),r&4){if(e.stateNode===null)throw Error(j(162));o=e.stateNode,l=e.memoizedProps;try{o.nodeValue=l}catch(b){he(e,e.return,b)}}break;case 3:if(St(t,e),Mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Vo(t.containerInfo)}catch(b){he(e,e.return,b)}break;case 4:St(t,e),Mt(e);break;case 13:St(t,e),Mt(e),o=e.child,o.flags&8192&&(l=o.memoizedState!==null,o.stateNode.isHidden=l,!l||o.alternate!==null&&o.alternate.memoizedState!==null||(sc=ye())),r&4&&Zd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Fe=(u=Fe)||c,St(t,e),Fe=u):St(t,e),Mt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(W=e,c=e.child;c!==null;){for(d=W=c;W!==null;){switch(f=W,v=f.child,f.tag){case 0:case 11:case 14:case 15:Io(4,f,f.return);break;case 1:Er(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(b){he(r,n,b)}}break;case 5:Er(f,f.return);break;case 22:if(f.memoizedState!==null){qd(d);continue}}v!==null?(v.return=f,W=v):qd(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(l=o.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(i=d.stateNode,s=d.memoizedProps.style,a=s!=null&&s.hasOwnProperty("display")?s.display:null,i.style.display=Qp("display",a))}catch(b){he(e,e.return,b)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(b){he(e,e.return,b)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:St(t,e),Mt(e),r&4&&Zd(e);break;case 21:break;default:St(t,e),Mt(e)}}function Mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(wh(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Fo(o,""),r.flags&=-33);var l=Xd(e);Ks(e,l,o);break;case 3:case 4:var a=r.stateNode.containerInfo,i=Xd(e);Us(e,i,a);break;default:throw Error(j(161))}}catch(s){he(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function d1(e,t,n){W=e,Ch(e)}function Ch(e,t,n){for(var r=(e.mode&1)!==0;W!==null;){var o=W,l=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||Rl;if(!a){var i=o.alternate,s=i!==null&&i.memoizedState!==null||Fe;i=Rl;var u=Fe;if(Rl=a,(Fe=s)&&!u)for(W=o;W!==null;)a=W,s=a.child,a.tag===22&&a.memoizedState!==null?ef(o):s!==null?(s.return=a,W=s):ef(o);for(;l!==null;)W=l,Ch(l),l=l.sibling;W=o,Rl=i,Fe=u}Jd(e)}else o.subtreeFlags&8772&&l!==null?(l.return=o,W=l):Jd(e)}}function Jd(e){for(;W!==null;){var t=W;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Fe||Qa(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Fe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ct(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&zd(t,l,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}zd(t,a,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Vo(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}Fe||t.flags&512&&Hs(t)}catch(f){he(t,t.return,f)}}if(t===e){W=null;break}if(n=t.sibling,n!==null){n.return=t.return,W=n;break}W=t.return}}function qd(e){for(;W!==null;){var t=W;if(t===e){W=null;break}var n=t.sibling;if(n!==null){n.return=t.return,W=n;break}W=t.return}}function ef(e){for(;W!==null;){var t=W;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Qa(4,t)}catch(s){he(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(s){he(t,o,s)}}var l=t.return;try{Hs(t)}catch(s){he(t,l,s)}break;case 5:var a=t.return;try{Hs(t)}catch(s){he(t,a,s)}}}catch(s){he(t,t.return,s)}if(t===e){W=null;break}var i=t.sibling;if(i!==null){i.return=t.return,W=i;break}W=t.return}}var f1=Math.ceil,ka=nn.ReactCurrentDispatcher,ac=nn.ReactCurrentOwner,vt=nn.ReactCurrentBatchConfig,ne=0,_e=null,Ee=null,Ie=0,rt=0,$r=Ln(0),Pe=0,qo=null,qn=0,Ya=0,ic=0,Ro=null,Ye=null,sc=0,Br=1/0,Ut=null,Pa=!1,Gs=null,$n=null,jl=!1,vn=null,Ta=0,jo=0,Qs=null,ra=-1,oa=0;function Ve(){return ne&6?ye():ra!==-1?ra:ra=ye()}function kn(e){return e.mode&1?ne&2&&Ie!==0?Ie&-Ie:Yy.transition!==null?(oa===0&&(oa=im()),oa):(e=oe,e!==0||(e=window.event,e=e===void 0?16:mm(e.type)),e):1}function Tt(e,t,n,r){if(50<jo)throw jo=0,Qs=null,Error(j(185));il(e,n,r),(!(ne&2)||e!==_e)&&(e===_e&&(!(ne&2)&&(Ya|=n),Pe===4&&pn(e,Ie)),et(e,r),n===1&&ne===0&&!(t.mode&1)&&(Br=ye()+500,Ua&&In()))}function et(e,t){var n=e.callbackNode;Y0(e,t);var r=da(e,e===_e?Ie:0);if(r===0)n!==null&&ud(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ud(n),t===1)e.tag===0?Qy(tf.bind(null,e)):Im(tf.bind(null,e)),Hy(function(){!(ne&6)&&In()}),n=null;else{switch(sm(r)){case 1:n=Ru;break;case 4:n=lm;break;case 16:n=ca;break;case 536870912:n=am;break;default:n=ca}n=Mh(n,Eh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Eh(e,t){if(ra=-1,oa=0,ne&6)throw Error(j(327));var n=e.callbackNode;if(Lr()&&e.callbackNode!==n)return null;var r=da(e,e===_e?Ie:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Na(e,r);else{t=r;var o=ne;ne|=2;var l=kh();(_e!==e||Ie!==t)&&(Ut=null,Br=ye()+500,Gn(e,t));do try{h1();break}catch(i){$h(e,i)}while(!0);Gu(),ka.current=l,ne=o,Ee!==null?t=0:(_e=null,Ie=0,t=Pe)}if(t!==0){if(t===2&&(o=xs(e),o!==0&&(r=o,t=Ys(e,o))),t===1)throw n=qo,Gn(e,0),pn(e,r),et(e,ye()),n;if(t===6)pn(e,r);else{if(o=e.current.alternate,!(r&30)&&!p1(o)&&(t=Na(e,r),t===2&&(l=xs(e),l!==0&&(r=l,t=Ys(e,l))),t===1))throw n=qo,Gn(e,0),pn(e,r),et(e,ye()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:Wn(e,Ye,Ut);break;case 3:if(pn(e,r),(r&130023424)===r&&(t=sc+500-ye(),10<t)){if(da(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ve(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ns(Wn.bind(null,e,Ye,Ut),t);break}Wn(e,Ye,Ut);break;case 4:if(pn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-Pt(r);l=1<<a,a=t[a],a>o&&(o=a),r&=~l}if(r=o,r=ye()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*f1(r/1960))-r,10<r){e.timeoutHandle=Ns(Wn.bind(null,e,Ye,Ut),r);break}Wn(e,Ye,Ut);break;case 5:Wn(e,Ye,Ut);break;default:throw Error(j(329))}}}return et(e,ye()),e.callbackNode===n?Eh.bind(null,e):null}function Ys(e,t){var n=Ro;return e.current.memoizedState.isDehydrated&&(Gn(e,t).flags|=256),e=Na(e,t),e!==2&&(t=Ye,Ye=n,t!==null&&Xs(t)),e}function Xs(e){Ye===null?Ye=e:Ye.push.apply(Ye,e)}function p1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],l=o.getSnapshot;o=o.value;try{if(!Nt(l(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pn(e,t){for(t&=~ic,t&=~Ya,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Pt(t),r=1<<n;e[n]=-1,t&=~r}}function tf(e){if(ne&6)throw Error(j(327));Lr();var t=da(e,0);if(!(t&1))return et(e,ye()),null;var n=Na(e,t);if(e.tag!==0&&n===2){var r=xs(e);r!==0&&(t=r,n=Ys(e,r))}if(n===1)throw n=qo,Gn(e,0),pn(e,t),et(e,ye()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Wn(e,Ye,Ut),et(e,ye()),null}function uc(e,t){var n=ne;ne|=1;try{return e(t)}finally{ne=n,ne===0&&(Br=ye()+500,Ua&&In())}}function er(e){vn!==null&&vn.tag===0&&!(ne&6)&&Lr();var t=ne;ne|=1;var n=vt.transition,r=oe;try{if(vt.transition=null,oe=1,e)return e()}finally{oe=r,vt.transition=n,ne=t,!(ne&6)&&In()}}function cc(){rt=$r.current,ce($r)}function Gn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,By(n)),Ee!==null)for(n=Ee.return;n!==null;){var r=n;switch(Hu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&va();break;case 3:Wr(),ce(Je),ce(Ae),qu();break;case 5:Ju(r);break;case 4:Wr();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:Qu(r.type._context);break;case 22:case 23:cc()}n=n.return}if(_e=e,Ee=e=Pn(e.current,null),Ie=rt=t,Pe=0,qo=null,ic=Ya=qn=0,Ye=Ro=null,Bn!==null){for(t=0;t<Bn.length;t++)if(n=Bn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,l=n.pending;if(l!==null){var a=l.next;l.next=o,r.next=a}n.pending=r}Bn=null}return e}function $h(e,t){do{var n=Ee;try{if(Gu(),ea.current=$a,Ea){for(var r=pe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ea=!1}if(Jn=0,Ne=ke=pe=null,Lo=!1,Xo=0,ac.current=null,n===null||n.return===null){Pe=1,qo=t,Ee=null;break}e:{var l=e,a=n.return,i=n,s=t;if(t=Ie,i.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var u=s,c=i,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var v=Vd(a);if(v!==null){v.flags&=-257,Bd(v,a,i,l,t),v.mode&1&&Wd(l,u,t),t=v,s=u;var y=t.updateQueue;if(y===null){var b=new Set;b.add(s),t.updateQueue=b}else y.add(s);break e}else{if(!(t&1)){Wd(l,u,t),dc();break e}s=Error(j(426))}}else if(de&&i.mode&1){var S=Vd(a);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Bd(S,a,i,l,t),Uu(Vr(s,i));break e}}l=s=Vr(s,i),Pe!==4&&(Pe=2),Ro===null?Ro=[l]:Ro.push(l),l=a;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var h=sh(l,s,t);jd(l,h);break e;case 1:i=s;var m=l.type,g=l.stateNode;if(!(l.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&($n===null||!$n.has(g)))){l.flags|=65536,t&=-t,l.lanes|=t;var x=uh(l,i,t);jd(l,x);break e}}l=l.return}while(l!==null)}Th(n)}catch(E){t=E,Ee===n&&n!==null&&(Ee=n=n.return);continue}break}while(!0)}function kh(){var e=ka.current;return ka.current=$a,e===null?$a:e}function dc(){(Pe===0||Pe===3||Pe===2)&&(Pe=4),_e===null||!(qn&268435455)&&!(Ya&268435455)||pn(_e,Ie)}function Na(e,t){var n=ne;ne|=2;var r=kh();(_e!==e||Ie!==t)&&(Ut=null,Gn(e,t));do try{m1();break}catch(o){$h(e,o)}while(!0);if(Gu(),ne=n,ka.current=r,Ee!==null)throw Error(j(261));return _e=null,Ie=0,Pe}function m1(){for(;Ee!==null;)Ph(Ee)}function h1(){for(;Ee!==null&&!D0();)Ph(Ee)}function Ph(e){var t=_h(e.alternate,e,rt);e.memoizedProps=e.pendingProps,t===null?Th(e):Ee=t,ac.current=null}function Th(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=s1(n,t),n!==null){n.flags&=32767,Ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Pe=6,Ee=null;return}}else if(n=i1(n,t,rt),n!==null){Ee=n;return}if(t=t.sibling,t!==null){Ee=t;return}Ee=t=e}while(t!==null);Pe===0&&(Pe=5)}function Wn(e,t,n){var r=oe,o=vt.transition;try{vt.transition=null,oe=1,v1(e,t,n,r)}finally{vt.transition=o,oe=r}return null}function v1(e,t,n,r){do Lr();while(vn!==null);if(ne&6)throw Error(j(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(X0(e,l),e===_e&&(Ee=_e=null,Ie=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||jl||(jl=!0,Mh(ca,function(){return Lr(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=vt.transition,vt.transition=null;var a=oe;oe=1;var i=ne;ne|=4,ac.current=null,c1(e,n),Sh(n,e),zy(Ps),fa=!!ks,Ps=ks=null,e.current=n,d1(n),W0(),ne=i,oe=a,vt.transition=l}else e.current=n;if(jl&&(jl=!1,vn=e,Ta=o),l=e.pendingLanes,l===0&&($n=null),H0(n.stateNode),et(e,ye()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Pa)throw Pa=!1,e=Gs,Gs=null,e;return Ta&1&&e.tag!==0&&Lr(),l=e.pendingLanes,l&1?e===Qs?jo++:(jo=0,Qs=e):jo=0,In(),null}function Lr(){if(vn!==null){var e=sm(Ta),t=vt.transition,n=oe;try{if(vt.transition=null,oe=16>e?16:e,vn===null)var r=!1;else{if(e=vn,vn=null,Ta=0,ne&6)throw Error(j(331));var o=ne;for(ne|=4,W=e.current;W!==null;){var l=W,a=l.child;if(W.flags&16){var i=l.deletions;if(i!==null){for(var s=0;s<i.length;s++){var u=i[s];for(W=u;W!==null;){var c=W;switch(c.tag){case 0:case 11:case 15:Io(8,c,l)}var d=c.child;if(d!==null)d.return=c,W=d;else for(;W!==null;){c=W;var f=c.sibling,v=c.return;if(bh(c),c===u){W=null;break}if(f!==null){f.return=v,W=f;break}W=v}}}var y=l.alternate;if(y!==null){var b=y.child;if(b!==null){y.child=null;do{var S=b.sibling;b.sibling=null,b=S}while(b!==null)}}W=l}}if(l.subtreeFlags&2064&&a!==null)a.return=l,W=a;else e:for(;W!==null;){if(l=W,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Io(9,l,l.return)}var h=l.sibling;if(h!==null){h.return=l.return,W=h;break e}W=l.return}}var m=e.current;for(W=m;W!==null;){a=W;var g=a.child;if(a.subtreeFlags&2064&&g!==null)g.return=a,W=g;else e:for(a=m;W!==null;){if(i=W,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Qa(9,i)}}catch(E){he(i,i.return,E)}if(i===a){W=null;break e}var x=i.sibling;if(x!==null){x.return=i.return,W=x;break e}W=i.return}}if(ne=o,In(),Ot&&typeof Ot.onPostCommitFiberRoot=="function")try{Ot.onPostCommitFiberRoot(Da,e)}catch{}r=!0}return r}finally{oe=n,vt.transition=t}}return!1}function nf(e,t,n){t=Vr(n,t),t=sh(e,t,1),e=En(e,t,1),t=Ve(),e!==null&&(il(e,1,t),et(e,t))}function he(e,t,n){if(e.tag===3)nf(e,e,n);else for(;t!==null;){if(t.tag===3){nf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&($n===null||!$n.has(r))){e=Vr(n,e),e=uh(t,e,1),t=En(t,e,1),e=Ve(),t!==null&&(il(t,1,e),et(t,e));break}}t=t.return}}function g1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ve(),e.pingedLanes|=e.suspendedLanes&n,_e===e&&(Ie&n)===n&&(Pe===4||Pe===3&&(Ie&130023424)===Ie&&500>ye()-sc?Gn(e,0):ic|=n),et(e,t)}function Nh(e,t){t===0&&(e.mode&1?(t=$l,$l<<=1,!($l&130023424)&&($l=4194304)):t=1);var n=Ve();e=en(e,t),e!==null&&(il(e,t,n),et(e,n))}function y1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Nh(e,n)}function b1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),Nh(e,n)}var _h;_h=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Je.current)Xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Xe=!1,a1(e,t,n);Xe=!!(e.flags&131072)}else Xe=!1,de&&t.flags&1048576&&Rm(t,ba,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;na(e,t),e=t.pendingProps;var o=Fr(t,Ae.current);Mr(t,n),o=tc(null,t,r,e,o,n);var l=nc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(r)?(l=!0,ga(t)):l=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Xu(t),o.updater=Ga,t.stateNode=o,o._reactInternals=t,zs(t,r,e,n),t=As(null,t,r,!0,l,n)):(t.tag=0,de&&l&&Bu(t),We(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(na(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=x1(r),e=Ct(r,e),o){case 0:t=Fs(null,t,r,e,n);break e;case 1:t=Kd(null,t,r,e,n);break e;case 11:t=Hd(null,t,r,e,n);break e;case 14:t=Ud(null,t,r,Ct(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Fs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Kd(e,t,r,o,n);case 3:e:{if(ph(t),e===null)throw Error(j(387));r=t.pendingProps,l=t.memoizedState,o=l.element,Dm(e,t),Sa(t,r,null,n);var a=t.memoizedState;if(r=a.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){o=Vr(Error(j(423)),t),t=Gd(e,t,r,n,o);break e}else if(r!==o){o=Vr(Error(j(424)),t),t=Gd(e,t,r,n,o);break e}else for(ot=Cn(t.stateNode.containerInfo.firstChild),lt=t,de=!0,kt=null,n=Fm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ar(),r===o){t=tn(e,t,n);break e}We(e,t,r,n)}t=t.child}return t;case 5:return Wm(t),e===null&&Is(t),r=t.type,o=t.pendingProps,l=e!==null?e.memoizedProps:null,a=o.children,Ts(r,o)?a=null:l!==null&&Ts(r,l)&&(t.flags|=32),fh(e,t),We(e,t,a,n),t.child;case 6:return e===null&&Is(t),null;case 13:return mh(e,t,n);case 4:return Zu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Dr(t,null,r,n):We(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Hd(e,t,r,o,n);case 7:return We(e,t,t.pendingProps,n),t.child;case 8:return We(e,t,t.pendingProps.children,n),t.child;case 12:return We(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,l=t.memoizedProps,a=o.value,ie(wa,r._currentValue),r._currentValue=a,l!==null)if(Nt(l.value,a)){if(l.children===o.children&&!Je.current){t=tn(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var i=l.dependencies;if(i!==null){a=l.child;for(var s=i.firstContext;s!==null;){if(s.context===r){if(l.tag===1){s=Zt(-1,n&-n),s.tag=2;var u=l.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?s.next=s:(s.next=c.next,c.next=s),u.pending=s}}l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Rs(l.return,n,t),i.lanes|=n;break}s=s.next}}else if(l.tag===10)a=l.type===t.type?null:l.child;else if(l.tag===18){if(a=l.return,a===null)throw Error(j(341));a.lanes|=n,i=a.alternate,i!==null&&(i.lanes|=n),Rs(a,n,t),a=l.sibling}else a=l.child;if(a!==null)a.return=l;else for(a=l;a!==null;){if(a===t){a=null;break}if(l=a.sibling,l!==null){l.return=a.return,a=l;break}a=a.return}l=a}We(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Mr(t,n),o=gt(o),r=r(o),t.flags|=1,We(e,t,r,n),t.child;case 14:return r=t.type,o=Ct(r,t.pendingProps),o=Ct(r.type,o),Ud(e,t,r,o,n);case 15:return ch(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),na(e,t),t.tag=1,qe(r)?(e=!0,ga(t)):e=!1,Mr(t,n),ih(t,r,o),zs(t,r,o,n),As(null,t,r,!0,e,n);case 19:return hh(e,t,n);case 22:return dh(e,t,n)}throw Error(j(156,t.tag))};function Mh(e,t){return om(e,t)}function w1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function mt(e,t,n,r){return new w1(e,t,n,r)}function fc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function x1(e){if(typeof e=="function")return fc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Mu)return 11;if(e===Lu)return 14}return 2}function Pn(e,t){var n=e.alternate;return n===null?(n=mt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function la(e,t,n,r,o,l){var a=2;if(r=e,typeof e=="function")fc(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case hr:return Qn(n.children,o,l,t);case _u:a=8,o|=8;break;case as:return e=mt(12,n,t,o|2),e.elementType=as,e.lanes=l,e;case is:return e=mt(13,n,t,o),e.elementType=is,e.lanes=l,e;case ss:return e=mt(19,n,t,o),e.elementType=ss,e.lanes=l,e;case Wp:return Xa(n,o,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ap:a=10;break e;case Dp:a=9;break e;case Mu:a=11;break e;case Lu:a=14;break e;case cn:a=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=mt(a,n,t,o),t.elementType=e,t.type=r,t.lanes=l,t}function Qn(e,t,n,r){return e=mt(7,e,r,t),e.lanes=n,e}function Xa(e,t,n,r){return e=mt(22,e,r,t),e.elementType=Wp,e.lanes=n,e.stateNode={isHidden:!1},e}function Wi(e,t,n){return e=mt(6,e,null,t),e.lanes=n,e}function Vi(e,t,n){return t=mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function S1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Si(0),this.expirationTimes=Si(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Si(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function pc(e,t,n,r,o,l,a,i,s){return e=new S1(e,t,n,i,s),t===1?(t=1,l===!0&&(t|=8)):t=0,l=mt(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Xu(l),e}function C1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Lh(e){if(!e)return _n;e=e._reactInternals;e:{if(ir(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(qe(n))return Lm(e,n,t)}return t}function Ih(e,t,n,r,o,l,a,i,s){return e=pc(n,r,!0,e,o,l,a,i,s),e.context=Lh(null),n=e.current,r=Ve(),o=kn(n),l=Zt(r,o),l.callback=t??null,En(n,l,o),e.current.lanes=o,il(e,o,r),et(e,r),e}function Za(e,t,n,r){var o=t.current,l=Ve(),a=kn(o);return n=Lh(n),t.context===null?t.context=n:t.pendingContext=n,t=Zt(l,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=En(o,t,a),e!==null&&(Tt(e,o,a,l),ql(e,o,a)),a}function _a(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function rf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function mc(e,t){rf(e,t),(e=e.alternate)&&rf(e,t)}function E1(){return null}var Rh=typeof reportError=="function"?reportError:function(e){console.error(e)};function hc(e){this._internalRoot=e}Ja.prototype.render=hc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));Za(e,t,null,null)};Ja.prototype.unmount=hc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;er(function(){Za(null,e,null,null)}),t[qt]=null}};function Ja(e){this._internalRoot=e}Ja.prototype.unstable_scheduleHydration=function(e){if(e){var t=dm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<fn.length&&t!==0&&t<fn[n].priority;n++);fn.splice(n,0,e),n===0&&pm(e)}};function vc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function qa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function of(){}function $1(e,t,n,r,o){if(o){if(typeof r=="function"){var l=r;r=function(){var u=_a(a);l.call(u)}}var a=Ih(t,r,e,0,null,!1,!1,"",of);return e._reactRootContainer=a,e[qt]=a.current,Uo(e.nodeType===8?e.parentNode:e),er(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var i=r;r=function(){var u=_a(s);i.call(u)}}var s=pc(e,0,!1,null,null,!1,!1,"",of);return e._reactRootContainer=s,e[qt]=s.current,Uo(e.nodeType===8?e.parentNode:e),er(function(){Za(t,s,n,r)}),s}function ei(e,t,n,r,o){var l=n._reactRootContainer;if(l){var a=l;if(typeof o=="function"){var i=o;o=function(){var s=_a(a);i.call(s)}}Za(t,a,e,o)}else a=$1(n,t,e,o,r);return _a(a)}um=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=xo(t.pendingLanes);n!==0&&(ju(t,n|1),et(t,ye()),!(ne&6)&&(Br=ye()+500,In()))}break;case 13:er(function(){var r=en(e,1);if(r!==null){var o=Ve();Tt(r,e,1,o)}}),mc(e,1)}};zu=function(e){if(e.tag===13){var t=en(e,134217728);if(t!==null){var n=Ve();Tt(t,e,134217728,n)}mc(e,134217728)}};cm=function(e){if(e.tag===13){var t=kn(e),n=en(e,t);if(n!==null){var r=Ve();Tt(n,e,t,r)}mc(e,t)}};dm=function(){return oe};fm=function(e,t){var n=oe;try{return oe=e,t()}finally{oe=n}};ys=function(e,t,n){switch(t){case"input":if(ds(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ha(r);if(!o)throw Error(j(90));Bp(r),ds(r,o)}}}break;case"textarea":Up(e,n);break;case"select":t=n.value,t!=null&&Pr(e,!!n.multiple,t,!1)}};Jp=uc;qp=er;var k1={usingClientEntryPoint:!1,Events:[ul,br,Ha,Xp,Zp,uc]},fo={findFiberByHostInstance:Vn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},P1={bundleType:fo.bundleType,version:fo.version,rendererPackageName:fo.rendererPackageName,rendererConfig:fo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:nn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=nm(e),e===null?null:e.stateNode},findFiberByHostInstance:fo.findFiberByHostInstance||E1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zl.isDisabled&&zl.supportsFiber)try{Da=zl.inject(P1),Ot=zl}catch{}}it.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=k1;it.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!vc(t))throw Error(j(200));return C1(e,t,null,n)};it.createRoot=function(e,t){if(!vc(e))throw Error(j(299));var n=!1,r="",o=Rh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=pc(e,1,!1,null,null,n,!1,r,o),e[qt]=t.current,Uo(e.nodeType===8?e.parentNode:e),new hc(t)};it.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=nm(t),e=e===null?null:e.stateNode,e};it.flushSync=function(e){return er(e)};it.hydrate=function(e,t,n){if(!qa(t))throw Error(j(200));return ei(null,e,t,!0,n)};it.hydrateRoot=function(e,t,n){if(!vc(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,o=!1,l="",a=Rh;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Ih(t,null,e,1,n??null,o,!1,l,a),e[qt]=t.current,Uo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ja(t)};it.render=function(e,t,n){if(!qa(t))throw Error(j(200));return ei(null,e,t,!1,n)};it.unmountComponentAtNode=function(e){if(!qa(e))throw Error(j(40));return e._reactRootContainer?(er(function(){ei(null,null,e,!1,function(){e._reactRootContainer=null,e[qt]=null})}),!0):!1};it.unstable_batchedUpdates=uc;it.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!qa(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return ei(e,t,n,!1,r)};it.version="18.3.1-next-f1338f8080-20240426";function jh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(jh)}catch(e){console.error(e)}}jh(),jp.exports=it;var zh=jp.exports;const Oh=Cp(zh);var lf=zh;os.createRoot=lf.createRoot,os.hydrateRoot=lf.hydrateRoot;/**
 * @remix-run/router v1.16.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function el(){return el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},el.apply(this,arguments)}var gn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(gn||(gn={}));const af="popstate";function T1(e){e===void 0&&(e={});function t(r,o){let{pathname:l,search:a,hash:i}=r.location;return Zs("",{pathname:l,search:a,hash:i},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Ah(o)}return _1(t,n,null,e)}function $e(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Fh(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function N1(){return Math.random().toString(36).substr(2,8)}function sf(e,t){return{usr:e.state,key:e.key,idx:t}}function Zs(e,t,n,r){return n===void 0&&(n=null),el({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Yr(t):t,{state:n,key:t&&t.key||r||N1()})}function Ah(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Yr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function _1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:l=!1}=r,a=o.history,i=gn.Pop,s=null,u=c();u==null&&(u=0,a.replaceState(el({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function d(){i=gn.Pop;let S=c(),h=S==null?null:S-u;u=S,s&&s({action:i,location:b.location,delta:h})}function f(S,h){i=gn.Push;let m=Zs(b.location,S,h);u=c()+1;let g=sf(m,u),x=b.createHref(m);try{a.pushState(g,"",x)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;o.location.assign(x)}l&&s&&s({action:i,location:b.location,delta:1})}function v(S,h){i=gn.Replace;let m=Zs(b.location,S,h);u=c();let g=sf(m,u),x=b.createHref(m);a.replaceState(g,"",x),l&&s&&s({action:i,location:b.location,delta:0})}function y(S){let h=o.location.origin!=="null"?o.location.origin:o.location.href,m=typeof S=="string"?S:Ah(S);return m=m.replace(/ $/,"%20"),$e(h,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,h)}let b={get action(){return i},get location(){return e(o,a)},listen(S){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(af,d),s=S,()=>{o.removeEventListener(af,d),s=null}},createHref(S){return t(o,S)},createURL:y,encodeLocation(S){let h=y(S);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:v,go(S){return a.go(S)}};return b}var uf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(uf||(uf={}));function M1(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?Yr(t):t,o=Vh(r.pathname||"/",n);if(o==null)return null;let l=Dh(e);L1(l);let a=null;for(let i=0;a==null&&i<l.length;++i){let s=H1(o);a=W1(l[i],s)}return a}function Dh(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(l,a,i)=>{let s={relativePath:i===void 0?l.path||"":i,caseSensitive:l.caseSensitive===!0,childrenIndex:a,route:l};s.relativePath.startsWith("/")&&($e(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(r.length));let u=Tn([r,s.relativePath]),c=n.concat(s);l.children&&l.children.length>0&&($e(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Dh(l.children,t,c,u)),!(l.path==null&&!l.index)&&t.push({path:u,score:A1(u,l.index),routesMeta:c})};return e.forEach((l,a)=>{var i;if(l.path===""||!((i=l.path)!=null&&i.includes("?")))o(l,a);else for(let s of Wh(l.path))o(l,a,s)}),t}function Wh(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return o?[l,""]:[l];let a=Wh(r.join("/")),i=[];return i.push(...a.map(s=>s===""?l:[l,s].join("/"))),o&&i.push(...a),i.map(s=>e.startsWith("/")&&s===""?"/":s)}function L1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:D1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const I1=/^:[\w-]+$/,R1=3,j1=2,z1=1,O1=10,F1=-2,cf=e=>e==="*";function A1(e,t){let n=e.split("/"),r=n.length;return n.some(cf)&&(r+=F1),t&&(r+=j1),n.filter(o=>!cf(o)).reduce((o,l)=>o+(I1.test(l)?R1:l===""?z1:O1),r)}function D1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function W1(e,t){let{routesMeta:n}=e,r={},o="/",l=[];for(let a=0;a<n.length;++a){let i=n[a],s=a===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",c=V1({path:i.relativePath,caseSensitive:i.caseSensitive,end:s},u);if(!c)return null;Object.assign(r,c.params);let d=i.route;l.push({params:r,pathname:Tn([o,c.pathname]),pathnameBase:Q1(Tn([o,c.pathnameBase])),route:d}),c.pathnameBase!=="/"&&(o=Tn([o,c.pathnameBase]))}return l}function V1(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=B1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let l=o[0],a=l.replace(/(.)\/+$/,"$1"),i=o.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:v}=c;if(f==="*"){let b=i[d]||"";a=l.slice(0,l.length-b.length).replace(/(.)\/+$/,"$1")}const y=i[d];return v&&!y?u[f]=void 0:u[f]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:l,pathnameBase:a,pattern:e}}function B1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Fh(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,i,s)=>(r.push({paramName:i,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function H1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Fh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Vh(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function U1(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Yr(e):e;return{pathname:n?n.startsWith("/")?n:K1(n,t):t,search:Y1(r),hash:X1(o)}}function K1(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Bi(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function G1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Bh(e,t){let n=G1(e);return t?n.map((r,o)=>o===e.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Hh(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Yr(e):(o=el({},e),$e(!o.pathname||!o.pathname.includes("?"),Bi("?","pathname","search",o)),$e(!o.pathname||!o.pathname.includes("#"),Bi("#","pathname","hash",o)),$e(!o.search||!o.search.includes("#"),Bi("#","search","hash",o)));let l=e===""||o.pathname==="",a=l?"/":o.pathname,i;if(a==null)i=n;else{let d=t.length-1;if(!r&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),d-=1;o.pathname=f.join("/")}i=d>=0?t[d]:"/"}let s=U1(o,i),u=a&&a!=="/"&&a.endsWith("/"),c=(l||a===".")&&n.endsWith("/");return!s.pathname.endsWith("/")&&(u||c)&&(s.pathname+="/"),s}const Tn=e=>e.join("/").replace(/\/\/+/g,"/"),Q1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Y1=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,X1=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Z1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Uh=["post","put","patch","delete"];new Set(Uh);const J1=["get",...Uh];new Set(J1);/**
 * React Router v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function tl(){return tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},tl.apply(this,arguments)}const gc=p.createContext(null),q1=p.createContext(null),Xr=p.createContext(null),ti=p.createContext(null),sr=p.createContext({outlet:null,matches:[],isDataRoute:!1}),Kh=p.createContext(null);function eb(e,t){let{relative:n}=t===void 0?{}:t;dl()||$e(!1);let{basename:r,navigator:o}=p.useContext(Xr),{hash:l,pathname:a,search:i}=rb(e,{relative:n}),s=a;return r!=="/"&&(s=a==="/"?r:Tn([r,a])),o.createHref({pathname:s,search:i,hash:l})}function dl(){return p.useContext(ti)!=null}function yc(){return dl()||$e(!1),p.useContext(ti).location}function Gh(e){p.useContext(Xr).static||p.useLayoutEffect(e)}function tb(){let{isDataRoute:e}=p.useContext(sr);return e?hb():nb()}function nb(){dl()||$e(!1);let e=p.useContext(gc),{basename:t,future:n,navigator:r}=p.useContext(Xr),{matches:o}=p.useContext(sr),{pathname:l}=yc(),a=JSON.stringify(Bh(o,n.v7_relativeSplatPath)),i=p.useRef(!1);return Gh(()=>{i.current=!0}),p.useCallback(function(u,c){if(c===void 0&&(c={}),!i.current)return;if(typeof u=="number"){r.go(u);return}let d=Hh(u,JSON.parse(a),l,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Tn([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,a,l,e])}function rb(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=p.useContext(Xr),{matches:o}=p.useContext(sr),{pathname:l}=yc(),a=JSON.stringify(Bh(o,r.v7_relativeSplatPath));return p.useMemo(()=>Hh(e,JSON.parse(a),l,n==="path"),[e,a,l,n])}function ob(e,t){return lb(e,t)}function lb(e,t,n,r){dl()||$e(!1);let{navigator:o}=p.useContext(Xr),{matches:l}=p.useContext(sr),a=l[l.length-1],i=a?a.params:{};a&&a.pathname;let s=a?a.pathnameBase:"/";a&&a.route;let u=yc(),c;if(t){var d;let S=typeof t=="string"?Yr(t):t;s==="/"||(d=S.pathname)!=null&&d.startsWith(s)||$e(!1),c=S}else c=u;let f=c.pathname||"/",v=f;if(s!=="/"){let S=s.replace(/^\//,"").split("/");v="/"+f.replace(/^\//,"").split("/").slice(S.length).join("/")}let y=M1(e,{pathname:v}),b=cb(y&&y.map(S=>Object.assign({},S,{params:Object.assign({},i,S.params),pathname:Tn([s,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?s:Tn([s,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),l,n,r);return t&&b?p.createElement(ti.Provider,{value:{location:tl({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:gn.Pop}},b):b}function ab(){let e=mb(),t=Z1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return p.createElement(p.Fragment,null,p.createElement("h2",null,"Unexpected Application Error!"),p.createElement("h3",{style:{fontStyle:"italic"}},t),n?p.createElement("pre",{style:o},n):null,null)}const ib=p.createElement(ab,null);class sb extends p.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?p.createElement(sr.Provider,{value:this.props.routeContext},p.createElement(Kh.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ub(e){let{routeContext:t,match:n,children:r}=e,o=p.useContext(gc);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),p.createElement(sr.Provider,{value:t},r)}function cb(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if((l=n)!=null&&l.errors)e=n.matches;else return null}let a=e,i=(o=n)==null?void 0:o.errors;if(i!=null){let c=a.findIndex(d=>d.route.id&&(i==null?void 0:i[d.route.id])!==void 0);c>=0||$e(!1),a=a.slice(0,Math.min(a.length,c+1))}let s=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let d=a[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:v}=n,y=d.route.loader&&f[d.route.id]===void 0&&(!v||v[d.route.id]===void 0);if(d.route.lazy||y){s=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,d,f)=>{let v,y=!1,b=null,S=null;n&&(v=i&&d.route.id?i[d.route.id]:void 0,b=d.route.errorElement||ib,s&&(u<0&&f===0?(vb("route-fallback"),y=!0,S=null):u===f&&(y=!0,S=d.route.hydrateFallbackElement||null)));let h=t.concat(a.slice(0,f+1)),m=()=>{let g;return v?g=b:y?g=S:d.route.Component?g=p.createElement(d.route.Component,null):d.route.element?g=d.route.element:g=c,p.createElement(ub,{match:d,routeContext:{outlet:c,matches:h,isDataRoute:n!=null},children:g})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?p.createElement(sb,{location:n.location,revalidation:n.revalidation,component:b,error:v,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()},null)}var Qh=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Qh||{}),Yh=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Yh||{});function db(e){let t=p.useContext(gc);return t||$e(!1),t}function fb(e){let t=p.useContext(q1);return t||$e(!1),t}function pb(e){let t=p.useContext(sr);return t||$e(!1),t}function Xh(e){let t=pb(),n=t.matches[t.matches.length-1];return n.route.id||$e(!1),n.route.id}function mb(){var e;let t=p.useContext(Kh),n=fb(),r=Xh();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function hb(){let{router:e}=db(Qh.UseNavigateStable),t=Xh(Yh.UseNavigateStable),n=p.useRef(!1);return Gh(()=>{n.current=!0}),p.useCallback(function(o,l){l===void 0&&(l={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,tl({fromRouteId:t},l)))},[e,t])}const df={};function vb(e,t,n){df[e]||(df[e]=!0)}function pr(e){$e(!1)}function gb(e){let{basename:t="/",children:n=null,location:r,navigationType:o=gn.Pop,navigator:l,static:a=!1,future:i}=e;dl()&&$e(!1);let s=t.replace(/^\/*/,"/"),u=p.useMemo(()=>({basename:s,navigator:l,static:a,future:tl({v7_relativeSplatPath:!1},i)}),[s,i,l,a]);typeof r=="string"&&(r=Yr(r));let{pathname:c="/",search:d="",hash:f="",state:v=null,key:y="default"}=r,b=p.useMemo(()=>{let S=Vh(c,s);return S==null?null:{location:{pathname:S,search:d,hash:f,state:v,key:y},navigationType:o}},[s,c,d,f,v,y,o]);return b==null?null:p.createElement(Xr.Provider,{value:u},p.createElement(ti.Provider,{children:n,value:b}))}function yb(e){let{children:t,location:n}=e;return ob(Js(t),n)}new Promise(()=>{});function Js(e,t){t===void 0&&(t=[]);let n=[];return p.Children.forEach(e,(r,o)=>{if(!p.isValidElement(r))return;let l=[...t,o];if(r.type===p.Fragment){n.push.apply(n,Js(r.props.children,l));return}r.type!==pr&&$e(!1),!r.props.index||!r.props.children||$e(!1);let a={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Js(r.props.children,l)),n.push(a)}),n}/**
 * React Router DOM v6.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const bb="6";try{window.__reactRouterVersion=bb}catch{}const wb="startTransition",ff=g0[wb];function xb(e){let{basename:t,children:n,future:r,window:o}=e,l=p.useRef();l.current==null&&(l.current=T1({window:o,v5Compat:!0}));let a=l.current,[i,s]=p.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},c=p.useCallback(d=>{u&&ff?ff(()=>s(d)):s(d)},[s,u]);return p.useLayoutEffect(()=>a.listen(c),[a,c]),p.createElement(gb,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:a,future:r})}var pf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(pf||(pf={}));var mf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(mf||(mf={}));function bc(e={}){const{strict:t=!0,errorMessage:n="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:r}=e,o=p.createContext(void 0);o.displayName=r;function l(){var a;const i=p.useContext(o);if(!i&&t){const s=new Error(n);throw s.name="ContextError",(a=Error.captureStackTrace)==null||a.call(Error,s,l),s}return i}return[o.Provider,l,o]}function Sb(e){return{UNSAFE_getDOMNode(){return e.current}}}function Ze(e){const t=p.useRef(null);return p.useImperativeHandle(e,()=>t.current),t}function wc(e){return Array.isArray(e)}function Cb(e){return wc(e)&&e.length===0}function Zh(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")&&!wc(e)}function Eb(e){return Zh(e)&&Object.keys(e).length===0}function $b(e){return wc(e)?Cb(e):Zh(e)?Eb(e):e==null||e===""}function kb(e){return typeof e=="function"}var B=e=>e?"true":void 0;function Jh(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Jh(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function ee(...e){for(var t=0,n,r,o="";t<e.length;)(n=e[t++])&&(r=Jh(n))&&(o&&(o+=" "),o+=r);return o}var Pb=(...e)=>{let t=" ";for(const n of e)if(typeof n=="string"&&n.length>0){t=n;break}return t};function Tb(e){return`${e}-${Math.floor(Math.random()*1e6)}`}function Dt(e){if(!e||typeof e!="object")return"";try{return JSON.stringify(e)}catch{return""}}function Nb(e,t,n){return Math.min(Math.max(e,t),n)}var hf={};function _b(e,t,...n){const o=`[Hero UI] : ${e}`;typeof console>"u"||hf[o]||(hf[o]=!0)}function Mb(e,t){if(e!=null){if(kb(e)){e(t);return}try{e.current=t}catch{throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}}}function qh(...e){return t=>{e.forEach(n=>Mb(n,t))}}var Lb=(e,t)=>{var n;let r=[];const o=(n=p.Children.map(e,a=>p.isValidElement(a)&&a.type===t?(r.push(a),null):a))==null?void 0:n.filter(Boolean),l=r.length>=0?r:void 0;return[o,l]},Ib=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),Rb=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),vf=/^(data-.*)$/,jb=/^(aria-.*)$/,Ol=/^(on[A-Z].*)$/;function Ma(e,t={}){let{labelable:n=!0,enabled:r=!0,propNames:o,omitPropNames:l,omitEventNames:a,omitDataProps:i,omitEventProps:s}=t,u={};if(!r)return e;for(const c in e)l!=null&&l.has(c)||a!=null&&a.has(c)&&Ol.test(c)||Ol.test(c)&&!Rb.has(c)||i&&vf.test(c)||s&&Ol.test(c)||(Object.prototype.hasOwnProperty.call(e,c)&&(Ib.has(c)||n&&jb.test(c)||o!=null&&o.has(c)||vf.test(c))||Ol.test(c))&&(u[c]=e[c]);return u}var[zb,rn]=bc({name:"ProviderContext",strict:!1});const Ob=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Fb=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function ev(e){if(Intl.Locale){let n=new Intl.Locale(e).maximize(),r=typeof n.getTextInfo=="function"?n.getTextInfo():n.textInfo;if(r)return r.direction==="rtl";if(n.script)return Ob.has(n.script)}let t=e.split("-")[0];return Fb.has(t)}const tv={prefix:String(Math.round(Math.random()*1e10)),current:0},nv=q.createContext(tv),Ab=q.createContext(!1);let Hi=new WeakMap;function Db(e=!1){let t=p.useContext(nv),n=p.useRef(null);if(n.current===null&&!e){var r,o;let l=(o=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||o===void 0||(r=o.ReactCurrentOwner)===null||r===void 0?void 0:r.current;if(l){let a=Hi.get(l);a==null?Hi.set(l,{id:t.current,state:l.memoizedState}):l.memoizedState!==a.state&&(t.current=a.id,Hi.delete(l))}n.current=++t.current}return n.current}function Wb(e){let t=p.useContext(nv),n=Db(!!e),r=`react-aria${t.prefix}`;return e||`${r}-${n}`}function Vb(e){let t=q.useId(),[n]=p.useState(ni()),r=n?"react-aria":`react-aria${tv.prefix}`;return e||`${r}-${t}`}const Bb=typeof q.useId=="function"?Vb:Wb;function Hb(){return!1}function Ub(){return!0}function Kb(e){return()=>{}}function ni(){return typeof q.useSyncExternalStore=="function"?q.useSyncExternalStore(Kb,Hb,Ub):p.useContext(Ab)}const Gb=Symbol.for("react-aria.i18n.locale");function rv(){let e=typeof window<"u"&&window[Gb]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:ev(e)?"rtl":"ltr"}}let qs=rv(),Co=new Set;function gf(){qs=rv();for(let e of Co)e(qs)}function ov(){let e=ni(),[t,n]=p.useState(qs);return p.useEffect(()=>(Co.size===0&&window.addEventListener("languagechange",gf),Co.add(n),()=>{Co.delete(n),Co.size===0&&window.removeEventListener("languagechange",gf)}),[]),e?{locale:"en-US",direction:"ltr"}:t}const lv=q.createContext(null);function Qb(e){let{locale:t,children:n}=e,r=ov(),o=q.useMemo(()=>t?{locale:t,direction:ev(t)?"rtl":"ltr"}:r,[r,t]);return q.createElement(lv.Provider,{value:o},n)}function Yb(){let e=ov();return p.useContext(lv)||e}function Xb(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Zb(e,t,n){Xb(e,t),t.set(e,n)}const be=typeof document<"u"?q.useLayoutEffect:()=>{};function Oe(e){const t=p.useRef(null);return be(()=>{t.current=e},[e]),p.useCallback((...n)=>{const r=t.current;return r==null?void 0:r(...n)},[])}function Jb(e){let[t,n]=p.useState(e),r=p.useRef(null),o=Oe(()=>{if(!r.current)return;let a=r.current.next();if(a.done){r.current=null;return}t===a.value?o():n(a.value)});be(()=>{r.current&&o()});let l=Oe(a=>{r.current=a(t),o()});return[t,l]}let qb=!!(typeof window<"u"&&window.document&&window.document.createElement),Ir=new Map,Eo;typeof FinalizationRegistry<"u"&&(Eo=new FinalizationRegistry(e=>{Ir.delete(e)}));function nl(e){let[t,n]=p.useState(e),r=p.useRef(null),o=Bb(t),l=p.useRef(null);if(Eo&&Eo.register(l,o),qb){const a=Ir.get(o);a&&!a.includes(r)?a.push(r):Ir.set(o,[r])}return be(()=>{let a=o;return()=>{Eo&&Eo.unregister(l),Ir.delete(a)}},[o]),p.useEffect(()=>{let a=r.current;return a&&n(a),()=>{a&&(r.current=null)}}),o}function ew(e,t){if(e===t)return e;let n=Ir.get(e);if(n)return n.forEach(o=>o.current=t),t;let r=Ir.get(t);return r?(r.forEach(o=>o.current=e),e):t}function yf(e=[]){let t=nl(),[n,r]=Jb(t),o=p.useCallback(()=>{r(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,r]);return be(o,[t,o,...e]),n}function At(...e){return(...t)=>{for(let n of e)typeof n=="function"&&n(...t)}}const le=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},ht=e=>e&&"window"in e&&e.window===e?e:le(e).defaultView||window;function tw(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function nw(e){return tw(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}let rw=!1;function ri(){return rw}function Qe(e,t){if(!ri())return t&&e?e.contains(t):!1;if(!e||!t)return!1;let n=t;for(;n!==null;){if(n===e)return!0;n.tagName==="SLOT"&&n.assignedSlot?n=n.assignedSlot.parentNode:nw(n)?n=n.host:n=n.parentNode}return!1}const tt=(e=document)=>{var t;if(!ri())return e.activeElement;let n=e.activeElement;for(;n&&"shadowRoot"in n&&(!((t=n.shadowRoot)===null||t===void 0)&&t.activeElement);)n=n.shadowRoot.activeElement;return n};function ge(e){return ri()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}class ow{get currentNode(){return this._currentNode}set currentNode(t){if(!Qe(this.root,t))throw new Error("Cannot set currentNode to a node that is not contained by the root node.");const n=[];let r=t,o=t;for(this._currentNode=t;r&&r!==this.root;)if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const a=r,i=this._doc.createTreeWalker(a,this.whatToShow,{acceptNode:this._acceptNode});n.push(i),i.currentNode=o,this._currentSetFor.add(i),r=o=a.host}else r=r.parentNode;const l=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});n.push(l),l.currentNode=o,this._currentSetFor.add(l),this._walkerStack=n}get doc(){return this._doc}firstChild(){let t=this.currentNode,n=this.nextNode();return Qe(t,n)?(n&&(this.currentNode=n),n):(this.currentNode=t,null)}lastChild(){let n=this._walkerStack[0].lastChild();return n&&(this.currentNode=n),n}nextNode(){const t=this._walkerStack[0].nextNode();if(t){if(t.shadowRoot){var n;let o;if(typeof this.filter=="function"?o=this.filter(t):!((n=this.filter)===null||n===void 0)&&n.acceptNode&&(o=this.filter.acceptNode(t)),o===NodeFilter.FILTER_ACCEPT)return this.currentNode=t,t;let l=this.nextNode();return l&&(this.currentNode=l),l}return t&&(this.currentNode=t),t}else if(this._walkerStack.length>1){this._walkerStack.shift();let r=this.nextNode();return r&&(this.currentNode=r),r}else return null}previousNode(){const t=this._walkerStack[0];if(t.currentNode===t.root){if(this._currentSetFor.has(t))if(this._currentSetFor.delete(t),this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null;return null}const n=t.previousNode();if(n){if(n.shadowRoot){var r;let l;if(typeof this.filter=="function"?l=this.filter(n):!((r=this.filter)===null||r===void 0)&&r.acceptNode&&(l=this.filter.acceptNode(n)),l===NodeFilter.FILTER_ACCEPT)return n&&(this.currentNode=n),n;let a=this.lastChild();return a&&(this.currentNode=a),a}return n&&(this.currentNode=n),n}else if(this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(t,n,r,o){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=a=>{if(a.nodeType===Node.ELEMENT_NODE){const s=a.shadowRoot;if(s){const u=this._doc.createTreeWalker(s,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(u),NodeFilter.FILTER_ACCEPT}else{var i;if(typeof this.filter=="function")return this.filter(a);if(!((i=this.filter)===null||i===void 0)&&i.acceptNode)return this.filter.acceptNode(a);if(this.filter===null)return NodeFilter.FILTER_ACCEPT}}return NodeFilter.FILTER_SKIP},this._doc=t,this.root=n,this.filter=o??null,this.whatToShow=r??NodeFilter.SHOW_ALL,this._currentNode=n,this._walkerStack.unshift(t.createTreeWalker(n,r,this._acceptNode));const l=n.shadowRoot;if(l){const a=this._doc.createTreeWalker(l,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(a)}}}function lw(e,t,n,r){return ri()?new ow(e,t,n,r):e.createTreeWalker(t,n,r)}function av(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=av(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function eu(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=av(e))&&(r&&(r+=" "),r+=t);return r}function Z(...e){let t={...e[0]};for(let n=1;n<e.length;n++){let r=e[n];for(let o in r){let l=t[o],a=r[o];typeof l=="function"&&typeof a=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=At(l,a):(o==="className"||o==="UNSAFE_className")&&typeof l=="string"&&typeof a=="string"?t[o]=eu(l,a):o==="id"&&l&&a?t.id=ew(l,a):t[o]=a!==void 0?a:l}}return t}function aw(...e){return e.length===1&&e[0]?e[0]:t=>{let n=!1;const r=e.map(o=>{const l=bf(o,t);return n||(n=typeof l=="function"),l});if(n)return()=>{r.forEach((o,l)=>{typeof o=="function"?o():bf(e[l],null)})}}}function bf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}const iw=new Set(["id"]),sw=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),uw=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),cw=/^(data-.*)$/;function Zr(e,t={}){let{labelable:n,isLink:r,propNames:o}=t,l={};for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(iw.has(a)||n&&sw.has(a)||r&&uw.has(a)||o!=null&&o.has(a)||cw.test(a))&&(l[a]=e[a]);return l}function Hr(e){if(dw())e.focus({preventScroll:!0});else{let t=fw(e);e.focus(),pw(t)}}let Fl=null;function dw(){if(Fl==null){Fl=!1;try{document.createElement("div").focus({get preventScroll(){return Fl=!0,!0}})}catch{}}return Fl}function fw(e){let t=e.parentNode,n=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&n.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),n}function pw(e){for(let{element:t,scrollTop:n,scrollLeft:r}of e)t.scrollTop=n,t.scrollLeft=r}function oi(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(n=>e.test(n.brand)))||e.test(window.navigator.userAgent)}function xc(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function Rn(e){let t=null;return()=>(t==null&&(t=e()),t)}const rl=Rn(function(){return xc(/^Mac/i)}),mw=Rn(function(){return xc(/^iPhone/i)}),iv=Rn(function(){return xc(/^iPad/i)||rl()&&navigator.maxTouchPoints>1}),Sc=Rn(function(){return mw()||iv()}),sv=Rn(function(){return oi(/AppleWebKit/i)&&!uv()}),uv=Rn(function(){return oi(/Chrome/i)}),Cc=Rn(function(){return oi(/Android/i)}),hw=Rn(function(){return oi(/Firefox/i)}),cv=p.createContext({isNative:!0,open:gw,useHref:e=>e});function vw(e){let{children:t,navigate:n,useHref:r}=e,o=p.useMemo(()=>({isNative:!1,open:(l,a,i,s)=>{pv(l,u=>{fv(u,a)?n(i,s):tr(u,a)})},useHref:r||(l=>l)}),[n,r]);return q.createElement(cv.Provider,{value:o},t)}function dv(){return p.useContext(cv)}function fv(e,t){let n=e.getAttribute("target");return(!n||n==="_self")&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function tr(e,t,n=!0){var r,o;let{metaKey:l,ctrlKey:a,altKey:i,shiftKey:s}=t;hw()&&(!((o=window.event)===null||o===void 0||(r=o.type)===null||r===void 0)&&r.startsWith("key"))&&e.target==="_blank"&&(rl()?l=!0:a=!0);let u=sv()&&rl()&&!iv()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:l,ctrlKey:a,altKey:i,shiftKey:s}):new MouseEvent("click",{metaKey:l,ctrlKey:a,altKey:i,shiftKey:s,bubbles:!0,cancelable:!0});tr.isOpening=n,Hr(e),e.dispatchEvent(u),tr.isOpening=!1}tr.isOpening=!1;function pv(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let n=document.createElement("a");n.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(n.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(n.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(n.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(n.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(n.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(n),t(n),e.removeChild(n)}}function gw(e,t){pv(e,n=>tr(n,t))}function yw(e){let t=dv();var n;const r=t.useHref((n=e==null?void 0:e.href)!==null&&n!==void 0?n:"");return{href:e!=null&&e.href?r:void 0,target:e==null?void 0:e.target,rel:e==null?void 0:e.rel,download:e==null?void 0:e.download,ping:e==null?void 0:e.ping,referrerPolicy:e==null?void 0:e.referrerPolicy}}let mn=new Map,tu=new Set;function wf(){if(typeof window>"u")return;function e(r){return"propertyName"in r}let t=r=>{if(!e(r)||!r.target)return;let o=mn.get(r.target);o||(o=new Set,mn.set(r.target,o),r.target.addEventListener("transitioncancel",n,{once:!0})),o.add(r.propertyName)},n=r=>{if(!e(r)||!r.target)return;let o=mn.get(r.target);if(o&&(o.delete(r.propertyName),o.size===0&&(r.target.removeEventListener("transitioncancel",n),mn.delete(r.target)),mn.size===0)){for(let l of tu)l();tu.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",n)}typeof document<"u"&&(document.readyState!=="loading"?wf():document.addEventListener("DOMContentLoaded",wf));function bw(){for(const[e]of mn)"isConnected"in e&&!e.isConnected&&mn.delete(e)}function mv(e){requestAnimationFrame(()=>{bw(),mn.size===0?e():tu.add(e)})}function Ec(){let e=p.useRef(new Map),t=p.useCallback((o,l,a,i)=>{let s=i!=null&&i.once?(...u)=>{e.current.delete(a),a(...u)}:a;e.current.set(a,{type:l,eventTarget:o,fn:s,options:i}),o.addEventListener(l,s,i)},[]),n=p.useCallback((o,l,a,i)=>{var s;let u=((s=e.current.get(a))===null||s===void 0?void 0:s.fn)||a;o.removeEventListener(l,u,i),e.current.delete(a)},[]),r=p.useCallback(()=>{e.current.forEach((o,l)=>{n(o.eventTarget,o.type,l,o.options)})},[n]);return p.useEffect(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}function ww(e,t){let{id:n,"aria-label":r,"aria-labelledby":o}=e;return n=nl(n),o&&r?o=[...new Set([n,...o.trim().split(/\s+/)])].join(" "):o&&(o=o.trim().split(/\s+/).join(" ")),!r&&!o&&t&&(r=t),{id:n,"aria-label":r,"aria-labelledby":o}}function xw(e){const t=p.useRef(null),n=p.useRef(void 0),r=p.useCallback(o=>{if(typeof e=="function"){const l=e,a=l(o);return()=>{typeof a=="function"?a():l(null)}}else if(e)return e.current=o,()=>{e.current=null}},[e]);return p.useMemo(()=>({get current(){return t.current},set current(o){t.current=o,n.current&&(n.current(),n.current=void 0),o!=null&&(n.current=r(o))}}),[r])}function Sw(){return typeof window.ResizeObserver<"u"}function nu(e){const{ref:t,box:n,onResize:r}=e;p.useEffect(()=>{let o=t==null?void 0:t.current;if(o)if(Sw()){const l=new window.ResizeObserver(a=>{a.length&&r()});return l.observe(o,{box:n}),()=>{o&&l.unobserve(o)}}else return window.addEventListener("resize",r,!1),()=>{window.removeEventListener("resize",r,!1)}},[r,t,n])}function hv(e,t){be(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}function xf(e,t){if(!e)return!1;let n=window.getComputedStyle(e),r=/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}function vv(e,t){let n=e;for(xf(n,t)&&(n=n.parentElement);n&&!xf(n,t);)n=n.parentElement;return n||document.scrollingElement||document.documentElement}function gv(e){return e.mozInputSource===0&&e.isTrusted?!0:Cc()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Cw(e){return!Cc()&&e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"}function yv(e,t,n){let r=p.useRef(t),o=Oe(()=>{n&&n(r.current)});p.useEffect(()=>{var l;let a=e==null||(l=e.current)===null||l===void 0?void 0:l.form;return a==null||a.addEventListener("reset",o),()=>{a==null||a.removeEventListener("reset",o)}},[e,o])}const $c=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],Ew=$c.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";$c.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const $w=$c.join(':not([hidden]):not([tabindex="-1"]),');function bv(e){return e.matches(Ew)}function kw(e){return e.matches($w)}function fl(e,t,n){let[r,o]=p.useState(e||t),l=p.useRef(e!==void 0),a=e!==void 0;p.useEffect(()=>{l.current,l.current=a},[a]);let i=a?e:r,s=p.useCallback((u,...c)=>{let d=(f,...v)=>{n&&(Object.is(i,f)||n(f,...v)),a||(i=f)};typeof u=="function"?o((v,...y)=>{let b=u(a?i:v,...y);return d(b,...c),a?v:b}):(a||o(u),d(u,...c))},[a,i,n]);return[i,s]}function ru(e,t=-1/0,n=1/0){return Math.min(Math.max(e,t),n)}const zt={top:"top",bottom:"top",left:"left",right:"left"},La={top:"bottom",bottom:"top",left:"right",right:"left"},Pw={top:"left",left:"top"},ou={top:"height",left:"width"},wv={width:"totalWidth",height:"totalHeight"},Al={};let Te=typeof document<"u"?window.visualViewport:null;function Sf(e){let t=0,n=0,r=0,o=0,l=0,a=0,i={};var s;let u=((s=Te==null?void 0:Te.scale)!==null&&s!==void 0?s:1)>1;if(e.tagName==="BODY"){let y=document.documentElement;r=y.clientWidth,o=y.clientHeight;var c;t=(c=Te==null?void 0:Te.width)!==null&&c!==void 0?c:r;var d;n=(d=Te==null?void 0:Te.height)!==null&&d!==void 0?d:o,i.top=y.scrollTop||e.scrollTop,i.left=y.scrollLeft||e.scrollLeft,Te&&(l=Te.offsetTop,a=Te.offsetLeft)}else({width:t,height:n,top:l,left:a}=Rr(e)),i.top=e.scrollTop,i.left=e.scrollLeft,r=t,o=n;if(sv()&&(e.tagName==="BODY"||e.tagName==="HTML")&&u){i.top=0,i.left=0;var f;l=(f=Te==null?void 0:Te.pageTop)!==null&&f!==void 0?f:0;var v;a=(v=Te==null?void 0:Te.pageLeft)!==null&&v!==void 0?v:0}return{width:t,height:n,totalWidth:r,totalHeight:o,scroll:i,top:l,left:a}}function Tw(e){return{top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}}function Cf(e,t,n,r,o,l,a){var i;let s=(i=o.scroll[e])!==null&&i!==void 0?i:0,u=r[ou[e]],c=r.scroll[zt[e]]+l,d=u+r.scroll[zt[e]]-l,f=t-s+a[e]-r[zt[e]],v=t-s+n+a[e]-r[zt[e]];return f<c?c-f:v>d?Math.max(d-v,c-f):0}function Nw(e){let t=window.getComputedStyle(e);return{top:parseInt(t.marginTop,10)||0,bottom:parseInt(t.marginBottom,10)||0,left:parseInt(t.marginLeft,10)||0,right:parseInt(t.marginRight,10)||0}}function Ef(e){if(Al[e])return Al[e];let[t,n]=e.split(" "),r=zt[t]||"right",o=Pw[r];zt[n]||(n="center");let l=ou[r],a=ou[o];return Al[e]={placement:t,crossPlacement:n,axis:r,crossAxis:o,size:l,crossSize:a},Al[e]}function Ui(e,t,n,r,o,l,a,i,s,u){let{placement:c,crossPlacement:d,axis:f,crossAxis:v,size:y,crossSize:b}=r,S={};var h;S[v]=(h=e[v])!==null&&h!==void 0?h:0;var m,g,x,E;d==="center"?S[v]+=(((m=e[b])!==null&&m!==void 0?m:0)-((g=n[b])!==null&&g!==void 0?g:0))/2:d!==v&&(S[v]+=((x=e[b])!==null&&x!==void 0?x:0)-((E=n[b])!==null&&E!==void 0?E:0)),S[v]+=l;const $=e[v]-n[b]+s+u,_=e[v]+e[b]-s-u;if(S[v]=ru(S[v],$,_),c===f){const M=i?a[y]:t[wv[y]];S[La[f]]=Math.floor(M-e[f]+o)}else S[f]=Math.floor(e[f]+e[y]+o);return S}function _w(e,t,n,r,o,l,a,i){const s=r?n.height:t[wv.height];var u;let c=e.top!=null?n.top+e.top:n.top+(s-((u=e.bottom)!==null&&u!==void 0?u:0)-a);var d,f,v,y,b,S;let h=i!=="top"?Math.max(0,t.height+t.top+((d=t.scroll.top)!==null&&d!==void 0?d:0)-c-(((f=o.top)!==null&&f!==void 0?f:0)+((v=o.bottom)!==null&&v!==void 0?v:0)+l)):Math.max(0,c+a-(t.top+((y=t.scroll.top)!==null&&y!==void 0?y:0))-(((b=o.top)!==null&&b!==void 0?b:0)+((S=o.bottom)!==null&&S!==void 0?S:0)+l));return Math.min(t.height-l*2,h)}function $f(e,t,n,r,o,l){let{placement:a,axis:i,size:s}=l;var u,c;if(a===i)return Math.max(0,n[i]-e[i]-((u=e.scroll[i])!==null&&u!==void 0?u:0)+t[i]-((c=r[i])!==null&&c!==void 0?c:0)-r[La[i]]-o);var d;return Math.max(0,e[s]+e[i]+e.scroll[i]-t[i]-n[i]-n[s]-((d=r[i])!==null&&d!==void 0?d:0)-r[La[i]]-o)}function Mw(e,t,n,r,o,l,a,i,s,u,c,d,f,v,y,b){let S=Ef(e),{size:h,crossAxis:m,crossSize:g,placement:x,crossPlacement:E}=S,$=Ui(t,i,n,S,c,d,u,f,y,b),_=c,M=$f(i,u,t,o,l+c,S);if(a&&r[h]>M){let K=Ef(`${La[x]} ${E}`),re=Ui(t,i,n,K,c,d,u,f,y,b);$f(i,u,t,o,l+c,K)>M&&(S=K,$=re,_=c)}let O="bottom";S.axis==="top"?S.placement==="top"?O="top":S.placement==="bottom"&&(O="bottom"):S.crossAxis==="top"&&(S.crossPlacement==="top"?O="bottom":S.crossPlacement==="bottom"&&(O="top"));let I=Cf(m,$[m],n[g],i,s,l,u);$[m]+=I;let C=_w($,i,u,f,o,l,n.height,O);v&&v<C&&(C=v),n.height=Math.min(n.height,C),$=Ui(t,i,n,S,_,d,u,f,y,b),I=Cf(m,$[m],n[g],i,s,l,u),$[m]+=I;let P={},z=t[m]+.5*t[g]-$[m]-o[zt[m]];const k=y/2+b;var D,N,F,T;const R=zt[m]==="left"?((D=o.left)!==null&&D!==void 0?D:0)+((N=o.right)!==null&&N!==void 0?N:0):((F=o.top)!==null&&F!==void 0?F:0)+((T=o.bottom)!==null&&T!==void 0?T:0),L=n[g]-R-y/2-b,V=t[m]+y/2-($[m]+o[zt[m]]),H=t[m]+t[g]-y/2-($[m]+o[zt[m]]),te=ru(z,V,H);return P[m]=ru(te,k,L),{position:$,maxHeight:C,arrowOffsetLeft:P.left,arrowOffsetTop:P.top,placement:S.placement}}function Lw(e){let{placement:t,targetNode:n,overlayNode:r,scrollNode:o,padding:l,shouldFlip:a,boundaryElement:i,offset:s,crossOffset:u,maxHeight:c,arrowSize:d=0,arrowBoundaryOffset:f=0}=e,v=r instanceof HTMLElement?Iw(r):document.documentElement,y=v===document.documentElement;const b=window.getComputedStyle(v).position;let S=!!b&&b!=="static",h=y?Rr(n):kf(n,v);if(!y){let{marginTop:P,marginLeft:z}=window.getComputedStyle(n);h.top+=parseInt(P,10)||0,h.left+=parseInt(z,10)||0}let m=Rr(r),g=Nw(r);var x,E;m.width+=((x=g.left)!==null&&x!==void 0?x:0)+((E=g.right)!==null&&E!==void 0?E:0);var $,_;m.height+=(($=g.top)!==null&&$!==void 0?$:0)+((_=g.bottom)!==null&&_!==void 0?_:0);let M=Tw(o),O=Sf(i),I=Sf(v),C=i.tagName==="BODY"?Rr(v):kf(v,i);return v.tagName==="HTML"&&i.tagName==="BODY"&&(I.scroll.top=0,I.scroll.left=0),Mw(t,h,m,M,g,l,a,O,I,C,s,u,S,c,d,f)}function Rr(e){let{top:t,left:n,width:r,height:o}=e.getBoundingClientRect(),{scrollTop:l,scrollLeft:a,clientTop:i,clientLeft:s}=document.documentElement;return{top:t+l-i,left:n+a-s,width:r,height:o}}function kf(e,t){let n=window.getComputedStyle(e),r;if(n.position==="fixed"){let{top:o,left:l,width:a,height:i}=e.getBoundingClientRect();r={top:o,left:l,width:a,height:i}}else{r=Rr(e);let o=Rr(t),l=window.getComputedStyle(t);o.top+=(parseInt(l.borderTopWidth,10)||0)-t.scrollTop,o.left+=(parseInt(l.borderLeftWidth,10)||0)-t.scrollLeft,r.top-=o.top,r.left-=o.left}return r.top-=parseInt(n.marginTop,10)||0,r.left-=parseInt(n.marginLeft,10)||0,r}function Iw(e){let t=e.offsetParent;if(t&&t===document.body&&window.getComputedStyle(t).position==="static"&&!Pf(t)&&(t=document.documentElement),t==null)for(t=e.parentElement;t&&!Pf(t);)t=t.parentElement;return t||document.documentElement}function Pf(e){let t=window.getComputedStyle(e);return t.transform!=="none"||/transform|perspective/.test(t.willChange)||t.filter!=="none"||t.contain==="paint"||"backdropFilter"in t&&t.backdropFilter!=="none"||"WebkitBackdropFilter"in t&&t.WebkitBackdropFilter!=="none"}const Rw=new WeakMap;function jw(e){let{triggerRef:t,isOpen:n,onClose:r}=e;p.useEffect(()=>{if(!n||r===null)return;let o=l=>{let a=l.target;if(!t.current||a instanceof Node&&!a.contains(t.current)||l.target instanceof HTMLInputElement||l.target instanceof HTMLTextAreaElement)return;let i=r||Rw.get(t.current);i&&i()};return window.addEventListener("scroll",o,!0),()=>{window.removeEventListener("scroll",o,!0)}},[n,r,t])}let ve=typeof document<"u"?window.visualViewport:null;function zw(e){let{direction:t}=Yb(),{arrowSize:n=0,targetRef:r,overlayRef:o,scrollRef:l=o,placement:a="bottom",containerPadding:i=12,shouldFlip:s=!0,boundaryElement:u=typeof document<"u"?document.body:null,offset:c=0,crossOffset:d=0,shouldUpdatePosition:f=!0,isOpen:v=!0,onClose:y,maxHeight:b,arrowBoundaryOffset:S=0}=e,[h,m]=p.useState(null),g=[f,a,o.current,r.current,l.current,i,s,u,c,d,v,t,b,S,n],x=p.useRef(ve==null?void 0:ve.scale);p.useEffect(()=>{v&&(x.current=ve==null?void 0:ve.scale)},[v]);let E=p.useCallback(()=>{if(f===!1||!v||!o.current||!r.current||!u||(ve==null?void 0:ve.scale)!==x.current)return;let I=null;if(l.current&&l.current.contains(document.activeElement)){var C;let T=(C=document.activeElement)===null||C===void 0?void 0:C.getBoundingClientRect(),R=l.current.getBoundingClientRect();var P;if(I={type:"top",offset:((P=T==null?void 0:T.top)!==null&&P!==void 0?P:0)-R.top},I.offset>R.height/2){I.type="bottom";var z;I.offset=((z=T==null?void 0:T.bottom)!==null&&z!==void 0?z:0)-R.bottom}}let k=o.current;if(!b&&o.current){var D;k.style.top="0px",k.style.bottom="";var N;k.style.maxHeight=((N=(D=window.visualViewport)===null||D===void 0?void 0:D.height)!==null&&N!==void 0?N:window.innerHeight)+"px"}let F=Lw({placement:Fw(a,t),overlayNode:o.current,targetNode:r.current,scrollNode:l.current||o.current,padding:i,shouldFlip:s,boundaryElement:u,offset:c,crossOffset:d,maxHeight:b,arrowSize:n,arrowBoundaryOffset:S});if(F.position){if(k.style.top="",k.style.bottom="",k.style.left="",k.style.right="",Object.keys(F.position).forEach(T=>k.style[T]=F.position[T]+"px"),k.style.maxHeight=F.maxHeight!=null?F.maxHeight+"px":"",I&&document.activeElement&&l.current){let T=document.activeElement.getBoundingClientRect(),R=l.current.getBoundingClientRect(),L=T[I.type]-R[I.type];l.current.scrollTop+=L-I.offset}m(F)}},g);be(E,g),Ow(E),nu({ref:o,onResize:E}),nu({ref:r,onResize:E});let $=p.useRef(!1);be(()=>{let I,C=()=>{$.current=!0,clearTimeout(I),I=setTimeout(()=>{$.current=!1},500),E()},P=()=>{$.current&&C()};return ve==null||ve.addEventListener("resize",C),ve==null||ve.addEventListener("scroll",P),()=>{ve==null||ve.removeEventListener("resize",C),ve==null||ve.removeEventListener("scroll",P)}},[E]);let _=p.useCallback(()=>{$.current||y==null||y()},[y,$]);jw({triggerRef:r,isOpen:v,onClose:y&&_});var M,O;return{overlayProps:{style:{position:"absolute",zIndex:1e5,...h==null?void 0:h.position,maxHeight:(M=h==null?void 0:h.maxHeight)!==null&&M!==void 0?M:"100vh"}},placement:(O=h==null?void 0:h.placement)!==null&&O!==void 0?O:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:h==null?void 0:h.arrowOffsetLeft,top:h==null?void 0:h.arrowOffsetTop}},updatePosition:E}}function Ow(e){be(()=>(window.addEventListener("resize",e,!1),()=>{window.removeEventListener("resize",e,!1)}),[e])}function Fw(e,t){return t==="rtl"?e.replace("start","right").replace("end","left"):e.replace("start","left").replace("end","right")}function Aw(e){const t=ht(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:n,visibility:r}=e.style,o=n!=="none"&&r!=="hidden"&&r!=="collapse";if(o){const{getComputedStyle:l}=e.ownerDocument.defaultView;let{display:a,visibility:i}=l(e);o=a!=="none"&&i!=="hidden"&&i!=="collapse"}return o}function Dw(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function xv(e,t){return e.nodeName!=="#comment"&&Aw(e)&&Dw(e,t)&&(!e.parentElement||xv(e.parentElement,e))}function kc(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function Sv(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function Cv(e){let t=p.useRef({isFocused:!1,observer:null});be(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let n=Oe(r=>{e==null||e(r)});return p.useCallback(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let o=r.target,l=a=>{if(t.current.isFocused=!1,o.disabled){let i=kc(a);n(i)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};o.addEventListener("focusout",l,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&o.disabled){var a;(a=t.current.observer)===null||a===void 0||a.disconnect();let i=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:i})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:i}))}}),t.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[n])}let Ia=!1;function Ww(e){for(;e&&!bv(e);)e=e.parentElement;let t=ht(e),n=t.document.activeElement;if(!n||n===e)return;Ia=!0;let r=!1,o=c=>{(c.target===n||r)&&c.stopImmediatePropagation()},l=c=>{(c.target===n||r)&&(c.stopImmediatePropagation(),!e&&!r&&(r=!0,Hr(n),s()))},a=c=>{(c.target===e||r)&&c.stopImmediatePropagation()},i=c=>{(c.target===e||r)&&(c.stopImmediatePropagation(),r||(r=!0,Hr(n),s()))};t.addEventListener("blur",o,!0),t.addEventListener("focusout",l,!0),t.addEventListener("focusin",i,!0),t.addEventListener("focus",a,!0);let s=()=>{cancelAnimationFrame(u),t.removeEventListener("blur",o,!0),t.removeEventListener("focusout",l,!0),t.removeEventListener("focusin",i,!0),t.removeEventListener("focus",a,!0),Ia=!1,r=!1},u=requestAnimationFrame(s);return s}let kr="default",lu="",aa=new WeakMap;function Vw(e){if(Sc()){if(kr==="default"){const t=le(e);lu=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}kr="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";aa.set(e,e.style[t]),e.style[t]="none"}}function Tf(e){if(Sc()){if(kr!=="disabled")return;kr="restoring",setTimeout(()=>{mv(()=>{if(kr==="restoring"){const t=le(e);t.documentElement.style.webkitUserSelect==="none"&&(t.documentElement.style.webkitUserSelect=lu||""),lu="",kr="default"}})},300)}else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&aa.has(e)){let t=aa.get(e),n="userSelect"in e.style?"userSelect":"webkitUserSelect";e.style[n]==="none"&&(e.style[n]=t),e.getAttribute("style")===""&&e.removeAttribute("style"),aa.delete(e)}}const Pc=q.createContext({register:()=>{}});Pc.displayName="PressResponderContext";function Bw(e,t){return t.get?t.get.call(e):t.value}function Ev(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function Hw(e,t){var n=Ev(e,t,"get");return Bw(e,n)}function Uw(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}function Nf(e,t,n){var r=Ev(e,t,"set");return Uw(e,r,n),n}function Kw(e){let t=p.useContext(Pc);if(t){let{register:n,...r}=t;e=Z(r,e),n()}return hv(t,e.ref),e}var Dl=new WeakMap;class Wl{continuePropagation(){Nf(this,Dl,!1)}get shouldStopPropagation(){return Hw(this,Dl)}constructor(t,n,r,o){Zb(this,Dl,{writable:!0,value:void 0}),Nf(this,Dl,!0);var l;let a=(l=o==null?void 0:o.target)!==null&&l!==void 0?l:r.currentTarget;const i=a==null?void 0:a.getBoundingClientRect();let s,u=0,c,d=null;r.clientX!=null&&r.clientY!=null&&(c=r.clientX,d=r.clientY),i&&(c!=null&&d!=null?(s=c-i.left,u=d-i.top):(s=i.width/2,u=i.height/2)),this.type=t,this.pointerType=n,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=u}}const _f=Symbol("linkClicked"),Mf="react-aria-pressable-style",Lf="data-react-aria-pressable";function Ur(e){let{onPress:t,onPressChange:n,onPressStart:r,onPressEnd:o,onPressUp:l,onClick:a,isDisabled:i,isPressed:s,preventFocusOnPress:u,shouldCancelOnPointerExit:c,allowTextSelectionOnPress:d,ref:f,...v}=Kw(e),[y,b]=p.useState(!1),S=p.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:h,removeAllGlobalListeners:m}=Ec(),g=Oe((C,P)=>{let z=S.current;if(i||z.didFirePressStart)return!1;let k=!0;if(z.isTriggeringEvent=!0,r){let D=new Wl("pressstart",P,C);r(D),k=D.shouldStopPropagation}return n&&n(!0),z.isTriggeringEvent=!1,z.didFirePressStart=!0,b(!0),k}),x=Oe((C,P,z=!0)=>{let k=S.current;if(!k.didFirePressStart)return!1;k.didFirePressStart=!1,k.isTriggeringEvent=!0;let D=!0;if(o){let N=new Wl("pressend",P,C);o(N),D=N.shouldStopPropagation}if(n&&n(!1),b(!1),t&&z&&!i){let N=new Wl("press",P,C);t(N),D&&(D=N.shouldStopPropagation)}return k.isTriggeringEvent=!1,D}),E=Oe((C,P)=>{let z=S.current;if(i)return!1;if(l){z.isTriggeringEvent=!0;let k=new Wl("pressup",P,C);return l(k),z.isTriggeringEvent=!1,k.shouldStopPropagation}return!0}),$=Oe(C=>{let P=S.current;if(P.isPressed&&P.target){P.didFirePressStart&&P.pointerType!=null&&x(zn(P.target,C),P.pointerType,!1),P.isPressed=!1,P.isOverTarget=!1,P.activePointerId=null,P.pointerType=null,m(),d||Tf(P.target);for(let z of P.disposables)z();P.disposables=[]}}),_=Oe(C=>{c&&$(C)}),M=Oe(C=>{a==null||a(C)}),O=Oe((C,P)=>{if(a){let z=new MouseEvent("click",C);Sv(z,P),a(kc(z))}}),I=p.useMemo(()=>{let C=S.current,P={onKeyDown(k){if(Ki(k.nativeEvent,k.currentTarget)&&Qe(k.currentTarget,ge(k.nativeEvent))){var D;If(ge(k.nativeEvent),k.key)&&k.preventDefault();let N=!0;if(!C.isPressed&&!k.repeat){C.target=k.currentTarget,C.isPressed=!0,C.pointerType="keyboard",N=g(k,"keyboard");let F=k.currentTarget,T=R=>{Ki(R,F)&&!R.repeat&&Qe(F,ge(R))&&C.target&&E(zn(C.target,R),"keyboard")};h(le(k.currentTarget),"keyup",At(T,z),!0)}N&&k.stopPropagation(),k.metaKey&&rl()&&((D=C.metaKeyEvents)===null||D===void 0||D.set(k.key,k.nativeEvent))}else k.key==="Meta"&&(C.metaKeyEvents=new Map)},onClick(k){if(!(k&&!Qe(k.currentTarget,ge(k.nativeEvent)))&&k&&k.button===0&&!C.isTriggeringEvent&&!tr.isOpening){let D=!0;if(i&&k.preventDefault(),!C.ignoreEmulatedMouseEvents&&!C.isPressed&&(C.pointerType==="virtual"||gv(k.nativeEvent))){let N=g(k,"virtual"),F=E(k,"virtual"),T=x(k,"virtual");M(k),D=N&&F&&T}else if(C.isPressed&&C.pointerType!=="keyboard"){let N=C.pointerType||k.nativeEvent.pointerType||"virtual",F=E(zn(k.currentTarget,k),N),T=x(zn(k.currentTarget,k),N,!0);D=F&&T,C.isOverTarget=!1,M(k),$(k)}C.ignoreEmulatedMouseEvents=!1,D&&k.stopPropagation()}}},z=k=>{var D;if(C.isPressed&&C.target&&Ki(k,C.target)){var N;If(ge(k),k.key)&&k.preventDefault();let T=ge(k),R=Qe(C.target,ge(k));x(zn(C.target,k),"keyboard",R),R&&O(k,C.target),m(),k.key!=="Enter"&&Tc(C.target)&&Qe(C.target,T)&&!k[_f]&&(k[_f]=!0,tr(C.target,k,!1)),C.isPressed=!1,(N=C.metaKeyEvents)===null||N===void 0||N.delete(k.key)}else if(k.key==="Meta"&&(!((D=C.metaKeyEvents)===null||D===void 0)&&D.size)){var F;let T=C.metaKeyEvents;C.metaKeyEvents=void 0;for(let R of T.values())(F=C.target)===null||F===void 0||F.dispatchEvent(new KeyboardEvent("keyup",R))}};if(typeof PointerEvent<"u"){P.onPointerDown=N=>{if(N.button!==0||!Qe(N.currentTarget,ge(N.nativeEvent)))return;if(Cw(N.nativeEvent)){C.pointerType="virtual";return}C.pointerType=N.pointerType;let F=!0;if(!C.isPressed){C.isPressed=!0,C.isOverTarget=!0,C.activePointerId=N.pointerId,C.target=N.currentTarget,d||Vw(C.target),F=g(N,C.pointerType);let T=ge(N.nativeEvent);"releasePointerCapture"in T&&T.releasePointerCapture(N.pointerId),h(le(N.currentTarget),"pointerup",k,!1),h(le(N.currentTarget),"pointercancel",D,!1)}F&&N.stopPropagation()},P.onMouseDown=N=>{if(Qe(N.currentTarget,ge(N.nativeEvent))&&N.button===0){if(u){let F=Ww(N.target);F&&C.disposables.push(F)}N.stopPropagation()}},P.onPointerUp=N=>{!Qe(N.currentTarget,ge(N.nativeEvent))||C.pointerType==="virtual"||N.button===0&&!C.isPressed&&E(N,C.pointerType||N.pointerType)},P.onPointerEnter=N=>{N.pointerId===C.activePointerId&&C.target&&!C.isOverTarget&&C.pointerType!=null&&(C.isOverTarget=!0,g(zn(C.target,N),C.pointerType))},P.onPointerLeave=N=>{N.pointerId===C.activePointerId&&C.target&&C.isOverTarget&&C.pointerType!=null&&(C.isOverTarget=!1,x(zn(C.target,N),C.pointerType,!1),_(N))};let k=N=>{if(N.pointerId===C.activePointerId&&C.isPressed&&N.button===0&&C.target){if(Qe(C.target,ge(N))&&C.pointerType!=null){let F=!1,T=setTimeout(()=>{C.isPressed&&C.target instanceof HTMLElement&&(F?$(N):(Hr(C.target),C.target.click()))},80);h(N.currentTarget,"click",()=>F=!0,!0),C.disposables.push(()=>clearTimeout(T))}else $(N);C.isOverTarget=!1}},D=N=>{$(N)};P.onDragStart=N=>{Qe(N.currentTarget,ge(N.nativeEvent))&&$(N)}}return P},[h,i,u,m,d,$,_,x,g,E,M,O]);return p.useEffect(()=>{if(!f)return;const C=le(f.current);if(!C||!C.head||C.getElementById(Mf))return;const P=C.createElement("style");P.id=Mf,P.textContent=`
@layer {
  [${Lf}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),C.head.prepend(P)},[f]),p.useEffect(()=>{let C=S.current;return()=>{var P;d||Tf((P=C.target)!==null&&P!==void 0?P:void 0);for(let z of C.disposables)z();C.disposables=[]}},[d]),{isPressed:s||y,pressProps:Z(v,I,{[Lf]:!0})}}function Tc(e){return e.tagName==="A"&&e.hasAttribute("href")}function Ki(e,t){const{key:n,code:r}=e,o=t,l=o.getAttribute("role");return(n==="Enter"||n===" "||n==="Spacebar"||r==="Space")&&!(o instanceof ht(o).HTMLInputElement&&!$v(o,n)||o instanceof ht(o).HTMLTextAreaElement||o.isContentEditable)&&!((l==="link"||!l&&Tc(o))&&n!=="Enter")}function zn(e,t){let n=t.clientX,r=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:n,clientY:r}}function Gw(e){return e instanceof HTMLInputElement?!1:e instanceof HTMLButtonElement?e.type!=="submit"&&e.type!=="reset":!Tc(e)}function If(e,t){return e instanceof HTMLInputElement?!$v(e,t):Gw(e)}const Qw=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function $v(e,t){return e.type==="checkbox"||e.type==="radio"?t===" ":Qw.has(e.type)}let ur=null,au=new Set,zo=new Map,nr=!1,iu=!1;const Yw={Tab:!0,Escape:!0};function li(e,t){for(let n of au)n(e,t)}function Xw(e){return!(e.metaKey||!rl()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Ra(e){nr=!0,Xw(e)&&(ur="keyboard",li("keyboard",e))}function jr(e){ur="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(nr=!0,li("pointer",e))}function kv(e){gv(e)&&(nr=!0,ur="virtual")}function Pv(e){e.target===window||e.target===document||Ia||!e.isTrusted||(!nr&&!iu&&(ur="virtual",li("virtual",e)),nr=!1,iu=!1)}function Tv(){Ia||(nr=!1,iu=!0)}function su(e){if(typeof window>"u"||typeof document>"u"||zo.get(ht(e)))return;const t=ht(e),n=le(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){nr=!0,r.apply(this,arguments)},n.addEventListener("keydown",Ra,!0),n.addEventListener("keyup",Ra,!0),n.addEventListener("click",kv,!0),t.addEventListener("focus",Pv,!0),t.addEventListener("blur",Tv,!1),typeof PointerEvent<"u"&&(n.addEventListener("pointerdown",jr,!0),n.addEventListener("pointermove",jr,!0),n.addEventListener("pointerup",jr,!0)),t.addEventListener("beforeunload",()=>{Nv(e)},{once:!0}),zo.set(t,{focus:r})}const Nv=(e,t)=>{const n=ht(e),r=le(e);t&&r.removeEventListener("DOMContentLoaded",t),zo.has(n)&&(n.HTMLElement.prototype.focus=zo.get(n).focus,r.removeEventListener("keydown",Ra,!0),r.removeEventListener("keyup",Ra,!0),r.removeEventListener("click",kv,!0),n.removeEventListener("focus",Pv,!0),n.removeEventListener("blur",Tv,!1),typeof PointerEvent<"u"&&(r.removeEventListener("pointerdown",jr,!0),r.removeEventListener("pointermove",jr,!0),r.removeEventListener("pointerup",jr,!0)),zo.delete(n))};function Zw(e){const t=le(e);let n;return t.readyState!=="loading"?su(e):(n=()=>{su(e)},t.addEventListener("DOMContentLoaded",n)),()=>Nv(e,n)}typeof document<"u"&&Zw();function Nc(){return ur!=="pointer"}function _c(){return ur}function Jw(e){ur=e,li(e,null)}const qw=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ex(e,t,n){let r=le(n==null?void 0:n.target);const o=typeof window<"u"?ht(n==null?void 0:n.target).HTMLInputElement:HTMLInputElement,l=typeof window<"u"?ht(n==null?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a=typeof window<"u"?ht(n==null?void 0:n.target).HTMLElement:HTMLElement,i=typeof window<"u"?ht(n==null?void 0:n.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof o&&!qw.has(r.activeElement.type)||r.activeElement instanceof l||r.activeElement instanceof a&&r.activeElement.isContentEditable,!(e&&t==="keyboard"&&n instanceof i&&!Yw[n.key])}function tx(e,t,n){su(),p.useEffect(()=>{let r=(o,l)=>{ex(!!(n!=null&&n.isTextInput),o,l)&&e(Nc())};return au.add(r),()=>{au.delete(r)}},t)}function _v(e){const t=le(e),n=tt(t);if(_c()==="virtual"){let r=n;mv(()=>{tt(t)===r&&e.isConnected&&Hr(e)})}else Hr(e)}function Mv(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e;const l=p.useCallback(s=>{if(s.target===s.currentTarget)return r&&r(s),o&&o(!1),!0},[r,o]),a=Cv(l),i=p.useCallback(s=>{const u=le(s.target),c=u?tt(u):tt();s.target===s.currentTarget&&c===ge(s.nativeEvent)&&(n&&n(s),o&&o(!0),a(s))},[o,n,a]);return{focusProps:{onFocus:!t&&(n||o||r)?i:void 0,onBlur:!t&&(r||o)?l:void 0}}}function Rf(e){if(!e)return;let t=!0;return n=>{let r={...n,preventDefault(){n.preventDefault()},isDefaultPrevented(){return n.isDefaultPrevented()},stopPropagation(){t=!0},continuePropagation(){t=!1},isPropagationStopped(){return t}};e(r),t&&n.stopPropagation()}}function nx(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:Rf(e.onKeyDown),onKeyUp:Rf(e.onKeyUp)}}}let rx=q.createContext(null);function ox(e){let t=p.useContext(rx)||{};hv(t,e);let{ref:n,...r}=t;return r}function Jr(e,t){let{focusProps:n}=Mv(e),{keyboardProps:r}=nx(e),o=Z(n,r),l=ox(t),a=e.isDisabled?{}:l,i=p.useRef(e.autoFocus);p.useEffect(()=>{i.current&&t.current&&_v(t.current),i.current=!1},[t]);let s=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(s=void 0),{focusableProps:Z({...o,tabIndex:s},a)}}function lx({children:e}){let t=p.useMemo(()=>({register:()=>{}}),[]);return q.createElement(Pc.Provider,{value:t},e)}function ai(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,l=p.useRef({isFocusWithin:!1}),{addGlobalListener:a,removeAllGlobalListeners:i}=Ec(),s=p.useCallback(d=>{d.currentTarget.contains(d.target)&&l.current.isFocusWithin&&!d.currentTarget.contains(d.relatedTarget)&&(l.current.isFocusWithin=!1,i(),n&&n(d),o&&o(!1))},[n,o,l,i]),u=Cv(s),c=p.useCallback(d=>{if(!d.currentTarget.contains(d.target))return;const f=le(d.target),v=tt(f);if(!l.current.isFocusWithin&&v===ge(d.nativeEvent)){r&&r(d),o&&o(!0),l.current.isFocusWithin=!0,u(d);let y=d.currentTarget;a(f,"focus",b=>{if(l.current.isFocusWithin&&!Qe(y,b.target)){let S=new f.defaultView.FocusEvent("blur",{relatedTarget:b.target});Sv(S,y);let h=kc(S);s(h)}},{capture:!0})}},[r,o,u,a,s]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:c,onBlur:s}}}let uu=!1,Gi=0;function ax(){uu=!0,setTimeout(()=>{uu=!1},50)}function jf(e){e.pointerType==="touch"&&ax()}function ix(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",jf),Gi++,()=>{Gi--,!(Gi>0)&&typeof PointerEvent<"u"&&document.removeEventListener("pointerup",jf)}}function rr(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[l,a]=p.useState(!1),i=p.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;p.useEffect(ix,[]);let{addGlobalListener:s,removeAllGlobalListeners:u}=Ec(),{hoverProps:c,triggerHoverEnd:d}=p.useMemo(()=>{let f=(b,S)=>{if(i.pointerType=S,o||S==="touch"||i.isHovered||!b.currentTarget.contains(b.target))return;i.isHovered=!0;let h=b.currentTarget;i.target=h,s(le(b.target),"pointerover",m=>{i.isHovered&&i.target&&!Qe(i.target,m.target)&&v(m,m.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:h,pointerType:S}),n&&n(!0),a(!0)},v=(b,S)=>{let h=i.target;i.pointerType="",i.target=null,!(S==="touch"||!i.isHovered||!h)&&(i.isHovered=!1,u(),r&&r({type:"hoverend",target:h,pointerType:S}),n&&n(!1),a(!1))},y={};return typeof PointerEvent<"u"&&(y.onPointerEnter=b=>{uu&&b.pointerType==="mouse"||f(b,b.pointerType)},y.onPointerLeave=b=>{!o&&b.currentTarget.contains(b.target)&&v(b,b.pointerType)}),{hoverProps:y,triggerHoverEnd:v}},[t,n,r,o,i,s,u]);return p.useEffect(()=>{o&&d({currentTarget:i.target},i.pointerType)},[o]),{hoverProps:c,isHovered:l}}function sx(e){let{ref:t,onInteractOutside:n,isDisabled:r,onInteractOutsideStart:o}=e,l=p.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),a=Oe(s=>{n&&zf(s,t)&&(o&&o(s),l.current.isPointerDown=!0)}),i=Oe(s=>{n&&n(s)});p.useEffect(()=>{let s=l.current;if(r)return;const u=t.current,c=le(u);if(typeof PointerEvent<"u"){let d=f=>{s.isPointerDown&&zf(f,t)&&i(f),s.isPointerDown=!1};return c.addEventListener("pointerdown",a,!0),c.addEventListener("click",d,!0),()=>{c.removeEventListener("pointerdown",a,!0),c.removeEventListener("click",d,!0)}}},[t,r,a,i])}function zf(e,t){if(e.button>0)return!1;if(e.target){const n=e.target.ownerDocument;if(!n||!n.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current?!e.composedPath().includes(t.current):!1}const Of=q.createContext(null),cu="react-aria-focus-scope-restore";let ae=null;function ux(e){let{children:t,contain:n,restoreFocus:r,autoFocus:o}=e,l=p.useRef(null),a=p.useRef(null),i=p.useRef([]),{parentNode:s}=p.useContext(Of)||{},u=p.useMemo(()=>new fu({scopeRef:i}),[i]);be(()=>{let f=s||Ce.root;if(Ce.getTreeNode(f.scopeRef)&&ae&&!ja(ae,f.scopeRef)){let v=Ce.getTreeNode(ae);v&&(f=v)}f.addChild(u),Ce.addNode(u)},[u,s]),be(()=>{let f=Ce.getTreeNode(i);f&&(f.contain=!!n)},[n]),be(()=>{var f;let v=(f=l.current)===null||f===void 0?void 0:f.nextSibling,y=[],b=S=>S.stopPropagation();for(;v&&v!==a.current;)y.push(v),v.addEventListener(cu,b),v=v.nextSibling;return i.current=y,()=>{for(let S of y)S.removeEventListener(cu,b)}},[t]),mx(i,r,n),dx(i,n),hx(i,r,n),px(i,o),p.useEffect(()=>{const f=tt(le(i.current?i.current[0]:void 0));let v=null;if(bt(f,i.current)){for(let y of Ce.traverse())y.scopeRef&&bt(f,y.scopeRef.current)&&(v=y);v===Ce.getTreeNode(i)&&(ae=v.scopeRef)}},[i]),be(()=>()=>{var f,v,y;let b=(y=(v=Ce.getTreeNode(i))===null||v===void 0||(f=v.parent)===null||f===void 0?void 0:f.scopeRef)!==null&&y!==void 0?y:null;(i===ae||ja(i,ae))&&(!b||Ce.getTreeNode(b))&&(ae=b),Ce.removeTreeNode(i)},[i]);let c=p.useMemo(()=>cx(i),[]),d=p.useMemo(()=>({focusManager:c,parentNode:u}),[u,c]);return q.createElement(Of.Provider,{value:d},q.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:l}),t,q.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:a}))}function cx(e){return{focusNext(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:a}=t;var i;let s=r||tt(le((i=n[0])!==null&&i!==void 0?i:void 0)),u=n[0].previousElementSibling,c=Un(n),d=bn(c,{tabbable:o,accept:a},n);d.currentNode=bt(s,n)?s:u;let f=d.nextNode();return!f&&l&&(d.currentNode=u,f=d.nextNode()),f&&Xt(f,!0),f},focusPrevious(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:a}=t;var i;let s=r||tt(le((i=n[0])!==null&&i!==void 0?i:void 0)),u=n[n.length-1].nextElementSibling,c=Un(n),d=bn(c,{tabbable:o,accept:a},n);d.currentNode=bt(s,n)?s:u;let f=d.previousNode();return!f&&l&&(d.currentNode=u,f=d.previousNode()),f&&Xt(f,!0),f},focusFirst(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=Un(n),a=bn(l,{tabbable:r,accept:o},n);a.currentNode=n[0].previousElementSibling;let i=a.nextNode();return i&&Xt(i,!0),i},focusLast(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=Un(n),a=bn(l,{tabbable:r,accept:o},n);a.currentNode=n[n.length-1].nextElementSibling;let i=a.previousNode();return i&&Xt(i,!0),i}}}function Un(e){return e[0].parentElement}function $o(e){let t=Ce.getTreeNode(ae);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function dx(e,t){let n=p.useRef(void 0),r=p.useRef(void 0);be(()=>{let o=e.current;if(!t){r.current&&(cancelAnimationFrame(r.current),r.current=void 0);return}const l=le(o?o[0]:void 0);let a=u=>{if(u.key!=="Tab"||u.altKey||u.ctrlKey||u.metaKey||!$o(e)||u.isComposing)return;let c=tt(l),d=e.current;if(!d||!bt(c,d))return;let f=Un(d),v=bn(f,{tabbable:!0},d);if(!c)return;v.currentNode=c;let y=u.shiftKey?v.previousNode():v.nextNode();y||(v.currentNode=u.shiftKey?d[d.length-1].nextElementSibling:d[0].previousElementSibling,y=u.shiftKey?v.previousNode():v.nextNode()),u.preventDefault(),y&&Xt(y,!0)},i=u=>{(!ae||ja(ae,e))&&bt(ge(u),e.current)?(ae=e,n.current=ge(u)):$o(e)&&!yn(ge(u),e)?n.current?n.current.focus():ae&&ae.current&&du(ae.current):$o(e)&&(n.current=ge(u))},s=u=>{r.current&&cancelAnimationFrame(r.current),r.current=requestAnimationFrame(()=>{let c=_c(),d=(c==="virtual"||c===null)&&Cc()&&uv(),f=tt(l);if(!d&&f&&$o(e)&&!yn(f,e)){ae=e;let y=ge(u);if(y&&y.isConnected){var v;n.current=y,(v=n.current)===null||v===void 0||v.focus()}else ae.current&&du(ae.current)}})};return l.addEventListener("keydown",a,!1),l.addEventListener("focusin",i,!1),o==null||o.forEach(u=>u.addEventListener("focusin",i,!1)),o==null||o.forEach(u=>u.addEventListener("focusout",s,!1)),()=>{l.removeEventListener("keydown",a,!1),l.removeEventListener("focusin",i,!1),o==null||o.forEach(u=>u.removeEventListener("focusin",i,!1)),o==null||o.forEach(u=>u.removeEventListener("focusout",s,!1))}},[e,t]),be(()=>()=>{r.current&&cancelAnimationFrame(r.current)},[r])}function Lv(e){return yn(e)}function bt(e,t){return!e||!t?!1:t.some(n=>n.contains(e))}function yn(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:n}of Ce.traverse(Ce.getTreeNode(t)))if(n&&bt(e,n.current))return!0;return!1}function fx(e){return yn(e,ae)}function ja(e,t){var n;let r=(n=Ce.getTreeNode(t))===null||n===void 0?void 0:n.parent;for(;r;){if(r.scopeRef===e)return!0;r=r.parent}return!1}function Xt(e,t=!1){if(e!=null&&!t)try{_v(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function Iv(e,t=!0){let n=e[0].previousElementSibling,r=Un(e),o=bn(r,{tabbable:t},e);o.currentNode=n;let l=o.nextNode();return t&&!l&&(r=Un(e),o=bn(r,{tabbable:!1},e),o.currentNode=n,l=o.nextNode()),l}function du(e,t=!0){Xt(Iv(e,t))}function px(e,t){const n=q.useRef(t);p.useEffect(()=>{if(n.current){ae=e;const r=le(e.current?e.current[0]:void 0);!bt(tt(r),ae.current)&&e.current&&du(e.current)}n.current=!1},[e])}function mx(e,t,n){be(()=>{if(t||n)return;let r=e.current;const o=le(r?r[0]:void 0);let l=a=>{let i=ge(a);bt(i,e.current)?ae=e:Lv(i)||(ae=null)};return o.addEventListener("focusin",l,!1),r==null||r.forEach(a=>a.addEventListener("focusin",l,!1)),()=>{o.removeEventListener("focusin",l,!1),r==null||r.forEach(a=>a.removeEventListener("focusin",l,!1))}},[e,t,n])}function Ff(e){let t=Ce.getTreeNode(ae);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return(t==null?void 0:t.scopeRef)===e}function hx(e,t,n){const r=p.useRef(typeof document<"u"?tt(le(e.current?e.current[0]:void 0)):null);be(()=>{let o=e.current;const l=le(o?o[0]:void 0);if(!t||n)return;let a=()=>{(!ae||ja(ae,e))&&bt(tt(l),e.current)&&(ae=e)};return l.addEventListener("focusin",a,!1),o==null||o.forEach(i=>i.addEventListener("focusin",a,!1)),()=>{l.removeEventListener("focusin",a,!1),o==null||o.forEach(i=>i.removeEventListener("focusin",a,!1))}},[e,n]),be(()=>{const o=le(e.current?e.current[0]:void 0);if(!t)return;let l=a=>{if(a.key!=="Tab"||a.altKey||a.ctrlKey||a.metaKey||!$o(e)||a.isComposing)return;let i=o.activeElement;if(!yn(i,e)||!Ff(e))return;let s=Ce.getTreeNode(e);if(!s)return;let u=s.nodeToRestore,c=bn(o.body,{tabbable:!0});c.currentNode=i;let d=a.shiftKey?c.previousNode():c.nextNode();if((!u||!u.isConnected||u===o.body)&&(u=void 0,s.nodeToRestore=void 0),(!d||!yn(d,e))&&u){c.currentNode=u;do d=a.shiftKey?c.previousNode():c.nextNode();while(yn(d,e));a.preventDefault(),a.stopPropagation(),d?Xt(d,!0):Lv(u)?Xt(u,!0):i.blur()}};return n||o.addEventListener("keydown",l,!0),()=>{n||o.removeEventListener("keydown",l,!0)}},[e,t,n]),be(()=>{const o=le(e.current?e.current[0]:void 0);if(!t)return;let l=Ce.getTreeNode(e);if(l){var a;return l.nodeToRestore=(a=r.current)!==null&&a!==void 0?a:void 0,()=>{let i=Ce.getTreeNode(e);if(!i)return;let s=i.nodeToRestore,u=tt(o);if(t&&s&&(u&&yn(u,e)||u===o.body&&Ff(e))){let c=Ce.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let d=c.getTreeNode(e);for(;d;){if(d.nodeToRestore&&d.nodeToRestore.isConnected){Af(d.nodeToRestore);return}d=d.parent}for(d=c.getTreeNode(e);d;){if(d.scopeRef&&d.scopeRef.current&&Ce.getTreeNode(d.scopeRef)){let f=Iv(d.scopeRef.current,!0);Af(f);return}d=d.parent}}})}}}},[e,t])}function Af(e){e.dispatchEvent(new CustomEvent(cu,{bubbles:!0,cancelable:!0}))&&Xt(e)}function bn(e,t,n){let r=t!=null&&t.tabbable?kw:bv,o=(e==null?void 0:e.nodeType)===Node.ELEMENT_NODE?e:null,l=le(o),a=lw(l,e||l,NodeFilter.SHOW_ELEMENT,{acceptNode(i){var s;return!(t==null||(s=t.from)===null||s===void 0)&&s.contains(i)?NodeFilter.FILTER_REJECT:r(i)&&xv(i)&&(!n||bt(i,n))&&(!(t!=null&&t.accept)||t.accept(i))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t!=null&&t.from&&(a.currentNode=t.from),a}class Mc{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,n,r){let o=this.fastMap.get(n??null);if(!o)return;let l=new fu({scopeRef:t});o.addChild(l),l.parent=o,this.fastMap.set(t,l),r&&(l.nodeToRestore=r)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let n=this.fastMap.get(t);if(!n)return;let r=n.parent;for(let l of this.traverse())l!==n&&n.nodeToRestore&&l.nodeToRestore&&n.scopeRef&&n.scopeRef.current&&bt(l.nodeToRestore,n.scopeRef.current)&&(l.nodeToRestore=n.nodeToRestore);let o=n.children;r&&(r.removeChild(n),o.size>0&&o.forEach(l=>r&&r.addChild(l))),this.fastMap.delete(n.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let n of t.children)yield*this.traverse(n)}clone(){var t;let n=new Mc;var r;for(let o of this.traverse())n.addTreeNode(o.scopeRef,(r=(t=o.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&r!==void 0?r:null,o.nodeToRestore);return n}constructor(){this.fastMap=new Map,this.root=new fu({scopeRef:null}),this.fastMap.set(null,this.root)}}class fu{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}let Ce=new Mc;function or(e={}){let{autoFocus:t=!1,isTextInput:n,within:r}=e,o=p.useRef({isFocused:!1,isFocusVisible:t||Nc()}),[l,a]=p.useState(!1),[i,s]=p.useState(()=>o.current.isFocused&&o.current.isFocusVisible),u=p.useCallback(()=>s(o.current.isFocused&&o.current.isFocusVisible),[]),c=p.useCallback(v=>{o.current.isFocused=v,a(v),u()},[u]);tx(v=>{o.current.isFocusVisible=v,u()},[],{isTextInput:n});let{focusProps:d}=Mv({isDisabled:r,onFocusChange:c}),{focusWithinProps:f}=ai({isDisabled:!r,onFocusWithinChange:c});return{isFocused:l,isFocusVisible:i,focusProps:r?f:d}}const Lt=[];function vx(e,t){let{onClose:n,shouldCloseOnBlur:r,isOpen:o,isDismissable:l=!1,isKeyboardDismissDisabled:a=!1,shouldCloseOnInteractOutside:i}=e;p.useEffect(()=>{if(o&&!Lt.includes(t))return Lt.push(t),()=>{let y=Lt.indexOf(t);y>=0&&Lt.splice(y,1)}},[o,t]);let s=()=>{Lt[Lt.length-1]===t&&n&&n()},u=y=>{(!i||i(y.target))&&Lt[Lt.length-1]===t&&(y.stopPropagation(),y.preventDefault())},c=y=>{(!i||i(y.target))&&(Lt[Lt.length-1]===t&&(y.stopPropagation(),y.preventDefault()),s())},d=y=>{y.key==="Escape"&&!a&&!y.nativeEvent.isComposing&&(y.stopPropagation(),y.preventDefault(),s())};sx({ref:t,onInteractOutside:l&&o?c:void 0,onInteractOutsideStart:u});let{focusWithinProps:f}=ai({isDisabled:!r,onBlurWithin:y=>{!y.relatedTarget||fx(y.relatedTarget)||(!i||i(y.relatedTarget))&&(n==null||n())}}),v=y=>{y.target===y.currentTarget&&y.preventDefault()};return{overlayProps:{onKeyDown:d,...f},underlayProps:{onPointerDown:v}}}const Qi=typeof document<"u"&&window.visualViewport,gx=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);let Vl=0,Yi;function yx(e={}){let{isDisabled:t}=e;be(()=>{if(!t)return Vl++,Vl===1&&(Sc()?Yi=wx():Yi=bx()),()=>{Vl--,Vl===0&&Yi()}},[t])}function bx(){let e=window.innerWidth-document.documentElement.clientWidth;return At(e>0&&("scrollbarGutter"in document.documentElement.style?Kn(document.documentElement,"scrollbarGutter","stable"):Kn(document.documentElement,"paddingRight",`${e}px`)),Kn(document.documentElement,"overflow","hidden"))}function wx(){let e,t,n=u=>{e=vv(u.target,!0),!(e===document.documentElement&&e===document.body)&&e instanceof HTMLElement&&window.getComputedStyle(e).overscrollBehavior==="auto"&&(t=Kn(e,"overscrollBehavior","contain"))},r=u=>{if(!e||e===document.documentElement||e===document.body){u.preventDefault();return}e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&u.preventDefault()},o=()=>{t&&t()},l=u=>{let c=u.target;xx(c)&&(i(),c.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{c.style.transform="",Qi&&(Qi.height<window.innerHeight?requestAnimationFrame(()=>{Df(c)}):Qi.addEventListener("resize",()=>Df(c),{once:!0}))}))},a=null,i=()=>{if(a)return;let u=()=>{window.scrollTo(0,0)},c=window.pageXOffset,d=window.pageYOffset;a=At(po(window,"scroll",u),Kn(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),Kn(document.documentElement,"overflow","hidden"),Kn(document.body,"marginTop",`-${d}px`),()=>{window.scrollTo(c,d)}),window.scrollTo(0,0)},s=At(po(document,"touchstart",n,{passive:!1,capture:!0}),po(document,"touchmove",r,{passive:!1,capture:!0}),po(document,"touchend",o,{passive:!1,capture:!0}),po(document,"focus",l,!0));return()=>{t==null||t(),a==null||a(),s()}}function Kn(e,t,n){let r=e.style[t];return e.style[t]=n,()=>{e.style[t]=r}}function po(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function Df(e){let t=document.scrollingElement||document.documentElement,n=e;for(;n&&n!==t;){let r=vv(n);if(r!==document.documentElement&&r!==document.body&&r!==n){let o=r.getBoundingClientRect().top,l=n.getBoundingClientRect().top;l>o+n.clientHeight&&(r.scrollTop+=l-o)}n=r.parentElement}}function xx(e){return e instanceof HTMLInputElement&&!gx.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}const Sx=p.createContext({});function Rv(){var e;return(e=p.useContext(Sx))!==null&&e!==void 0?e:{}}const pu=q.createContext(null);function Cx(e){let{children:t}=e,n=p.useContext(pu),[r,o]=p.useState(0),l=p.useMemo(()=>({parent:n,modalCount:r,addModal(){o(a=>a+1),n&&n.addModal()},removeModal(){o(a=>a-1),n&&n.removeModal()}}),[n,r]);return q.createElement(pu.Provider,{value:l},t)}function Ex(){let e=p.useContext(pu);return{modalProviderProps:{"aria-hidden":e&&e.modalCount>0?!0:void 0}}}function $x(e){let{modalProviderProps:t}=Ex();return q.createElement("div",{"data-overlay-container":!0,...e,...t})}function jv(e){return q.createElement(Cx,null,q.createElement($x,e))}function Wf(e){let t=ni(),{portalContainer:n=t?null:document.body,...r}=e,{getContainer:o}=Rv();if(!e.portalContainer&&o&&(n=o()),q.useEffect(()=>{if(n!=null&&n.closest("[data-overlay-container]"))throw new Error("An OverlayContainer must not be inside another container. Please change the portalContainer prop.")},[n]),!n)return null;let l=q.createElement(jv,r);return Oh.createPortal(l,n)}const Vf={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function kx(e={}){let{style:t,isFocusable:n}=e,[r,o]=p.useState(!1),{focusWithinProps:l}=ai({isDisabled:!n,onFocusWithinChange:i=>o(i)}),a=p.useMemo(()=>r?t:t?{...Vf,...t}:Vf,[r]);return{visuallyHiddenProps:{...l,style:a}}}function Px(e){let{children:t,elementType:n="div",isFocusable:r,style:o,...l}=e,{visuallyHiddenProps:a}=kx(e);return q.createElement(n,Z(l,a),t)}const Tx=q.createContext(null);function Bf(e){let t=ni(),{portalContainer:n=t?null:document.body,isExiting:r}=e,[o,l]=p.useState(!1),a=p.useMemo(()=>({contain:o,setContain:l}),[o,l]),{getContainer:i}=Rv();if(!e.portalContainer&&i&&(n=i()),!n)return null;let s=e.children;return e.disableFocusManagement||(s=q.createElement(ux,{restoreFocus:!0,contain:(e.shouldContainFocus||o)&&!r},s)),s=q.createElement(Tx.Provider,{value:a},q.createElement(lx,null,s)),Oh.createPortal(s,n)}function Nx(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}function zv(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const _x=e=>Array.isArray(e);function mu(e){return typeof e=="string"||Array.isArray(e)}function Hf(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Mx(e,t,n,r){if(typeof t=="function"){const[o,l]=Hf(r);t=t(n!==void 0?n:e.custom,o,l)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,l]=Hf(r);t=t(n!==void 0?n:e.custom,o,l)}return t}const Lx=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ix=["initial",...Lx],ii=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ov=new Set(ii),Rx={skipAnimations:!1,useManualTiming:!1},jx=e=>e;function zx(e){let t=new Set,n=new Set,r=!1,o=!1;const l=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function i(u){l.has(u)&&(s.schedule(u),e()),u(a)}const s={schedule:(u,c=!1,d=!1)=>{const v=d&&r?t:n;return c&&l.add(u),v.has(u)||v.add(u),u},cancel:u=>{n.delete(u),l.delete(u)},process:u=>{if(a=u,r){o=!0;return}r=!0,[t,n]=[n,t],t.forEach(i),t.clear(),r=!1,o&&(o=!1,s.process(u))}};return s}const Bl=["read","resolveKeyframes","update","preRender","render","postRender"],Ox=40;function Fv(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},l=()=>n=!0,a=Bl.reduce((h,m)=>(h[m]=zx(l),h),{}),{read:i,resolveKeyframes:s,update:u,preRender:c,render:d,postRender:f}=a,v=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,Ox),1),o.timestamp=h,o.isProcessing=!0,i.process(o),s.process(o),u.process(o),c.process(o),d.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(v))},y=()=>{n=!0,r=!0,o.isProcessing||e(v)};return{schedule:Bl.reduce((h,m)=>{const g=a[m];return h[m]=(x,E=!1,$=!1)=>(n||y(),g.schedule(x,E,$)),h},{}),cancel:h=>{for(let m=0;m<Bl.length;m++)a[Bl[m]].cancel(h)},state:o,steps:a}}const{schedule:Uf,cancel:bE,state:wE}=Fv(typeof requestAnimationFrame<"u"?requestAnimationFrame:jx,!0),Av=e=>t=>typeof t=="string"&&t.startsWith(e),Fx=Av("--"),Ax=Av("var(--"),xE=e=>Ax(e)?Dx.test(e.split("/*")[0].trim()):!1,Dx=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Wx=(e,t,n)=>n>t?t:n<e?e:n,Lc={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},hu={...Lc,transform:e=>Wx(0,1,e)},Hl={...Lc,default:1},pl=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),On=pl("deg"),Xi=pl("%"),Q=pl("px"),SE=pl("vh"),CE=pl("vw"),Kf={...Xi,parse:e=>Xi.parse(e)/100,transform:e=>Xi.transform(e*100)},Vx={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q},Bx={rotate:On,rotateX:On,rotateY:On,rotateZ:On,scale:Hl,scaleX:Hl,scaleY:Hl,scaleZ:Hl,skew:On,skewX:On,skewY:On,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:hu,originX:Kf,originY:Kf,originZ:Q},Gf={...Lc,transform:Math.round},Dv={...Vx,...Bx,zIndex:Gf,size:Q,fillOpacity:hu,strokeOpacity:hu,numOctaves:Gf},Hx=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),EE=e=>_x(e)?e[e.length-1]||0:e,Wv=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ux="framerAppearId",Kx="data-"+Wv(Ux),lr=e=>!!(e&&e.getVelocity);function Vv(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const Ic=p.createContext(null),Bv=p.createContext({}),Gx=p.createContext({}),Qx={},{schedule:Yx}=Fv(queueMicrotask,!1);function Xx(e){const t=lr(e)?e.get():e;return Hx(t)?t.toValue():t}const ol=p.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),si=p.createContext({}),Hv=typeof window<"u",Uv=Hv?p.useLayoutEffect:p.useEffect,Rc=p.createContext({strict:!1});function Zx(e,t,n,r,o){var l,a;const{visualElement:i}=p.useContext(si),s=p.useContext(Rc),u=p.useContext(Ic),c=p.useContext(ol).reducedMotion,d=p.useRef(null);r=r||s.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:i,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,v=p.useContext(Gx);f&&!f.projection&&o&&(f.type==="html"||f.type==="svg")&&Jx(d.current,n,o,v);const y=p.useRef(!1);p.useInsertionEffect(()=>{f&&y.current&&f.update(n,u)});const b=n[Kx],S=p.useRef(!!b&&!(!((l=window.MotionHandoffIsComplete)===null||l===void 0)&&l.call(window,b))&&((a=window.MotionHasOptimisedAnimation)===null||a===void 0?void 0:a.call(window,b)));return Uv(()=>{f&&(y.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Yx.render(f.render),S.current&&f.animationState&&f.animationState.animateChanges())}),p.useEffect(()=>{f&&(!S.current&&f.animationState&&f.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var h;(h=window.MotionHandoffMarkAsComplete)===null||h===void 0||h.call(window,b)}),S.current=!1))}),f}function Jx(e,t,n,r){const{layoutId:o,layout:l,drag:a,dragConstraints:i,layoutScroll:s,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Kv(e.parent)),e.projection.setOptions({layoutId:o,layout:l,alwaysMeasureLayout:!!a||i&&Vv(i),visualElement:e,animationType:typeof l=="string"?l:"both",initialPromotionConfig:r,layoutScroll:s,layoutRoot:u})}function Kv(e){if(e)return e.options.allowProjection!==!1?e.projection:Kv(e.parent)}function qx(e,t,n){return p.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Vv(n)&&(n.current=r))},[t])}function jc(e){return zv(e.animate)||Ix.some(t=>mu(e[t]))}function e2(e){return!!(jc(e)||e.variants)}function t2(e,t){if(jc(e)){const{initial:n,animate:r}=e;return{initial:n===!1||mu(n)?n:void 0,animate:mu(r)?r:void 0}}return e.inherit!==!1?t:{}}function n2(e){const{initial:t,animate:n}=t2(e,p.useContext(si));return p.useMemo(()=>({initial:t,animate:n}),[Qf(t),Qf(n)])}function Qf(e){return Array.isArray(e)?e.join(" "):e}const Yf={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},za={};for(const e in Yf)za[e]={isEnabled:t=>Yf[e].some(n=>!!t[n])};function Xf(e){for(const t in e)za[t]={...za[t],...e[t]}}const r2=Symbol.for("motionComponentSymbol");function o2({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){function l(i,s){let u;const c={...p.useContext(ol),...i,layoutId:l2(i)},{isStatic:d}=c,f=n2(i),v=r(i,d);if(!d&&Hv){a2();const y=i2(c);u=y.MeasureLayout,f.visualElement=Zx(o,v,c,t,y.ProjectionNode)}return w.jsxs(si.Provider,{value:f,children:[u&&f.visualElement?w.jsx(u,{visualElement:f.visualElement,...c}):null,n(o,i,qx(v,f.visualElement,s),v,d,f.visualElement)]})}const a=p.forwardRef(l);return a[r2]=o,a}function l2({layoutId:e}){const t=p.useContext(Bv).id;return t&&e!==void 0?t+"-"+e:e}function a2(e,t){p.useContext(Rc).strict}function i2(e){const{drag:t,layout:n}=za;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const s2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Gv(e){return typeof e!="string"||e.includes("-")?!1:!!(s2.indexOf(e)>-1||/[A-Z]/u.test(e))}function u2(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const l in n)e.style.setProperty(l,n[l])}const c2=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function d2(e,t,n,r){u2(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(c2.has(o)?o:Wv(o),t.attrs[o])}function Qv(e,{layout:t,layoutId:n}){return Ov.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Qx[e]||e==="opacity")}function Yv(e,t,n){var r;const{style:o}=e,l={};for(const a in o)(lr(o[a])||t.style&&lr(t.style[a])||Qv(a,e)||((r=n==null?void 0:n.getValue(a))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(l[a]=o[a]);return l}function f2(e,t,n){const r=Yv(e,t,n);for(const o in e)if(lr(e[o])||lr(t[o])){const l=ii.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[l]=e[o]}return r}function ui(e){const t=p.useRef(null);return t.current===null&&(t.current=e()),t.current}function p2({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,o,l){const a={latestValues:m2(r,o,l,e),renderState:t()};return n&&(a.mount=i=>n(r,i,a)),a}const Xv=e=>(t,n)=>{const r=p.useContext(si),o=p.useContext(Ic),l=()=>p2(e,t,r,o);return n?l():ui(l)};function m2(e,t,n,r){const o={},l=r(e,{});for(const f in l)o[f]=Xx(l[f]);let{initial:a,animate:i}=e;const s=jc(e),u=e2(e);t&&u&&!s&&e.inherit!==!1&&(a===void 0&&(a=t.initial),i===void 0&&(i=t.animate));let c=n?n.initial===!1:!1;c=c||a===!1;const d=c?i:a;if(d&&typeof d!="boolean"&&!zv(d)){const f=Array.isArray(d)?d:[d];for(let v=0;v<f.length;v++){const y=Mx(e,f[v]);if(y){const{transitionEnd:b,transition:S,...h}=y;for(const m in h){let g=h[m];if(Array.isArray(g)){const x=c?g.length-1:0;g=g[x]}g!==null&&(o[m]=g)}for(const m in b)o[m]=b[m]}}}return o}const zc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Zv=()=>({...zc(),attrs:{}}),Jv=(e,t)=>t&&typeof e=="number"?t.transform(e):e,h2={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},v2=ii.length;function g2(e,t,n){let r="",o=!0;for(let l=0;l<v2;l++){const a=ii[l],i=e[a];if(i===void 0)continue;let s=!0;if(typeof i=="number"?s=i===(a.startsWith("scale")?1:0):s=parseFloat(i)===0,!s||n){const u=Jv(i,Dv[a]);if(!s){o=!1;const c=h2[a]||a;r+=`${c}(${u}) `}n&&(t[a]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function qv(e,t,n){const{style:r,vars:o,transformOrigin:l}=e;let a=!1,i=!1;for(const s in t){const u=t[s];if(Ov.has(s)){a=!0;continue}else if(Fx(s)){o[s]=u;continue}else{const c=Jv(u,Dv[s]);s.startsWith("origin")?(i=!0,l[s]=c):r[s]=c}}if(t.transform||(a||n?r.transform=g2(t,e.transform,n):r.transform&&(r.transform="none")),i){const{originX:s="50%",originY:u="50%",originZ:c=0}=l;r.transformOrigin=`${s} ${u} ${c}`}}function Zf(e,t,n){return typeof e=="string"?e:Q.transform(t+n*e)}function y2(e,t,n){const r=Zf(t,e.x,e.width),o=Zf(n,e.y,e.height);return`${r} ${o}`}const b2={offset:"stroke-dashoffset",array:"stroke-dasharray"},w2={offset:"strokeDashoffset",array:"strokeDasharray"};function x2(e,t,n=1,r=0,o=!0){e.pathLength=1;const l=o?b2:w2;e[l.offset]=Q.transform(-r);const a=Q.transform(t),i=Q.transform(n);e[l.array]=`${a} ${i}`}function eg(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:l,pathLength:a,pathSpacing:i=1,pathOffset:s=0,...u},c,d){if(qv(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:v,dimensions:y}=e;f.transform&&(y&&(v.transform=f.transform),delete f.transform),y&&(o!==void 0||l!==void 0||v.transform)&&(v.transformOrigin=y2(y,o!==void 0?o:.5,l!==void 0?l:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),a!==void 0&&x2(f,a,i,s,!1)}const tg=e=>typeof e=="string"&&e.toLowerCase()==="svg",S2={useVisualState:Xv({scrapeMotionValuesFromProps:f2,createRenderState:Zv,onMount:(e,t,{renderState:n,latestValues:r})=>{Uf.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),Uf.render(()=>{eg(n,r,tg(t.tagName),e.transformTemplate),d2(t,n)})}})},C2={useVisualState:Xv({scrapeMotionValuesFromProps:Yv,createRenderState:zc})};function ng(e,t,n){for(const r in t)!lr(t[r])&&!Qv(r,n)&&(e[r]=t[r])}function E2({transformTemplate:e},t){return p.useMemo(()=>{const n=zc();return qv(n,t,e),Object.assign({},n.vars,n.style)},[t])}function $2(e,t){const n=e.style||{},r={};return ng(r,n,e),Object.assign(r,E2(e,t)),r}function k2(e,t){const n={},r=$2(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const P2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Oa(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||P2.has(e)}let rg=e=>!Oa(e);function og(e){e&&(rg=t=>t.startsWith("on")?!Oa(t):e(t))}try{og(require("@emotion/is-prop-valid").default)}catch{}function T2(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(rg(o)||n===!0&&Oa(o)||!t&&!Oa(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function N2(e,t,n,r){const o=p.useMemo(()=>{const l=Zv();return eg(l,t,tg(r),e.transformTemplate),{...l.attrs,style:{...l.style}}},[t]);if(e.style){const l={};ng(l,e.style,e),o.style={...l,...o.style}}return o}function _2(e=!1){return(n,r,o,{latestValues:l},a)=>{const s=(Gv(n)?N2:k2)(r,l,a,n),u=T2(r,typeof n=="string",e),c=n!==p.Fragment?{...u,...s,ref:o}:{},{children:d}=r,f=p.useMemo(()=>lr(d)?d.get():d,[d]);return p.createElement(n,{...c,children:f})}}function M2(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const a={...Gv(r)?S2:C2,preloadedFeatures:e,useRender:_2(o),createVisualElement:t,Component:r};return o2(a)}}const L2=M2(),ci=Nx(L2);class I2 extends p.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function R2({children:e,isPresent:t}){const n=p.useId(),r=p.useRef(null),o=p.useRef({width:0,height:0,top:0,left:0}),{nonce:l}=p.useContext(ol);return p.useInsertionEffect(()=>{const{width:a,height:i,top:s,left:u}=o.current;if(t||!r.current||!a||!i)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${i}px !important;
            top: ${s}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),w.jsx(I2,{isPresent:t,childRef:r,sizeRef:o,children:p.cloneElement(e,{ref:r})})}const j2=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:l,mode:a})=>{const i=ui(z2),s=p.useId(),u=p.useCallback(d=>{i.set(d,!0);for(const f of i.values())if(!f)return;r&&r()},[i,r]),c=p.useMemo(()=>({id:s,initial:t,isPresent:n,custom:o,onExitComplete:u,register:d=>(i.set(d,!1),()=>i.delete(d))}),l?[Math.random(),u]:[n,u]);return p.useMemo(()=>{i.forEach((d,f)=>i.set(f,!1))},[n]),p.useEffect(()=>{!n&&!i.size&&r&&r()},[n]),a==="popLayout"&&(e=w.jsx(R2,{isPresent:n,children:e})),w.jsx(Ic.Provider,{value:c,children:e})};function z2(){return new Map}const Ul=e=>e.key||"";function Jf(e){const t=[];return p.Children.forEach(e,n=>{p.isValidElement(n)&&t.push(n)}),t}const Oc=({children:e,exitBeforeEnter:t,custom:n,initial:r=!0,onExitComplete:o,presenceAffectsLayout:l=!0,mode:a="sync"})=>{const i=p.useMemo(()=>Jf(e),[e]),s=i.map(Ul),u=p.useRef(!0),c=p.useRef(i),d=ui(()=>new Map),[f,v]=p.useState(i),[y,b]=p.useState(i);Uv(()=>{u.current=!1,c.current=i;for(let m=0;m<y.length;m++){const g=Ul(y[m]);s.includes(g)?d.delete(g):d.get(g)!==!0&&d.set(g,!1)}},[y,s.length,s.join("-")]);const S=[];if(i!==f){let m=[...i];for(let g=0;g<y.length;g++){const x=y[g],E=Ul(x);s.includes(E)||(m.splice(g,0,x),S.push(x))}a==="wait"&&S.length&&(m=S),b(Jf(m)),v(i);return}const{forceRender:h}=p.useContext(Bv);return w.jsx(w.Fragment,{children:y.map(m=>{const g=Ul(m),x=i===y||s.includes(g),E=()=>{if(d.has(g))d.set(g,!0);else return;let $=!0;d.forEach(_=>{_||($=!1)}),$&&(h==null||h(),b(c.current),o&&o())};return w.jsx(j2,{isPresent:x,initial:!u.current||r?void 0:!1,custom:x?void 0:n,presenceAffectsLayout:l,mode:a,onExitComplete:x?void 0:E,children:m},g)})})};function O2({children:e,isValidProp:t,...n}){t&&og(t),n={...p.useContext(ol),...n},n.isStatic=ui(()=>n.isStatic);const r=p.useMemo(()=>n,[JSON.stringify(n.transition),n.transformPagePoint,n.reducedMotion]);return w.jsx(ol.Provider,{value:r,children:e})}function di({children:e,features:t,strict:n=!1}){const[,r]=p.useState(!Zi(t)),o=p.useRef(void 0);if(!Zi(t)){const{renderer:l,...a}=t;o.current=l,Xf(a)}return p.useEffect(()=>{Zi(t)&&t().then(({renderer:l,...a})=>{Xf(a),o.current=l,r(!0)})},[]),w.jsx(Rc.Provider,{value:{renderer:o.current,strict:n},children:e})}function Zi(e){return typeof e=="function"}var F2=({children:e,navigate:t,disableAnimation:n,useHref:r,disableRipple:o=!1,skipFramerMotionAnimations:l=n,reducedMotion:a="never",validationBehavior:i,locale:s="en-US",labelPlacement:u,defaultDates:c,createCalendar:d,spinnerVariant:f,...v})=>{let y=e;t&&(y=w.jsx(vw,{navigate:t,useHref:r,children:y}));const b=p.useMemo(()=>(n&&l&&(Rx.skipAnimations=!0),{createCalendar:d,defaultDates:c,disableAnimation:n,disableRipple:o,validationBehavior:i,labelPlacement:u,spinnerVariant:f}),[d,c==null?void 0:c.maxDate,c==null?void 0:c.minDate,n,o,i,u,f]);return w.jsx(zb,{value:b,children:w.jsx(Qb,{locale:s,children:w.jsx(O2,{reducedMotion:a,children:w.jsx(jv,{...v,children:y})})})})};function A2(e){const t=rn(),n=t==null?void 0:t.labelPlacement;return p.useMemo(()=>{var r,o;const l=(o=(r=e.labelPlacement)!=null?r:n)!=null?o:"inside";return l==="inside"&&!e.label?"outside":l},[e.labelPlacement,n,e.label])}function Ue(e){return p.forwardRef(e)}var on=(e,t,n=!0)=>{if(!t)return[e,{}];const r=t.reduce((o,l)=>l in e?{...o,[l]:e[l]}:o,{});return n?[Object.keys(e).filter(l=>!t.includes(l)).reduce((l,a)=>({...l,[a]:e[a]}),{}),r]:[e,r]},D2={default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},W2={default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground"},V2={default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger"},B2={default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500"},H2={default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger"},U2={default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger"},K2={default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger"},A={solid:D2,shadow:W2,bordered:V2,flat:B2,faded:H2,light:U2,ghost:K2},Kl=["small","medium","large"],qf={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:Kl,borderRadius:Kl},classGroups:{shadow:[{shadow:Kl}],"font-size":[{text:["tiny",...Kl]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}},ep=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ge=e=>!e||typeof e!="object"||Object.keys(e).length===0,G2=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function lg(e,t){e.forEach(function(n){Array.isArray(n)?lg(n,t):t.push(n)})}function ag(e){let t=[];return lg(e,t),t}var ig=(...e)=>ag(e).filter(Boolean),sg=(e,t)=>{let n={},r=Object.keys(e),o=Object.keys(t);for(let l of r)if(o.includes(l)){let a=e[l],i=t[l];Array.isArray(a)||Array.isArray(i)?n[l]=ig(i,a):typeof a=="object"&&typeof i=="object"?n[l]=sg(a,i):n[l]=i+" "+a}else n[l]=e[l];for(let l of o)r.includes(l)||(n[l]=t[l]);return n},tp=e=>!e||typeof e!="string"?e:e.replace(/\s+/g," ").trim();const Fc="-",Q2=e=>{const t=X2(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:a=>{const i=a.split(Fc);return i[0]===""&&i.length!==1&&i.shift(),ug(i,t)||Y2(a)},getConflictingClassGroupIds:(a,i)=>{const s=n[a]||[];return i&&r[a]?[...s,...r[a]]:s}}},ug=(e,t)=>{var a;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?ug(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const l=e.join(Fc);return(a=t.validators.find(({validator:i})=>i(l)))==null?void 0:a.classGroupId},np=/^\[(.+)\]$/,Y2=e=>{if(np.test(e)){const t=np.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},X2=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return J2(Object.entries(e.classGroups),n).forEach(([l,a])=>{vu(a,r,l,t)}),r},vu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const l=o===""?t:rp(t,o);l.classGroupId=n;return}if(typeof o=="function"){if(Z2(o)){vu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([l,a])=>{vu(a,rp(t,l),n,r)})})},rp=(e,t)=>{let n=e;return t.split(Fc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Z2=e=>e.isThemeGetter,J2=(e,t)=>t?e.map(([n,r])=>{const o=r.map(l=>typeof l=="string"?t+l:typeof l=="object"?Object.fromEntries(Object.entries(l).map(([a,i])=>[t+a,i])):l);return[n,o]}):e,q2=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(l,a)=>{n.set(l,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(l){let a=n.get(l);if(a!==void 0)return a;if((a=r.get(l))!==void 0)return o(l,a),a},set(l,a){n.has(l)?n.set(l,a):o(l,a)}}},cg="!",eS=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],l=t.length,a=i=>{const s=[];let u=0,c=0,d;for(let S=0;S<i.length;S++){let h=i[S];if(u===0){if(h===o&&(r||i.slice(S,S+l)===t)){s.push(i.slice(c,S)),c=S+l;continue}if(h==="/"){d=S;continue}}h==="["?u++:h==="]"&&u--}const f=s.length===0?i:i.substring(c),v=f.startsWith(cg),y=v?f.substring(1):f,b=d&&d>c?d-c:void 0;return{modifiers:s,hasImportantModifier:v,baseClassName:y,maybePostfixModifierPosition:b}};return n?i=>n({className:i,parseClassName:a}):a},tS=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},nS=e=>({cache:q2(e.cacheSize),parseClassName:eS(e),...Q2(e)}),rS=/\s+/,oS=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],a=e.trim().split(rS);let i="";for(let s=a.length-1;s>=0;s-=1){const u=a[s],{modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:v}=n(u);let y=!!v,b=r(y?f.substring(0,v):f);if(!b){if(!y){i=u+(i.length>0?" "+i:i);continue}if(b=r(f),!b){i=u+(i.length>0?" "+i:i);continue}y=!1}const S=tS(c).join(":"),h=d?S+cg:S,m=h+b;if(l.includes(m))continue;l.push(m);const g=o(b,y);for(let x=0;x<g.length;++x){const E=g[x];l.push(h+E)}i=u+(i.length>0?" "+i:i)}return i};function lS(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=dg(t))&&(r&&(r+=" "),r+=n);return r}const dg=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=dg(e[r]))&&(n&&(n+=" "),n+=t);return n};function gu(e,...t){let n,r,o,l=a;function a(s){const u=t.reduce((c,d)=>d(c),e());return n=nS(u),r=n.cache.get,o=n.cache.set,l=i,i(s)}function i(s){const u=r(s);if(u)return u;const c=oS(s,n);return o(s,c),c}return function(){return l(lS.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},fg=/^\[(?:([a-z-]+):)?(.+)\]$/i,aS=/^\d+\/\d+$/,iS=new Set(["px","full","screen"]),sS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,uS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,cS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,dS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Bt=e=>zr(e)||iS.has(e)||aS.test(e),sn=e=>qr(e,"length",wS),zr=e=>!!e&&!Number.isNaN(Number(e)),Ji=e=>qr(e,"number",zr),mo=e=>!!e&&Number.isInteger(Number(e)),pS=e=>e.endsWith("%")&&zr(e.slice(0,-1)),X=e=>fg.test(e),un=e=>sS.test(e),mS=new Set(["length","size","percentage"]),hS=e=>qr(e,mS,pg),vS=e=>qr(e,"position",pg),gS=new Set(["image","url"]),yS=e=>qr(e,gS,SS),bS=e=>qr(e,"",xS),ho=()=>!0,qr=(e,t,n)=>{const r=fg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},wS=e=>uS.test(e)&&!cS.test(e),pg=()=>!1,xS=e=>dS.test(e),SS=e=>fS.test(e),yu=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),l=se("borderRadius"),a=se("borderSpacing"),i=se("borderWidth"),s=se("contrast"),u=se("grayscale"),c=se("hueRotate"),d=se("invert"),f=se("gap"),v=se("gradientColorStops"),y=se("gradientColorStopPositions"),b=se("inset"),S=se("margin"),h=se("opacity"),m=se("padding"),g=se("saturate"),x=se("scale"),E=se("sepia"),$=se("skew"),_=se("space"),M=se("translate"),O=()=>["auto","contain","none"],I=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto",X,t],P=()=>[X,t],z=()=>["",Bt,sn],k=()=>["auto",zr,X],D=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],N=()=>["solid","dashed","dotted","double","none"],F=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",X],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>[zr,X];return{cacheSize:500,separator:":",theme:{colors:[ho],spacing:[Bt,sn],blur:["none","",un,X],brightness:V(),borderColor:[e],borderRadius:["none","","full",un,X],borderSpacing:P(),borderWidth:z(),contrast:V(),grayscale:R(),hueRotate:V(),invert:R(),gap:P(),gradientColorStops:[e],gradientColorStopPositions:[pS,sn],inset:C(),margin:C(),opacity:V(),padding:P(),saturate:V(),scale:V(),sepia:R(),skew:V(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[un]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...D(),X]}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",mo,X]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",mo,X]}],"grid-cols":[{"grid-cols":[ho]}],"col-start-end":[{col:["auto",{span:["full",mo,X]},X]}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":[ho]}],"row-start-end":[{row:["auto",{span:[mo,X]},X]}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[m]}],px:[{px:[m]}],py:[{py:[m]}],ps:[{ps:[m]}],pe:[{pe:[m]}],pt:[{pt:[m]}],pr:[{pr:[m]}],pb:[{pb:[m]}],pl:[{pl:[m]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,t]}],"min-w":[{"min-w":[X,t,"min","max","fit"]}],"max-w":[{"max-w":[X,t,"none","full","min","max","fit","prose",{screen:[un]},un]}],h:[{h:[X,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,t,"auto","min","max","fit"]}],"font-size":[{text:["base",un,sn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ji]}],"font-family":[{font:[ho]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",zr,Ji]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Bt,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...N(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Bt,sn]}],"underline-offset":[{"underline-offset":["auto",Bt,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...D(),vS]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",hS]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},yS]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[y]}],"gradient-via-pos":[{via:[y]}],"gradient-to-pos":[{to:[y]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...N(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:N()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...N()]}],"outline-offset":[{"outline-offset":[Bt,X]}],"outline-w":[{outline:[Bt,sn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Bt,sn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",un,bS]}],"shadow-color":[{shadow:[ho]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...F(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":F()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",un,X]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[g]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:V()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:V()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[mo,X]}],"translate-x":[{"translate-x":[M]}],"translate-y":[{"translate-y":[M]}],"skew-x":[{"skew-x":[$]}],"skew-y":[{"skew-y":[$]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Bt,sn,Ji]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},CS=(e,{cacheSize:t,prefix:n,separator:r,experimentalParseClassName:o,extend:l={},override:a={}})=>{ko(e,"cacheSize",t),ko(e,"prefix",n),ko(e,"separator",r),ko(e,"experimentalParseClassName",o);for(const i in a)ES(e[i],a[i]);for(const i in l)$S(e[i],l[i]);return e},ko=(e,t,n)=>{n!==void 0&&(e[t]=n)},ES=(e,t)=>{if(t)for(const n in t)ko(e,n,t[n])},$S=(e,t)=>{if(t)for(const n in t){const r=t[n];r!==void 0&&(e[n]=(e[n]||[]).concat(r))}},kS=(e,...t)=>typeof e=="function"?gu(yu,e,...t):gu(()=>CS(yu(),e),...t),PS=gu(yu);var TS={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},mg=e=>e||void 0,ll=(...e)=>mg(ag(e).filter(Boolean).join(" ")),qi=null,Kt={},bu=!1,vo=(...e)=>t=>t.twMerge?((!qi||bu)&&(bu=!1,qi=Ge(Kt)?PS:kS({...Kt,extend:{theme:Kt.theme,classGroups:Kt.classGroups,conflictingClassGroupModifiers:Kt.conflictingClassGroupModifiers,conflictingClassGroups:Kt.conflictingClassGroups,...Kt.extend}})),mg(qi(ll(e)))):ll(e),op=(e,t)=>{for(let n in t)e.hasOwnProperty(n)?e[n]=ll(e[n],t[n]):e[n]=t[n];return e},Ac=(e,t)=>{let{extend:n=null,slots:r={},variants:o={},compoundVariants:l=[],compoundSlots:a=[],defaultVariants:i={}}=e,s={...TS,...t},u=n!=null&&n.base?ll(n.base,e==null?void 0:e.base):e==null?void 0:e.base,c=n!=null&&n.variants&&!Ge(n.variants)?sg(o,n.variants):o,d=n!=null&&n.defaultVariants&&!Ge(n.defaultVariants)?{...n.defaultVariants,...i}:i;!Ge(s.twMergeConfig)&&!G2(s.twMergeConfig,Kt)&&(bu=!0,Kt=s.twMergeConfig);let f=Ge(n==null?void 0:n.slots),v=Ge(r)?{}:{base:ll(e==null?void 0:e.base,f&&(n==null?void 0:n.base)),...r},y=f?v:op({...n==null?void 0:n.slots},Ge(v)?{base:e==null?void 0:e.base}:v),b=Ge(n==null?void 0:n.compoundVariants)?l:ig(n==null?void 0:n.compoundVariants,l),S=m=>{if(Ge(c)&&Ge(r)&&f)return vo(u,m==null?void 0:m.class,m==null?void 0:m.className)(s);if(b&&!Array.isArray(b))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof b}`);if(a&&!Array.isArray(a))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof a}`);let g=(P,z,k=[],D)=>{let N=k;if(typeof z=="string")N=N.concat(tp(z).split(" ").map(F=>`${P}:${F}`));else if(Array.isArray(z))N=N.concat(z.reduce((F,T)=>F.concat(`${P}:${T}`),[]));else if(typeof z=="object"&&typeof D=="string"){for(let F in z)if(z.hasOwnProperty(F)&&F===D){let T=z[F];if(T&&typeof T=="string"){let R=tp(T);N[D]?N[D]=N[D].concat(R.split(" ").map(L=>`${P}:${L}`)):N[D]=R.split(" ").map(L=>`${P}:${L}`)}else Array.isArray(T)&&T.length>0&&(N[D]=T.reduce((R,L)=>R.concat(`${P}:${L}`),[]))}}return N},x=(P,z=c,k=null,D=null)=>{var N;let F=z[P];if(!F||Ge(F))return null;let T=(N=D==null?void 0:D[P])!=null?N:m==null?void 0:m[P];if(T===null)return null;let R=ep(T),L=Array.isArray(s.responsiveVariants)&&s.responsiveVariants.length>0||s.responsiveVariants===!0,V=d==null?void 0:d[P],H=[];if(typeof R=="object"&&L)for(let[re,G]of Object.entries(R)){let we=F[G];if(re==="initial"){V=G;continue}Array.isArray(s.responsiveVariants)&&!s.responsiveVariants.includes(re)||(H=g(re,we,H,k))}let te=R!=null&&typeof R!="object"?R:ep(V),K=F[te||"false"];return typeof H=="object"&&typeof k=="string"&&H[k]?op(H,K):H.length>0?(H.push(K),k==="base"?H.join(" "):H):K},E=()=>c?Object.keys(c).map(P=>x(P,c)):null,$=(P,z)=>{if(!c||typeof c!="object")return null;let k=new Array;for(let D in c){let N=x(D,c,P,z),F=P==="base"&&typeof N=="string"?N:N&&N[P];F&&(k[k.length]=F)}return k},_={};for(let P in m)m[P]!==void 0&&(_[P]=m[P]);let M=(P,z)=>{var k;let D=typeof(m==null?void 0:m[P])=="object"?{[P]:(k=m[P])==null?void 0:k.initial}:{};return{...d,..._,...D,...z}},O=(P=[],z)=>{let k=[];for(let{class:D,className:N,...F}of P){let T=!0;for(let[R,L]of Object.entries(F)){let V=M(R,z)[R];if(Array.isArray(L)){if(!L.includes(V)){T=!1;break}}else{let H=te=>te==null||te===!1;if(H(L)&&H(V))continue;if(V!==L){T=!1;break}}}T&&(D&&k.push(D),N&&k.push(N))}return k},I=P=>{let z=O(b,P);if(!Array.isArray(z))return z;let k={};for(let D of z)if(typeof D=="string"&&(k.base=vo(k.base,D)(s)),typeof D=="object")for(let[N,F]of Object.entries(D))k[N]=vo(k[N],F)(s);return k},C=P=>{if(a.length<1)return null;let z={};for(let{slots:k=[],class:D,className:N,...F}of a){if(!Ge(F)){let T=!0;for(let R of Object.keys(F)){let L=M(R,P)[R];if(L===void 0||(Array.isArray(F[R])?!F[R].includes(L):F[R]!==L)){T=!1;break}}if(!T)continue}for(let T of k)z[T]=z[T]||[],z[T].push([D,N])}return z};if(!Ge(r)||!f){let P={};if(typeof y=="object"&&!Ge(y))for(let z of Object.keys(y))P[z]=k=>{var D,N;return vo(y[z],$(z,k),((D=I(k))!=null?D:[])[z],((N=C(k))!=null?N:[])[z],k==null?void 0:k.class,k==null?void 0:k.className)(s)};return P}return vo(u,E(),O(b),m==null?void 0:m.class,m==null?void 0:m.className)(s)},h=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return S.variantKeys=h(),S.extend=n,S.base=u,S.slots=y,S.variants=c,S.defaultVariants=d,S.compoundSlots=a,S.compoundVariants=b,S},wt=(e,t)=>{var n,r,o;return Ac(e,{...t,twMerge:(n=void 0)!=null?n:!0,twMergeConfig:{theme:{...(r=void 0)==null?void 0:r.theme,...qf.theme},classGroups:{...(o=void 0)==null?void 0:o.classGroups,...qf.classGroups}}})},es=wt({slots:{base:"inline-flex items-center justify-between h-fit rounded-large gap-2",pre:"bg-transparent text-inherit font-mono font-normal inline-block whitespace-nowrap",content:"flex flex-col",symbol:"select-none",copyButton:["group","relative","z-10","text-large","text-inherit","data-[hover=true]:bg-transparent"],copyIcon:["absolute text-inherit opacity-100 scale-100 group-data-[copied=true]:opacity-0 group-data-[copied=true]:scale-50"],checkIcon:["absolute text-inherit opacity-0 scale-50 group-data-[copied=true]:opacity-100 group-data-[copied=true]:scale-100"]},variants:{variant:{flat:"",solid:"",bordered:"border-medium bg-transparent",shadow:""},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{base:"px-1.5 py-0.5 text-tiny rounded-small"},md:{base:"px-3 py-1.5 text-small rounded-medium"},lg:{base:"px-4 py-2 text-medium rounded-large"}},radius:{none:{base:"rounded-none"},sm:{base:"rounded-small"},md:{base:"rounded-medium"},lg:{base:"rounded-large"}},fullWidth:{true:{base:"w-full"}},disableAnimation:{true:{},false:{copyIcon:"transition-transform-opacity",checkIcon:"transition-transform-opacity"}}},defaultVariants:{color:"default",variant:"flat",size:"md",fullWidth:!1},compoundVariants:[{variant:["solid","shadow"],color:"default",class:{copyButton:"data-[focus-visible]:outline-default-foreground"}},{variant:["solid","shadow"],color:"primary",class:{copyButton:"data-[focus-visible]:outline-primary-foreground"}},{variant:["solid","shadow"],color:"secondary",class:{copyButton:"data-[focus-visible]:outline-secondary-foreground"}},{variant:["solid","shadow"],color:"success",class:{copyButton:"data-[focus-visible]:outline-success-foreground"}},{variant:["solid","shadow"],color:"warning",class:{copyButton:"data-[focus-visible]:outline-warning-foreground"}},{variant:["solid","shadow"],color:"danger",class:{copyButton:"data-[focus-visible]:outline-danger-foreground"}},{variant:"flat",color:"default",class:{base:A.flat.default}},{variant:"flat",color:"primary",class:{base:A.flat.primary}},{variant:"flat",color:"secondary",class:{base:A.flat.secondary}},{variant:"flat",color:"success",class:{base:A.flat.success}},{variant:"flat",color:"warning",class:{base:A.flat.warning}},{variant:"flat",color:"danger",class:{base:A.flat.danger}},{variant:"solid",color:"default",class:{base:A.solid.default}},{variant:"solid",color:"primary",class:{base:A.solid.primary}},{variant:"solid",color:"secondary",class:{base:A.solid.secondary}},{variant:"solid",color:"success",class:{base:A.solid.success}},{variant:"solid",color:"warning",class:{base:A.solid.warning}},{variant:"solid",color:"danger",class:{base:A.solid.danger}},{variant:"shadow",color:"default",class:{base:A.shadow.default}},{variant:"shadow",color:"primary",class:{base:A.shadow.primary}},{variant:"shadow",color:"secondary",class:{base:A.shadow.secondary}},{variant:"shadow",color:"success",class:{base:A.shadow.success}},{variant:"shadow",color:"warning",class:{base:A.shadow.warning}},{variant:"shadow",color:"danger",class:{base:A.shadow.danger}},{variant:"bordered",color:"default",class:{base:A.bordered.default}},{variant:"bordered",color:"primary",class:{base:A.bordered.primary}},{variant:"bordered",color:"secondary",class:{base:A.bordered.secondary}},{variant:"bordered",color:"success",class:{base:A.bordered.success}},{variant:"bordered",color:"warning",class:{base:A.bordered.warning}},{variant:"bordered",color:"danger",class:{base:A.bordered.danger}}]}),lp=wt({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",label:"text-foreground dark:text-foreground-dark font-regular",circle1:"absolute w-full h-full rounded-full",circle2:"absolute w-full h-full rounded-full",dots:"relative rounded-full mx-auto",spinnerBars:["absolute","animate-fade-out","rounded-full","w-[25%]","h-[8%]","left-[calc(37.5%)]","top-[calc(46%)]","spinner-bar-animation"]},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",dots:"size-1",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",dots:"size-1.5",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",dots:"size-2",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current",dots:"bg-current",spinnerBars:"bg-current"},white:{circle1:"border-b-white",circle2:"border-b-white",dots:"bg-white",spinnerBars:"bg-white"},default:{circle1:"border-b-default",circle2:"border-b-default",dots:"bg-default",spinnerBars:"bg-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary",dots:"bg-primary",spinnerBars:"bg-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary",dots:"bg-secondary",spinnerBars:"bg-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success",dots:"bg-success",spinnerBars:"bg-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning",dots:"bg-warning",spinnerBars:"bg-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger",dots:"bg-danger",spinnerBars:"bg-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}},variant:{default:{circle1:["animate-spinner-ease-spin","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["opacity-75","animate-spinner-linear-spin","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"]},gradient:{circle1:["border-0","bg-gradient-to-b","from-transparent","via-transparent","to-primary","animate-spinner-linear-spin","[animation-duration:1s]","[-webkit-mask:radial-gradient(closest-side,rgba(0,0,0,0.0)calc(100%-3px),rgba(0,0,0,1)calc(100%-3px))]"],circle2:["hidden"]},wave:{wrapper:"translate-y-3/4",dots:["animate-sway","spinner-dot-animation"]},dots:{wrapper:"translate-y-2/4",dots:["animate-blink","spinner-dot-blink-animation"]},spinner:{},simple:{wrapper:"text-foreground h-5 w-5 animate-spin",circle1:"opacity-25",circle2:"opacity-75"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground",variant:"default"},compoundVariants:[{variant:"gradient",color:"current",class:{circle1:"to-current"}},{variant:"gradient",color:"white",class:{circle1:"to-white"}},{variant:"gradient",color:"default",class:{circle1:"to-default"}},{variant:"gradient",color:"primary",class:{circle1:"to-primary"}},{variant:"gradient",color:"secondary",class:{circle1:"to-secondary"}},{variant:"gradient",color:"success",class:{circle1:"to-success"}},{variant:"gradient",color:"warning",class:{circle1:"to-warning"}},{variant:"gradient",color:"danger",class:{circle1:"to-danger"}},{variant:"wave",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"wave",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"wave",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"dots",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"dots",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"dots",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"simple",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"simple",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",color:"current",class:{wrapper:"text-current"}},{variant:"simple",color:"white",class:{wrapper:"text-white"}},{variant:"simple",color:"default",class:{wrapper:"text-default"}},{variant:"simple",color:"primary",class:{wrapper:"text-primary"}},{variant:"simple",color:"secondary",class:{wrapper:"text-secondary"}},{variant:"simple",color:"success",class:{wrapper:"text-success"}},{variant:"simple",color:"warning",class:{wrapper:"text-warning"}},{variant:"simple",color:"danger",class:{wrapper:"text-danger"}}]}),ml=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],hg=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],fr={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]},NS=["font-inherit","text-[100%]","leading-[1.15]","m-0","p-0","overflow-visible","box-border","absolute","top-0","w-full","h-full","opacity-[0.0001]","z-[1]","cursor-pointer","disabled:cursor-default"],ap=wt({slots:{base:"group relative max-w-fit inline-flex items-center justify-start cursor-pointer touch-none tap-highlight-transparent select-none",wrapper:["px-1","relative","inline-flex","items-center","justify-start","flex-shrink-0","overflow-hidden","bg-default-200","rounded-full",...hg],thumb:["z-10","flex","items-center","justify-center","bg-white","shadow-small","rounded-full","origin-right","pointer-events-none"],hiddenInput:NS,startContent:"z-0 absolute start-1.5 text-current",endContent:"z-0 absolute end-1.5 text-default-600",thumbIcon:"text-black",label:"relative text-foreground select-none ms-2"},variants:{color:{default:{wrapper:["group-data-[selected=true]:bg-default-400","group-data-[selected=true]:text-default-foreground"]},primary:{wrapper:["group-data-[selected=true]:bg-primary","group-data-[selected=true]:text-primary-foreground"]},secondary:{wrapper:["group-data-[selected=true]:bg-secondary","group-data-[selected=true]:text-secondary-foreground"]},success:{wrapper:["group-data-[selected=true]:bg-success","group-data-[selected=true]:text-success-foreground"]},warning:{wrapper:["group-data-[selected=true]:bg-warning","group-data-[selected=true]:text-warning-foreground"]},danger:{wrapper:["group-data-[selected=true]:bg-danger","data-[selected=true]:text-danger-foreground"]}},size:{sm:{wrapper:"w-10 h-6",thumb:["w-4 h-4 text-tiny","group-data-[selected=true]:ms-4"],endContent:"text-tiny",startContent:"text-tiny",label:"text-small"},md:{wrapper:"w-12 h-7",thumb:["w-5 h-5 text-small","group-data-[selected=true]:ms-5"],endContent:"text-small",startContent:"text-small",label:"text-medium"},lg:{wrapper:"w-14 h-8",thumb:["w-6 h-6 text-medium","group-data-[selected=true]:ms-6"],endContent:"text-medium",startContent:"text-medium",label:"text-large"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{wrapper:"transition-none",thumb:"transition-none"},false:{wrapper:"transition-background",thumb:"transition-all",startContent:["opacity-0","scale-50","transition-transform-opacity","group-data-[selected=true]:scale-100","group-data-[selected=true]:opacity-100"],endContent:["opacity-100","transition-transform-opacity","group-data-[selected=true]:translate-x-3","group-data-[selected=true]:opacity-0"]}}},defaultVariants:{color:"primary",size:"md",isDisabled:!1},compoundVariants:[{disableAnimation:!1,size:"sm",class:{thumb:["group-data-[pressed=true]:w-5","group-data-[selected]:group-data-[pressed]:ml-3"]}},{disableAnimation:!1,size:"md",class:{thumb:["group-data-[pressed=true]:w-6","group-data-[selected]:group-data-[pressed]:ml-4"]}},{disableAnimation:!1,size:"lg",class:{thumb:["group-data-[pressed=true]:w-7","group-data-[selected]:group-data-[pressed]:ml-5"]}}]}),ip=wt({slots:{base:["z-0","relative","bg-transparent","before:content-['']","before:hidden","before:z-[-1]","before:absolute","before:rotate-45","before:w-2.5","before:h-2.5","before:rounded-sm","data-[arrow=true]:before:block","data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top]:before:left-1/2","data-[placement=top]:before:-translate-x-1/2","data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-start]:before:left-3","data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-end]:before:right-3","data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom]:before:left-1/2","data-[placement=bottom]:before:-translate-x-1/2","data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-start]:before:left-3","data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-end]:before:right-3","data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=left]:before:top-1/2","data-[placement=left]:before:-translate-y-1/2","data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-start]:before:top-1/4","data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-end]:before:bottom-1/4","data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=right]:before:top-1/2","data-[placement=right]:before:-translate-y-1/2","data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-start]:before:top-1/4","data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-end]:before:bottom-1/4",...ml],content:["z-10","px-2.5","py-1","w-full","inline-flex","flex-col","items-center","justify-center","box-border","subpixel-antialiased","outline-none","box-border"],trigger:["z-10"],backdrop:["hidden"],arrow:[]},variants:{size:{sm:{content:"text-tiny"},md:{content:"text-small"},lg:{content:"text-medium"}},color:{default:{base:"before:bg-content1 before:shadow-small",content:"bg-content1"},foreground:{base:"before:bg-foreground",content:A.solid.foreground},primary:{base:"before:bg-primary",content:A.solid.primary},secondary:{base:"before:bg-secondary",content:A.solid.secondary},success:{base:"before:bg-success",content:A.solid.success},warning:{base:"before:bg-warning",content:A.solid.warning},danger:{base:"before:bg-danger",content:A.solid.danger}},radius:{none:{content:"rounded-none"},sm:{content:"rounded-small"},md:{content:"rounded-medium"},lg:{content:"rounded-large"},full:{content:"rounded-full"}},shadow:{none:{content:"shadow-none"},sm:{content:"shadow-small"},md:{content:"shadow-medium"},lg:{content:"shadow-large"}},backdrop:{transparent:{},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-sm backdrop-saturate-150 bg-overlay/30"}},triggerScaleOnOpen:{true:{trigger:["aria-expanded:scale-[0.97]","aria-expanded:opacity-70","subpixel-antialiased"]},false:{}},disableAnimation:{true:{base:"animate-none"}},isTriggerDisabled:{true:{trigger:"opacity-disabled pointer-events-none"},false:{}}},defaultVariants:{color:"default",radius:"lg",size:"md",shadow:"md",backdrop:"transparent",triggerScaleOnOpen:!0},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"block w-full h-full fixed inset-0 -z-30"}}]}),sp=wt({slots:{base:"group flex flex-col data-[hidden=true]:hidden",label:["absolute","z-10","pointer-events-none","origin-top-left","flex-shrink-0","rtl:origin-top-right","subpixel-antialiased","block","text-small","text-foreground-500"],mainWrapper:"h-full",inputWrapper:"relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-sm px-3 gap-3",innerWrapper:"inline-flex w-full items-center h-full box-border",input:["w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none","data-[has-start-content=true]:ps-1.5","data-[has-end-content=true]:pe-1.5","data-[type=color]:rounded-none","file:cursor-pointer file:bg-transparent file:border-0","autofill:bg-transparent bg-clip-text"],clearButton:["p-2","-m-2","z-10","absolute","end-3","start-auto","pointer-events-none","appearance-none","outline-none","select-none","opacity-0","cursor-pointer","active:!opacity-70","rounded-full",...ml],helperWrapper:"hidden group-data-[has-helper=true]:flex p-1 relative flex-col gap-1.5",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger"},variants:{variant:{flat:{inputWrapper:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-100"]},faded:{inputWrapper:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 focus-within:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground"},bordered:{inputWrapper:["border-medium","border-default-200","data-[hover=true]:border-default-400","group-data-[focus=true]:border-default-foreground"]},underlined:{inputWrapper:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","group-data-[focus=true]:after:w-full"],innerWrapper:"pb-1",label:"group-data-[filled-within=true]:text-foreground"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{label:"text-tiny",inputWrapper:"h-8 min-h-8 px-2 rounded-small",input:"text-small",clearButton:"text-medium"},md:{inputWrapper:"h-10 min-h-10 rounded-medium",input:"text-small",clearButton:"text-large hover:!opacity-100"},lg:{label:"text-medium",inputWrapper:"h-12 min-h-12 rounded-large",input:"text-medium",clearButton:"text-large hover:!opacity-100"}},radius:{none:{inputWrapper:"rounded-none"},sm:{inputWrapper:"rounded-small"},md:{inputWrapper:"rounded-medium"},lg:{inputWrapper:"rounded-large"},full:{inputWrapper:"rounded-full"}},labelPlacement:{outside:{mainWrapper:"flex flex-col"},"outside-left":{base:"flex-row items-center flex-nowrap data-[has-helper=true]:items-start",inputWrapper:"flex-1",mainWrapper:"flex flex-col",label:"relative text-foreground pe-2 ps-2 pointer-events-auto"},inside:{label:"cursor-text",inputWrapper:"flex-col items-start justify-center gap-0",innerWrapper:"group-data-[has-label=true]:items-end"}},fullWidth:{true:{base:"w-full"},false:{}},isClearable:{true:{input:"peer pe-6 input-search-cancel-button-none",clearButton:["peer-data-[filled=true]:pointer-events-auto","peer-data-[filled=true]:opacity-70 peer-data-[filled=true]:block","peer-data-[filled=true]:scale-100"]}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",inputWrapper:"pointer-events-none",label:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",input:"!placeholder:text-danger !text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",inputWrapper:"!h-auto",innerWrapper:"items-start group-data-[has-label=true]:items-start",input:"resize-none data-[hide-scroll=true]:scrollbar-hide",clearButton:"absolute top-2 right-2 rtl:right-auto rtl:left-2 z-10"}},disableAnimation:{true:{input:"transition-none",inputWrapper:"transition-none",label:"transition-none"},false:{inputWrapper:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","!duration-200","!ease-out","motion-reduce:transition-none","transition-[transform,color,left,opacity]"],clearButton:["scale-90","ease-out","duration-150","transition-[opacity,transform]","motion-reduce:transition-none","motion-reduce:scale-100"]}}},defaultVariants:{variant:"flat",color:"default",size:"md",fullWidth:!0,isDisabled:!1,isMultiline:!1},compoundVariants:[{variant:"flat",color:"default",class:{input:"group-data-[has-value=true]:text-default-foreground"}},{variant:"flat",color:"primary",class:{inputWrapper:["bg-primary-100","data-[hover=true]:bg-primary-50","text-primary","group-data-[focus=true]:bg-primary-50","placeholder:text-primary"],input:"placeholder:text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{inputWrapper:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50","placeholder:text-secondary"],input:"placeholder:text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{inputWrapper:["bg-success-100","text-success-600","dark:text-success","placeholder:text-success-600","dark:placeholder:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],input:"placeholder:text-success-600 dark:placeholder:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{inputWrapper:["bg-warning-100","text-warning-600","dark:text-warning","placeholder:text-warning-600","dark:placeholder:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],input:"placeholder:text-warning-600 dark:placeholder:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{inputWrapper:["bg-danger-100","text-danger","dark:text-danger-500","placeholder:text-danger","dark:placeholder:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],input:"placeholder:text-danger dark:placeholder:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{label:"text-primary",inputWrapper:"data-[hover=true]:border-primary focus-within:border-primary"}},{variant:"faded",color:"secondary",class:{label:"text-secondary",inputWrapper:"data-[hover=true]:border-secondary focus-within:border-secondary"}},{variant:"faded",color:"success",class:{label:"text-success",inputWrapper:"data-[hover=true]:border-success focus-within:border-success"}},{variant:"faded",color:"warning",class:{label:"text-warning",inputWrapper:"data-[hover=true]:border-warning focus-within:border-warning"}},{variant:"faded",color:"danger",class:{label:"text-danger",inputWrapper:"data-[hover=true]:border-danger focus-within:border-danger"}},{variant:"underlined",color:"default",class:{input:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{inputWrapper:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{inputWrapper:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{inputWrapper:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{inputWrapper:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{inputWrapper:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{inputWrapper:"group-data-[focus=true]:border-primary",label:"text-primary"}},{variant:"bordered",color:"secondary",class:{inputWrapper:"group-data-[focus=true]:border-secondary",label:"text-secondary"}},{variant:"bordered",color:"success",class:{inputWrapper:"group-data-[focus=true]:border-success",label:"text-success"}},{variant:"bordered",color:"warning",class:{inputWrapper:"group-data-[focus=true]:border-warning",label:"text-warning"}},{variant:"bordered",color:"danger",class:{inputWrapper:"group-data-[focus=true]:border-danger",label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled-within=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled-within=true]:text-foreground"}},{radius:"full",size:["sm"],class:{inputWrapper:"px-3"}},{radius:"full",size:"md",class:{inputWrapper:"px-4"}},{radius:"full",size:"lg",class:{inputWrapper:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{inputWrapper:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{inputWrapper:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{inputWrapper:[...hg]}},{isInvalid:!0,variant:"flat",class:{inputWrapper:["!bg-danger-50","data-[hover=true]:!bg-danger-100","group-data-[focus=true]:!bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{inputWrapper:"!border-danger group-data-[focus=true]:!border-danger"}},{isInvalid:!0,variant:"underlined",class:{inputWrapper:"after:!bg-danger"}},{labelPlacement:"inside",size:"sm",class:{inputWrapper:"h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{inputWrapper:"h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{inputWrapper:"h-16 py-2.5 gap-0"}},{labelPlacement:"inside",size:"sm",variant:["bordered","faded"],class:{inputWrapper:"py-1"}},{labelPlacement:["inside","outside"],class:{label:["group-data-[filled-within=true]:pointer-events-auto"]}},{labelPlacement:"outside",isMultiline:!1,class:{base:"relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled-within=true]:start-0"]}},{labelPlacement:["inside"],class:{label:["group-data-[filled-within=true]:scale-85"]}},{labelPlacement:["inside"],variant:"flat",class:{innerWrapper:"pb-0.5"}},{variant:"underlined",size:"sm",class:{innerWrapper:"pb-1"}},{variant:"underlined",size:["md","lg"],class:{innerWrapper:"pb-1.5"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px)]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",size:"lg",isMultiline:!1,class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.tiny)/2_+_16px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_8px)]"}},{labelPlacement:"outside",size:"md",isMultiline:!1,class:{label:["start-3","end-auto","text-small","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)]"}},{labelPlacement:"outside",size:"lg",isMultiline:!1,class:{label:["start-3","end-auto","text-medium","group-data-[filled-within=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_24px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_12px)]"}},{labelPlacement:"outside-left",size:"sm",class:{label:"group-data-[has-helper=true]:pt-2"}},{labelPlacement:"outside-left",size:"md",class:{label:"group-data-[has-helper=true]:pt-3"}},{labelPlacement:"outside-left",size:"lg",class:{label:"group-data-[has-helper=true]:pt-4"}},{labelPlacement:["outside","outside-left"],isMultiline:!0,class:{inputWrapper:"py-2"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:"inside",isMultiline:!0,class:{label:"pb-0.5",input:"pt-0"}},{isMultiline:!0,disableAnimation:!1,class:{input:"transition-height !duration-100 motion-reduce:transition-none"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}},{isMultiline:!0,radius:"full",class:{inputWrapper:"data-[has-multiple-rows=true]:rounded-large"}},{isClearable:!0,isMultiline:!0,class:{clearButton:["group-data-[has-value=true]:opacity-70 group-data-[has-value=true]:block","group-data-[has-value=true]:scale-100","group-data-[has-value=true]:pointer-events-auto"]}}]}),up=wt({slots:{base:["px-1.5","py-0.5","inline-flex","space-x-0.5","rtl:space-x-reverse","items-center","font-sans","font-normal","text-center","text-small","shadow-small","bg-default-100","text-foreground-600","rounded-small"],abbr:"no-underline",content:""},variants:{},defaultVariants:{}}),cp=wt({base:["relative inline-flex items-center outline-none tap-highlight-transparent",...ml],variants:{size:{sm:"text-small",md:"text-medium",lg:"text-large"},color:{foreground:"text-foreground",primary:"text-primary",secondary:"text-secondary",success:"text-success",warning:"text-warning",danger:"text-danger"},underline:{none:"no-underline",hover:"hover:underline",always:"underline",active:"active:underline",focus:"focus:underline"},isBlock:{true:["px-2","py-1","hover:after:opacity-100","after:content-['']","after:inset-0","after:opacity-0","after:w-full","after:h-full","after:rounded-xl","after:transition-background","after:absolute"],false:"hover:opacity-hover active:opacity-disabled transition-opacity"},isDisabled:{true:"opacity-disabled cursor-default pointer-events-none"},disableAnimation:{true:"after:transition-none transition-none"}},compoundVariants:[{isBlock:!0,color:"foreground",class:"hover:after:bg-foreground/10"},{isBlock:!0,color:"primary",class:"hover:after:bg-primary/20"},{isBlock:!0,color:"secondary",class:"hover:after:bg-secondary/20"},{isBlock:!0,color:"success",class:"hover:after:bg-success/20"},{isBlock:!0,color:"warning",class:"hover:after:bg-warning/20"},{isBlock:!0,color:"danger",class:"hover:after:bg-danger/20"},{underline:["hover","always","active","focus"],class:"underline-offset-4"}],defaultVariants:{color:"primary",size:"md",isBlock:!1,underline:"none",isDisabled:!1}}),_S="flex mx-1 text-current self-center",dp=wt({slots:{base:["flex","z-40","w-full","h-auto","items-center","justify-center","data-[menu-open=true]:border-none"],wrapper:["z-40","flex","px-6","gap-4","w-full","flex-row","relative","flex-nowrap","items-center","justify-between","h-[var(--navbar-height)]"],toggle:["group","flex","items-center","justify-center","w-6","h-full","outline-none","rounded-small","tap-highlight-transparent",...ml],srOnly:["sr-only"],toggleIcon:["w-full","h-full","pointer-events-none","flex","flex-col","items-center","justify-center","text-inherit","group-data-[pressed=true]:opacity-70","transition-opacity","before:content-['']","before:block","before:h-px","before:w-6","before:bg-current","before:transition-transform","before:duration-150","before:-translate-y-1","before:rotate-0","group-data-[open=true]:before:translate-y-px","group-data-[open=true]:before:rotate-45","after:content-['']","after:block","after:h-px","after:w-6","after:bg-current","after:transition-transform","after:duration-150","after:translate-y-1","after:rotate-0","group-data-[open=true]:after:translate-y-0","group-data-[open=true]:after:-rotate-45"],brand:["flex","basis-0","flex-row","flex-grow","flex-nowrap","justify-start","bg-transparent","items-center","no-underline","text-medium","whitespace-nowrap","box-border"],content:["flex","gap-4","h-full","flex-row","flex-nowrap","items-center","data-[justify=start]:justify-start","data-[justify=start]:flex-grow","data-[justify=start]:basis-0","data-[justify=center]:justify-center","data-[justify=end]:justify-end","data-[justify=end]:flex-grow","data-[justify=end]:basis-0"],item:["text-medium","whitespace-nowrap","box-border","list-none","data-[active=true]:font-semibold"],menu:["z-30","px-6","pt-2","fixed","flex","max-w-full","top-[var(--navbar-height)]","inset-x-0","bottom-0","w-screen","flex-col","gap-2","overflow-y-auto"],menuItem:["text-large","data-[active=true]:font-semibold"]},variants:{position:{static:{base:"static"},sticky:{base:"sticky top-0 inset-x-0"}},maxWidth:{sm:{wrapper:"max-w-[640px]"},md:{wrapper:"max-w-[768px]"},lg:{wrapper:"max-w-[1024px]"},xl:{wrapper:"max-w-[1280px]"},"2xl":{wrapper:"max-w-[1536px]"},full:{wrapper:"max-w-full"}},hideOnScroll:{true:{base:["sticky","top-0","inset-x-0"]}},isBordered:{true:{base:["border-b","border-divider"]}},isBlurred:{false:{base:"bg-background",menu:"bg-background"},true:{base:["backdrop-blur-lg","data-[menu-open=true]:backdrop-blur-xl","backdrop-saturate-150","bg-background/70"],menu:["backdrop-blur-xl","backdrop-saturate-150","bg-background/70"]}},disableAnimation:{true:{menu:["hidden","h-[calc(100dvh_-_var(--navbar-height))]","data-[open=true]:flex"]}}},defaultVariants:{maxWidth:"lg",position:"sticky",isBlurred:!0}}),MS=wt({base:"flex flex-col gap-2 items-start"}),LS=wt({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-none","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","transform-gpu data-[pressed=true]:scale-[0.97]",...ml],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:A.solid.default},{variant:"solid",color:"primary",class:A.solid.primary},{variant:"solid",color:"secondary",class:A.solid.secondary},{variant:"solid",color:"success",class:A.solid.success},{variant:"solid",color:"warning",class:A.solid.warning},{variant:"solid",color:"danger",class:A.solid.danger},{variant:"shadow",color:"default",class:A.shadow.default},{variant:"shadow",color:"primary",class:A.shadow.primary},{variant:"shadow",color:"secondary",class:A.shadow.secondary},{variant:"shadow",color:"success",class:A.shadow.success},{variant:"shadow",color:"warning",class:A.shadow.warning},{variant:"shadow",color:"danger",class:A.shadow.danger},{variant:"bordered",color:"default",class:A.bordered.default},{variant:"bordered",color:"primary",class:A.bordered.primary},{variant:"bordered",color:"secondary",class:A.bordered.secondary},{variant:"bordered",color:"success",class:A.bordered.success},{variant:"bordered",color:"warning",class:A.bordered.warning},{variant:"bordered",color:"danger",class:A.bordered.danger},{variant:"flat",color:"default",class:A.flat.default},{variant:"flat",color:"primary",class:A.flat.primary},{variant:"flat",color:"secondary",class:A.flat.secondary},{variant:"flat",color:"success",class:A.flat.success},{variant:"flat",color:"warning",class:A.flat.warning},{variant:"flat",color:"danger",class:A.flat.danger},{variant:"faded",color:"default",class:A.faded.default},{variant:"faded",color:"primary",class:A.faded.primary},{variant:"faded",color:"secondary",class:A.faded.secondary},{variant:"faded",color:"success",class:A.faded.success},{variant:"faded",color:"warning",class:A.faded.warning},{variant:"faded",color:"danger",class:A.faded.danger},{variant:"light",color:"default",class:[A.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[A.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[A.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[A.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[A.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[A.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[A.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[A.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[A.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[A.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[A.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[A.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:fr.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:fr.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:fr.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:fr.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:fr.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:fr.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});wt({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}});var fp=wt({base:["px-2","py-1","h-fit","font-mono","font-normal","inline-block","whitespace-nowrap"],variants:{color:{default:A.flat.default,primary:A.flat.primary,secondary:A.flat.secondary,success:A.flat.success,warning:A.flat.warning,danger:A.flat.danger},size:{sm:"text-small",md:"text-medium",lg:"text-large"},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"}},defaultVariants:{color:"default",size:"sm",radius:"sm"}});const IS="modulepreload",RS=function(e){return"/"+e},pp={},fi=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));o=Promise.all(n.map(i=>{if(i=RS(i),i in pp)return;pp[i]=!0;const s=i.endsWith(".css"),u=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${u}`))return;const c=document.createElement("link");if(c.rel=s?"stylesheet":IS,s||(c.as="script",c.crossOrigin=""),c.href=i,a&&c.setAttribute("nonce",a),document.head.appendChild(c),s)return new Promise((d,f)=>{c.addEventListener("load",d),c.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${i}`)))})}))}return o.then(()=>t()).catch(l=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l})};function jS(e,t){let{elementType:n="button",isDisabled:r,onPress:o,onPressStart:l,onPressEnd:a,onPressUp:i,onPressChange:s,preventFocusOnPress:u,allowFocusWhenDisabled:c,onClick:d,href:f,target:v,rel:y,type:b="button"}=e,S;n==="button"?S={type:b,disabled:r}:S={role:"button",href:n==="a"&&!r?f:void 0,target:n==="a"?v:void 0,type:n==="input"?b:void 0,disabled:n==="input"?r:void 0,"aria-disabled":!r||n==="input"?void 0:r,rel:n==="a"?y:void 0};let{pressProps:h,isPressed:m}=Ur({onPressStart:l,onPressEnd:a,onPressChange:s,onPress:o,onPressUp:i,onClick:d,isDisabled:r,preventFocusOnPress:u,ref:t}),{focusableProps:g}=Jr(e,t);c&&(g.tabIndex=r?-1:g.tabIndex);let x=Z(g,h,Zr(e,{labelable:!0}));return{isPressed:m,buttonProps:Z(S,x,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}function zS(e,t,n){const{isSelected:r}=t,{isPressed:o,buttonProps:l}=jS({...e,onPress:At(t.toggle,e.onPress)},n);return{isPressed:o,isSelected:r,isDisabled:e.isDisabled||!1,buttonProps:Z(l,{"aria-pressed":r})}}var OS=e=>w.jsx("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,viewBox:"0 0 24 24",width:"1em",...e,children:w.jsx("polyline",{points:"20 6 9 17 4 12"})}),FS=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M16 17.1c0 3.5-1.4 4.9-4.9 4.9H6.9C3.4 22 2 20.6 2 17.1v-4.2C2 9.4 3.4 8 6.9 8h4.2c3.5 0 4.9 1.4 4.9 4.9Z"}),w.jsx("path",{d:"M8 8V6.9C8 3.4 9.4 2 12.9 2h4.2C20.6 2 22 3.4 22 6.9v4.2c0 3.5-1.4 4.9-4.9 4.9H16"}),w.jsx("path",{d:"M16 12.9C16 9.4 14.6 8 11.1 8"})]}),AS=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",shapeRendering:"geometricPrecision",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"}),w.jsx("path",{d:"M15 3h6v6"}),w.jsx("path",{d:"M10 14L21 3"})]}),DS=e=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:w.jsx("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})}),mp={easeIn:[.4,0,1,1],easeOut:[0,0,.2,1]},WS={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}}},VS=e=>{const t={top:{originY:1},bottom:{originY:0},left:{originX:1},right:{originX:0},"top-start":{originX:0,originY:1},"top-end":{originX:1,originY:1},"bottom-start":{originX:0,originY:0},"bottom-end":{originX:1,originY:0},"right-start":{originX:0,originY:0},"right-end":{originX:0,originY:1},"left-start":{originX:1,originY:0},"left-end":{originX:1,originY:1}};return(t==null?void 0:t[e])||{}},BS=e=>({top:"top",bottom:"bottom",left:"left",right:"right","top-start":"top start","top-end":"top end","bottom-start":"bottom start","bottom-end":"bottom end","left-start":"left top","left-end":"left bottom","right-start":"right top","right-end":"right bottom"})[e],hp=(e,t)=>{if(t.includes("-")){const[,n]=t.split("-");return`${e}-${n}`}return e},Dc=globalThis!=null&&globalThis.document?p.useLayoutEffect:p.useEffect,[$E,HS]=bc({name:"ButtonGroupContext",strict:!1});function US(e,t){let{elementType:n="button",isDisabled:r,onPress:o,onPressStart:l,onPressEnd:a,onPressChange:i,preventFocusOnPress:s,allowFocusWhenDisabled:u,onClick:c,href:d,target:f,rel:v,type:y="button",allowTextSelectionOnPress:b}=e,S;n==="button"?S={type:y,disabled:r}:S={role:"button",href:n==="a"&&!r?d:void 0,target:n==="a"?f:void 0,type:n==="input"?y:void 0,disabled:n==="input"?r:void 0,"aria-disabled":!r||n==="input"?void 0:r,rel:n==="a"?v:void 0};let{pressProps:h,isPressed:m}=Ur({onClick:c,onPressStart:l,onPressEnd:a,onPressChange:i,onPress:o,isDisabled:r,preventFocusOnPress:s,allowTextSelectionOnPress:b,ref:t}),{focusableProps:g}=Jr(e,t);u&&(g.tabIndex=r?-1:g.tabIndex);let x=Z(g,h,Zr(e,{labelable:!0}));return{isPressed:m,buttonProps:Z(S,x,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}var KS=()=>fi(()=>import("./index-DdjR5iIE.js"),__vite__mapDeps([])).then(e=>e.default),vg=e=>{const{ripples:t=[],motionProps:n,color:r="currentColor",style:o,onClear:l}=e;return w.jsx(w.Fragment,{children:t.map(a=>{const i=Nb(.01*a.size,.2,a.size>100?.75:.5);return w.jsx(di,{features:KS,children:w.jsx(Oc,{mode:"popLayout",children:w.jsx(ci.span,{animate:{transform:"scale(2)",opacity:0},className:"heroui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:r,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:a.y,left:a.x,width:`${a.size}px`,height:`${a.size}px`,...o},transition:{duration:i},onAnimationComplete:()=>{l(a.key)},...n})})},a.key)})})};vg.displayName="HeroUI.Ripple";var GS=vg;function QS(e={}){const[t,n]=p.useState([]),r=p.useCallback(l=>{const a=l.target,i=Math.max(a.clientWidth,a.clientHeight);n(s=>[...s,{key:Tb(s.length.toString()),size:i,x:l.x-i/2,y:l.y-i/2}])},[]),o=p.useCallback(l=>{n(a=>a.filter(i=>i.key!==l))},[]);return{ripples:t,onClear:o,onPress:r,...e}}function YS(e){var t,n,r,o,l,a,i,s,u;const c=HS(),d=rn(),f=!!c,{ref:v,as:y,children:b,startContent:S,endContent:h,autoFocus:m,className:g,spinner:x,isLoading:E=!1,disableRipple:$=!1,fullWidth:_=(t=c==null?void 0:c.fullWidth)!=null?t:!1,radius:M=c==null?void 0:c.radius,size:O=(n=c==null?void 0:c.size)!=null?n:"md",color:I=(r=c==null?void 0:c.color)!=null?r:"default",variant:C=(o=c==null?void 0:c.variant)!=null?o:"solid",disableAnimation:P=(a=(l=c==null?void 0:c.disableAnimation)!=null?l:d==null?void 0:d.disableAnimation)!=null?a:!1,isDisabled:z=(i=c==null?void 0:c.isDisabled)!=null?i:!1,isIconOnly:k=(s=c==null?void 0:c.isIconOnly)!=null?s:!1,spinnerPlacement:D="start",onPress:N,onClick:F,...T}=e,R=y||"button",L=typeof R=="string",V=Ze(v),H=(u=$||(d==null?void 0:d.disableRipple))!=null?u:P,{isFocusVisible:te,isFocused:K,focusProps:re}=or({autoFocus:m}),G=z||E,we=p.useMemo(()=>LS({size:O,color:I,variant:C,radius:M,fullWidth:_,isDisabled:G,isInGroup:f,disableAnimation:P,isIconOnly:k,className:g}),[O,I,C,M,_,G,f,k,P,g]),{onPress:ut,onClear:xe,ripples:Me}=QS(),nt=p.useCallback(dt=>{H||G||P||V.current&&ut(dt)},[H,G,P,V,ut]),{buttonProps:De,isPressed:Ke}=US({elementType:y,isDisabled:G,onPress:At(N,nt),onClick:F,...T},V),{isHovered:xt,hoverProps:ct}=rr({isDisabled:G}),U=p.useCallback((dt={})=>({"data-disabled":B(G),"data-focus":B(K),"data-pressed":B(Ke),"data-focus-visible":B(te),"data-hover":B(xt),"data-loading":B(E),...Z(De,re,ct,Ma(T,{enabled:L}),Ma(dt)),className:we}),[E,G,K,Ke,L,te,xt,De,re,ct,T,we]),Wt=dt=>p.isValidElement(dt)?p.cloneElement(dt,{"aria-hidden":!0,focusable:!1}):null,ln=Wt(S),pi=Wt(h),mi=p.useMemo(()=>({sm:"sm",md:"sm",lg:"md"})[O],[O]),vl=p.useCallback(()=>({ripples:Me,onClear:xe}),[Me,xe]);return{Component:R,children:b,domRef:V,spinner:x,styles:we,startContent:ln,endContent:pi,isLoading:E,spinnerPlacement:D,spinnerSize:mi,disableRipple:H,getButtonProps:U,getRippleProps:vl,isIconOnly:k}}function XS(e){var t,n;const[r,o]=on(e,lp.variantKeys),l=rn(),a=(n=(t=e==null?void 0:e.variant)!=null?t:l==null?void 0:l.spinnerVariant)!=null?n:"default",{children:i,className:s,classNames:u,label:c,...d}=r,f=p.useMemo(()=>lp({...o}),[Dt(o)]),v=ee(u==null?void 0:u.base,s),y=c||i,b=p.useMemo(()=>y&&typeof y=="string"?y:d["aria-label"]?"":"Loading",[i,y,d["aria-label"]]),S=p.useCallback(()=>({"aria-label":b,className:f.base({class:v}),...d}),[b,f,v,d]);return{label:y,slots:f,classNames:u,variant:a,getSpinnerProps:S}}var gg=Ue((e,t)=>{const{slots:n,classNames:r,label:o,variant:l,getSpinnerProps:a}=XS({...e});return l==="wave"||l==="dots"?w.jsxs("div",{ref:t,...a(),children:[w.jsx("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[...new Array(3)].map((i,s)=>w.jsx("i",{className:n.dots({class:r==null?void 0:r.dots}),style:{"--dot-index":s}},`dot-${s}`))}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):l==="simple"?w.jsxs("div",{ref:t,...a(),children:[w.jsxs("svg",{className:n.wrapper({class:r==null?void 0:r.wrapper}),fill:"none",viewBox:"0 0 24 24",children:[w.jsx("circle",{className:n.circle1({class:r==null?void 0:r.circle1}),cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),w.jsx("path",{className:n.circle2({class:r==null?void 0:r.circle2}),d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",fill:"currentColor"})]}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):l==="spinner"?w.jsxs("div",{ref:t,...a(),children:[w.jsx("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[...new Array(12)].map((i,s)=>w.jsx("i",{className:n.spinnerBars({class:r==null?void 0:r.spinnerBars}),style:{"--bar-index":s}},`star-${s}`))}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]}):w.jsxs("div",{ref:t,...a(),children:[w.jsxs("div",{className:n.wrapper({class:r==null?void 0:r.wrapper}),children:[w.jsx("i",{className:n.circle1({class:r==null?void 0:r.circle1})}),w.jsx("i",{className:n.circle2({class:r==null?void 0:r.circle2})})]}),o&&w.jsx("span",{className:n.label({class:r==null?void 0:r.label}),children:o})]})});gg.displayName="HeroUI.Spinner";var ZS=gg,yg=Ue((e,t)=>{const{Component:n,domRef:r,children:o,spinnerSize:l,spinner:a=w.jsx(ZS,{color:"current",size:l}),spinnerPlacement:i,startContent:s,endContent:u,isLoading:c,disableRipple:d,getButtonProps:f,getRippleProps:v,isIconOnly:y}=YS({...e,ref:t});return w.jsxs(n,{ref:r,...f(),children:[s,c&&i==="start"&&a,c&&y?null:o,c&&i==="end"&&a,u,!d&&w.jsx(GS,{...v()})]})});yg.displayName="HeroUI.Button";var Fa=yg;const bg={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},wg={...bg,customError:!0,valid:!1},go={isInvalid:!1,validationDetails:bg,validationErrors:[]},xg=p.createContext({}),vp="__formValidationState"+Date.now();function JS(e){if(e[vp]){let{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}=e[vp];return{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}}return qS(e)}function qS(e){let{isInvalid:t,validationState:n,name:r,value:o,builtinValidation:l,validate:a,validationBehavior:i="aria"}=e;n&&(t||(t=n==="invalid"));let s=t!==void 0?{isInvalid:t,validationErrors:[],validationDetails:wg}:null,u=p.useMemo(()=>{if(!a||o==null)return null;let I=eC(a,o);return gp(I)},[a,o]);l!=null&&l.validationDetails.valid&&(l=void 0);let c=p.useContext(xg),d=p.useMemo(()=>r?Array.isArray(r)?r.flatMap(I=>wu(c[I])):wu(c[r]):[],[c,r]),[f,v]=p.useState(c),[y,b]=p.useState(!1);c!==f&&(v(c),b(!1));let S=p.useMemo(()=>gp(y?[]:d),[y,d]),h=p.useRef(go),[m,g]=p.useState(go),x=p.useRef(go),E=()=>{if(!$)return;_(!1);let I=u||l||h.current;ts(I,x.current)||(x.current=I,g(I))},[$,_]=p.useState(!1);return p.useEffect(E),{realtimeValidation:s||S||u||l||go,displayValidation:i==="native"?s||S||m:s||S||u||l||m,updateValidation(I){i==="aria"&&!ts(m,I)?g(I):h.current=I},resetValidation(){let I=go;ts(I,x.current)||(x.current=I,g(I)),i==="native"&&_(!1),b(!0)},commitValidation(){i==="native"&&_(!0),b(!0)}}}function wu(e){return e?Array.isArray(e)?e:[e]:[]}function eC(e,t){if(typeof e=="function"){let n=e(t);if(n&&typeof n!="boolean")return wu(n)}return[]}function gp(e){return e.length?{isInvalid:!0,validationErrors:e,validationDetails:wg}:null}function ts(e,t){return e===t?!0:!!e&&!!t&&e.isInvalid===t.isInvalid&&e.validationErrors.length===t.validationErrors.length&&e.validationErrors.every((n,r)=>n===t.validationErrors[r])&&Object.entries(e.validationDetails).every(([n,r])=>t.validationDetails[n]===r)}function tC(e,t,n){let{validationBehavior:r,focus:o}=e;be(()=>{if(r==="native"&&(n!=null&&n.current)&&!n.current.disabled){let s=t.realtimeValidation.isInvalid?t.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";n.current.setCustomValidity(s),n.current.hasAttribute("title")||(n.current.title=""),t.realtimeValidation.isInvalid||t.updateValidation(rC(n.current))}});let l=Oe(()=>{t.resetValidation()}),a=Oe(s=>{var u;t.displayValidation.isInvalid||t.commitValidation();let c=n==null||(u=n.current)===null||u===void 0?void 0:u.form;if(!s.defaultPrevented&&n&&c&&oC(c)===n.current){var d;o?o():(d=n.current)===null||d===void 0||d.focus(),Jw("keyboard")}s.preventDefault()}),i=Oe(()=>{t.commitValidation()});p.useEffect(()=>{let s=n==null?void 0:n.current;if(!s)return;let u=s.form;return s.addEventListener("invalid",a),s.addEventListener("change",i),u==null||u.addEventListener("reset",l),()=>{s.removeEventListener("invalid",a),s.removeEventListener("change",i),u==null||u.removeEventListener("reset",l)}},[n,a,i,l,r])}function nC(e){let t=e.validity;return{badInput:t.badInput,customError:t.customError,patternMismatch:t.patternMismatch,rangeOverflow:t.rangeOverflow,rangeUnderflow:t.rangeUnderflow,stepMismatch:t.stepMismatch,tooLong:t.tooLong,tooShort:t.tooShort,typeMismatch:t.typeMismatch,valueMissing:t.valueMissing,valid:t.valid}}function rC(e){return{isInvalid:!e.validity.valid,validationDetails:nC(e),validationErrors:e.validationMessage?[e.validationMessage]:[]}}function oC(e){for(let t=0;t<e.elements.length;t++){let n=e.elements[t];if(!n.validity.valid)return n}return null}function lC(e,t,n){let{isDisabled:r=!1,isReadOnly:o=!1,value:l,name:a,children:i,"aria-label":s,"aria-labelledby":u,validationState:c="valid",isInvalid:d}=e,f=x=>{x.stopPropagation(),t.setSelected(x.target.checked)},{pressProps:v,isPressed:y}=Ur({isDisabled:r}),{pressProps:b,isPressed:S}=Ur({onPress(){var x;t.toggle(),(x=n.current)===null||x===void 0||x.focus()},isDisabled:r||o}),{focusableProps:h}=Jr(e,n),m=Z(v,h),g=Zr(e,{labelable:!0});return yv(n,t.isSelected,t.setSelected),{labelProps:Z(b,{onClick:x=>x.preventDefault()}),inputProps:Z(g,{"aria-invalid":d||c==="invalid"||void 0,"aria-errormessage":e["aria-errormessage"],"aria-controls":e["aria-controls"],"aria-readonly":o||void 0,onChange:f,disabled:r,...l==null?{}:{value:l},name:a,type:"checkbox",...m}),isSelected:t.isSelected,isPressed:y||S,isDisabled:r,isReadOnly:o,isInvalid:d||c==="invalid"}}function aC(e){let{id:t,label:n,"aria-labelledby":r,"aria-label":o,labelElementType:l="label"}=e;t=nl(t);let a=nl(),i={};n&&(r=r?`${a} ${r}`:a,i={id:a,htmlFor:l==="label"?t:void 0});let s=ww({id:t,"aria-label":o,"aria-labelledby":r});return{labelProps:i,fieldProps:s}}function iC(e){let{description:t,errorMessage:n,isInvalid:r,validationState:o}=e,{labelProps:l,fieldProps:a}=aC(e),i=yf([!!t,!!n,r,o]),s=yf([!!t,!!n,r,o]);return a=Z(a,{"aria-describedby":[i,s,e["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:l,fieldProps:a,descriptionProps:{id:i},errorMessageProps:{id:s}}}function Sg(e={}){let{isReadOnly:t}=e,[n,r]=fl(e.isSelected,e.defaultSelected||!1,e.onChange);function o(a){t||r(a)}function l(){t||r(!n)}return{isSelected:n,setSelected:o,toggle:l}}var yp=Symbol("default");function Cg(e,t){let n=p.useContext(e);if(t===null)return null;if(n&&typeof n=="object"&&"slots"in n&&n.slots){let r=new Intl.ListFormat().format(Object.keys(n.slots).map(l=>`"${l}"`));if(!t&&!n.slots[yp])throw new Error(`A slot prop is required. Valid slot names are ${r}.`);let o=t||yp;if(!n.slots[o])throw new Error(`Invalid slot "${t}". Valid slot names are ${r}.`);return n.slots[o]}return n}function sC(e,t,n){let r=Cg(n,e.slot)||{},{ref:o,...l}=r,a=xw(p.useMemo(()=>aw(t,o),[t,o])),i=Z(l,e);return"style"in l&&l.style&&"style"in e&&e.style&&(typeof l.style=="function"||typeof e.style=="function"?i.style=s=>{let u=typeof l.style=="function"?l.style(s):l.style,c={...s.defaultStyle,...u},d=typeof e.style=="function"?e.style({...s,defaultStyle:c}):e.style;return{...c,...d}}:i.style={...l.style,...e.style}),[i,a]}var xu=p.createContext(null);p.forwardRef(function(t,n){[t,n]=sC(t,n,xu);let{validationErrors:r,validationBehavior:o="native",children:l,className:a,...i}=t;const s=p.useMemo(()=>MS({className:a}),[a]);return w.jsx("form",{noValidate:o!=="native",...i,ref:n,className:s,children:w.jsx(xu.Provider,{value:{...t,validationBehavior:o},children:w.jsx(xg.Provider,{value:r??{},children:l})})})});function uC(e){const[t,n]=on(e,fp.variantKeys),{as:r,children:o,className:l,...a}=t,i=r||"code",s=p.useMemo(()=>fp({...n,className:l}),[Dt(n),l]);return{Component:i,children:o,getCodeProps:()=>({className:s,...a})}}var Eg=Ue((e,t)=>{const{Component:n,children:r,getCodeProps:o}=uC({...e});return w.jsx(n,{ref:t,...o(),children:r})});Eg.displayName="HeroUI.Code";var cC=Eg;function dC(e,t){let{elementType:n="a",onPress:r,onPressStart:o,onPressEnd:l,onClick:a,isDisabled:i,...s}=e,u={};n!=="a"&&(u={role:"link",tabIndex:i?void 0:0});let{focusableProps:c}=Jr(e,t),{pressProps:d,isPressed:f}=Ur({onClick:a,onPress:r,onPressStart:o,onPressEnd:l,isDisabled:i,ref:t}),v=Zr(s,{labelable:!0,isLink:n==="a"}),y=Z(c,d),b=dv(),S=yw(e);return{isPressed:f,linkProps:Z(v,S,{...y,...u,"aria-disabled":i||void 0,"aria-current":e["aria-current"],onClick:h=>{var m;(m=d.onClick)==null||m.call(d,h),!b.isNative&&h.currentTarget instanceof HTMLAnchorElement&&h.currentTarget.href&&!h.isDefaultPrevented()&&fv(h.currentTarget,h)&&e.href&&(h.preventDefault(),b.open(h.currentTarget,h,e.href,e.routerOptions))}})}}function fC(e){var t,n,r,o;const l=rn(),[a,i]=on(e,cp.variantKeys),{ref:s,as:u,children:c,anchorIcon:d,isExternal:f=!1,showAnchorIcon:v=!1,autoFocus:y=!1,className:b,onPress:S,onPressStart:h,onPressEnd:m,onClick:g,...x}=a,E=u||"a",$=Ze(s),_=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:l==null?void 0:l.disableAnimation)!=null?n:!1,{linkProps:M}=dC({...x,onPress:S,onPressStart:h,onPressEnd:m,onClick:g,isDisabled:e.isDisabled,elementType:`${u}`},$),{isFocused:O,isFocusVisible:I,focusProps:C}=or({autoFocus:y});f&&(x.rel=(r=x.rel)!=null?r:"noopener noreferrer",x.target=(o=x.target)!=null?o:"_blank");const P=p.useMemo(()=>cp({...i,disableAnimation:_,className:b}),[Dt(i),_,b]),z=p.useCallback(()=>({ref:$,className:P,"data-focus":B(O),"data-disabled":B(e.isDisabled),"data-focus-visible":B(I),...Z(C,M,x)}),[P,O,I,C,M,x]);return{Component:E,children:c,anchorIcon:d,showAnchorIcon:v,getLinkProps:z}}var $g=Ue((e,t)=>{const{Component:n,children:r,showAnchorIcon:o,anchorIcon:l=w.jsx(AS,{className:_S}),getLinkProps:a}=fC({ref:t,...e});return w.jsx(n,{...a(),children:w.jsxs(w.Fragment,{children:[r,o&&l]})})});$g.displayName="HeroUI.Link";var Et=$g,pC=e=>e.replace(/[\u00A0]/g," ");function mC({timeout:e=2e3}={}){const[t,n]=p.useState(null),[r,o]=p.useState(!1),[l,a]=p.useState(null),i=p.useCallback(()=>{l&&clearTimeout(l)},[l]),s=p.useCallback(d=>{i(),a(setTimeout(()=>o(!1),e)),o(d)},[i,e]),u=p.useCallback(d=>{if("clipboard"in navigator){const f=typeof d=="string"?pC(d):d;navigator.clipboard.writeText(f).then(()=>s(!0)).catch(v=>n(v))}else n(new Error("useClipboard: navigator.clipboard is not supported"))},[s]),c=p.useCallback(()=>{o(!1),n(null),i()},[i]);return{copy:u,reset:c,error:t,copied:r}}function hC(e){var t,n,r,o;const l=rn(),[a,i]=on(e,es.variantKeys),{ref:s,as:u,children:c,symbol:d="$",classNames:f,timeout:v,copyIcon:y,checkIcon:b,codeString:S,disableCopy:h=!1,disableTooltip:m=!1,hideCopyButton:g=!1,autoFocus:x=!1,hideSymbol:E=!1,onCopy:$,tooltipProps:_={},copyButtonProps:M={},className:O,...I}=a,C=u||"div",P=typeof C=="string",z=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:l==null?void 0:l.disableAnimation)!=null?n:!1,k={offset:15,delay:1e3,content:"Copy to clipboard",color:(o=e==null?void 0:e.color)!=null?o:(r=es.defaultVariants)==null?void 0:r.color,isDisabled:a.disableCopy,..._},D=Ze(s),N=p.useRef(null),{copy:F,copied:T}=mC({timeout:v}),R=c&&Array.isArray(c),{isFocusVisible:L,isFocused:V,focusProps:H}=or({autoFocus:x}),te=p.useMemo(()=>es({...i,disableAnimation:z}),[Dt(i),z]),K=p.useMemo(()=>{if(!d||typeof d!="string")return d;const Me=d.trim();return Me?`${Me} `:""},[d]),re=ee(f==null?void 0:f.base,O),G=p.useCallback(()=>({className:te.base({class:re}),...Ma(I,{enabled:P})}),[te,re,R,I]),we=p.useCallback(()=>{var Me;if(h)return;let nt="";typeof c=="string"?nt=c:Array.isArray(c)&&c.forEach(Ke=>{var xt,ct;const U=typeof Ke=="string"?Ke:(ct=(xt=Ke==null?void 0:Ke.props)==null?void 0:xt.children)==null?void 0:ct.toString();U&&(nt+=U+`
`)});const De=S||nt||((Me=N.current)==null?void 0:Me.textContent)||"";F(De),$==null||$(De)},[F,S,h,$,c]),ut={"aria-label":typeof k.content=="string"?k.content:"Copy to clipboard",size:"sm",variant:"light",isDisabled:h,onPress:we,isIconOnly:!0,...M},xe=p.useCallback(()=>({...ut,"data-copied":B(T),className:te.copyButton({class:ee(f==null?void 0:f.copyButton)})}),[te,L,V,h,f==null?void 0:f.copyButton,ut,H]);return{Component:C,as:u,domRef:D,preRef:N,children:c,slots:te,classNames:f,copied:T,onCopy:we,copyIcon:y,checkIcon:b,symbolBefore:K,isMultiLine:R,isFocusVisible:L,hideCopyButton:g,disableCopy:h,disableTooltip:m,hideSymbol:E,tooltipProps:k,getSnippetProps:G,getCopyButtonProps:xe}}function vC(e){let[t,n]=fl(e.isOpen,e.defaultOpen||!1,e.onOpenChange);const r=p.useCallback(()=>{n(!0)},[n]),o=p.useCallback(()=>{n(!1)},[n]),l=p.useCallback(()=>{n(!t)},[n,t]);return{isOpen:t,setOpen:n,open:r,close:o,toggle:l}}const gC=1500,bp=500;let Fn={},yC=0,yo=!1,Ht=null,An=null;function bC(e={}){let{delay:t=gC,closeDelay:n=bp}=e,{isOpen:r,open:o,close:l}=vC(e),a=p.useMemo(()=>`${++yC}`,[]),i=p.useRef(null),s=p.useRef(l),u=()=>{Fn[a]=f},c=()=>{for(let y in Fn)y!==a&&(Fn[y](!0),delete Fn[y])},d=()=>{i.current&&clearTimeout(i.current),i.current=null,c(),u(),yo=!0,o(),Ht&&(clearTimeout(Ht),Ht=null),An&&(clearTimeout(An),An=null)},f=y=>{y||n<=0?(i.current&&clearTimeout(i.current),i.current=null,s.current()):i.current||(i.current=setTimeout(()=>{i.current=null,s.current()},n)),Ht&&(clearTimeout(Ht),Ht=null),yo&&(An&&clearTimeout(An),An=setTimeout(()=>{delete Fn[a],An=null,yo=!1},Math.max(bp,n)))},v=()=>{c(),u(),!r&&!Ht&&!yo?Ht=setTimeout(()=>{Ht=null,yo=!0,d()},t):r||d()};return p.useEffect(()=>{s.current=l},[l]),p.useEffect(()=>()=>{i.current&&clearTimeout(i.current),Fn[a]&&delete Fn[a]},[a]),{isOpen:r,open:y=>{!y&&t>0&&!i.current?v():d()},close:f}}function wC(e,t){let n=Zr(e,{labelable:!0}),{hoverProps:r}=rr({onHoverStart:()=>t==null?void 0:t.open(!0),onHoverEnd:()=>t==null?void 0:t.close()});return{tooltipProps:Z(n,r,{role:"tooltip"})}}function xC(e,t,n){let{isDisabled:r,trigger:o}=e,l=nl(),a=p.useRef(!1),i=p.useRef(!1),s=()=>{(a.current||i.current)&&t.open(i.current)},u=h=>{!a.current&&!i.current&&t.close(h)};p.useEffect(()=>{let h=m=>{n&&n.current&&m.key==="Escape"&&(m.stopPropagation(),t.close(!0))};if(t.isOpen)return document.addEventListener("keydown",h,!0),()=>{document.removeEventListener("keydown",h,!0)}},[n,t]);let c=()=>{o!=="focus"&&(_c()==="pointer"?a.current=!0:a.current=!1,s())},d=()=>{o!=="focus"&&(i.current=!1,a.current=!1,u())},f=()=>{i.current=!1,a.current=!1,u(!0)},v=()=>{Nc()&&(i.current=!0,s())},y=()=>{i.current=!1,a.current=!1,u(!0)},{hoverProps:b}=rr({isDisabled:r,onHoverStart:c,onHoverEnd:d}),{focusableProps:S}=Jr({isDisabled:r,onFocus:v,onBlur:y},n);return{triggerProps:{"aria-describedby":t.isOpen?l:void 0,...Z(S,b,{onPointerDown:f,onKeyDown:f,tabIndex:void 0})},tooltipProps:{id:l}}}function SC(e){var t,n;const r=rn(),[o,l]=on(e,ip.variantKeys),{ref:a,as:i,isOpen:s,content:u,children:c,defaultOpen:d,onOpenChange:f,isDisabled:v,trigger:y,shouldFlip:b=!0,containerPadding:S=12,placement:h="top",delay:m=0,closeDelay:g=500,showArrow:x=!1,offset:E=7,crossOffset:$=0,isDismissable:_,shouldCloseOnBlur:M=!0,portalContainer:O,isKeyboardDismissDisabled:I=!1,updatePositionDeps:C=[],shouldCloseOnInteractOutside:P,className:z,onClose:k,motionProps:D,classNames:N,...F}=o,T=i||"div",R=(n=(t=e==null?void 0:e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,L=bC({delay:m,closeDelay:g,isDisabled:v,defaultOpen:d,isOpen:s,onOpenChange:U=>{f==null||f(U),U||k==null||k()}}),V=p.useRef(null),H=p.useRef(null),te=p.useId(),K=L.isOpen&&!v;p.useImperativeHandle(a,()=>Sb(H));const{triggerProps:re,tooltipProps:G}=xC({isDisabled:v,trigger:y},L,V),{tooltipProps:we}=wC({isOpen:K,...Z(o,G)},L),{overlayProps:ut,placement:xe,updatePosition:Me}=zw({isOpen:K,targetRef:V,placement:BS(h),overlayRef:H,offset:x?E+3:E,crossOffset:$,shouldFlip:b,containerPadding:S});Dc(()=>{C.length&&Me()},C);const{overlayProps:nt}=vx({isOpen:K,onClose:L.close,isDismissable:_,shouldCloseOnBlur:M,isKeyboardDismissDisabled:I,shouldCloseOnInteractOutside:P},H),De=p.useMemo(()=>{var U,Wt,ln;return ip({...l,disableAnimation:R,radius:(U=e==null?void 0:e.radius)!=null?U:"md",size:(Wt=e==null?void 0:e.size)!=null?Wt:"md",shadow:(ln=e==null?void 0:e.shadow)!=null?ln:"sm"})},[Dt(l),R,e==null?void 0:e.radius,e==null?void 0:e.size,e==null?void 0:e.shadow]),Ke=p.useCallback((U={},Wt=null)=>({...Z(re,U),ref:qh(Wt,V),"aria-describedby":K?te:void 0}),[re,K,te,L]),xt=p.useCallback(()=>({ref:H,"data-slot":"base","data-open":B(K),"data-arrow":B(x),"data-disabled":B(v),"data-placement":hp(xe||"top",h),...Z(we,nt,F),style:Z(ut.style,F.style,o.style),className:De.base({class:N==null?void 0:N.base}),id:te}),[De,K,x,v,xe,h,we,nt,F,ut,o,te]),ct=p.useCallback(()=>({"data-slot":"content","data-open":B(K),"data-arrow":B(x),"data-disabled":B(v),"data-placement":hp(xe||"top",h),className:De.content({class:ee(N==null?void 0:N.content,z)})}),[De,K,x,v,xe,h,N]);return{Component:T,content:u,children:c,isOpen:K,triggerRef:V,showArrow:x,portalContainer:O,placement:h,disableAnimation:R,isDisabled:v,motionProps:D,getTooltipContentProps:ct,getTriggerProps:Ke,getTooltipProps:xt}}var CC=()=>fi(()=>import("./index-DdjR5iIE.js"),__vite__mapDeps([])).then(e=>e.default),kg=Ue((e,t)=>{var n;const{Component:r,children:o,content:l,isOpen:a,portalContainer:i,placement:s,disableAnimation:u,motionProps:c,getTriggerProps:d,getTooltipProps:f,getTooltipContentProps:v}=SC({...e,ref:t});let y;try{if(p.Children.count(o)!==1)throw new Error;if(!p.isValidElement(o))y=w.jsx("p",{...d(),children:o});else{const E=o,$=(n=E.props.ref)!=null?n:E.ref;y=p.cloneElement(E,d(E.props,$))}}catch{y=w.jsx("span",{}),_b("Tooltip must have only one child node. Please, check your code.")}const{ref:b,id:S,style:h,...m}=f(),g=w.jsx("div",{ref:b,id:S,style:h,children:w.jsx(ci.div,{animate:"enter",exit:"exit",initial:"exit",variants:WS.scaleSpring,...Z(c,m),style:{...VS(s)},children:w.jsx(r,{...v(),children:l})},`${S}-tooltip-inner`)},`${S}-tooltip-content`);return w.jsxs(w.Fragment,{children:[y,u?a&&w.jsx(Wf,{portalContainer:i,children:w.jsx("div",{ref:b,id:S,style:h,...m,children:w.jsx(r,{...v(),children:l})})}):w.jsx(di,{features:CC,children:w.jsx(Oc,{children:a&&w.jsx(Wf,{portalContainer:i,children:g})})})]})});kg.displayName="HeroUI.Tooltip";var EC=kg,Pg=Ue((e,t)=>{const{Component:n,domRef:r,preRef:o,children:l,slots:a,classNames:i,copied:s,copyIcon:u=w.jsx(FS,{}),checkIcon:c=w.jsx(OS,{}),symbolBefore:d,disableCopy:f,disableTooltip:v,hideSymbol:y,hideCopyButton:b,tooltipProps:S,isMultiLine:h,onCopy:m,getSnippetProps:g,getCopyButtonProps:x}=hC({...e,ref:t}),E=p.useCallback(({children:M})=>w.jsx(EC,{...S,isDisabled:s||S.isDisabled,children:M}),[Dt(S)]),$=p.useMemo(()=>{if(b)return null;const M=c&&p.cloneElement(c,{className:a.checkIcon()}),O=u&&p.cloneElement(u,{className:a.copyIcon()}),I=w.jsxs(Fa,{...x(),children:[M,O]});return v?I:w.jsx(E,{children:I})},[a,i==null?void 0:i.copyButton,s,c,u,m,E,f,v,b]),_=p.useMemo(()=>h&&l&&Array.isArray(l)?w.jsx("div",{className:a.content({class:i==null?void 0:i.content}),children:l.map((M,O)=>w.jsxs("pre",{className:a.pre({class:i==null?void 0:i.pre}),children:[!y&&w.jsx("span",{className:a.symbol({class:i==null?void 0:i.symbol}),children:d}),M]},`${O}-${M}`))}):w.jsxs("pre",{ref:o,className:a.pre({class:i==null?void 0:i.pre}),children:[!y&&w.jsx("span",{className:a.symbol({class:i==null?void 0:i.symbol}),children:d}),l]}),[l,y,h,d,i==null?void 0:i.pre,a]);return w.jsxs(n,{ref:r,...g(),children:[_,$]})});Pg.displayName="HeroUI.Snippet";var $C=Pg;function kC(e,t,n){let{labelProps:r,inputProps:o,isSelected:l,isPressed:a,isDisabled:i,isReadOnly:s}=lC(e,t,n);return{labelProps:r,inputProps:{...o,role:"switch",checked:l},isSelected:l,isPressed:a,isDisabled:i,isReadOnly:s}}function PC(e={}){var t,n;const r=rn(),[o,l]=on(e,ap.variantKeys),{ref:a,as:i,name:s,value:u="",isReadOnly:c=!1,autoFocus:d=!1,startContent:f,endContent:v,defaultSelected:y,isSelected:b,children:S,thumbIcon:h,className:m,classNames:g,onChange:x,onValueChange:E,...$}=o,_=i||"label",M=p.useRef(null),O=p.useRef(null),I=(n=(t=e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,C=p.useId(),P=p.useMemo(()=>{const U=$["aria-label"]||typeof S=="string"?S:void 0;return{name:s,value:u,children:S,autoFocus:d,defaultSelected:y,isSelected:b,isDisabled:!!e.isDisabled,isReadOnly:c,"aria-label":U,"aria-labelledby":$["aria-labelledby"]||C,onChange:E}},[u,s,C,S,d,c,b,y,e.isDisabled,$["aria-label"],$["aria-labelledby"],E]),z=Sg(P);Dc(()=>{if(!O.current)return;const U=!!O.current.checked;z.setSelected(U)},[O.current]);const{inputProps:k,isPressed:D,isReadOnly:N}=kC(P,z,O),{focusProps:F,isFocused:T,isFocusVisible:R}=or({autoFocus:k.autoFocus}),{hoverProps:L,isHovered:V}=rr({isDisabled:k.disabled}),te=P.isDisabled||N?!1:D,K=k.checked,re=k.disabled,G=p.useMemo(()=>ap({...l,disableAnimation:I}),[Dt(l),I]),we=ee(g==null?void 0:g.base,m),ut=U=>({...Z(L,$,U),ref:M,className:G.base({class:ee(we,U==null?void 0:U.className)}),"data-disabled":B(re),"data-selected":B(K),"data-readonly":B(N),"data-focus":B(T),"data-focus-visible":B(R),"data-hover":B(V),"data-pressed":B(te)}),xe=p.useCallback((U={})=>({...U,"aria-hidden":!0,className:ee(G.wrapper({class:ee(g==null?void 0:g.wrapper,U==null?void 0:U.className)}))}),[G,g==null?void 0:g.wrapper]),Me=(U={})=>({...Z(k,F,U),ref:qh(O,a),id:k.id,className:G.hiddenInput({class:g==null?void 0:g.hiddenInput}),onChange:At(x,k.onChange)}),nt=p.useCallback((U={})=>({...U,className:G.thumb({class:ee(g==null?void 0:g.thumb,U==null?void 0:U.className)})}),[G,g==null?void 0:g.thumb]),De=p.useCallback((U={})=>({...U,id:C,className:G.label({class:ee(g==null?void 0:g.label,U==null?void 0:U.className)})}),[G,g==null?void 0:g.label,re,K]),Ke=p.useCallback((U={includeStateProps:!1})=>Z({width:"1em",height:"1em",className:G.thumbIcon({class:ee(g==null?void 0:g.thumbIcon)})},U.includeStateProps?{isSelected:K}:{}),[G,g==null?void 0:g.thumbIcon,K]),xt=p.useCallback((U={})=>({width:"1em",height:"1em",...U,className:G.startContent({class:ee(g==null?void 0:g.startContent,U==null?void 0:U.className)})}),[G,g==null?void 0:g.startContent,K]),ct=p.useCallback((U={})=>({width:"1em",height:"1em",...U,className:G.endContent({class:ee(g==null?void 0:g.endContent,U==null?void 0:U.className)})}),[G,g==null?void 0:g.endContent,K]);return{Component:_,slots:G,classNames:g,domRef:M,children:S,thumbIcon:h,startContent:f,endContent:v,isHovered:V,isSelected:K,isPressed:te,isFocused:T,isFocusVisible:R,isDisabled:re,getBaseProps:ut,getWrapperProps:xe,getInputProps:Me,getLabelProps:De,getThumbProps:nt,getThumbIconProps:Ke,getStartContentProps:xt,getEndContentProps:ct}}function TC(e,t){let{inputElementType:n="input",isDisabled:r=!1,isRequired:o=!1,isReadOnly:l=!1,type:a="text",validationBehavior:i="aria"}=e,[s,u]=fl(e.value,e.defaultValue||"",e.onChange),{focusableProps:c}=Jr(e,t),d=JS({...e,value:s}),{isInvalid:f,validationErrors:v,validationDetails:y}=d.displayValidation,{labelProps:b,fieldProps:S,descriptionProps:h,errorMessageProps:m}=iC({...e,isInvalid:f,errorMessage:e.errorMessage||v}),g=Zr(e,{labelable:!0});const x={type:a,pattern:e.pattern};return yv(t,s,u),tC(e,d,t),p.useEffect(()=>{if(t.current instanceof ht(t.current).HTMLTextAreaElement){let E=t.current;Object.defineProperty(E,"defaultValue",{get:()=>E.value,set:()=>{},configurable:!0})}},[t]),{labelProps:b,inputProps:Z(g,n==="input"?x:void 0,{disabled:r,readOnly:l,required:o&&i==="native","aria-required":o&&i==="aria"||void 0,"aria-invalid":f||void 0,"aria-errormessage":e["aria-errormessage"],"aria-activedescendant":e["aria-activedescendant"],"aria-autocomplete":e["aria-autocomplete"],"aria-haspopup":e["aria-haspopup"],"aria-controls":e["aria-controls"],value:s,onChange:E=>u(E.target.value),autoComplete:e.autoComplete,autoCapitalize:e.autoCapitalize,maxLength:e.maxLength,minLength:e.minLength,name:e.name,placeholder:e.placeholder,inputMode:e.inputMode,autoCorrect:e.autoCorrect,spellCheck:e.spellCheck,[parseInt(q.version,10)>=17?"enterKeyHint":"enterkeyhint"]:e.enterKeyHint,onCopy:e.onCopy,onCut:e.onCut,onPaste:e.onPaste,onCompositionEnd:e.onCompositionEnd,onCompositionStart:e.onCompositionStart,onCompositionUpdate:e.onCompositionUpdate,onSelect:e.onSelect,onBeforeInput:e.onBeforeInput,onInput:e.onInput,...c,...S}),descriptionProps:h,errorMessageProps:m,isInvalid:f,validationErrors:v,validationDetails:y}}function NC(e){var t,n,r,o,l,a,i;const s=rn(),{validationBehavior:u}=Cg(xu)||{},[c,d]=on(e,sp.variantKeys),{ref:f,as:v,type:y,label:b,baseRef:S,wrapperRef:h,description:m,className:g,classNames:x,autoFocus:E,startContent:$,endContent:_,onClear:M,onChange:O,validationState:I,validationBehavior:C=(t=u??(s==null?void 0:s.validationBehavior))!=null?t:"native",innerWrapperRef:P,onValueChange:z=()=>{},...k}=c,D=p.useCallback(Y=>{z(Y??"")},[z]),[N,F]=p.useState(!1),T=v||"div",R=(r=(n=e.disableAnimation)!=null?n:s==null?void 0:s.disableAnimation)!=null?r:!1,L=Ze(f),V=Ze(S),H=Ze(h),te=Ze(P),[K,re]=fl(c.value,(o=c.defaultValue)!=null?o:"",D),G=y==="file",we=((i=(a=(l=L==null?void 0:L.current)==null?void 0:l.files)==null?void 0:a.length)!=null?i:0)>0,ut=["date","time","month","week","range"].includes(y),xe=!$b(K)||ut||we,Me=xe||N,nt=y==="hidden",De=e.isMultiline,Ke=ee(x==null?void 0:x.base,g,xe?"is-filled":""),xt=p.useCallback(()=>{var Y;G?L.current.value="":re(""),M==null||M(),(Y=L.current)==null||Y.focus()},[re,M,G]);Dc(()=>{L.current&&re(L.current.value)},[L.current]);const{labelProps:ct,inputProps:U,isInvalid:Wt,validationErrors:ln,validationDetails:pi,descriptionProps:mi,errorMessageProps:vl}=TC({...e,validationBehavior:C,autoCapitalize:e.autoCapitalize,value:K,"aria-label":Pb(e["aria-label"],e.label,e.placeholder),inputElementType:De?"textarea":"input",onChange:re},L);G&&(delete U.value,delete U.onChange);const{isFocusVisible:dt,isFocused:gl,focusProps:Vc}=or({autoFocus:E,isTextInput:!0}),{isHovered:yl,hoverProps:Ag}=rr({isDisabled:!!(e!=null&&e.isDisabled)}),{isHovered:to,hoverProps:Dg}=rr({isDisabled:!!(e!=null&&e.isDisabled)}),{focusProps:Bc,isFocusVisible:Hc}=or(),{focusWithinProps:Uc}=ai({onFocusWithinChange:F}),{pressProps:Kc}=Ur({isDisabled:!!(e!=null&&e.isDisabled)||!!(e!=null&&e.isReadOnly),onPress:xt}),cr=I==="invalid"||Wt,_t=A2({labelPlacement:e.labelPlacement,label:b}),hi=typeof c.errorMessage=="function"?c.errorMessage({isInvalid:cr,validationErrors:ln,validationDetails:pi}):c.errorMessage||(ln==null?void 0:ln.join(" ")),no=!!M||e.isClearable,Gc=!!b||!!m||!!hi,jn=!!c.placeholder,Qc=!!b,vi=!!m||!!hi,Yc=_t==="outside"||_t==="outside-left",Wg=_t==="inside",bl=L.current?(!L.current.value||L.current.value===""||!K||K==="")&&jn:!1,Vg=_t==="outside-left",Vt=!!$,Bg=Yc?_t==="outside-left"||jn||_t==="outside"&&Vt:!1,Hg=_t==="outside"&&!jn&&!Vt,Se=p.useMemo(()=>sp({...d,isInvalid:cr,labelPlacement:_t,isClearable:no,disableAnimation:R}),[Dt(d),cr,_t,no,Vt,R]),Ug=p.useCallback((Y={})=>({ref:V,className:Se.base({class:Ke}),"data-slot":"base","data-filled":B(xe||jn||Vt||bl||G),"data-filled-within":B(Me||jn||Vt||bl||G),"data-focus-within":B(N),"data-focus-visible":B(dt),"data-readonly":B(e.isReadOnly),"data-focus":B(gl),"data-hover":B(yl||to),"data-required":B(e.isRequired),"data-invalid":B(cr),"data-disabled":B(e.isDisabled),"data-has-elements":B(Gc),"data-has-helper":B(vi),"data-has-label":B(Qc),"data-has-value":B(!bl),"data-hidden":B(nt),...Uc,...Y}),[Se,Ke,xe,gl,yl,to,cr,vi,Qc,Gc,bl,Vt,N,dt,Me,jn,Uc,nt,e.isReadOnly,e.isRequired,e.isDisabled]),Kg=p.useCallback((Y={})=>({"data-slot":"label",className:Se.label({class:x==null?void 0:x.label}),...Z(ct,Dg,Y)}),[Se,to,ct,x==null?void 0:x.label]),Xc=p.useCallback(Y=>{Y.key==="Escape"&&K&&(no||M)&&!e.isReadOnly&&(re(""),M==null||M())},[K,re,M,no,e.isReadOnly]),Gg=p.useCallback((Y={})=>({"data-slot":"input","data-filled":B(xe),"data-filled-within":B(Me),"data-has-start-content":B(Vt),"data-has-end-content":B(!!_),"data-type":y,className:Se.input({class:ee(x==null?void 0:x.input,xe?"is-filled":"",De?"pe-0":"",y==="password"?"[&::-ms-reveal]:hidden":"")}),...Z(Vc,U,Ma(k,{enabled:!0,labelable:!0,omitEventNames:new Set(Object.keys(U))}),Y),"aria-readonly":B(e.isReadOnly),onChange:At(U.onChange,O),onKeyDown:At(U.onKeyDown,Y.onKeyDown,Xc),ref:L}),[Se,K,Vc,U,k,xe,Me,Vt,_,x==null?void 0:x.input,e.isReadOnly,e.isRequired,O,Xc]),Qg=p.useCallback((Y={})=>({ref:H,"data-slot":"input-wrapper","data-hover":B(yl||to),"data-focus-visible":B(dt),"data-focus":B(gl),className:Se.inputWrapper({class:ee(x==null?void 0:x.inputWrapper,xe?"is-filled":"")}),...Z(Y,Ag),onClick:ro=>{L.current&&ro.currentTarget===ro.target&&L.current.focus()},style:{cursor:"text",...Y.style}}),[Se,yl,to,dt,gl,K,x==null?void 0:x.inputWrapper]),Yg=p.useCallback((Y={})=>({...Y,ref:te,"data-slot":"inner-wrapper",onClick:ro=>{L.current&&ro.currentTarget===ro.target&&L.current.focus()},className:Se.innerWrapper({class:ee(x==null?void 0:x.innerWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.innerWrapper]),Xg=p.useCallback((Y={})=>({...Y,"data-slot":"main-wrapper",className:Se.mainWrapper({class:ee(x==null?void 0:x.mainWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.mainWrapper]),Zg=p.useCallback((Y={})=>({...Y,"data-slot":"helper-wrapper",className:Se.helperWrapper({class:ee(x==null?void 0:x.helperWrapper,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.helperWrapper]),Jg=p.useCallback((Y={})=>({...Y,...mi,"data-slot":"description",className:Se.description({class:ee(x==null?void 0:x.description,Y==null?void 0:Y.className)})}),[Se,x==null?void 0:x.description]),qg=p.useCallback((Y={})=>({...Y,...vl,"data-slot":"error-message",className:Se.errorMessage({class:ee(x==null?void 0:x.errorMessage,Y==null?void 0:Y.className)})}),[Se,vl,x==null?void 0:x.errorMessage]),e0=p.useCallback((Y={})=>({...Y,type:"button",tabIndex:-1,disabled:e.isDisabled,"aria-label":"clear input","data-slot":"clear-button","data-focus-visible":B(Hc),className:Se.clearButton({class:ee(x==null?void 0:x.clearButton,Y==null?void 0:Y.className)}),...Z(Kc,Bc)}),[Se,Hc,Kc,Bc,x==null?void 0:x.clearButton]);return{Component:T,classNames:x,domRef:L,label:b,description:m,startContent:$,endContent:_,labelPlacement:_t,isClearable:no,hasHelper:vi,hasStartContent:Vt,isLabelOutside:Bg,isOutsideLeft:Vg,isLabelOutsideAsPlaceholder:Hg,shouldLabelBeOutside:Yc,shouldLabelBeInside:Wg,hasPlaceholder:jn,isInvalid:cr,errorMessage:hi,getBaseProps:Ug,getLabelProps:Kg,getInputProps:Gg,getMainWrapperProps:Xg,getInputWrapperProps:Qg,getInnerWrapperProps:Yg,getHelperWrapperProps:Zg,getDescriptionProps:Jg,getErrorMessageProps:qg,getClearButtonProps:e0}}var Tg=Ue((e,t)=>{const{Component:n,label:r,description:o,isClearable:l,startContent:a,endContent:i,labelPlacement:s,hasHelper:u,isOutsideLeft:c,shouldLabelBeOutside:d,errorMessage:f,isInvalid:v,getBaseProps:y,getLabelProps:b,getInputProps:S,getInnerWrapperProps:h,getInputWrapperProps:m,getMainWrapperProps:g,getHelperWrapperProps:x,getDescriptionProps:E,getErrorMessageProps:$,getClearButtonProps:_}=NC({...e,ref:t}),M=r?w.jsx("label",{...b(),children:r}):null,O=p.useMemo(()=>l?w.jsx("button",{..._(),children:i||w.jsx(DS,{})}):i,[l,_]),I=p.useMemo(()=>{const z=v&&f;return!u||!(z||o)?null:w.jsx("div",{...x(),children:z?w.jsx("div",{...$(),children:f}):w.jsx("div",{...E(),children:o})})},[u,v,f,o,x,$,E]),C=p.useMemo(()=>w.jsxs("div",{...h(),children:[a,w.jsx("input",{...S()}),O]}),[a,O,S,h]),P=p.useMemo(()=>d?w.jsxs("div",{...g(),children:[w.jsxs("div",{...m(),children:[c?null:M,C]}),I]}):w.jsxs(w.Fragment,{children:[w.jsxs("div",{...m(),children:[M,C]}),I]}),[s,I,d,M,C,f,o,g,m,$,E]);return w.jsxs(n,{...y(),children:[c?M:null,P]})});Tg.displayName="HeroUI.Input";var _C=Tg,[MC,eo]=bc({name:"NavbarContext",strict:!0,errorMessage:"useNavbarContext: `context` is undefined. Seems you forgot to wrap component within <Navbar />"}),LC={enter:{height:"calc(100vh - var(--navbar-height))",transition:{duration:.3,easings:"easeOut"}},exit:{height:0,transition:{duration:.25,easings:"easeIn"}}},IC=()=>fi(()=>import("./index-DdjR5iIE.js"),__vite__mapDeps([])).then(e=>e.default),Ng=Ue((e,t)=>{var n,r;const{className:o,children:l,portalContainer:a,motionProps:i,style:s,...u}=e,c=Ze(t),{slots:d,isMenuOpen:f,height:v,disableAnimation:y,classNames:b}=eo(),S=ee(b==null?void 0:b.menu,o);return y?f?w.jsx(Bf,{portalContainer:a,children:w.jsx("ul",{ref:c,className:(n=d.menu)==null?void 0:n.call(d,{class:S}),"data-open":B(f),style:{"--navbar-height":typeof v=="number"?`${v}px`:v},...u,children:l})}):null:w.jsx(Oc,{mode:"wait",children:f?w.jsx(Bf,{portalContainer:a,children:w.jsx(di,{features:IC,children:w.jsx(ci.ul,{ref:c,layoutScroll:!0,animate:"enter",className:(r=d.menu)==null?void 0:r.call(d,{class:S}),"data-open":B(f),exit:"exit",initial:"exit",style:{"--navbar-height":typeof v=="number"?`${v}px`:v,...s},variants:LC,...Z(i,u),children:l})})}):null})});Ng.displayName="HeroUI.NavbarMenu";var _g=Ng,RC={visible:{y:0,transition:{ease:mp.easeOut}},hidden:{y:"-100%",transition:{ease:mp.easeIn}}},jC=typeof window<"u";function wp(e){return jC?e?{x:e.scrollLeft,y:e.scrollTop}:{x:window.scrollX,y:window.scrollY}:{x:0,y:0}}var zC=e=>{const{elementRef:t,delay:n=30,callback:r,isEnabled:o}=e,l=p.useRef(o?wp(t==null?void 0:t.current):{x:0,y:0}),a=p.useRef(null),i=p.useCallback(()=>{const s=wp(t==null?void 0:t.current);typeof r=="function"&&r({prevPos:l.current,currPos:s}),l.current=s,a.current=null},[r,t]);return p.useEffect(()=>{if(!o)return;const s=()=>{n?(a.current&&clearTimeout(a.current),a.current=setTimeout(i,n)):i()},u=(t==null?void 0:t.current)||window;return u.addEventListener("scroll",s),()=>{u.removeEventListener("scroll",s),a.current&&(clearTimeout(a.current),a.current=null)}},[t==null?void 0:t.current,n,i,o]),l.current};function OC(e){var t,n;const r=rn(),[o,l]=on(e,dp.variantKeys),{ref:a,as:i,parentRef:s,height:u="4rem",shouldHideOnScroll:c=!1,disableScrollHandler:d=!1,shouldBlockScroll:f=!0,onScrollPositionChange:v,isMenuOpen:y,isMenuDefaultOpen:b,onMenuOpenChange:S=()=>{},motionProps:h,className:m,classNames:g,...x}=o,E=i||"nav",$=(n=(t=e.disableAnimation)!=null?t:r==null?void 0:r.disableAnimation)!=null?n:!1,_=Ze(a),M=p.useRef(0),O=p.useRef(0),[I,C]=p.useState(!1),P=p.useCallback(L=>{S(L||!1)},[S]),[z,k]=fl(y,b??!1,P),D=()=>{if(_.current){const L=_.current.offsetWidth;L!==M.current&&(M.current=L)}};yx({isDisabled:!(f&&z)}),nu({ref:_,onResize:()=>{var L;const V=(L=_.current)==null?void 0:L.offsetWidth,H=window.innerWidth-document.documentElement.clientWidth;V&&V+H==M.current||V!==M.current&&(D(),k(!1))}}),p.useEffect(()=>{var L;D(),O.current=((L=_.current)==null?void 0:L.offsetHeight)||0},[]);const N=p.useMemo(()=>dp({...l,disableAnimation:$,hideOnScroll:c}),[Dt(l),$,c]),F=ee(g==null?void 0:g.base,m);return zC({elementRef:s,isEnabled:c||!d,callback:({prevPos:L,currPos:V})=>{v==null||v(V.y),c&&C(H=>{const te=V.y>L.y&&V.y>O.current;return te!==H?te:H})}}),{Component:E,slots:N,domRef:_,height:u,isHidden:I,disableAnimation:$,shouldHideOnScroll:c,isMenuOpen:z,classNames:g,setIsMenuOpen:k,motionProps:h,getBaseProps:(L={})=>({...Z(x,L),"data-hidden":B(I),"data-menu-open":B(z),ref:_,className:N.base({class:ee(F,L==null?void 0:L.className)}),style:{"--navbar-height":typeof u=="number"?`${u}px`:u,...x==null?void 0:x.style,...L==null?void 0:L.style}}),getWrapperProps:(L={})=>({...L,"data-menu-open":B(z),className:N.wrapper({class:ee(g==null?void 0:g.wrapper,L==null?void 0:L.className)})})}}var FC=()=>fi(()=>import("./index-DdjR5iIE.js"),__vite__mapDeps([])).then(e=>e.default),Mg=Ue((e,t)=>{const{children:n,...r}=e,o=OC({...r,ref:t}),l=o.Component,[a,i]=Lb(n,_g),s=w.jsxs(w.Fragment,{children:[w.jsx("header",{...o.getWrapperProps(),children:a}),i]});return w.jsx(MC,{value:o,children:o.shouldHideOnScroll?w.jsx(di,{features:FC,children:w.jsx(ci.nav,{animate:o.isHidden?"hidden":"visible",initial:!1,variants:RC,...Z(o.getBaseProps(),o.motionProps),children:s})}):w.jsx(l,{...o.getBaseProps(),children:s})})});Mg.displayName="HeroUI.Navbar";var AC=Mg,Lg=Ue((e,t)=>{var n;const{as:r,className:o,children:l,...a}=e,i=r||"div",s=Ze(t),{slots:u,classNames:c}=eo(),d=ee(c==null?void 0:c.brand,o);return w.jsx(i,{ref:s,className:(n=u.brand)==null?void 0:n.call(u,{class:d}),...a,children:l})});Lg.displayName="HeroUI.NavbarBrand";var DC=Lg,Ig=Ue((e,t)=>{var n;const{as:r,className:o,children:l,justify:a="start",...i}=e,s=r||"ul",u=Ze(t),{slots:c,classNames:d}=eo(),f=ee(d==null?void 0:d.content,o);return w.jsx(s,{ref:u,className:(n=c.content)==null?void 0:n.call(c,{class:f}),"data-justify":a,...i,children:l})});Ig.displayName="HeroUI.NavbarContent";var ns=Ig,Rg=Ue((e,t)=>{var n;const{as:r,className:o,children:l,isActive:a,...i}=e,s=r||"li",u=Ze(t),{slots:c,classNames:d}=eo(),f=ee(d==null?void 0:d.item,o);return w.jsx(s,{ref:u,className:(n=c.item)==null?void 0:n.call(c,{class:f}),"data-active":B(a),...i,children:l})});Rg.displayName="HeroUI.NavbarItem";var Gl=Rg,jg=Ue((e,t)=>{var n;const{className:r,children:o,isActive:l,...a}=e,i=Ze(t),{slots:s,isMenuOpen:u,classNames:c}=eo(),d=ee(c==null?void 0:c.menuItem,r);return w.jsx("li",{ref:i,className:(n=s.menuItem)==null?void 0:n.call(s,{class:d}),"data-active":B(l),"data-open":B(u),...a,children:o})});jg.displayName="HeroUI.NavbarMenuItem";var WC=jg,zg=Ue((e,t)=>{var n;const{as:r,icon:o,className:l,onChange:a,autoFocus:i,srOnlyText:s,...u}=e,c=r||"button",d=Ze(t),{slots:f,classNames:v,isMenuOpen:y,setIsMenuOpen:b}=eo(),h=Sg({...u,isSelected:y,onChange:C=>{a==null||a(C),b(C)}}),{buttonProps:m,isPressed:g}=zS(e,h,d),{isFocusVisible:x,focusProps:E}=or({autoFocus:i}),{isHovered:$,hoverProps:_}=rr({}),M=ee(v==null?void 0:v.toggle,l),O=p.useMemo(()=>typeof o=="function"?o(y??!1):o||w.jsx("span",{className:f.toggleIcon({class:v==null?void 0:v.toggleIcon})}),[o,y,f.toggleIcon,v==null?void 0:v.toggleIcon]),I=p.useMemo(()=>s||(h.isSelected?"close navigation menu":"open navigation menu"),[s,y]);return w.jsxs(c,{ref:d,className:(n=f.toggle)==null?void 0:n.call(f,{class:M}),"data-focus-visible":B(x),"data-hover":B($),"data-open":B(y),"data-pressed":B(g),...Z(m,E,_,u),children:[w.jsx("span",{className:f.srOnly(),children:I}),O]})});zg.displayName="HeroUI.NavbarMenuToggle";var VC=zg;function BC(e){const[t,n]=on(e,up.variantKeys),{as:r,children:o,className:l,keys:a,title:i,classNames:s,...u}=t,c=r||"kbd",d=p.useMemo(()=>up({...n}),[Dt(n)]),f=ee(s==null?void 0:s.base,l),v=typeof a=="string"?[a]:Array.isArray(a)?a:[];return{Component:c,slots:d,classNames:s,title:i,children:o,keysToRender:v,getKbdProps:(b={})=>({...u,...b,className:ee(d.base({class:ee(f,b.className)}))})}}var HC={command:"⌘",shift:"⇧",ctrl:"⌃",option:"⌥",enter:"↵",delete:"⌫",escape:"⎋",tab:"⇥",capslock:"⇪",up:"↑",right:"→",down:"↓",left:"←",pageup:"⇞",pagedown:"⇟",home:"↖",end:"↘",help:"?",space:"␣",fn:"Fn",win:"⌘",alt:"⌥"},UC={command:"Command",shift:"Shift",ctrl:"Control",option:"Option",enter:"Enter",delete:"Delete",escape:"Escape",tab:"Tab",capslock:"Caps Lock",up:"Up",right:"Right",down:"Down",left:"Left",pageup:"Page Up",pagedown:"Page Down",home:"Home",end:"End",help:"Help",space:"Space",fn:"Fn",win:"Win",alt:"Alt"},Og=Ue((e,t)=>{const{Component:n,children:r,slots:o,classNames:l,keysToRender:a,getKbdProps:i}=BC({...e}),s=p.useMemo(()=>a.map(u=>w.jsx("abbr",{className:o.abbr({class:l==null?void 0:l.abbr}),title:UC[u],children:HC[u]},u)),[a]);return w.jsxs(n,{ref:t,...i(),children:[s,r&&w.jsx("span",{className:o.content({class:l==null?void 0:l.content}),children:r})]})});Og.displayName="HeroUI.Kbd";var KC=Og;const Rt={navItems:[{label:"Home",href:"/"},{label:"Docs",href:"/docs"},{label:"Pricing",href:"/pricing"},{label:"Blog",href:"/blog"},{label:"About",href:"/about"}],navMenuItems:[{label:"Profile",href:"/profile"},{label:"Dashboard",href:"/dashboard"},{label:"Projects",href:"/projects"},{label:"Team",href:"/team"},{label:"Calendar",href:"/calendar"},{label:"Settings",href:"/settings"},{label:"Help & Feedback",href:"/help-feedback"},{label:"Logout",href:"/logout"}],links:{github:"https://github.com/frontio-ai/heroui",twitter:"https://twitter.com/hero_ui",docs:"https://heroui.com",discord:"https://discord.gg/9b6yyZKmH4",sponsor:"https://patreon.com/jrgarciadev"}},Yn=Ac({base:"tracking-tight inline font-semibold",variants:{color:{violet:"from-[#FF1CF7] to-[#b249f8]",yellow:"from-[#FF705B] to-[#FFB457]",blue:"from-[#5EA2EF] to-[#0072F5]",cyan:"from-[#00b7fa] to-[#01cfea]",green:"from-[#6FEE8D] to-[#17c964]",pink:"from-[#FF72E1] to-[#F54C7A]",foreground:"dark:from-[#FFFFFF] dark:to-[#4B4B4B]"},size:{sm:"text-3xl lg:text-4xl",md:"text-[2.3rem] lg:text-5xl leading-9",lg:"text-4xl lg:text-6xl"},fullWidth:{true:"w-full block"}},defaultVariants:{size:"md"},compoundVariants:[{color:["violet","yellow","blue","cyan","green","pink","foreground"],class:"bg-clip-text text-transparent bg-gradient-to-b"}]}),GC=Ac({base:"w-full md:w-1/2 my-2 text-lg lg:text-xl text-default-600 block max-w-full",variants:{fullWidth:{true:"!w-full"}},defaultVariants:{fullWidth:!0}}),QC=({size:e=36,height:t,...n})=>w.jsx("svg",{fill:"none",height:e||t,viewBox:"0 0 32 32",width:e||t,...n,children:w.jsx("path",{clipRule:"evenodd",d:"M17.6482 10.1305L15.8785 7.02583L7.02979 22.5499H10.5278L17.6482 10.1305ZM19.8798 14.0457L18.11 17.1983L19.394 19.4511H16.8453L15.1056 22.5499H24.7272L19.8798 14.0457Z",fill:"currentColor",fillRule:"evenodd"})}),YC=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M14.82 4.26a10.14 10.14 0 0 0-.53 1.1 14.66 14.66 0 0 0-4.58 0 10.14 10.14 0 0 0-.53-1.1 16 16 0 0 0-4.13 1.3 17.33 17.33 0 0 0-3 11.59 16.6 16.6 0 0 0 5.07 2.59A12.89 12.89 0 0 0 8.23 18a9.65 9.65 0 0 1-1.71-.83 3.39 3.39 0 0 0 .42-.33 11.66 11.66 0 0 0 10.12 0q.21.18.42.33a10.84 10.84 0 0 1-1.71.84 12.41 12.41 0 0 0 1.08 1.78 16.44 16.44 0 0 0 5.06-2.59 17.22 17.22 0 0 0-3-11.59 16.09 16.09 0 0 0-4.09-1.35zM8.68 14.81a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.93 1.93 0 0 1 1.8 2 1.93 1.93 0 0 1-1.8 2zm6.64 0a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.92 1.92 0 0 1 1.8 2 1.92 1.92 0 0 1-1.8 2z",fill:"currentColor"})}),XC=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z",fill:"currentColor"})}),Su=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{height:e||n,viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{clipRule:"evenodd",d:"M12.026 2c-5.509 0-9.974 4.465-9.974 9.974 0 4.406 2.857 8.145 6.821 9.465.499.09.679-.217.679-.481 0-.237-.008-.865-.011-1.696-2.775.602-3.361-1.338-3.361-1.338-.452-1.152-1.107-1.459-1.107-1.459-.905-.619.069-.605.069-.605 1.002.07 1.527 1.028 1.527 1.028.89 1.524 2.336 1.084 2.902.829.091-.645.351-1.085.635-1.334-2.214-.251-4.542-1.107-4.542-4.93 0-1.087.389-1.979 1.024-2.675-.101-.253-.446-1.268.099-2.64 0 0 .837-.269 2.742 1.021a9.582 9.582 0 0 1 2.496-.336 9.554 9.554 0 0 1 2.496.336c1.906-1.291 2.742-1.021 2.742-1.021.545 1.372.203 2.387.099 2.64.64.696 1.024 1.587 1.024 2.675 0 3.833-2.33 4.675-4.552 4.922.355.308.675.916.675 1.846 0 1.334-.012 2.41-.012 2.737 0 .267.178.577.687.479C19.146 20.115 22 16.379 22 11.974 22 6.465 17.535 2 12.026 2z",fill:"currentColor",fillRule:"evenodd"})}),ZC=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z",fill:"currentColor"})}),JC=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsxs("g",{fill:"currentColor",children:[w.jsx("path",{d:"M19 12a7 7 0 11-7-7 7 7 0 017 7z"}),w.jsx("path",{d:"M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z"})]})}),qC=({size:e=24,width:t,height:n,...r})=>w.jsx("svg",{"aria-hidden":"true",focusable:"false",height:e||n,role:"presentation",viewBox:"0 0 24 24",width:e||t,...r,children:w.jsx("path",{d:"M12.62 20.81c-.34.12-.9.12-1.24 0C8.48 19.82 2 15.69 2 8.69 2 5.6 4.49 3.1 7.56 3.1c1.82 0 3.43.88 4.44 2.24a5.53 5.53 0 0 1 4.44-2.24C19.51 3.1 22 5.6 22 8.69c0 7-6.48 11.13-9.38 12.12Z",fill:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5})}),eE=e=>w.jsxs("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:[w.jsx("path",{d:"M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"}),w.jsx("path",{d:"M22 22L20 20",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"})]});var tE=(e,t,n,r,o,l,a,i)=>{let s=document.documentElement,u=["light","dark"];function c(v){(Array.isArray(e)?e:[e]).forEach(y=>{let b=y==="class",S=b&&l?o.map(h=>l[h]||h):o;b?(s.classList.remove(...S),s.classList.add(l&&l[v]?l[v]:v)):s.setAttribute(y,v)}),d(v)}function d(v){i&&u.includes(v)&&(s.style.colorScheme=v)}function f(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(r)c(r);else try{let v=localStorage.getItem(t)||n,y=a&&v==="system"?f():v;c(y)}catch{}},xp=["light","dark"],Fg="(prefers-color-scheme: dark)",nE=typeof window>"u",Wc=p.createContext(void 0),rE={setTheme:e=>{},themes:[]},oE=()=>{var e;return(e=p.useContext(Wc))!=null?e:rE},lE=e=>p.useContext(Wc)?p.createElement(p.Fragment,null,e.children):p.createElement(iE,{...e}),aE=["light","dark"],iE=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:n=!0,enableColorScheme:r=!0,storageKey:o="theme",themes:l=aE,defaultTheme:a=n?"system":"light",attribute:i="data-theme",value:s,children:u,nonce:c,scriptProps:d})=>{let[f,v]=p.useState(()=>uE(o,a)),[y,b]=p.useState(()=>f==="system"?rs():f),S=s?Object.values(s):l,h=p.useCallback(E=>{let $=E;if(!$)return;E==="system"&&n&&($=rs());let _=s?s[$]:$,M=t?cE(c):null,O=document.documentElement,I=C=>{C==="class"?(O.classList.remove(...S),_&&O.classList.add(_)):C.startsWith("data-")&&(_?O.setAttribute(C,_):O.removeAttribute(C))};if(Array.isArray(i)?i.forEach(I):I(i),r){let C=xp.includes(a)?a:null,P=xp.includes($)?$:C;O.style.colorScheme=P}M==null||M()},[c]),m=p.useCallback(E=>{let $=typeof E=="function"?E(f):E;v($);try{localStorage.setItem(o,$)}catch{}},[f]),g=p.useCallback(E=>{let $=rs(E);b($),f==="system"&&n&&!e&&h("system")},[f,e]);p.useEffect(()=>{let E=window.matchMedia(Fg);return E.addListener(g),g(E),()=>E.removeListener(g)},[g]),p.useEffect(()=>{let E=$=>{$.key===o&&($.newValue?v($.newValue):m(a))};return window.addEventListener("storage",E),()=>window.removeEventListener("storage",E)},[m]),p.useEffect(()=>{h(e??f)},[e,f]);let x=p.useMemo(()=>({theme:f,setTheme:m,forcedTheme:e,resolvedTheme:f==="system"?y:f,themes:n?[...l,"system"]:l,systemTheme:n?y:void 0}),[f,m,e,y,n,l]);return p.createElement(Wc.Provider,{value:x},p.createElement(sE,{forcedTheme:e,storageKey:o,attribute:i,enableSystem:n,enableColorScheme:r,defaultTheme:a,value:s,themes:l,nonce:c,scriptProps:d}),u)},sE=p.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:l,value:a,themes:i,nonce:s,scriptProps:u})=>{let c=JSON.stringify([n,t,l,e,i,a,r,o]).slice(1,-1);return p.createElement("script",{...u,suppressHydrationWarning:!0,nonce:typeof window>"u"?s:"",dangerouslySetInnerHTML:{__html:`(${tE.toString()})(${c})`}})}),uE=(e,t)=>{if(nE)return;let n;try{n=localStorage.getItem(e)||void 0}catch{}return n||t},cE=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},rs=e=>(e||(e=window.matchMedia(Fg)),e.matches?"dark":"light");const Sp=({className:e,classNames:t})=>{const[n,r]=p.useState(!1),{theme:o,setTheme:l}=oE(),{Component:a,slots:i,isSelected:s,getBaseProps:u,getInputProps:c,getWrapperProps:d}=PC({isSelected:o==="light",onChange:()=>l(o==="light"?"dark":"light")});return p.useEffect(()=>{r(!0)},[n]),n?w.jsxs(a,{"aria-label":s?"Switch to dark mode":"Switch to light mode",...u({className:eu("px-px transition-opacity hover:opacity-80 cursor-pointer",e,t==null?void 0:t.base)}),children:[w.jsx(Px,{children:w.jsx("input",{...c()})}),w.jsx("div",{...d(),className:i.wrapper({class:eu(["w-auto h-auto","bg-transparent","rounded-lg","flex items-center justify-center","group-data-[selected=true]:bg-transparent","!text-default-500","pt-px","px-0","mx-0"],t==null?void 0:t.wrapper)}),children:s?w.jsx(ZC,{size:22}):w.jsx(JC,{size:22})})]}):w.jsx("div",{className:"w-6 h-6"})},dE=()=>{const e=w.jsx(_C,{"aria-label":"Search",classNames:{inputWrapper:"bg-default-100",input:"text-sm"},endContent:w.jsx(KC,{className:"hidden lg:inline-block",keys:["command"],children:"K"}),labelPlacement:"outside",placeholder:"Search...",startContent:w.jsx(eE,{className:"text-base text-default-400 pointer-events-none flex-shrink-0"}),type:"search"});return w.jsxs(AC,{maxWidth:"xl",position:"sticky",children:[w.jsxs(ns,{className:"basis-1/5 sm:basis-full",justify:"start",children:[w.jsx(DC,{className:"gap-3 max-w-fit",children:w.jsxs(Et,{className:"flex justify-start items-center gap-1",color:"foreground",href:"/",children:[w.jsx(QC,{}),w.jsx("p",{className:"font-bold text-inherit",children:"ACME"})]})}),w.jsx("div",{className:"hidden lg:flex gap-4 justify-start ml-2",children:Rt.navItems.map(t=>w.jsx(Gl,{children:w.jsx(Et,{className:"data-[active=true]:text-primary data-[active=true]:font-medium",color:"foreground",href:t.href,children:t.label})},t.href))})]}),w.jsxs(ns,{className:"hidden sm:flex basis-1/5 sm:basis-full",justify:"end",children:[w.jsxs(Gl,{className:"hidden sm:flex gap-2",children:[w.jsx(Et,{isExternal:!0,href:Rt.links.twitter,title:"Twitter",children:w.jsx(XC,{className:"text-default-500"})}),w.jsx(Et,{isExternal:!0,href:Rt.links.discord,title:"Discord",children:w.jsx(YC,{className:"text-default-500"})}),w.jsx(Et,{isExternal:!0,href:Rt.links.github,title:"GitHub",children:w.jsx(Su,{className:"text-default-500"})}),w.jsx(Sp,{})]}),w.jsx(Gl,{className:"hidden lg:flex",children:e}),w.jsx(Gl,{className:"hidden md:flex",children:w.jsx(Fa,{isExternal:!0,as:Et,className:"text-sm font-normal text-default-600 bg-default-100",href:Rt.links.sponsor,startContent:w.jsx(qC,{className:"text-danger"}),variant:"flat",children:"Sponsor"})})]}),w.jsxs(ns,{className:"sm:hidden basis-1 pl-4",justify:"end",children:[w.jsx(Et,{isExternal:!0,href:Rt.links.github,children:w.jsx(Su,{className:"text-default-500"})}),w.jsx(Sp,{}),w.jsx(VC,{})]}),w.jsxs(_g,{children:[e,w.jsx("div",{className:"mx-4 mt-2 flex flex-col gap-2",children:Rt.navMenuItems.map((t,n)=>w.jsx(WC,{children:w.jsx(Et,{color:n===2?"primary":n===Rt.navMenuItems.length-1?"danger":"foreground",href:"#",size:"lg",children:t.label})},`${t}-${n}`))})]})]})};function hl({children:e}){return w.jsxs("div",{className:"relative flex flex-col h-screen",children:[w.jsx(dE,{}),w.jsx("main",{className:"container mx-auto max-w-7xl px-6 flex-grow pt-16",children:e}),w.jsx("footer",{className:"w-full flex items-center justify-center py-3",children:w.jsxs(Et,{isExternal:!0,className:"flex items-center gap-1 text-current",href:"https://heroui.com",title:"heroui.com homepage",children:[w.jsx("span",{className:"text-default-600",children:"Powered by"}),w.jsx("p",{className:"text-primary",children:"HeroUI"})]})})]})}function fE(){return w.jsx(hl,{children:w.jsxs("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:[w.jsxs("div",{className:"inline-block max-w-lg text-center justify-center",children:[w.jsx("span",{className:Yn(),children:"Make "}),w.jsx("span",{className:Yn({color:"violet"}),children:"beautiful "}),w.jsx("br",{}),w.jsx("span",{className:Yn(),children:"websites regardless of your design experience."}),w.jsx("div",{className:GC({class:"mt-4"}),children:"Beautiful, fast and modern React UI library."})]}),w.jsxs("div",{className:"flex gap-3",children:[w.jsx(Fa,{as:Et,isExternal:!0,color:"primary",radius:"full",variant:"shadow",href:Rt.links.docs,children:"Documentation"}),w.jsxs(Fa,{as:Et,isExternal:!0,variant:"bordered",radius:"full",href:Rt.links.github,children:[w.jsx(Su,{size:20}),"GitHub"]})]}),w.jsx("div",{className:"mt-8",children:w.jsx($C,{hideCopyButton:!0,hideSymbol:!0,variant:"bordered",children:w.jsxs("span",{children:["Get started by editing"," ",w.jsx(cC,{color:"primary",children:"pages/index.tsx"})]})})})]})})}function pE(){return w.jsx(hl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Yn(),children:"Docs"})})})})}function mE(){return w.jsx(hl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Yn(),children:"Pricing"})})})})}function hE(){return w.jsx(hl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Yn(),children:"Blog"})})})})}function vE(){return w.jsx(hl,{children:w.jsx("section",{className:"flex flex-col items-center justify-center gap-4 py-8 md:py-10",children:w.jsx("div",{className:"inline-block max-w-lg text-center justify-center",children:w.jsx("h1",{className:Yn(),children:"About"})})})})}function gE(){return w.jsxs(yb,{children:[w.jsx(pr,{element:w.jsx(fE,{}),path:"/"}),w.jsx(pr,{element:w.jsx(pE,{}),path:"/docs"}),w.jsx(pr,{element:w.jsx(mE,{}),path:"/pricing"}),w.jsx(pr,{element:w.jsx(hE,{}),path:"/blog"}),w.jsx(pr,{element:w.jsx(vE,{}),path:"/about"})]})}function yE({children:e}){const t=tb();return w.jsx(F2,{navigate:t,useHref:eb,children:w.jsx(lE,{attribute:"class",defaultTheme:"dark",children:e})})}os.createRoot(document.getElementById("root")).render(w.jsx(q.StrictMode,{children:w.jsx(xb,{children:w.jsx(yE,{children:w.jsx(gE,{})})})}));export{jc as A,e2 as B,za as C,u2 as D,Fx as E,qv as F,Yv as G,Wv as H,c2 as I,f2 as J,eg as K,d2 as L,Rx as M,tg as N,Gv as O,p as P,Lc as a,ii as b,Xi as c,On as d,SE as e,Uf as f,hu as g,Wx as h,xE as i,Dv as j,wE as k,bE as l,EE as m,jx as n,Kx as o,Q as p,lr as q,Mx as r,mu as s,Ov as t,Ix as u,CE as v,zv as w,_x as x,Lx as y,Hv as z};

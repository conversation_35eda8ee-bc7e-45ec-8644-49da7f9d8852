rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isGroupMember(groupId) {
      return isAuthenticated() && 
             request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
    }
    
    function isGroupModerator(groupId) {
      return isAuthenticated() && 
             (request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.moderators ||
              request.auth.uid == get(/databases/$(database)/documents/groups/$(groupId)).data.createdBy);
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own profile and public profiles
      allow read: if isAuthenticated() && 
                     (isOwner(userId) || 
                      resource.data.settings.privacy.profileVisibility == 'public' ||
                      (resource.data.settings.privacy.profileVisibility == 'friends' && 
                       request.auth.uid in resource.data.followers));
      
      // Users can only write to their own profile
      allow write: if isOwner(userId);
      
      // Allow creating new user profiles
      allow create: if isAuthenticated() && isOwner(userId);
    }

    // Posts collection
    match /posts/{postId} {
      // Anyone authenticated can read public posts
      allow read: if isAuthenticated() && 
                     (resource.data.isPublic == true ||
                      isOwner(resource.data.authorId) ||
                      (exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) &&
                       isGroupMember(resource.data.groupId)));
      
      // Users can create posts
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.authorId) &&
                       resource.data.authorId == request.auth.uid;
      
      // Users can update/delete their own posts
      allow update, delete: if isAuthenticated() && isOwner(resource.data.authorId);
    }

    // Comments collection
    match /comments/{commentId} {
      // Anyone authenticated can read comments on posts they can see
      allow read: if isAuthenticated() &&
                     exists(/databases/$(database)/documents/posts/$(resource.data.postId));
      
      // Users can create comments
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.authorId) &&
                       resource.data.authorId == request.auth.uid &&
                       exists(/databases/$(database)/documents/posts/$(resource.data.postId));
      
      // Users can update/delete their own comments
      allow update, delete: if isAuthenticated() && isOwner(resource.data.authorId);
    }

    // Groups collection
    match /groups/{groupId} {
      // Public groups can be read by anyone authenticated
      // Private groups only by members
      allow read: if isAuthenticated() && 
                     (resource.data.privacy == 'public' || 
                      isGroupMember(groupId) ||
                      isOwner(resource.data.createdBy));
      
      // Anyone authenticated can create groups
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.createdBy) &&
                       resource.data.createdBy == request.auth.uid;
      
      // Only group creator and moderators can update groups
      allow update: if isAuthenticated() && isGroupModerator(groupId);
      
      // Only group creator can delete groups
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Tournaments collection
    match /tournaments/{tournamentId} {
      // Anyone authenticated can read tournaments
      allow read: if isAuthenticated();
      
      // Anyone authenticated can create tournaments
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.createdBy) &&
                       resource.data.createdBy == request.auth.uid;
      
      // Only tournament creator and organizers can update
      allow update: if isAuthenticated() && 
                       (isOwner(resource.data.createdBy) ||
                        request.auth.uid in resource.data.organizers);
      
      // Only tournament creator can delete
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Users can only read their own notifications
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      
      // System can create notifications for users
      allow create: if isAuthenticated();
      
      // Users can update their own notifications (mark as read)
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      
      // Users can delete their own notifications
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Achievements collection
    match /achievements/{achievementId} {
      // Anyone authenticated can read achievements
      allow read: if isAuthenticated();
      
      // Only admins can create/update/delete achievements (handled server-side)
      allow write: if false;
    }

    // User achievements collection
    match /userAchievements/{userAchievementId} {
      // Users can read their own achievements and public achievements of others
      allow read: if isAuthenticated() && 
                     (isOwner(resource.data.userId) ||
                      !get(/databases/$(database)/documents/achievements/$(resource.data.achievementId)).data.isSecret);
      
      // Only system can create user achievements (handled server-side)
      allow write: if false;
    }

    // Games collection
    match /games/{gameId} {
      // Anyone authenticated can read games
      allow read: if isAuthenticated();
      
      // Only admins can create/update/delete games (handled server-side)
      allow write: if false;
    }

    // Messages collection (for future chat feature)
    match /messages/{messageId} {
      // Users can read messages they sent or received
      allow read: if isAuthenticated() && 
                     (isOwner(resource.data.senderId) ||
                      isOwner(resource.data.recipientId) ||
                      (resource.data.groupId != null && isGroupMember(resource.data.groupId)));
      
      // Users can create messages
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.senderId) &&
                       resource.data.senderId == request.auth.uid;
      
      // Users can update/delete their own messages
      allow update, delete: if isAuthenticated() && isOwner(resource.data.senderId);
    }

    // Analytics collection (for admin dashboard)
    match /analytics/{document=**} {
      // Only admins can access analytics
      allow read, write: if false;
    }

    // Reports collection (for content moderation)
    match /reports/{reportId} {
      // Users can create reports
      allow create: if isAuthenticated() && 
                       isOwner(resource.data.reportedBy) &&
                       resource.data.reportedBy == request.auth.uid;
      
      // Only admins can read/update reports
      allow read, update: if false;
    }
  }
}

import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  Timestamp,
  increment,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from './firebase';
import type {
  User,
  Post,
  Comment,
  Group,
  Tournament,
  Notification,
  Achievement,
  Game,
  CollectionName,
  QueryOptions,
} from '../types/database';

// Generic CRUD operations
export class FirestoreService {
  // Create document
  static async create<T>(collectionName: CollectionName, data: Omit<T, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, collectionName), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    return docRef.id;
  }

  // Create document with custom ID
  static async createWithId<T>(
    collectionName: CollectionName,
    id: string,
    data: Omit<T, 'id'>
  ): Promise<void> {
    await setDoc(doc(db, collectionName, id), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  }

  // Get document by ID
  static async getById<T>(collectionName: CollectionName, id: string): Promise<T | null> {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T;
    }
    return null;
  }

  // Update document
  static async update(
    collectionName: CollectionName,
    id: string,
    data: Partial<any>
  ): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await updateDoc(docRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });
  }

  // Delete document
  static async delete(collectionName: CollectionName, id: string): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
  }

  // Query documents
  static async query<T>(
    collectionName: CollectionName,
    options: QueryOptions = { limit: 10 }
  ): Promise<T[]> {
    let q = query(collection(db, collectionName));

    // Add where clauses
    if (options.where) {
      options.where.forEach(({ field, operator, value }) => {
        q = query(q, where(field, operator, value));
      });
    }

    // Add ordering
    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));
    }

    // Add pagination
    if (options.lastDoc) {
      q = query(q, startAfter(options.lastDoc));
    }

    q = query(q, limit(options.limit));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as T[];
  }

  // Real-time listener
  static onSnapshot<T>(
    collectionName: CollectionName,
    callback: (data: T[]) => void,
    options: QueryOptions = { limit: 10 }
  ): () => void {
    let q = query(collection(db, collectionName));

    if (options.where) {
      options.where.forEach(({ field, operator, value }) => {
        q = query(q, where(field, operator, value));
      });
    }

    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));
    }

    q = query(q, limit(options.limit));

    return onSnapshot(q, (querySnapshot) => {
      const data = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as T[];
      callback(data);
    });
  }
}

// Specialized services for each entity
export class UserService {
  static async createUser(userData: Omit<User, 'id'>): Promise<void> {
    await FirestoreService.createWithId('users', userData.uid, userData);
  }

  static async getUser(uid: string): Promise<User | null> {
    return FirestoreService.getById<User>('users', uid);
  }

  static async updateUser(uid: string, data: Partial<User>): Promise<void> {
    await FirestoreService.update('users', uid, data);
  }

  static async followUser(currentUserId: string, targetUserId: string): Promise<void> {
    // Add to current user's following list
    await FirestoreService.update('users', currentUserId, {
      following: arrayUnion(targetUserId),
    });

    // Add to target user's followers list
    await FirestoreService.update('users', targetUserId, {
      followers: arrayUnion(currentUserId),
    });
  }

  static async unfollowUser(currentUserId: string, targetUserId: string): Promise<void> {
    // Remove from current user's following list
    await FirestoreService.update('users', currentUserId, {
      following: arrayRemove(targetUserId),
    });

    // Remove from target user's followers list
    await FirestoreService.update('users', targetUserId, {
      followers: arrayRemove(currentUserId),
    });
  }

  static async updateOnlineStatus(uid: string, isOnline: boolean): Promise<void> {
    await FirestoreService.update('users', uid, {
      isOnline,
      lastActive: serverTimestamp(),
    });
  }
}

export class PostService {
  static async createPost(postData: Omit<Post, 'id'>): Promise<string> {
    return FirestoreService.create<Post>('posts', postData);
  }

  static async getPost(id: string): Promise<Post | null> {
    return FirestoreService.getById<Post>('posts', id);
  }

  static async updatePost(id: string, data: Partial<Post>): Promise<void> {
    await FirestoreService.update('posts', id, data);
  }

  static async deletePost(id: string): Promise<void> {
    await FirestoreService.delete('posts', id);
  }

  static async likePost(postId: string, userId: string): Promise<void> {
    await FirestoreService.update('posts', postId, {
      likes: arrayUnion(userId),
      likesCount: increment(1),
    });
  }

  static async unlikePost(postId: string, userId: string): Promise<void> {
    await FirestoreService.update('posts', postId, {
      likes: arrayRemove(userId),
      likesCount: increment(-1),
    });
  }

  static async getFeedPosts(options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'isPublic', operator: '==', value: true }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }

  static async getUserPosts(userId: string, options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'authorId', operator: '==', value: userId }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }

  static async getGroupPosts(groupId: string, options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'groupId', operator: '==', value: groupId }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }
}

export class CommentService {
  static async createComment(commentData: Omit<Comment, 'id'>): Promise<string> {
    const commentId = await FirestoreService.create<Comment>('comments', commentData);
    
    // Increment post comment count
    await FirestoreService.update('posts', commentData.postId, {
      commentsCount: increment(1),
    });

    return commentId;
  }

  static async getPostComments(postId: string, options: QueryOptions = { limit: 20 }): Promise<Comment[]> {
    return FirestoreService.query<Comment>('comments', {
      ...options,
      where: [{ field: 'postId', operator: '==', value: postId }],
      orderBy: 'createdAt',
      orderDirection: 'asc',
    });
  }

  static async likeComment(commentId: string, userId: string): Promise<void> {
    await FirestoreService.update('comments', commentId, {
      likes: arrayUnion(userId),
      likesCount: increment(1),
    });
  }

  static async unlikeComment(commentId: string, userId: string): Promise<void> {
    await FirestoreService.update('comments', commentId, {
      likes: arrayRemove(userId),
      likesCount: increment(-1),
    });
  }
}

export class GroupService {
  static async createGroup(groupData: Omit<Group, 'id'>): Promise<string> {
    return FirestoreService.create<Group>('groups', groupData);
  }

  static async getGroup(id: string): Promise<Group | null> {
    return FirestoreService.getById<Group>('groups', id);
  }

  static async joinGroup(groupId: string, userId: string): Promise<void> {
    await FirestoreService.update('groups', groupId, {
      members: arrayUnion(userId),
      memberCount: increment(1),
    });
  }

  static async leaveGroup(groupId: string, userId: string): Promise<void> {
    await FirestoreService.update('groups', groupId, {
      members: arrayRemove(userId),
      memberCount: increment(-1),
    });
  }

  static async getPublicGroups(options: QueryOptions = { limit: 10 }): Promise<Group[]> {
    return FirestoreService.query<Group>('groups', {
      ...options,
      where: [{ field: 'privacy', operator: '==', value: 'public' }],
      orderBy: 'memberCount',
      orderDirection: 'desc',
    });
  }

  static async getUserGroups(userId: string): Promise<Group[]> {
    return FirestoreService.query<Group>('groups', {
      where: [{ field: 'members', operator: 'array-contains', value: userId }],
      orderBy: 'lastActivity',
      orderDirection: 'desc',
      limit: 50,
    });
  }
}

import { Timestamp } from 'firebase/firestore';

// User Types
export interface User {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  bio?: string;
  favoriteGames: string[];
  level: number;
  xp: number;
  rank?: string;
  achievements: string[];
  followers: string[];
  following: string[];
  createdAt: Timestamp;
  lastActive: Timestamp;
  isOnline: boolean;
  settings: UserSettings;
}

export interface UserSettings {
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private';
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
  };
  notifications: {
    email: boolean;
    push: boolean;
    comments: boolean;
    likes: boolean;
    follows: boolean;
    groupInvites: boolean;
  };
  theme: 'light' | 'dark' | 'auto';
}

// Post Types
export interface Post {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  images?: string[];
  gameTag?: string;
  type: 'text' | 'image' | 'video' | 'achievement' | 'tournament';
  likes: string[];
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  isPublic: boolean;
  groupId?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  tags: string[];
  metadata?: PostMetadata;
}

export interface PostMetadata {
  achievement?: {
    gameTitle: string;
    achievementName: string;
    description: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  };
  tournament?: {
    tournamentId: string;
    tournamentName: string;
    placement: number;
    totalParticipants: number;
  };
  game?: {
    title: string;
    platform: string;
    score?: number;
    duration?: number;
  };
}

// Comment Types
export interface Comment {
  id: string;
  postId: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  likes: string[];
  likesCount: number;
  parentCommentId?: string;
  replies?: Comment[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Group Types
export interface Group {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  banner?: string;
  gameTitle: string;
  platform: string[];
  category: 'casual' | 'competitive' | 'professional' | 'community';
  privacy: 'public' | 'private' | 'invite-only';
  createdBy: string;
  moderators: string[];
  members: string[];
  memberCount: number;
  maxMembers?: number;
  rules: string[];
  tags: string[];
  createdAt: Timestamp;
  lastActivity: Timestamp;
  isActive: boolean;
  stats: GroupStats;
}

export interface GroupStats {
  totalPosts: number;
  totalMembers: number;
  activeMembers: number;
  weeklyActivity: number;
  monthlyActivity: number;
}

// Tournament Types
export interface Tournament {
  id: string;
  name: string;
  description: string;
  gameTitle: string;
  platform: string;
  format: 'single-elimination' | 'double-elimination' | 'round-robin' | 'swiss';
  type: 'solo' | 'team' | 'duo';
  maxParticipants: number;
  currentParticipants: number;
  prizePool?: number;
  currency?: string;
  entryFee?: number;
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed' | 'cancelled';
  createdBy: string;
  organizers: string[];
  participants: TournamentParticipant[];
  brackets?: TournamentBracket[];
  rules: string[];
  startDate: Timestamp;
  endDate: Timestamp;
  registrationDeadline: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TournamentParticipant {
  userId: string;
  userName: string;
  userAvatar?: string;
  teamName?: string;
  teamMembers?: string[];
  registeredAt: Timestamp;
  status: 'registered' | 'checked-in' | 'eliminated' | 'winner';
  placement?: number;
}

export interface TournamentBracket {
  round: number;
  matches: TournamentMatch[];
}

export interface TournamentMatch {
  id: string;
  participant1: string;
  participant2: string;
  winner?: string;
  score?: string;
  scheduledAt?: Timestamp;
  completedAt?: Timestamp;
  status: 'pending' | 'ongoing' | 'completed';
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'group-invite' | 'tournament' | 'achievement' | 'system';
  title: string;
  message: string;
  actionUrl?: string;
  isRead: boolean;
  createdAt: Timestamp;
  metadata?: {
    fromUserId?: string;
    fromUserName?: string;
    postId?: string;
    groupId?: string;
    tournamentId?: string;
  };
}

// Achievement Types
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'social' | 'gaming' | 'tournament' | 'community' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
  requirements: AchievementRequirement[];
  isSecret: boolean;
  createdAt: Timestamp;
}

export interface AchievementRequirement {
  type: 'posts' | 'likes' | 'comments' | 'followers' | 'tournaments' | 'groups' | 'login-streak';
  target: number;
  description: string;
}

export interface UserAchievement {
  userId: string;
  achievementId: string;
  unlockedAt: Timestamp;
  progress?: number;
}

// Game Types
export interface Game {
  id: string;
  title: string;
  developer: string;
  publisher: string;
  platforms: string[];
  genres: string[];
  releaseDate: Timestamp;
  description: string;
  images: {
    cover: string;
    banner: string;
    screenshots: string[];
  };
  rating: number;
  playerCount: number;
  isActive: boolean;
  tags: string[];
  externalIds: {
    steam?: string;
    epic?: string;
    origin?: string;
    battlenet?: string;
  };
}

// Message Types (for future chat feature)
export interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  recipientId?: string;
  groupId?: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  readBy: string[];
}

// Utility Types
export type CollectionName = 
  | 'users' 
  | 'posts' 
  | 'comments' 
  | 'groups' 
  | 'tournaments' 
  | 'notifications' 
  | 'achievements' 
  | 'userAchievements' 
  | 'games' 
  | 'messages';

export interface PaginationOptions {
  limit: number;
  lastDoc?: any;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface QueryOptions extends PaginationOptions {
  where?: {
    field: string;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains' | 'array-contains-any';
    value: any;
  }[];
}

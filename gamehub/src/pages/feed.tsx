import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner,
} from '@heroui/react';
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  PlusIcon,
  PhotoIcon,
  VideoCameraIcon,
  TrophyIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

import { useAuth } from '@/contexts/AuthContext';
import { PostService } from '@/services/firestore';
import { Post } from '@/types/database';
import DefaultLayout from '@/layouts/default';

export default function FeedPage() {
  const { currentUser, userProfile } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [newPostContent, setNewPostContent] = useState('');
  const [postType, setPostType] = useState<'text' | 'image' | 'achievement'>('text');

  useEffect(() => {
    if (currentUser) {
      loadFeedPosts();
    }
  }, [currentUser]);

  const loadFeedPosts = async () => {
    try {
      setLoading(true);
      const feedPosts = await PostService.getFeedPosts({ limit: 20 });
      setPosts(feedPosts);
    } catch (error) {
      console.error('Error loading feed posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePost = async () => {
    if (!currentUser || !userProfile || !newPostContent.trim()) return;

    try {
      const postData = {
        authorId: currentUser.uid,
        authorName: userProfile.displayName,
        authorAvatar: userProfile.photoURL,
        content: newPostContent,
        type: postType,
        likes: [],
        likesCount: 0,
        commentsCount: 0,
        sharesCount: 0,
        isPublic: true,
        tags: extractHashtags(newPostContent),
        createdAt: new Date() as any,
        updatedAt: new Date() as any,
      };

      await PostService.createPost(postData);
      setNewPostContent('');
      onClose();
      loadFeedPosts(); // Reload feed
    } catch (error) {
      console.error('Error creating post:', error);
    }
  };

  const handleLikePost = async (postId: string, isLiked: boolean) => {
    if (!currentUser) return;

    try {
      if (isLiked) {
        await PostService.unlikePost(postId, currentUser.uid);
      } else {
        await PostService.likePost(postId, currentUser.uid);
      }
      loadFeedPosts(); // Reload to update likes
    } catch (error) {
      console.error('Error liking post:', error);
    }
  };

  const extractHashtags = (text: string): string[] => {
    const hashtags = text.match(/#\w+/g);
    return hashtags ? hashtags.map(tag => tag.slice(1)) : [];
  };

  const formatTimeAgo = (timestamp: any): string => {
    const now = new Date();
    const postTime = timestamp?.toDate ? timestamp.toDate() : new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  const getPostTypeIcon = (type: string) => {
    switch (type) {
      case 'achievement':
        return <TrophyIcon className="w-4 h-4 text-warning" />;
      case 'image':
        return <PhotoIcon className="w-4 h-4 text-primary" />;
      case 'video':
        return <VideoCameraIcon className="w-4 h-4 text-secondary" />;
      default:
        return null;
    }
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver o feed</h2>
              <p className="text-default-500">
                Entre na sua conta para ver posts da comunidade gaming
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Create Post Card */}
        <Card>
          <CardBody className="p-4">
            <div className="flex gap-3">
              <Avatar
                src={userProfile?.photoURL}
                name={userProfile?.displayName}
                size="sm"
              />
              <Button
                variant="flat"
                className="flex-1 justify-start text-default-500"
                onPress={onOpen}
              >
                O que você está jogando hoje?
              </Button>
              <Button
                isIconOnly
                color="primary"
                variant="flat"
                onPress={onOpen}
              >
                <PlusIcon className="w-5 h-5" />
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Posts Feed */}
        {loading ? (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        ) : posts.length === 0 ? (
          <Card>
            <CardBody className="text-center py-12">
              <h3 className="text-lg font-semibold mb-2">Nenhum post ainda</h3>
              <p className="text-default-500 mb-4">
                Seja o primeiro a compartilhar algo com a comunidade!
              </p>
              <Button color="primary" onPress={onOpen}>
                Criar Post
              </Button>
            </CardBody>
          </Card>
        ) : (
          posts.map((post) => (
            <Card key={post.id} className="w-full">
              <CardHeader className="flex gap-3 p-4">
                <Avatar
                  src={post.authorAvatar}
                  name={post.authorName}
                  size="sm"
                />
                <div className="flex flex-col flex-1">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-semibold">{post.authorName}</p>
                    {getPostTypeIcon(post.type)}
                    {post.gameTag && (
                      <Chip size="sm" variant="flat" color="primary">
                        {post.gameTag}
                      </Chip>
                    )}
                  </div>
                  <p className="text-xs text-default-500">
                    {formatTimeAgo(post.createdAt)}
                  </p>
                </div>
              </CardHeader>
              
              <CardBody className="px-4 py-0">
                <p className="text-sm mb-3">{post.content}</p>
                
                {post.images && post.images.length > 0 && (
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {post.images.map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt="Post image"
                        className="rounded-lg object-cover aspect-square"
                      />
                    ))}
                  </div>
                )}

                {post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {post.tags.map((tag, index) => (
                      <Chip key={index} size="sm" variant="flat" color="default">
                        #{tag}
                      </Chip>
                    ))}
                  </div>
                )}

                <div className="flex items-center justify-between py-3 border-t border-divider">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="light"
                      size="sm"
                      startContent={
                        post.likes.includes(currentUser.uid) ? (
                          <HeartSolidIcon className="w-4 h-4 text-danger" />
                        ) : (
                          <HeartIcon className="w-4 h-4" />
                        )
                      }
                      onPress={() => handleLikePost(post.id, post.likes.includes(currentUser.uid))}
                      className={post.likes.includes(currentUser.uid) ? 'text-danger' : ''}
                    >
                      {post.likesCount}
                    </Button>
                    
                    <Button
                      variant="light"
                      size="sm"
                      startContent={<ChatBubbleOvalLeftIcon className="w-4 h-4" />}
                    >
                      {post.commentsCount}
                    </Button>
                    
                    <Button
                      variant="light"
                      size="sm"
                      startContent={<ShareIcon className="w-4 h-4" />}
                    >
                      {post.sharesCount}
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))
        )}

        {/* Create Post Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="2xl">
          <ModalContent>
            <ModalHeader>Criar Post</ModalHeader>
            <ModalBody>
              <div className="flex gap-3 mb-4">
                <Avatar
                  src={userProfile?.photoURL}
                  name={userProfile?.displayName}
                  size="sm"
                />
                <div className="flex-1">
                  <p className="font-semibold text-sm">{userProfile?.displayName}</p>
                  <p className="text-xs text-default-500">Público</p>
                </div>
              </div>
              
              <Textarea
                placeholder="O que você está jogando hoje?"
                value={newPostContent}
                onChange={(e) => setNewPostContent(e.target.value)}
                minRows={3}
                maxRows={8}
              />
              
              <div className="flex gap-2 mt-4">
                <Button
                  variant={postType === 'text' ? 'solid' : 'flat'}
                  size="sm"
                  onPress={() => setPostType('text')}
                >
                  Texto
                </Button>
                <Button
                  variant={postType === 'image' ? 'solid' : 'flat'}
                  size="sm"
                  startContent={<PhotoIcon className="w-4 h-4" />}
                  onPress={() => setPostType('image')}
                >
                  Imagem
                </Button>
                <Button
                  variant={postType === 'achievement' ? 'solid' : 'flat'}
                  size="sm"
                  startContent={<TrophyIcon className="w-4 h-4" />}
                  onPress={() => setPostType('achievement')}
                >
                  Conquista
                </Button>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                onPress={handleCreatePost}
                isDisabled={!newPostContent.trim()}
              >
                Publicar
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner,
  Tabs,
  Tab,
  Badge,
  Progress,
  Textarea,
  Select,
  SelectItem,
} from '@heroui/react';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  UsersIcon,
  FireIcon,
  TrophyIcon,
  ChatBubbleOvalLeftIcon,
  StarIcon,
  GlobeAltIcon,
  LockClosedIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { GroupService } from '@/services/firestore';
import { Group } from '@/types/database';
import DefaultLayout from '@/layouts/default';

const popularGames = [
  { id: 'cs2', name: 'Counter-Strike 2', icon: '🎯', color: 'warning' },
  { id: 'valorant', name: '<PERSON><PERSON><PERSON>', icon: '🔫', color: 'danger' },
  { id: 'lol', name: 'League of Legends', icon: '⚔️', color: 'primary' },
  { id: 'fortnite', name: '<PERSON><PERSON><PERSON>', icon: '🏗️', color: 'secondary' },
  { id: 'apex', name: 'Apex Legends', icon: '🎮', color: 'success' },
  { id: 'minecraft', name: 'Minecraft', icon: '🧱', color: 'default' },
];

const platforms = [
  { id: 'pc', name: 'PC', icon: '💻' },
  { id: 'ps5', name: 'PlayStation 5', icon: '🎮' },
  { id: 'xbox', name: 'Xbox Series', icon: '🎯' },
  { id: 'mobile', name: 'Mobile', icon: '📱' },
  { id: 'switch', name: 'Nintendo Switch', icon: '🕹️' },
];

export default function GroupsPage() {
  const { currentUser, userProfile } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [groups, setGroups] = useState<Group[]>([]);
  const [myGroups, setMyGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('discover');
  const [selectedGame, setSelectedGame] = useState('');

  // Create group form
  const [newGroup, setNewGroup] = useState({
    name: '',
    description: '',
    gameTitle: '',
    platform: [],
    category: 'casual' as const,
    privacy: 'public' as const,
    maxMembers: 50,
  });

  useEffect(() => {
    if (currentUser) {
      loadGroups();
      loadMyGroups();
    }
  }, [currentUser]);

  const loadGroups = async () => {
    try {
      setLoading(true);
      const publicGroups = await GroupService.getPublicGroups({ limit: 20 });
      setGroups(publicGroups);
    } catch (error) {
      console.error('Error loading groups:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMyGroups = async () => {
    if (!currentUser) return;
    try {
      const userGroups = await GroupService.getUserGroups(currentUser.uid);
      setMyGroups(userGroups);
    } catch (error) {
      console.error('Error loading user groups:', error);
    }
  };

  const handleCreateGroup = async () => {
    if (!currentUser || !userProfile) return;

    try {
      const groupData = {
        ...newGroup,
        createdBy: currentUser.uid,
        moderators: [currentUser.uid],
        members: [currentUser.uid],
        memberCount: 1,
        rules: [
          'Seja respeitoso com todos os membros',
          'Não faça spam ou autopromoção excessiva',
          'Mantenha as discussões relacionadas ao jogo',
          'Não compartilhe conteúdo ofensivo',
        ],
        tags: [newGroup.gameTitle.toLowerCase(), newGroup.category],
        createdAt: new Date() as any,
        lastActivity: new Date() as any,
        isActive: true,
        stats: {
          totalPosts: 0,
          totalMembers: 1,
          activeMembers: 1,
          weeklyActivity: 0,
          monthlyActivity: 0,
        },
      };

      await GroupService.createGroup(groupData);
      setNewGroup({
        name: '',
        description: '',
        gameTitle: '',
        platform: [],
        category: 'casual',
        privacy: 'public',
        maxMembers: 50,
      });
      onClose();
      loadGroups();
      loadMyGroups();
    } catch (error) {
      console.error('Error creating group:', error);
    }
  };

  const handleJoinGroup = async (groupId: string) => {
    if (!currentUser) return;
    try {
      await GroupService.joinGroup(groupId, currentUser.uid);
      loadGroups();
      loadMyGroups();
    } catch (error) {
      console.error('Error joining group:', error);
    }
  };

  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         group.gameTitle.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGame = !selectedGame || group.gameTitle.toLowerCase() === selectedGame;
    return matchesSearch && matchesGame;
  });

  const getActivityLevel = (stats: any) => {
    const activity = stats.weeklyActivity || 0;
    if (activity >= 50) return { level: 'Alta', color: 'success', value: 90 };
    if (activity >= 20) return { level: 'Média', color: 'warning', value: 60 };
    return { level: 'Baixa', color: 'danger', value: 30 };
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'competitive': return <TrophyIcon className="w-4 h-4" />;
      case 'casual': return <StarIcon className="w-4 h-4" />;
      case 'professional': return <FireIcon className="w-4 h-4" />;
      default: return <UsersIcon className="w-4 h-4" />;
    }
  };

  const getPrivacyIcon = (privacy: string) => {
    switch (privacy) {
      case 'public': return <GlobeAltIcon className="w-4 h-4 text-success" />;
      case 'private': return <LockClosedIcon className="w-4 h-4 text-danger" />;
      default: return <EyeIcon className="w-4 h-4 text-warning" />;
    }
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver os grupos</h2>
              <p className="text-default-500">
                Entre na sua conta para descobrir e participar de grupos gaming
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Grupos Gaming</h1>
            <p className="text-default-500">Encontre sua squad perfeita</p>
          </div>
          <Button
            color="primary"
            startContent={<PlusIcon className="w-5 h-5" />}
            onPress={onOpen}
          >
            Criar Grupo
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardBody className="text-center p-4">
              <div className="text-2xl font-bold text-primary">{groups.length}</div>
              <div className="text-sm text-default-500">Grupos Ativos</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center p-4">
              <div className="text-2xl font-bold text-success">{myGroups.length}</div>
              <div className="text-sm text-default-500">Meus Grupos</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center p-4">
              <div className="text-2xl font-bold text-warning">
                {groups.reduce((sum, g) => sum + g.memberCount, 0)}
              </div>
              <div className="text-sm text-default-500">Membros Total</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center p-4">
              <div className="text-2xl font-bold text-secondary">{popularGames.length}</div>
              <div className="text-sm text-default-500">Jogos Populares</div>
            </CardBody>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardBody className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder="Buscar grupos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                className="flex-1"
              />
              <Select
                placeholder="Filtrar por jogo"
                value={selectedGame}
                onChange={(e) => setSelectedGame(e.target.value)}
                className="w-full md:w-48"
              >
                <SelectItem key="" value="">Todos os jogos</SelectItem>
                {popularGames.map((game) => (
                  <SelectItem key={game.id} value={game.id}>
                    {game.icon} {game.name}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Popular Games */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Jogos Populares</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="flex flex-wrap gap-2">
              {popularGames.map((game) => (
                <Chip
                  key={game.id}
                  variant={selectedGame === game.id ? 'solid' : 'flat'}
                  color={game.color as any}
                  className="cursor-pointer"
                  onClick={() => setSelectedGame(selectedGame === game.id ? '' : game.id)}
                >
                  {game.icon} {game.name}
                </Chip>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab key="discover" title="Descobrir">
            {loading ? (
              <div className="flex justify-center py-8">
                <Spinner size="lg" />
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredGroups.map((group) => {
                  const activity = getActivityLevel(group.stats);
                  const isJoined = group.members.includes(currentUser.uid);
                  
                  return (
                    <Card key={group.id} className="hover:scale-105 transition-transform">
                      <CardHeader className="flex gap-3">
                        <Avatar
                          src={group.avatar}
                          name={group.name}
                          size="md"
                          className="bg-gradient-to-r from-primary to-secondary"
                        />
                        <div className="flex flex-col flex-1">
                          <div className="flex items-center gap-2">
                            <p className="text-md font-semibold">{group.name}</p>
                            {getPrivacyIcon(group.privacy)}
                          </div>
                          <div className="flex items-center gap-2">
                            <Chip size="sm" variant="flat" color="primary">
                              {group.gameTitle}
                            </Chip>
                            <Chip
                              size="sm"
                              variant="flat"
                              color={group.category === 'competitive' ? 'warning' : 'default'}
                              startContent={getCategoryIcon(group.category)}
                            >
                              {group.category}
                            </Chip>
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardBody className="pt-0">
                        <p className="text-sm text-default-600 mb-3 line-clamp-2">
                          {group.description}
                        </p>
                        
                        <div className="space-y-2 mb-4">
                          <div className="flex justify-between text-sm">
                            <span>Atividade</span>
                            <span className={`text-${activity.color}`}>{activity.level}</span>
                          </div>
                          <Progress
                            value={activity.value}
                            color={activity.color as any}
                            size="sm"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-default-500">
                            <div className="flex items-center gap-1">
                              <UsersIcon className="w-4 h-4" />
                              {group.memberCount}
                              {group.maxMembers && `/${group.maxMembers}`}
                            </div>
                            <div className="flex items-center gap-1">
                              <ChatBubbleOvalLeftIcon className="w-4 h-4" />
                              {group.stats.totalPosts}
                            </div>
                          </div>
                          
                          {isJoined ? (
                            <Badge content="Membro" color="success">
                              <Button size="sm" variant="flat" color="success">
                                Entrar
                              </Button>
                            </Badge>
                          ) : (
                            <Button
                              size="sm"
                              color="primary"
                              variant="flat"
                              onPress={() => handleJoinGroup(group.id)}
                            >
                              Participar
                            </Button>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  );
                })}
              </div>
            )}
          </Tab>
          
          <Tab key="my-groups" title="Meus Grupos">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {myGroups.map((group) => (
                <Card key={group.id} className="hover:scale-105 transition-transform">
                  <CardHeader className="flex gap-3">
                    <Avatar
                      src={group.avatar}
                      name={group.name}
                      size="md"
                      className="bg-gradient-to-r from-success to-primary"
                    />
                    <div className="flex flex-col flex-1">
                      <p className="text-md font-semibold">{group.name}</p>
                      <div className="flex items-center gap-2">
                        <Chip size="sm" variant="flat" color="success">
                          {group.gameTitle}
                        </Chip>
                        {group.moderators.includes(currentUser.uid) && (
                          <Chip size="sm" variant="flat" color="warning">
                            Moderador
                          </Chip>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardBody className="pt-0">
                    <div className="flex items-center justify-between text-sm text-default-500 mb-3">
                      <div className="flex items-center gap-1">
                        <UsersIcon className="w-4 h-4" />
                        {group.memberCount} membros
                      </div>
                      <div className="flex items-center gap-1">
                        <ChatBubbleOvalLeftIcon className="w-4 h-4" />
                        {group.stats.totalPosts} posts
                      </div>
                    </div>
                    
                    <Button size="sm" color="primary" fullWidth>
                      Abrir Grupo
                    </Button>
                  </CardBody>
                </Card>
              ))}
              
              {myGroups.length === 0 && (
                <div className="col-span-full text-center py-12">
                  <UsersIcon className="w-12 h-12 mx-auto text-default-300 mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Nenhum grupo ainda</h3>
                  <p className="text-default-500 mb-4">
                    Participe de grupos para encontrar outros gamers
                  </p>
                  <Button color="primary" onPress={() => setSelectedTab('discover')}>
                    Descobrir Grupos
                  </Button>
                </div>
              )}
            </div>
          </Tab>
        </Tabs>

        {/* Create Group Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="2xl" scrollBehavior="inside">
          <ModalContent>
            <ModalHeader>Criar Novo Grupo</ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <Input
                  label="Nome do Grupo"
                  placeholder="Ex: Squad CS2 Competitivo"
                  value={newGroup.name}
                  onChange={(e) => setNewGroup({ ...newGroup, name: e.target.value })}
                  isRequired
                />
                
                <Textarea
                  label="Descrição"
                  placeholder="Descreva o propósito do seu grupo..."
                  value={newGroup.description}
                  onChange={(e) => setNewGroup({ ...newGroup, description: e.target.value })}
                  minRows={3}
                />
                
                <Select
                  label="Jogo Principal"
                  placeholder="Selecione o jogo"
                  value={newGroup.gameTitle}
                  onChange={(e) => setNewGroup({ ...newGroup, gameTitle: e.target.value })}
                  isRequired
                >
                  {popularGames.map((game) => (
                    <SelectItem key={game.name} value={game.name}>
                      {game.icon} {game.name}
                    </SelectItem>
                  ))}
                </Select>
                
                <div className="grid grid-cols-2 gap-4">
                  <Select
                    label="Categoria"
                    value={newGroup.category}
                    onChange={(e) => setNewGroup({ ...newGroup, category: e.target.value as any })}
                  >
                    <SelectItem key="casual" value="casual">Casual</SelectItem>
                    <SelectItem key="competitive" value="competitive">Competitivo</SelectItem>
                    <SelectItem key="professional" value="professional">Profissional</SelectItem>
                    <SelectItem key="community" value="community">Comunidade</SelectItem>
                  </Select>
                  
                  <Select
                    label="Privacidade"
                    value={newGroup.privacy}
                    onChange={(e) => setNewGroup({ ...newGroup, privacy: e.target.value as any })}
                  >
                    <SelectItem key="public" value="public">Público</SelectItem>
                    <SelectItem key="invite-only" value="invite-only">Apenas Convite</SelectItem>
                    <SelectItem key="private" value="private">Privado</SelectItem>
                  </Select>
                </div>
                
                <Input
                  type="number"
                  label="Máximo de Membros"
                  value={newGroup.maxMembers.toString()}
                  onChange={(e) => setNewGroup({ ...newGroup, maxMembers: parseInt(e.target.value) || 50 })}
                  min={5}
                  max={500}
                />
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                onPress={handleCreateGroup}
                isDisabled={!newGroup.name || !newGroup.gameTitle}
              >
                Criar Grupo
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

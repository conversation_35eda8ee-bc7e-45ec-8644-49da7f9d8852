import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner,
  Tabs,
  Tab,
  Progress,
  Divider,
  Badge,
  Select,
  SelectItem,
  DatePicker,
} from '@heroui/react';
import {
  TrophyIcon,
  CalendarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  FireIcon,
  ClockIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { Tournament } from '@/types/database';
import DefaultLayout from '@/layouts/default';

// Mock data for tournaments
const mockTournaments: Tournament[] = [
  {
    id: '1',
    name: 'Copa GameHub CS2',
    description: 'Torneio oficial de Counter-Strike 2 com premiação de R$ 10.000',
    gameTitle: 'Counter-Strike 2',
    platform: 'PC',
    format: 'single-elimination',
    type: 'team',
    maxParticipants: 32,
    currentParticipants: 28,
    prizePool: 10000,
    currency: 'BRL',
    entryFee: 50,
    status: 'registration',
    createdBy: 'admin',
    organizers: ['admin'],
    participants: [],
    rules: ['Equipes de 5 jogadores', 'Mapas oficiais apenas', 'Anti-cheat obrigatório'],
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) as any,
    endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) as any,
    registrationDeadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) as any,
    createdAt: new Date() as any,
    updatedAt: new Date() as any,
  },
  {
    id: '2',
    name: 'Valorant Champions League',
    description: 'Liga competitiva de Valorant com temporadas mensais',
    gameTitle: 'Valorant',
    platform: 'PC',
    format: 'round-robin',
    type: 'team',
    maxParticipants: 16,
    currentParticipants: 12,
    prizePool: 5000,
    currency: 'BRL',
    status: 'ongoing',
    createdBy: 'admin',
    organizers: ['admin'],
    participants: [],
    rules: ['Equipes de 5 jogadores', 'Mapas competitivos', 'Streaming obrigatório'],
    startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) as any,
    endDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000) as any,
    registrationDeadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) as any,
    createdAt: new Date() as any,
    updatedAt: new Date() as any,
  },
  {
    id: '3',
    name: 'LoL Solo Queue Challenge',
    description: 'Desafio individual de League of Legends com ranking especial',
    gameTitle: 'League of Legends',
    platform: 'PC',
    format: 'swiss',
    type: 'solo',
    maxParticipants: 64,
    currentParticipants: 45,
    prizePool: 3000,
    currency: 'BRL',
    status: 'upcoming',
    createdBy: 'admin',
    organizers: ['admin'],
    participants: [],
    rules: ['Apenas jogadores Solo/Duo', 'Rank mínimo: Ouro', 'Partidas ranqueadas'],
    startDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000) as any,
    endDate: new Date(Date.now() + 17 * 24 * 60 * 60 * 1000) as any,
    registrationDeadline: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000) as any,
    createdAt: new Date() as any,
    updatedAt: new Date() as any,
  },
];

export default function TournamentsPage() {
  const { currentUser, userProfile } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [tournaments, setTournaments] = useState<Tournament[]>(mockTournaments);
  const [loading, setLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'primary';
      case 'registration': return 'warning';
      case 'ongoing': return 'success';
      case 'completed': return 'default';
      case 'cancelled': return 'danger';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'upcoming': return 'Em Breve';
      case 'registration': return 'Inscrições Abertas';
      case 'ongoing': return 'Em Andamento';
      case 'completed': return 'Finalizado';
      case 'cancelled': return 'Cancelado';
      default: return status;
    }
  };

  const getFormatText = (format: string) => {
    switch (format) {
      case 'single-elimination': return 'Eliminação Simples';
      case 'double-elimination': return 'Eliminação Dupla';
      case 'round-robin': return 'Todos contra Todos';
      case 'swiss': return 'Sistema Suíço';
      default: return format;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'solo': return 'Individual';
      case 'duo': return 'Dupla';
      case 'team': return 'Equipe';
      default: return type;
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: currency === 'BRL' ? 'BRL' : 'USD',
    }).format(amount);
  };

  const formatDate = (date: any) => {
    const d = date?.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDaysUntil = (date: any) => {
    const d = date?.toDate ? date.toDate() : new Date(date);
    const now = new Date();
    const diffTime = d.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const filteredTournaments = tournaments.filter(tournament => {
    const matchesSearch = tournament.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tournament.gameTitle.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (selectedTab === 'all') return matchesSearch;
    if (selectedTab === 'open') return matchesSearch && tournament.status === 'registration';
    if (selectedTab === 'ongoing') return matchesSearch && tournament.status === 'ongoing';
    if (selectedTab === 'upcoming') return matchesSearch && tournament.status === 'upcoming';
    
    return matchesSearch;
  });

  const handleJoinTournament = (tournamentId: string) => {
    // Implementation for joining tournament
    console.log('Joining tournament:', tournamentId);
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver os torneios</h2>
              <p className="text-default-500">
                Entre na sua conta para participar de torneios e competições
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <TrophyIcon className="w-8 h-8 text-warning" />
              Torneios
            </h1>
            <p className="text-default-500">Compete e mostre suas habilidades</p>
          </div>
          <Button
            color="primary"
            startContent={<PlusIcon className="w-5 h-5" />}
            onPress={onOpen}
          >
            Criar Torneio
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-r from-warning/10 to-warning/5">
            <CardBody className="text-center p-4">
              <TrophyIcon className="w-8 h-8 mx-auto text-warning mb-2" />
              <div className="text-2xl font-bold text-warning">{tournaments.length}</div>
              <div className="text-sm text-default-500">Torneios Ativos</div>
            </CardBody>
          </Card>
          
          <Card className="bg-gradient-to-r from-success/10 to-success/5">
            <CardBody className="text-center p-4">
              <CurrencyDollarIcon className="w-8 h-8 mx-auto text-success mb-2" />
              <div className="text-2xl font-bold text-success">
                {formatCurrency(tournaments.reduce((sum, t) => sum + (t.prizePool || 0), 0), 'BRL')}
              </div>
              <div className="text-sm text-default-500">Premiação Total</div>
            </CardBody>
          </Card>
          
          <Card className="bg-gradient-to-r from-primary/10 to-primary/5">
            <CardBody className="text-center p-4">
              <UsersIcon className="w-8 h-8 mx-auto text-primary mb-2" />
              <div className="text-2xl font-bold text-primary">
                {tournaments.reduce((sum, t) => sum + t.currentParticipants, 0)}
              </div>
              <div className="text-sm text-default-500">Participantes</div>
            </CardBody>
          </Card>
          
          <Card className="bg-gradient-to-r from-secondary/10 to-secondary/5">
            <CardBody className="text-center p-4">
              <FireIcon className="w-8 h-8 mx-auto text-secondary mb-2" />
              <div className="text-2xl font-bold text-secondary">
                {tournaments.filter(t => t.status === 'ongoing').length}
              </div>
              <div className="text-sm text-default-500">Em Andamento</div>
            </CardBody>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardBody className="p-4">
            <Input
              placeholder="Buscar torneios..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
            />
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab key="all" title="Todos">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredTournaments.map((tournament) => {
                const daysUntil = getDaysUntil(tournament.startDate);
                const registrationOpen = tournament.status === 'registration';
                const canJoin = registrationOpen && tournament.currentParticipants < tournament.maxParticipants;
                
                return (
                  <Card key={tournament.id} className="hover:scale-[1.02] transition-transform">
                    <CardHeader className="flex gap-3">
                      <div className="relative">
                        <Avatar
                          name={tournament.gameTitle}
                          size="lg"
                          className="bg-gradient-to-r from-primary to-secondary"
                        />
                        <Badge
                          content={<TrophyIcon className="w-3 h-3" />}
                          color="warning"
                          placement="bottom-right"
                        />
                      </div>
                      
                      <div className="flex flex-col flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold">{tournament.name}</h3>
                          <Chip
                            size="sm"
                            color={getStatusColor(tournament.status) as any}
                            variant="flat"
                          >
                            {getStatusText(tournament.status)}
                          </Chip>
                        </div>
                        
                        <div className="flex items-center gap-2 mt-1">
                          <Chip size="sm" variant="flat" color="primary">
                            {tournament.gameTitle}
                          </Chip>
                          <Chip size="sm" variant="flat" color="default">
                            {getTypeText(tournament.type)}
                          </Chip>
                          <Chip size="sm" variant="flat" color="secondary">
                            {getFormatText(tournament.format)}
                          </Chip>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardBody className="pt-0">
                      <p className="text-sm text-default-600 mb-4 line-clamp-2">
                        {tournament.description}
                      </p>
                      
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <CurrencyDollarIcon className="w-4 h-4 text-success" />
                            <span className="font-semibold text-success">
                              {tournament.prizePool ? formatCurrency(tournament.prizePool, tournament.currency || 'BRL') : 'Gratuito'}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <UsersIcon className="w-4 h-4 text-primary" />
                            <span>
                              {tournament.currentParticipants}/{tournament.maxParticipants} participantes
                            </span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <CalendarIcon className="w-4 h-4 text-warning" />
                            <span>
                              {daysUntil > 0 ? `${daysUntil} dias` : 'Hoje'}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 text-sm">
                            <ClockIcon className="w-4 h-4 text-secondary" />
                            <span>{formatDate(tournament.startDate)}</span>
                          </div>
                        </div>
                      </div>
                      
                      {tournament.status === 'registration' && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Inscrições</span>
                            <span>{tournament.currentParticipants}/{tournament.maxParticipants}</span>
                          </div>
                          <Progress
                            value={(tournament.currentParticipants / tournament.maxParticipants) * 100}
                            color="primary"
                            size="sm"
                          />
                        </div>
                      )}
                      
                      <Divider className="my-4" />
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {tournament.entryFee && (
                            <Chip size="sm" variant="flat" color="warning">
                              Taxa: {formatCurrency(tournament.entryFee, tournament.currency || 'BRL')}
                            </Chip>
                          )}
                        </div>
                        
                        <div className="flex gap-2">
                          <Button size="sm" variant="flat">
                            Ver Detalhes
                          </Button>
                          
                          {canJoin ? (
                            <Button
                              size="sm"
                              color="primary"
                              startContent={<PlayIcon className="w-4 h-4" />}
                              onPress={() => handleJoinTournament(tournament.id)}
                            >
                              Participar
                            </Button>
                          ) : tournament.status === 'ongoing' ? (
                            <Button
                              size="sm"
                              color="success"
                              variant="flat"
                              startContent={<PlayIcon className="w-4 h-4" />}
                            >
                              Assistir
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="flat"
                              isDisabled
                              startContent={
                                tournament.currentParticipants >= tournament.maxParticipants ? 
                                <XCircleIcon className="w-4 h-4" /> : 
                                <CheckCircleIcon className="w-4 h-4" />
                              }
                            >
                              {tournament.currentParticipants >= tournament.maxParticipants ? 'Lotado' : 'Fechado'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                );
              })}
            </div>
          </Tab>
          
          <Tab key="open" title="Inscrições Abertas">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredTournaments.filter(t => t.status === 'registration').map((tournament) => (
                <Card key={tournament.id} className="border-2 border-warning/20">
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <TrophyIcon className="w-6 h-6 text-warning" />
                      <h3 className="text-lg font-semibold">{tournament.name}</h3>
                      <Chip size="sm" color="warning" variant="flat">
                        Inscrições Abertas
                      </Chip>
                    </div>
                  </CardHeader>
                  <CardBody>
                    <p className="text-sm text-default-600 mb-4">{tournament.description}</p>
                    <div className="flex justify-between items-center">
                      <div className="text-sm">
                        <div className="font-semibold text-success">
                          {tournament.prizePool ? formatCurrency(tournament.prizePool, tournament.currency || 'BRL') : 'Gratuito'}
                        </div>
                        <div className="text-default-500">
                          {tournament.currentParticipants}/{tournament.maxParticipants} participantes
                        </div>
                      </div>
                      <Button
                        color="warning"
                        startContent={<PlayIcon className="w-4 h-4" />}
                        onPress={() => handleJoinTournament(tournament.id)}
                      >
                        Inscrever-se
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="ongoing" title="Em Andamento">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredTournaments.filter(t => t.status === 'ongoing').map((tournament) => (
                <Card key={tournament.id} className="border-2 border-success/20">
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full animate-pulse" />
                      <h3 className="text-lg font-semibold">{tournament.name}</h3>
                      <Chip size="sm" color="success" variant="flat">
                        Ao Vivo
                      </Chip>
                    </div>
                  </CardHeader>
                  <CardBody>
                    <p className="text-sm text-default-600 mb-4">{tournament.description}</p>
                    <div className="flex justify-between items-center">
                      <div className="text-sm">
                        <div className="font-semibold text-success">
                          {tournament.prizePool ? formatCurrency(tournament.prizePool, tournament.currency || 'BRL') : 'Gratuito'}
                        </div>
                        <div className="text-default-500">
                          {tournament.currentParticipants} participantes
                        </div>
                      </div>
                      <Button
                        color="success"
                        variant="flat"
                        startContent={<PlayIcon className="w-4 h-4" />}
                      >
                        Assistir Ao Vivo
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="upcoming" title="Em Breve">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredTournaments.filter(t => t.status === 'upcoming').map((tournament) => {
                const daysUntil = getDaysUntil(tournament.startDate);
                
                return (
                  <Card key={tournament.id} className="border-2 border-primary/20">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="w-6 h-6 text-primary" />
                        <h3 className="text-lg font-semibold">{tournament.name}</h3>
                        <Chip size="sm" color="primary" variant="flat">
                          {daysUntil} dias
                        </Chip>
                      </div>
                    </CardHeader>
                    <CardBody>
                      <p className="text-sm text-default-600 mb-4">{tournament.description}</p>
                      <div className="flex justify-between items-center">
                        <div className="text-sm">
                          <div className="font-semibold text-success">
                            {tournament.prizePool ? formatCurrency(tournament.prizePool, tournament.currency || 'BRL') : 'Gratuito'}
                          </div>
                          <div className="text-default-500">
                            Início: {formatDate(tournament.startDate)}
                          </div>
                        </div>
                        <Button
                          color="primary"
                          variant="flat"
                          startContent={<CalendarIcon className="w-4 h-4" />}
                        >
                          Lembrar-me
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                );
              })}
            </div>
          </Tab>
        </Tabs>

        {/* Create Tournament Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="3xl" scrollBehavior="inside">
          <ModalContent>
            <ModalHeader>Criar Novo Torneio</ModalHeader>
            <ModalBody>
              <div className="text-center py-8">
                <TrophyIcon className="w-16 h-16 mx-auto text-warning mb-4" />
                <h3 className="text-lg font-semibold mb-2">Funcionalidade em Desenvolvimento</h3>
                <p className="text-default-500">
                  A criação de torneios estará disponível em breve. 
                  Entre em contato com nossa equipe para organizar torneios oficiais.
                </p>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Fechar
              </Button>
              <Button color="primary" onPress={onClose}>
                Entrar em Contato
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

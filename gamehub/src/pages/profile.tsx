import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Avatar,
  Button,
  Chip,
  Progress,
  Tabs,
  Tab,
  Badge,
  Divider,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
} from '@heroui/react';
import {
  PencilIcon,
  TrophyIcon,
  StarIcon,
  FireIcon,
  CalendarIcon,
  UsersIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  CogIcon,
  ShareIcon,
  PlusIcon,
  GamepadIcon,
  ChartBarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { CrownIcon } from '@heroicons/react/24/solid';

import { useAuth } from '@/contexts/AuthContext';
import DefaultLayout from '@/layouts/default';

// Mock user data
const mockUserStats = {
  totalGames: 1247,
  winRate: 73.2,
  hoursPlayed: 892,
  favoriteGame: 'Counter-Strike 2',
  rank: 'Global Elite',
  kda: 1.34,
  headshots: 68.5,
  clutches: 156,
  mvps: 89,
  achievements: [
    { id: 1, name: 'First Blood', description: 'Primeira kill em 100 partidas', icon: '🎯', rarity: 'common', unlockedAt: '2024-01-15' },
    { id: 2, name: 'Ace Master', description: 'Fez 50 aces', icon: '🔥', rarity: 'rare', unlockedAt: '2024-02-20' },
    { id: 3, name: 'Clutch King', description: 'Venceu 100 clutches 1v2+', icon: '👑', rarity: 'epic', unlockedAt: '2024-03-10' },
    { id: 4, name: 'Tournament Champion', description: 'Venceu um torneio oficial', icon: '🏆', rarity: 'legendary', unlockedAt: '2024-03-25' },
  ],
  recentMatches: [
    { id: 1, game: 'CS2', result: 'win', score: '16-12', kda: '24/15/8', date: '2024-03-28', map: 'Dust2' },
    { id: 2, game: 'CS2', result: 'win', score: '16-9', kda: '19/12/5', date: '2024-03-28', map: 'Mirage' },
    { id: 3, game: 'CS2', result: 'loss', score: '14-16', kda: '18/20/7', date: '2024-03-27', map: 'Inferno' },
    { id: 4, game: 'CS2', result: 'win', score: '16-8', kda: '22/10/9', date: '2024-03-27', map: 'Cache' },
  ],
  favoriteGames: [
    { name: 'Counter-Strike 2', hours: 450, rank: 'Global Elite', winRate: 73.2 },
    { name: 'Valorant', hours: 280, rank: 'Immortal 2', winRate: 68.5 },
    { name: 'League of Legends', hours: 162, rank: 'Diamond 3', winRate: 71.8 },
  ],
  friends: [
    { id: 1, name: 'SkillMaster', avatar: '', status: 'online', game: 'CS2' },
    { id: 2, name: 'ProGamer2024', avatar: '', status: 'away', game: 'Valorant' },
    { id: 3, name: 'ElitePlayer', avatar: '', status: 'offline', game: null },
  ],
};

export default function ProfilePage() {
  const { currentUser, userProfile, updateUserProfile } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTab, setSelectedTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    displayName: userProfile?.displayName || '',
    bio: userProfile?.bio || '',
    favoriteGames: userProfile?.favoriteGames || [],
  });

  const handleSaveProfile = async () => {
    if (!currentUser) return;
    
    try {
      await updateUserProfile(editForm);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'warning';
      case 'epic': return 'secondary';
      case 'rare': return 'primary';
      case 'common': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'away': return 'warning';
      case 'offline': return 'default';
      default: return 'default';
    }
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver seu perfil</h2>
              <p className="text-default-500">
                Entre na sua conta para acessar e editar seu perfil
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Profile Header */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10">
          <CardBody className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              <div className="relative">
                <Avatar
                  src={userProfile?.photoURL}
                  name={userProfile?.displayName}
                  size="xl"
                  className="w-24 h-24 border-4 border-primary"
                />
                <Badge
                  content={<CrownIcon className="w-4 h-4" />}
                  color="warning"
                  placement="bottom-right"
                />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold">{userProfile?.displayName}</h1>
                  <Chip color="primary" variant="flat">
                    Nível {userProfile?.level || 15}
                  </Chip>
                  <Chip color="warning" variant="flat" startContent={<TrophyIcon className="w-4 h-4" />}>
                    Rank #156
                  </Chip>
                </div>
                
                <p className="text-default-600 mb-4">
                  {userProfile?.bio || 'Gamer apaixonado por FPS e estratégia. Sempre em busca da próxima vitória!'}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {(userProfile?.favoriteGames || ['Counter-Strike 2', 'Valorant']).map((game, index) => (
                    <Chip key={index} size="sm" variant="flat" color="secondary">
                      {game}
                    </Chip>
                  ))}
                </div>
                
                <div className="flex items-center gap-4 text-sm text-default-500">
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="w-4 h-4" />
                    Membro desde {new Date(userProfile?.createdAt?.toDate?.() || Date.now()).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}
                  </div>
                  <div className="flex items-center gap-1">
                    <UsersIcon className="w-4 h-4" />
                    {userProfile?.followers?.length || 0} seguidores
                  </div>
                  <div className="flex items-center gap-1">
                    <StarIcon className="w-4 h-4" />
                    {userProfile?.xp || 2400} XP
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-2">
                <Button
                  color="primary"
                  startContent={<PencilIcon className="w-4 h-4" />}
                  onPress={() => setIsEditing(true)}
                >
                  Editar Perfil
                </Button>
                <Button
                  variant="flat"
                  startContent={<ShareIcon className="w-4 h-4" />}
                >
                  Compartilhar
                </Button>
              </div>
            </div>
            
            {/* XP Progress */}
            <div className="mt-6">
              <div className="flex justify-between text-sm mb-2">
                <span>Progresso para Nível {(userProfile?.level || 15) + 1}</span>
                <span>{(userProfile?.xp || 2400) % 1000}/1000 XP</span>
              </div>
              <Progress
                value={((userProfile?.xp || 2400) % 1000) / 10}
                color="primary"
                size="md"
                className="mb-2"
              />
            </div>
          </CardBody>
        </Card>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardBody className="text-center p-4">
              <GamepadIcon className="w-8 h-8 mx-auto text-primary mb-2" />
              <div className="text-2xl font-bold">{mockUserStats.totalGames}</div>
              <div className="text-sm text-default-500">Partidas</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center p-4">
              <TrophyIcon className="w-8 h-8 mx-auto text-success mb-2" />
              <div className="text-2xl font-bold">{mockUserStats.winRate}%</div>
              <div className="text-sm text-default-500">Win Rate</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center p-4">
              <ClockIcon className="w-8 h-8 mx-auto text-warning mb-2" />
              <div className="text-2xl font-bold">{mockUserStats.hoursPlayed}h</div>
              <div className="text-sm text-default-500">Jogadas</div>
            </CardBody>
          </Card>
          
          <Card>
            <CardBody className="text-center p-4">
              <StarIcon className="w-8 h-8 mx-auto text-secondary mb-2" />
              <div className="text-2xl font-bold">{mockUserStats.achievements.length}</div>
              <div className="text-sm text-default-500">Conquistas</div>
            </CardBody>
          </Card>
        </div>

        {/* Tabs Content */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab key="overview" title="Visão Geral">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Matches */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Partidas Recentes</h3>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="space-y-3">
                    {mockUserStats.recentMatches.map((match) => (
                      <div key={match.id} className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${match.result === 'win' ? 'bg-success' : 'bg-danger'}`} />
                          <div>
                            <div className="font-semibold">{match.game} - {match.map}</div>
                            <div className="text-sm text-default-500">{match.date}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{match.score}</div>
                          <div className="text-sm text-default-500">{match.kda}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* Favorite Games */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Jogos Favoritos</h3>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="space-y-4">
                    {mockUserStats.favoriteGames.map((game, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-semibold">{game.name}</div>
                            <div className="text-sm text-default-500">{game.rank}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">{game.hours}h</div>
                            <div className="text-sm text-success">{game.winRate}% WR</div>
                          </div>
                        </div>
                        <Progress
                          value={game.winRate}
                          color="success"
                          size="sm"
                        />
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
          </Tab>

          <Tab key="achievements" title="Conquistas">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockUserStats.achievements.map((achievement) => (
                <Card key={achievement.id} className="hover:scale-105 transition-transform">
                  <CardBody className="text-center p-6">
                    <div className="text-4xl mb-3">{achievement.icon}</div>
                    <h4 className="font-bold mb-2">{achievement.name}</h4>
                    <p className="text-sm text-default-600 mb-3">{achievement.description}</p>
                    <Chip
                      size="sm"
                      color={getRarityColor(achievement.rarity) as any}
                      variant="flat"
                    >
                      {achievement.rarity}
                    </Chip>
                    <div className="text-xs text-default-500 mt-2">
                      Desbloqueado em {new Date(achievement.unlockedAt).toLocaleDateString('pt-BR')}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>

          <Tab key="friends" title="Amigos">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockUserStats.friends.map((friend) => (
                <Card key={friend.id}>
                  <CardBody className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar
                          src={friend.avatar}
                          name={friend.name}
                          size="md"
                        />
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white bg-${getStatusColor(friend.status)}`} />
                      </div>
                      
                      <div className="flex-1">
                        <div className="font-semibold">{friend.name}</div>
                        <div className="text-sm text-default-500">
                          {friend.status === 'online' && friend.game ? `Jogando ${friend.game}` : 
                           friend.status === 'away' ? 'Ausente' : 'Offline'}
                        </div>
                      </div>
                      
                      <Button size="sm" variant="flat">
                        Ver Perfil
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>

          <Tab key="stats" title="Estatísticas">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">CS2 Stats</h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>K/D/A</span>
                      <span className="font-semibold">{mockUserStats.kda}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Headshot %</span>
                      <span className="font-semibold">{mockUserStats.headshots}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Clutches</span>
                      <span className="font-semibold">{mockUserStats.clutches}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>MVPs</span>
                      <span className="font-semibold">{mockUserStats.mvps}</span>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Progresso Mensal</h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>XP Ganho</span>
                        <span>2,400 / 5,000</span>
                      </div>
                      <Progress value={48} color="primary" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Partidas Vencidas</span>
                        <span>45 / 60</span>
                      </div>
                      <Progress value={75} color="success" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Conquistas</span>
                        <span>3 / 5</span>
                      </div>
                      <Progress value={60} color="warning" />
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </Tab>
        </Tabs>

        {/* Edit Profile Modal */}
        <Modal isOpen={isEditing} onClose={() => setIsEditing(false)} size="2xl">
          <ModalContent>
            <ModalHeader>Editar Perfil</ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <Input
                  label="Nome de Usuário"
                  value={editForm.displayName}
                  onChange={(e) => setEditForm({ ...editForm, displayName: e.target.value })}
                />
                
                <Textarea
                  label="Bio"
                  placeholder="Conte um pouco sobre você..."
                  value={editForm.bio}
                  onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                  minRows={3}
                />
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Jogos Favoritos</label>
                  <div className="flex flex-wrap gap-2">
                    {['Counter-Strike 2', 'Valorant', 'League of Legends', 'Fortnite', 'Apex Legends'].map((game) => (
                      <Chip
                        key={game}
                        variant={editForm.favoriteGames.includes(game) ? 'solid' : 'flat'}
                        color="primary"
                        className="cursor-pointer"
                        onClick={() => {
                          const newGames = editForm.favoriteGames.includes(game)
                            ? editForm.favoriteGames.filter(g => g !== game)
                            : [...editForm.favoriteGames, game];
                          setEditForm({ ...editForm, favoriteGames: newGames });
                        }}
                      >
                        {game}
                      </Chip>
                    ))}
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setIsEditing(false)}>
                Cancelar
              </Button>
              <Button color="primary" onPress={handleSaveProfile}>
                Salvar
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Avatar,
  Chip,
  Progress,
  Tabs,
  Tab,
  Button,
  Badge,
  Divider,
  Select,
  SelectItem,
} from '@heroui/react';
import {
  TrophyIcon,
  StarIcon,
  FireIcon,
  BoltIcon,
  ChartBarIcon,
  UsersIcon,
  GamepadIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { TrophyIcon as CrownIcon } from '@heroicons/react/24/solid';

import { useAuth } from '@/contexts/AuthContext';
import DefaultLayout from '@/layouts/default';

// Mock ranking data
const mockRankingData = [
  {
    id: '1',
    rank: 1,
    userId: 'user1',
    displayName: 'ProGamer2024',
    avatar: '',
    level: 87,
    xp: 125400,
    totalScore: 9850,
    weeklyScore: 1250,
    monthlyScore: 4800,
    achievements: 45,
    tournamentsWon: 12,
    favoriteGame: 'Counter-Strike 2',
    winRate: 78.5,
    streak: 15,
    tier: 'Legendary',
    badges: ['Champion', 'Veteran', 'Clutch Master'],
  },
  {
    id: '2',
    rank: 2,
    userId: 'user2',
    displayName: 'SkillMaster',
    avatar: '',
    level: 82,
    xp: 118200,
    totalScore: 9420,
    weeklyScore: 980,
    monthlyScore: 4200,
    achievements: 38,
    tournamentsWon: 8,
    favoriteGame: 'Valorant',
    winRate: 74.2,
    streak: 8,
    tier: 'Master',
    badges: ['Sharpshooter', 'Strategist'],
  },
  {
    id: '3',
    rank: 3,
    userId: 'user3',
    displayName: 'ElitePlayer',
    avatar: '',
    level: 79,
    xp: 112800,
    totalScore: 8950,
    weeklyScore: 850,
    monthlyScore: 3900,
    achievements: 42,
    tournamentsWon: 6,
    favoriteGame: 'League of Legends',
    winRate: 71.8,
    streak: 12,
    tier: 'Master',
    badges: ['Tactician', 'Team Player'],
  },
];

// Add more mock users for demonstration
for (let i = 4; i <= 50; i++) {
  mockRankingData.push({
    id: `user${i}`,
    rank: i,
    userId: `user${i}`,
    displayName: `Player${i}`,
    avatar: '',
    level: Math.floor(Math.random() * 50) + 30,
    xp: Math.floor(Math.random() * 50000) + 50000,
    totalScore: Math.floor(Math.random() * 5000) + 3000,
    weeklyScore: Math.floor(Math.random() * 500) + 100,
    monthlyScore: Math.floor(Math.random() * 2000) + 1000,
    achievements: Math.floor(Math.random() * 30) + 10,
    tournamentsWon: Math.floor(Math.random() * 5),
    favoriteGame: ['Counter-Strike 2', 'Valorant', 'League of Legends', 'Fortnite'][Math.floor(Math.random() * 4)],
    winRate: Math.floor(Math.random() * 30) + 50,
    streak: Math.floor(Math.random() * 10),
    tier: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Master'][Math.floor(Math.random() * 6)],
    badges: ['Rookie', 'Dedicated', 'Active'],
  });
}

export default function RankingPage() {
  const { currentUser, userProfile } = useAuth();
  const [selectedTab, setSelectedTab] = useState('overall');
  const [selectedPeriod, setSelectedPeriod] = useState('all-time');
  const [selectedGame, setSelectedGame] = useState('all');

  const getTierColor = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'legendary': return 'warning';
      case 'master': return 'secondary';
      case 'diamond': return 'primary';
      case 'platinum': return 'success';
      case 'gold': return 'warning';
      case 'silver': return 'default';
      case 'bronze': return 'danger';
      default: return 'default';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'legendary': return <CrownIcon className="w-4 h-4" />;
      case 'master': return <TrophyIcon className="w-4 h-4" />;
      case 'diamond': return <StarIcon className="w-4 h-4" />;
      default: return <ChartBarIcon className="w-4 h-4" />;
    }
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <CrownIcon className="w-6 h-6 text-warning" />;
    if (rank === 2) return <TrophyIcon className="w-6 h-6 text-default-400" />;
    if (rank === 3) return <TrophyIcon className="w-6 h-6 text-amber-600" />;
    return <span className="text-lg font-bold text-default-500">#{rank}</span>;
  };

  const currentUserRank = mockRankingData.find(user => user.userId === currentUser?.uid);
  const myRank = currentUserRank || {
    rank: 156,
    displayName: userProfile?.displayName || 'Você',
    level: userProfile?.level || 15,
    xp: userProfile?.xp || 2400,
    totalScore: 1250,
    tier: 'Silver',
    achievements: 8,
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver o ranking</h2>
              <p className="text-default-500">
                Entre na sua conta para ver sua posição no ranking global
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <TrophyIcon className="w-8 h-8 text-warning" />
            Ranking Global
          </h1>
          <p className="text-default-500 mt-2">Compete e suba no ranking da comunidade</p>
        </div>

        {/* My Rank Card */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-2 border-primary/20">
          <CardHeader>
            <div className="flex items-center gap-2">
              <StarIcon className="w-6 h-6 text-primary" />
              <h3 className="text-lg font-semibold">Sua Posição</h3>
            </div>
          </CardHeader>
          <CardBody>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">#{myRank.rank}</div>
                  <div className="text-sm text-default-500">Posição</div>
                </div>
                
                <Avatar
                  src={userProfile?.photoURL}
                  name={myRank.displayName}
                  size="lg"
                  className="border-2 border-primary"
                />
                
                <div>
                  <div className="font-semibold text-lg">{myRank.displayName}</div>
                  <div className="flex items-center gap-2">
                    <Chip
                      size="sm"
                      color={getTierColor(myRank.tier) as any}
                      variant="flat"
                      startContent={getTierIcon(myRank.tier)}
                    >
                      {myRank.tier}
                    </Chip>
                    <Chip size="sm" variant="flat" color="default">
                      Nível {myRank.level}
                    </Chip>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-2xl font-bold text-success">{myRank.totalScore}</div>
                <div className="text-sm text-default-500">Pontos Totais</div>
                <div className="text-sm text-warning">{myRank.achievements} conquistas</div>
              </div>
            </div>
            
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Progresso para próximo nível</span>
                <span>{myRank.xp % 1000}/1000 XP</span>
              </div>
              <Progress
                value={(myRank.xp % 1000) / 10}
                color="primary"
                size="sm"
              />
            </div>
          </CardBody>
        </Card>

        {/* Filters */}
        <Card>
          <CardBody className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <Select
                label="Período"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full md:w-48"
              >
                <SelectItem key="all-time" value="all-time">Todos os Tempos</SelectItem>
                <SelectItem key="monthly" value="monthly">Este Mês</SelectItem>
                <SelectItem key="weekly" value="weekly">Esta Semana</SelectItem>
                <SelectItem key="daily" value="daily">Hoje</SelectItem>
              </Select>
              
              <Select
                label="Jogo"
                value={selectedGame}
                onChange={(e) => setSelectedGame(e.target.value)}
                className="w-full md:w-48"
              >
                <SelectItem key="all" value="all">Todos os Jogos</SelectItem>
                <SelectItem key="cs2" value="cs2">Counter-Strike 2</SelectItem>
                <SelectItem key="valorant" value="valorant">Valorant</SelectItem>
                <SelectItem key="lol" value="lol">League of Legends</SelectItem>
                <SelectItem key="fortnite" value="fortnite">Fortnite</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Top 3 Podium */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <TrophyIcon className="w-6 h-6 text-warning" />
              Top 3 Jogadores
            </h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {mockRankingData.slice(0, 3).map((player, index) => (
                <Card
                  key={player.id}
                  className={`text-center ${
                    index === 0 ? 'bg-gradient-to-b from-warning/20 to-warning/5 border-2 border-warning/30' :
                    index === 1 ? 'bg-gradient-to-b from-default-200/20 to-default-100/5 border-2 border-default-200/30' :
                    'bg-gradient-to-b from-amber-600/20 to-amber-600/5 border-2 border-amber-600/30'
                  }`}
                >
                  <CardBody className="p-6">
                    <div className="relative mb-4">
                      <Avatar
                        src={player.avatar}
                        name={player.displayName}
                        size="xl"
                        className="mx-auto"
                      />
                      <Badge
                        content={getRankIcon(player.rank)}
                        placement="bottom-right"
                        className="bg-transparent"
                      />
                    </div>
                    
                    <h4 className="font-bold text-lg mb-2">{player.displayName}</h4>
                    
                    <div className="space-y-2">
                      <Chip
                        color={getTierColor(player.tier) as any}
                        variant="flat"
                        startContent={getTierIcon(player.tier)}
                      >
                        {player.tier}
                      </Chip>
                      
                      <div className="text-2xl font-bold text-success">
                        {player.totalScore.toLocaleString()}
                      </div>
                      <div className="text-sm text-default-500">pontos</div>
                      
                      <div className="flex justify-center gap-4 text-sm">
                        <div>
                          <div className="font-semibold">{player.tournamentsWon}</div>
                          <div className="text-default-500">vitórias</div>
                        </div>
                        <div>
                          <div className="font-semibold">{player.winRate}%</div>
                          <div className="text-default-500">win rate</div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Full Ranking */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab key="overall" title="Geral">
            <div className="space-y-2">
              {mockRankingData.slice(3, 20).map((player) => (
                <Card key={player.id} className="hover:bg-default-50 transition-colors">
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="text-center min-w-[40px]">
                          {getRankIcon(player.rank)}
                        </div>
                        
                        <Avatar
                          src={player.avatar}
                          name={player.displayName}
                          size="md"
                        />
                        
                        <div>
                          <div className="font-semibold">{player.displayName}</div>
                          <div className="flex items-center gap-2">
                            <Chip
                              size="sm"
                              color={getTierColor(player.tier) as any}
                              variant="flat"
                              startContent={getTierIcon(player.tier)}
                            >
                              {player.tier}
                            </Chip>
                            <span className="text-sm text-default-500">
                              Nível {player.level}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-6">
                        <div className="text-center">
                          <div className="font-bold text-success">
                            {player.totalScore.toLocaleString()}
                          </div>
                          <div className="text-xs text-default-500">pontos</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="font-bold text-warning">
                            {player.achievements}
                          </div>
                          <div className="text-xs text-default-500">conquistas</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="font-bold text-primary">
                            {player.winRate}%
                          </div>
                          <div className="text-xs text-default-500">win rate</div>
                        </div>
                        
                        {player.streak > 0 && (
                          <div className="text-center">
                            <Chip size="sm" color="danger" variant="flat">
                              <FireIcon className="w-3 h-3 mr-1" />
                              {player.streak}
                            </Chip>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="weekly" title="Semanal">
            <div className="space-y-2">
              {mockRankingData
                .sort((a, b) => b.weeklyScore - a.weeklyScore)
                .slice(0, 20)
                .map((player, index) => (
                  <Card key={player.id} className="hover:bg-default-50 transition-colors">
                    <CardBody className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="text-center min-w-[40px]">
                            <span className="text-lg font-bold text-default-500">#{index + 1}</span>
                          </div>
                          
                          <Avatar
                            src={player.avatar}
                            name={player.displayName}
                            size="md"
                          />
                          
                          <div>
                            <div className="font-semibold">{player.displayName}</div>
                            <div className="text-sm text-default-500">
                              {player.favoriteGame}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-6">
                          <div className="text-center">
                            <div className="font-bold text-success">
                              {player.weeklyScore.toLocaleString()}
                            </div>
                            <div className="text-xs text-default-500">pontos esta semana</div>
                          </div>
                          
                          <Chip size="sm" color="primary" variant="flat">
                            <BoltIcon className="w-3 h-3 mr-1" />
                            +{Math.floor(Math.random() * 200) + 50}
                          </Chip>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
            </div>
          </Tab>
          
          <Tab key="achievements" title="Conquistas">
            <div className="space-y-2">
              {mockRankingData
                .sort((a, b) => b.achievements - a.achievements)
                .slice(0, 20)
                .map((player, index) => (
                  <Card key={player.id} className="hover:bg-default-50 transition-colors">
                    <CardBody className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="text-center min-w-[40px]">
                            <span className="text-lg font-bold text-default-500">#{index + 1}</span>
                          </div>
                          
                          <Avatar
                            src={player.avatar}
                            name={player.displayName}
                            size="md"
                          />
                          
                          <div>
                            <div className="font-semibold">{player.displayName}</div>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {player.badges.slice(0, 3).map((badge, i) => (
                                <Chip key={i} size="sm" variant="flat" color="warning">
                                  {badge}
                                </Chip>
                              ))}
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <div className="font-bold text-warning text-2xl">
                            {player.achievements}
                          </div>
                          <div className="text-xs text-default-500">conquistas</div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
            </div>
          </Tab>
        </Tabs>

        {/* Load More */}
        <div className="text-center">
          <Button variant="flat" size="lg">
            Carregar Mais
          </Button>
        </div>
      </div>
    </DefaultLayout>
  );
}

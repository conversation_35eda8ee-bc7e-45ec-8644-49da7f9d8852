import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";
import {
  GamepadIcon,
  UsersIcon,
  TrophyIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon,
  FireIcon
} from "@heroicons/react/24/outline";

import { title, subtitle } from "@/components/primitives";
import { useAuth } from "@/contexts/AuthContext";
import DefaultLayout from "@/layouts/default";

export default function IndexPage() {
  const { currentUser } = useAuth();

  const features = [
    {
      icon: <GamepadIcon className="w-8 h-8" />,
      title: "Jogos Populares",
      description: "Conecte-se com jogadores de CS2, Valorant, LoL, Fortnite e muito mais",
      color: "primary" as const,
    },
    {
      icon: <UsersIcon className="w-8 h-8" />,
      title: "Grupos & Clãs",
      description: "Crie ou participe de grupos para encontrar parceiros de jogo",
      color: "secondary" as const,
    },
    {
      icon: <TrophyIcon className="w-8 h-8" />,
      title: "Torneios",
      description: "Participe de torneios e competições da comunidade",
      color: "warning" as const,
    },
    {
      icon: <ChatBubbleLeftRightIcon className="w-8 h-8" />,
      title: "Chat em Tempo Real",
      description: "Converse com outros gamers e compartilhe suas experiências",
      color: "success" as const,
    },
  ];

  const stats = [
    { label: "Gamers Ativos", value: "50K+", icon: <UsersIcon className="w-6 h-6" /> },
    { label: "Grupos", value: "1.2K+", icon: <GamepadIcon className="w-6 h-6" /> },
    { label: "Torneios", value: "200+", icon: <TrophyIcon className="w-6 h-6" /> },
    { label: "Posts Diários", value: "5K+", icon: <ChatBubbleLeftRightIcon className="w-6 h-6" /> },
  ];

  return (
    <DefaultLayout>
      {/* Hero Section */}
      <section className="flex flex-col items-center justify-center gap-6 py-12 md:py-16">
        <div className="text-center max-w-4xl">
          <div className="flex items-center justify-center gap-2 mb-4">
            <FireIcon className="w-8 h-8 text-orange-500" />
            <Chip color="primary" variant="flat" size="sm">
              Beta v1.0
            </Chip>
          </div>

          <h1 className={title({ size: "lg" })}>
            A maior comunidade{" "}
            <span className={title({ color: "violet", size: "lg" })}>gaming</span>
            {" "}do Brasil
          </h1>

          <p className={subtitle({ class: "mt-6 max-w-2xl mx-auto" })}>
            Conecte-se com milhares de gamers, forme equipes, participe de torneios
            e compartilhe suas conquistas na maior rede social gaming do país.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          {!currentUser ? (
            <>
              <Button
                color="primary"
                size="lg"
                radius="full"
                className="font-semibold"
                startContent={<SparklesIcon className="w-5 h-5" />}
              >
                Começar Agora
              </Button>
              <Button
                variant="bordered"
                size="lg"
                radius="full"
                className="font-semibold"
              >
                Explorar Comunidade
              </Button>
            </>
          ) : (
            <Button
              color="primary"
              size="lg"
              radius="full"
              className="font-semibold"
              startContent={<GamepadIcon className="w-5 h-5" />}
            >
              Ir para Feed
            </Button>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 border-y border-divider">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="flex items-center justify-center mb-2 text-primary">
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="text-sm text-default-500">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className={title({ size: "md" })}>
            Tudo que você precisa para{" "}
            <span className={title({ color: "blue", size: "md" })}>dominar</span>
          </h2>
          <p className={subtitle({ class: "mt-4" })}>
            Recursos desenvolvidos especialmente para a comunidade gaming
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="p-4 hover:scale-105 transition-transform">
              <CardHeader className="flex gap-3">
                <div className={`p-2 rounded-lg bg-${feature.color}/10 text-${feature.color}`}>
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{feature.title}</h3>
                </div>
              </CardHeader>
              <CardBody className="pt-0">
                <p className="text-default-600">{feature.description}</p>
              </CardBody>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      {!currentUser && (
        <section className="py-16 text-center">
          <Card className="p-8 bg-gradient-to-r from-primary/10 to-secondary/10">
            <CardBody>
              <h2 className={title({ size: "md" })}>
                Pronto para começar?
              </h2>
              <p className={subtitle({ class: "mt-4 mb-8" })}>
                Junte-se a milhares de gamers e leve sua experiência gaming para o próximo nível
              </p>
              <Button
                color="primary"
                size="lg"
                radius="full"
                className="font-semibold"
                startContent={<SparklesIcon className="w-5 h-5" />}
              >
                Criar Conta Grátis
              </Button>
            </CardBody>
          </Card>
        </section>
      )}
    </DefaultLayout>
  );
}

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Progress,
  Tabs,
  Tab,
  Badge,
  Input,
  Select,
  SelectItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@heroui/react';
import {
  TrophyIcon,
  StarIcon,
  FireIcon,
  BoltIcon,
  ShieldCheckIcon,
  CrownIcon as CrownOutlineIcon,
  MagnifyingGlassIcon,
  LockClosedIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { CrownIcon } from '@heroicons/react/24/solid';

import { useAuth } from '@/contexts/AuthContext';
import DefaultLayout from '@/layouts/default';

// Mock achievements data
const mockAchievements = [
  {
    id: 1,
    name: 'Primeiro Passo',
    description: 'Complete seu primeiro jogo',
    icon: '🎮',
    category: 'gaming',
    rarity: 'common',
    xpReward: 100,
    progress: 100,
    maxProgress: 1,
    unlocked: true,
    unlockedAt: '2024-01-15',
    requirements: 'Jogue uma partida completa',
  },
  {
    id: 2,
    name: 'Socialite',
    description: 'Faça 10 amigos na plataforma',
    icon: '👥',
    category: 'social',
    rarity: 'common',
    xpReward: 200,
    progress: 7,
    maxProgress: 10,
    unlocked: false,
    requirements: 'Adicione 10 jogadores como amigos',
  },
  {
    id: 3,
    name: 'Ace Master',
    description: 'Faça 50 aces em CS2',
    icon: '🔥',
    category: 'gaming',
    rarity: 'rare',
    xpReward: 500,
    progress: 23,
    maxProgress: 50,
    unlocked: false,
    requirements: 'Elimine todos os 5 inimigos em uma rodada',
  },
  {
    id: 4,
    name: 'Campeão',
    description: 'Vença seu primeiro torneio',
    icon: '🏆',
    category: 'tournament',
    rarity: 'epic',
    xpReward: 1000,
    progress: 0,
    maxProgress: 1,
    unlocked: false,
    requirements: 'Seja o vencedor de um torneio oficial',
  },
  {
    id: 5,
    name: 'Lenda Viva',
    description: 'Alcance o rank mais alto em 3 jogos diferentes',
    icon: '👑',
    category: 'gaming',
    rarity: 'legendary',
    xpReward: 2000,
    progress: 1,
    maxProgress: 3,
    unlocked: false,
    requirements: 'Rank máximo em CS2, Valorant e LoL',
  },
  {
    id: 6,
    name: 'Influenciador',
    description: 'Tenha 100 seguidores',
    icon: '⭐',
    category: 'social',
    rarity: 'rare',
    xpReward: 750,
    progress: 45,
    maxProgress: 100,
    unlocked: false,
    requirements: 'Ganhe 100 seguidores no seu perfil',
  },
  {
    id: 7,
    name: 'Veterano',
    description: 'Jogue por 365 dias consecutivos',
    icon: '🛡️',
    category: 'community',
    rarity: 'epic',
    xpReward: 1500,
    progress: 89,
    maxProgress: 365,
    unlocked: false,
    requirements: 'Login diário por 1 ano',
  },
  {
    id: 8,
    name: 'Criador de Conteúdo',
    description: 'Publique 100 posts',
    icon: '📝',
    category: 'social',
    rarity: 'rare',
    xpReward: 600,
    progress: 34,
    maxProgress: 100,
    unlocked: false,
    requirements: 'Crie e publique 100 posts na comunidade',
  },
];

export default function AchievementsPage() {
  const { currentUser, userProfile } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTab, setSelectedTab] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAchievement, setSelectedAchievement] = useState<any>(null);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'warning';
      case 'epic': return 'secondary';
      case 'rare': return 'primary';
      case 'common': return 'success';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'gaming': return <TrophyIcon className="w-4 h-4" />;
      case 'social': return <StarIcon className="w-4 h-4" />;
      case 'tournament': return <CrownOutlineIcon className="w-4 h-4" />;
      case 'community': return <ShieldCheckIcon className="w-4 h-4" />;
      default: return <BoltIcon className="w-4 h-4" />;
    }
  };

  const filteredAchievements = mockAchievements.filter(achievement => {
    const matchesSearch = achievement.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         achievement.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || achievement.category === selectedCategory;
    
    if (selectedTab === 'all') return matchesSearch && matchesCategory;
    if (selectedTab === 'unlocked') return matchesSearch && matchesCategory && achievement.unlocked;
    if (selectedTab === 'locked') return matchesSearch && matchesCategory && !achievement.unlocked;
    if (selectedTab === 'in-progress') return matchesSearch && matchesCategory && !achievement.unlocked && achievement.progress > 0;
    
    return matchesSearch && matchesCategory;
  });

  const unlockedCount = mockAchievements.filter(a => a.unlocked).length;
  const totalXP = mockAchievements.filter(a => a.unlocked).reduce((sum, a) => sum + a.xpReward, 0);
  const completionRate = (unlockedCount / mockAchievements.length) * 100;

  const handleAchievementClick = (achievement: any) => {
    setSelectedAchievement(achievement);
    onOpen();
  };

  if (!currentUser) {
    return (
      <DefaultLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center">
            <CardBody>
              <h2 className="text-xl font-semibold mb-4">Faça login para ver as conquistas</h2>
              <p className="text-default-500">
                Entre na sua conta para acompanhar seu progresso e desbloquear conquistas
              </p>
            </CardBody>
          </Card>
        </div>
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <TrophyIcon className="w-8 h-8 text-warning" />
            Conquistas
          </h1>
          <p className="text-default-500 mt-2">Desbloqueie conquistas e ganhe XP</p>
        </div>

        {/* Progress Overview */}
        <Card className="bg-gradient-to-r from-warning/10 to-warning/5">
          <CardBody className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-warning mb-2">{unlockedCount}/{mockAchievements.length}</div>
                <div className="text-sm text-default-500">Conquistas Desbloqueadas</div>
                <Progress
                  value={completionRate}
                  color="warning"
                  size="sm"
                  className="mt-2"
                />
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-success mb-2">{totalXP.toLocaleString()}</div>
                <div className="text-sm text-default-500">XP Total Ganho</div>
                <div className="flex items-center justify-center gap-1 mt-2">
                  <BoltIcon className="w-4 h-4 text-success" />
                  <span className="text-sm text-success">+{totalXP} XP</span>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">{Math.round(completionRate)}%</div>
                <div className="text-sm text-default-500">Taxa de Conclusão</div>
                <div className="flex justify-center gap-1 mt-2">
                  {['legendary', 'epic', 'rare', 'common'].map((rarity) => {
                    const count = mockAchievements.filter(a => a.rarity === rarity && a.unlocked).length;
                    return (
                      <Chip key={rarity} size="sm" color={getRarityColor(rarity) as any} variant="flat">
                        {count}
                      </Chip>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Filters */}
        <Card>
          <CardBody className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder="Buscar conquistas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                className="flex-1"
              />
              
              <Select
                label="Categoria"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full md:w-48"
              >
                <SelectItem key="all" value="all">Todas</SelectItem>
                <SelectItem key="gaming" value="gaming">Gaming</SelectItem>
                <SelectItem key="social" value="social">Social</SelectItem>
                <SelectItem key="tournament" value="tournament">Torneios</SelectItem>
                <SelectItem key="community" value="community">Comunidade</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab key="all" title={`Todas (${filteredAchievements.length})`}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAchievements.map((achievement) => (
                <Card
                  key={achievement.id}
                  className={`cursor-pointer hover:scale-105 transition-transform ${
                    achievement.unlocked ? 'bg-gradient-to-br from-success/10 to-success/5' : 
                    achievement.progress > 0 ? 'bg-gradient-to-br from-warning/10 to-warning/5' : ''
                  }`}
                  onPress={() => handleAchievementClick(achievement)}
                >
                  <CardBody className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="relative">
                        <div className={`text-3xl ${!achievement.unlocked && achievement.progress === 0 ? 'grayscale opacity-50' : ''}`}>
                          {achievement.icon}
                        </div>
                        {achievement.unlocked && (
                          <Badge
                            content={<CheckCircleIcon className="w-3 h-3" />}
                            color="success"
                            placement="bottom-right"
                          />
                        )}
                        {!achievement.unlocked && achievement.progress === 0 && (
                          <Badge
                            content={<LockClosedIcon className="w-3 h-3" />}
                            color="default"
                            placement="bottom-right"
                          />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-sm">{achievement.name}</h4>
                          <Chip
                            size="sm"
                            color={getRarityColor(achievement.rarity) as any}
                            variant="flat"
                          >
                            {achievement.rarity}
                          </Chip>
                        </div>
                        
                        <p className="text-xs text-default-600 mb-2 line-clamp-2">
                          {achievement.description}
                        </p>
                        
                        <div className="flex items-center gap-2 mb-2">
                          <Chip
                            size="sm"
                            variant="flat"
                            color="default"
                            startContent={getCategoryIcon(achievement.category)}
                          >
                            {achievement.category}
                          </Chip>
                          <Chip size="sm" variant="flat" color="success">
                            +{achievement.xpReward} XP
                          </Chip>
                        </div>
                        
                        {!achievement.unlocked && achievement.maxProgress > 1 && (
                          <div>
                            <div className="flex justify-between text-xs mb-1">
                              <span>Progresso</span>
                              <span>{achievement.progress}/{achievement.maxProgress}</span>
                            </div>
                            <Progress
                              value={(achievement.progress / achievement.maxProgress) * 100}
                              color={achievement.progress > 0 ? 'warning' : 'default'}
                              size="sm"
                            />
                          </div>
                        )}
                        
                        {achievement.unlocked && (
                          <div className="text-xs text-success">
                            ✓ Desbloqueado em {new Date(achievement.unlockedAt!).toLocaleDateString('pt-BR')}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="unlocked" title={`Desbloqueadas (${unlockedCount})`}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAchievements.filter(a => a.unlocked).map((achievement) => (
                <Card
                  key={achievement.id}
                  className="cursor-pointer hover:scale-105 transition-transform bg-gradient-to-br from-success/10 to-success/5"
                  onPress={() => handleAchievementClick(achievement)}
                >
                  <CardBody className="p-4 text-center">
                    <div className="text-4xl mb-3">{achievement.icon}</div>
                    <h4 className="font-bold mb-2">{achievement.name}</h4>
                    <p className="text-sm text-default-600 mb-3">{achievement.description}</p>
                    <div className="flex justify-center gap-2 mb-3">
                      <Chip
                        size="sm"
                        color={getRarityColor(achievement.rarity) as any}
                        variant="flat"
                      >
                        {achievement.rarity}
                      </Chip>
                      <Chip size="sm" variant="flat" color="success">
                        +{achievement.xpReward} XP
                      </Chip>
                    </div>
                    <div className="text-xs text-success">
                      ✓ {new Date(achievement.unlockedAt!).toLocaleDateString('pt-BR')}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="in-progress" title="Em Progresso">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredAchievements.filter(a => !a.unlocked && a.progress > 0).map((achievement) => (
                <Card
                  key={achievement.id}
                  className="cursor-pointer hover:scale-105 transition-transform bg-gradient-to-br from-warning/10 to-warning/5"
                  onPress={() => handleAchievementClick(achievement)}
                >
                  <CardBody className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="text-4xl">{achievement.icon}</div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-bold">{achievement.name}</h4>
                          <Chip
                            size="sm"
                            color={getRarityColor(achievement.rarity) as any}
                            variant="flat"
                          >
                            {achievement.rarity}
                          </Chip>
                        </div>
                        
                        <p className="text-sm text-default-600 mb-3">{achievement.description}</p>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progresso</span>
                            <span className="font-semibold">
                              {achievement.progress}/{achievement.maxProgress}
                            </span>
                          </div>
                          <Progress
                            value={(achievement.progress / achievement.maxProgress) * 100}
                            color="warning"
                            size="md"
                          />
                          <div className="text-xs text-default-500">
                            {achievement.maxProgress - achievement.progress} restantes
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="locked" title="Bloqueadas">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAchievements.filter(a => !a.unlocked && a.progress === 0).map((achievement) => (
                <Card
                  key={achievement.id}
                  className="cursor-pointer hover:scale-105 transition-transform opacity-75"
                  onPress={() => handleAchievementClick(achievement)}
                >
                  <CardBody className="p-4 text-center">
                    <div className="text-4xl mb-3 grayscale">{achievement.icon}</div>
                    <h4 className="font-bold mb-2">{achievement.name}</h4>
                    <p className="text-sm text-default-600 mb-3">{achievement.description}</p>
                    <div className="flex justify-center gap-2 mb-3">
                      <Chip
                        size="sm"
                        color={getRarityColor(achievement.rarity) as any}
                        variant="flat"
                      >
                        {achievement.rarity}
                      </Chip>
                      <Chip size="sm" variant="flat" color="success">
                        +{achievement.xpReward} XP
                      </Chip>
                    </div>
                    <div className="flex items-center justify-center gap-1 text-xs text-default-500">
                      <LockClosedIcon className="w-3 h-3" />
                      Bloqueada
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
        </Tabs>

        {/* Achievement Detail Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size="lg">
          <ModalContent>
            {selectedAchievement && (
              <>
                <ModalHeader className="flex items-center gap-3">
                  <div className="text-3xl">{selectedAchievement.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold">{selectedAchievement.name}</h3>
                    <Chip
                      size="sm"
                      color={getRarityColor(selectedAchievement.rarity) as any}
                      variant="flat"
                    >
                      {selectedAchievement.rarity}
                    </Chip>
                  </div>
                </ModalHeader>
                <ModalBody>
                  <div className="space-y-4">
                    <p className="text-default-600">{selectedAchievement.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-default-500">Categoria</div>
                        <div className="flex items-center gap-1">
                          {getCategoryIcon(selectedAchievement.category)}
                          <span className="capitalize">{selectedAchievement.category}</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-default-500">Recompensa</div>
                        <div className="flex items-center gap-1 text-success">
                          <BoltIcon className="w-4 h-4" />
                          <span>+{selectedAchievement.xpReward} XP</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm text-default-500 mb-2">Requisitos</div>
                      <div className="p-3 bg-default-100 rounded-lg text-sm">
                        {selectedAchievement.requirements}
                      </div>
                    </div>
                    
                    {!selectedAchievement.unlocked && selectedAchievement.maxProgress > 1 && (
                      <div>
                        <div className="text-sm text-default-500 mb-2">Progresso</div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span>{selectedAchievement.progress}/{selectedAchievement.maxProgress}</span>
                            <span>{Math.round((selectedAchievement.progress / selectedAchievement.maxProgress) * 100)}%</span>
                          </div>
                          <Progress
                            value={(selectedAchievement.progress / selectedAchievement.maxProgress) * 100}
                            color="primary"
                            size="md"
                          />
                        </div>
                      </div>
                    )}
                    
                    {selectedAchievement.unlocked && (
                      <div className="p-3 bg-success/10 rounded-lg">
                        <div className="flex items-center gap-2 text-success">
                          <CheckCircleIcon className="w-5 h-5" />
                          <span className="font-semibold">Conquista Desbloqueada!</span>
                        </div>
                        <div className="text-sm text-success mt-1">
                          {new Date(selectedAchievement.unlockedAt).toLocaleDateString('pt-BR', {
                            day: '2-digit',
                            month: 'long',
                            year: 'numeric'
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button variant="light" onPress={onClose}>
                    Fechar
                  </Button>
                  {!selectedAchievement.unlocked && (
                    <Button color="primary">
                      Ver Dicas
                    </Button>
                  )}
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

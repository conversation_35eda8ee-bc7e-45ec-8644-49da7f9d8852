import React, { useState, useEffect } from 'react';
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Button,
  Badge,
  Avatar,
  Chip,
  Divider,
  ScrollShadow,
} from '@heroui/react';
import {
  BellIcon,
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  TrophyIcon,
  UsersIcon,
  StarIcon,
  FireIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';

interface Notification {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'achievement' | 'tournament' | 'group-invite' | 'system';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
  actionUrl?: string;
  metadata?: {
    fromUserId?: string;
    fromUserName?: string;
    fromUserAvatar?: string;
    postId?: string;
    achievementId?: string;
    tournamentId?: string;
    groupId?: string;
  };
}

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'like',
    title: 'Nova curtida',
    message: 'ProGamer2024 curtiu seu post sobre CS2',
    isRead: false,
    createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    metadata: {
      fromUserId: 'user1',
      fromUserName: 'ProGamer2024',
      fromUserAvatar: '',
      postId: 'post1',
    },
  },
  {
    id: '2',
    type: 'achievement',
    title: 'Conquista desbloqueada!',
    message: 'Você desbloqueou a conquista "Ace Master"',
    isRead: false,
    createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    metadata: {
      achievementId: 'achievement1',
    },
  },
  {
    id: '3',
    type: 'comment',
    title: 'Novo comentário',
    message: 'SkillMaster comentou: "Boa jogada! Como você fez aquele clutch?"',
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    metadata: {
      fromUserId: 'user2',
      fromUserName: 'SkillMaster',
      fromUserAvatar: '',
      postId: 'post2',
    },
  },
  {
    id: '4',
    type: 'tournament',
    title: 'Torneio começando',
    message: 'Copa GameHub CS2 começa em 1 hora',
    isRead: true,
    createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    metadata: {
      tournamentId: 'tournament1',
    },
  },
  {
    id: '5',
    type: 'follow',
    title: 'Novo seguidor',
    message: 'ElitePlayer começou a seguir você',
    isRead: true,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    metadata: {
      fromUserId: 'user3',
      fromUserName: 'ElitePlayer',
      fromUserAvatar: '',
    },
  },
  {
    id: '6',
    type: 'group-invite',
    title: 'Convite para grupo',
    message: 'Você foi convidado para o grupo "CS2 Competitivo"',
    isRead: true,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    metadata: {
      groupId: 'group1',
      fromUserName: 'TeamLeader',
    },
  },
];

export const NotificationCenter: React.FC = () => {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [isOpen, setIsOpen] = useState(false);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return <HeartIcon className="w-5 h-5 text-danger" />;
      case 'comment':
        return <ChatBubbleOvalLeftIcon className="w-5 h-5 text-primary" />;
      case 'follow':
        return <UsersIcon className="w-5 h-5 text-success" />;
      case 'achievement':
        return <TrophyIcon className="w-5 h-5 text-warning" />;
      case 'tournament':
        return <StarIcon className="w-5 h-5 text-secondary" />;
      case 'group-invite':
        return <UsersIcon className="w-5 h-5 text-primary" />;
      case 'system':
        return <FireIcon className="w-5 h-5 text-default-500" />;
      default:
        return <BellIcon className="w-5 h-5 text-default-500" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'like': return 'danger';
      case 'comment': return 'primary';
      case 'follow': return 'success';
      case 'achievement': return 'warning';
      case 'tournament': return 'secondary';
      case 'group-invite': return 'primary';
      default: return 'default';
    }
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, isRead: true }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(n => n.id !== notificationId)
    );
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    // Navigate to the relevant page based on notification type
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    } else {
      switch (notification.type) {
        case 'like':
        case 'comment':
          if (notification.metadata?.postId) {
            window.location.href = `/feed?post=${notification.metadata.postId}`;
          }
          break;
        case 'follow':
          if (notification.metadata?.fromUserId) {
            window.location.href = `/profile/${notification.metadata.fromUserId}`;
          }
          break;
        case 'achievement':
          window.location.href = '/achievements';
          break;
        case 'tournament':
          if (notification.metadata?.tournamentId) {
            window.location.href = `/tournaments/${notification.metadata.tournamentId}`;
          } else {
            window.location.href = '/tournaments';
          }
          break;
        case 'group-invite':
          if (notification.metadata?.groupId) {
            window.location.href = `/groups/${notification.metadata.groupId}`;
          } else {
            window.location.href = '/groups';
          }
          break;
      }
    }
    setIsOpen(false);
  };

  if (!currentUser) {
    return null;
  }

  return (
    <Dropdown
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      placement="bottom-end"
      classNames={{
        content: "w-80 max-w-80",
      }}
    >
      <DropdownTrigger>
        <Button
          variant="light"
          isIconOnly
          className="relative"
        >
          <BellIcon className="w-6 h-6" />
          {unreadCount > 0 && (
            <Badge
              content={unreadCount > 99 ? '99+' : unreadCount.toString()}
              color="danger"
              placement="top-right"
              size="sm"
            />
          )}
        </Button>
      </DropdownTrigger>
      
      <DropdownMenu
        aria-label="Notificações"
        className="p-0"
        itemClasses={{
          base: "p-0 gap-0 data-[hover=true]:bg-transparent",
        }}
      >
        <DropdownItem key="header" className="p-0" textValue="header">
          <div className="p-4 border-b border-divider">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Notificações</h3>
              {unreadCount > 0 && (
                <Button
                  size="sm"
                  variant="light"
                  color="primary"
                  onPress={markAllAsRead}
                >
                  Marcar todas como lidas
                </Button>
              )}
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-default-500 mt-1">
                {unreadCount} não {unreadCount === 1 ? 'lida' : 'lidas'}
              </p>
            )}
          </div>
        </DropdownItem>

        <DropdownItem key="notifications" className="p-0" textValue="notifications">
          <ScrollShadow className="max-h-96">
            {notifications.length === 0 ? (
              <div className="p-8 text-center">
                <BellIcon className="w-12 h-12 mx-auto text-default-300 mb-4" />
                <h4 className="font-semibold mb-2">Nenhuma notificação</h4>
                <p className="text-sm text-default-500">
                  Você está em dia com todas as suas notificações!
                </p>
              </div>
            ) : (
              <div className="divide-y divide-divider">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-default-50 cursor-pointer transition-colors ${
                      !notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0">
                        {notification.metadata?.fromUserAvatar ? (
                          <Avatar
                            src={notification.metadata.fromUserAvatar}
                            name={notification.metadata.fromUserName}
                            size="sm"
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-default-100 flex items-center justify-center">
                            {getNotificationIcon(notification.type)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-foreground">
                              {notification.title}
                            </p>
                            <p className="text-sm text-default-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Chip
                                size="sm"
                                variant="flat"
                                color={getNotificationColor(notification.type) as any}
                                startContent={getNotificationIcon(notification.type)}
                              >
                                {notification.type}
                              </Chip>
                              <span className="text-xs text-default-500">
                                {formatTimeAgo(notification.createdAt)}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-primary rounded-full" />
                            )}
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              onPress={(e) => {
                                e.stopPropagation();
                                deleteNotification(notification.id);
                              }}
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollShadow>
        </DropdownItem>

        {notifications.length > 0 && (
          <DropdownItem key="footer" className="p-0" textValue="footer">
            <div className="p-4 border-t border-divider text-center">
              <Button
                variant="light"
                color="primary"
                size="sm"
                fullWidth
                as="a"
                href="/notifications"
              >
                Ver todas as notificações
              </Button>
            </div>
          </DropdownItem>
        )}
      </DropdownMenu>
    </Dropdown>
  );
};

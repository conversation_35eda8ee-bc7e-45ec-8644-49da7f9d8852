{"name": "@heroui/calendar", "version": "2.2.22", "description": "A calendar displays one or more date grids and allows users to select a single date.", "keywords": ["calendar"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/calendar"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"@heroui/system": ">=2.4.17", "@heroui/theme": ">=2.4.6", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0"}, "dependencies": {"@internationalized/date": "3.8.2", "@react-aria/calendar": "3.8.3", "@react-aria/focus": "3.20.5", "@react-aria/i18n": "3.12.10", "@react-stately/calendar": "3.8.2", "@react-types/button": "3.12.2", "@react-aria/visually-hidden": "3.8.25", "@react-aria/utils": "3.29.1", "@react-stately/utils": "3.10.7", "@react-types/calendar": "3.7.2", "@react-aria/interactions": "3.25.3", "@react-types/shared": "3.30.0", "scroll-into-view-if-needed": "3.0.10", "@heroui/react-utils": "2.1.11", "@heroui/shared-utils": "2.1.9", "@heroui/shared-icons": "2.1.9", "@heroui/framer-utils": "2.1.18", "@heroui/use-aria-button": "2.2.16", "@heroui/button": "2.2.22", "@heroui/dom-animation": "2.1.9"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}
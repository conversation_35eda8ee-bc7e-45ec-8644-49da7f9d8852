# @heroui/table

Tables are used to display tabular data using rows and columns. They allow users to quickly scan, sort, compare, and take action on large amounts of data.

Please refer to the [documentation](https://heroui.com/docs/components/table) for more information.

## Installation

```sh
yarn add @heroui/table
# or
npm i @heroui/table
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).

{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAmDM,SAAS,0CAA6B,KAAqC,EAAE,KAAoB;IACtG,IAAI,mBACF,eAAe,sBACf,kBAAkB,cAClB,aAAa,GACd,GAAG;IAEJ,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,eAAO,EAAc;IAC/D,IAAI,eAAe,CAAA,GAAA,cAAM,EACvB,IAAM,IAAI,CAAA,GAAA,yCAAgB,EAAE;6BAC1B;gCACA;QACF,IACA;QAAC;QAAiB;KAAmB;IAGvC,IAAI,CAAC,mBAAmB,oBAAoB,GAAG,CAAA,GAAA,cAAM,EAAE,IACnD,aAAa,yCAAyC,CAAC,MAAM,UAAU,CAAC,OAAO,GACjF;QAAC,MAAM,UAAU,CAAC,OAAO;QAAE;KAAa;IAE1C,6BAA6B;IAC7B,IAAI,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,eAAO,EAAE,IACzD,aAAa,4BAA4B,CAAC;IAG5C,qDAAqD;IACrD,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,eAAO,EAAE,MAAM,UAAU,CAAC,OAAO;IACrE,IAAI,MAAM,UAAU,CAAC,OAAO,KAAK,aAAa;QAC5C,IACE,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY,MAAM,IACtD,MAAM,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,CAAC,GAAG,GACpE;YACA,IAAI,wBAAwB,aAAa,4BAA4B,CAAC;YACtE,sBAAsB;QACxB;QACA,eAAe,MAAM,UAAU,CAAC,OAAO;IACzC;IAEA,6EAA6E;IAC7E,IAAI,YAAY,CAAA,GAAA,cAAM,EAAE,IACpB,aAAa,gBAAgB,CAAC,MAAM,UAAU,CAAC,OAAO,EAAE,oBAAoB,qBAAqB,oBACnG;QAAC,MAAM,UAAU,CAAC,OAAO;QAAE;QAAoB;QAAqB;QAAmB;KAAa;IAEtG,IAAI,cAAc,CAAA,GAAA,kBAAU,EAAE,CAAC;QAC7B,kBAAkB;IACpB,GAAG;QAAC;KAAkB;IAEtB,IAAI,uBAAuB,CAAA,GAAA,kBAAU,EAAE,CAAC,KAAU;QAChD,IAAI,WAAW,aAAa,iBAAiB,CAAC,MAAM,UAAU,EAAE,oBAAoB,KAAK;QACzF,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,IAAI,GAAK;gBAAC;gBAAK,SAAS,GAAG,CAAC;aAAM;QAC1F,IAAI,GAAG,CAAC,KAAK;QACb,sBAAsB;QACtB,OAAO;IACT,GAAG;QAAC;QAAqB;QAAuB;QAAc,MAAM,UAAU;QAAE;KAAmB;IAEnG,IAAI,YAAY,CAAA,GAAA,kBAAU,EAAE;QAC1B,kBAAkB;IACpB,GAAG;QAAC;KAAkB;IAEtB,IAAI,eAAe,CAAA,GAAA,cAAM,EAAE,IACzB,aAAa,iBAAiB,CAAC,YAAY,MAAM,UAAU,EAAE,YAC7D;QAAC;QAAY,MAAM,UAAU;QAAE;QAAW;KAAa;IAEzD,OAAO,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;4BACpB;kCACA;yBACA;uBACA;YACA,gBAAgB,CAAC,MACf,aAAa,cAAc,CAAC;YAC9B,mBAAmB,CAAC,MAClB,aAAa,iBAAiB,CAAC;YACjC,mBAAmB,CAAC,MAClB,aAAa,iBAAiB,CAAC;YACjC,YAAY;0BACZ;QACF,CAAA,GAAI;QACF;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH", "sources": ["packages/@react-stately/table/src/useTableColumnResizeState.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ColumnSize} from '@react-types/table';\nimport {GridNode} from '@react-types/grid';\nimport {Key} from '@react-types/shared';\nimport {TableColumnLayout} from './TableColumnLayout';\nimport {TableState} from './useTableState';\nimport {useCallback, useMemo, useState} from 'react';\n\nexport interface TableColumnResizeStateProps<T> {\n  /**\n   * Current width of the table or table viewport that the columns\n   * should be calculated against.\n   */\n  tableWidth: number,\n  /** A function that is called to find the default width for a given column. */\n  getDefaultWidth?: (node: GridNode<T>) => ColumnSize | null | undefined,\n  /** A function that is called to find the default minWidth for a given column. */\n  getDefaultMinWidth?: (node: GridNode<T>) => ColumnSize | null | undefined\n}\nexport interface TableColumnResizeState<T> {\n  /**\n   * Called to update the state that a resize event has occurred.\n   * Returns the new widths for all columns based on the resized column.\n   */\n  updateResizedColumns: (key: Key, width: number) => Map<Key, ColumnSize>,\n  /** Callback for when onColumnResize has started. */\n  startResize: (key: Key) => void,\n  /** Callback for when onColumnResize has ended. */\n  endResize: () => void,\n  /** Gets the current width for the specified column. */\n  getColumnWidth: (key: Key) => number,\n  /** Gets the current minWidth for the specified column. */\n  getColumnMinWidth: (key: Key) => number,\n  /** Gets the current maxWidth for the specified column. */\n  getColumnMaxWidth: (key: Key) => number,\n  /** Key of the currently resizing column. */\n  resizingColumn: Key | null,\n  /** A reference to the table state. */\n  tableState: TableState<T>,\n  /** A map of the current column widths. */\n  columnWidths: Map<Key, number>\n}\n\n/**\n * Provides column width state management for a table component with column resizing support. Handles building\n * a map of column widths calculated from the table's width and any provided column width information from the collection.\n * In addition, it tracks the currently resizing column and provides callbacks for updating the widths upon resize operations.\n * @param props - Props for the table.\n * @param state - State for the table, as returned by `useTableState`.\n */\nexport function useTableColumnResizeState<T>(props: TableColumnResizeStateProps<T>, state: TableState<T>): TableColumnResizeState<T> {\n  let {\n    getDefaultWidth,\n    getDefaultMinWidth,\n    tableWidth = 0\n  } = props;\n\n  let [resizingColumn, setResizingColumn] = useState<Key | null>(null);\n  let columnLayout = useMemo(\n    () => new TableColumnLayout({\n      getDefaultWidth,\n      getDefaultMinWidth\n    }),\n    [getDefaultWidth, getDefaultMinWidth]\n  );\n\n  let [controlledColumns, uncontrolledColumns] = useMemo(() =>\n      columnLayout.splitColumnsIntoControlledAndUncontrolled(state.collection.columns)\n  , [state.collection.columns, columnLayout]);\n\n  // uncontrolled column widths\n  let [uncontrolledWidths, setUncontrolledWidths] = useState(() =>\n    columnLayout.getInitialUncontrolledWidths(uncontrolledColumns)\n  );\n\n  // Update uncontrolled widths if the columns changed.\n  let [lastColumns, setLastColumns] = useState(state.collection.columns);\n  if (state.collection.columns !== lastColumns) {\n    if (\n      state.collection.columns.length !== lastColumns.length ||\n      state.collection.columns.some((c, i) => c.key !== lastColumns[i].key)\n    ) {\n      let newUncontrolledWidths = columnLayout.getInitialUncontrolledWidths(uncontrolledColumns);\n      setUncontrolledWidths(newUncontrolledWidths);\n    }\n    setLastColumns(state.collection.columns);\n  }\n\n  // combine columns back into one map that maintains same order as the columns\n  let colWidths = useMemo(() =>\n      columnLayout.recombineColumns(state.collection.columns, uncontrolledWidths, uncontrolledColumns, controlledColumns)\n  , [state.collection.columns, uncontrolledWidths, uncontrolledColumns, controlledColumns, columnLayout]);\n\n  let startResize = useCallback((key: Key) => {\n    setResizingColumn(key);\n  }, [setResizingColumn]);\n\n  let updateResizedColumns = useCallback((key: Key, width: number): Map<Key, ColumnSize> => {\n    let newSizes = columnLayout.resizeColumnWidth(state.collection, uncontrolledWidths, key, width);\n    let map = new Map(Array.from(uncontrolledColumns).map(([key]) => [key, newSizes.get(key)!]));\n    map.set(key, width);\n    setUncontrolledWidths(map);\n    return newSizes;\n  }, [uncontrolledColumns, setUncontrolledWidths, columnLayout, state.collection, uncontrolledWidths]);\n\n  let endResize = useCallback(() => {\n    setResizingColumn(null);\n  }, [setResizingColumn]);\n\n  let columnWidths = useMemo(() =>\n    columnLayout.buildColumnWidths(tableWidth, state.collection, colWidths)\n  , [tableWidth, state.collection, colWidths, columnLayout]);\n\n  return useMemo(() => ({\n    resizingColumn,\n    updateResizedColumns,\n    startResize,\n    endResize,\n    getColumnWidth: (key: Key) =>\n      columnLayout.getColumnWidth(key),\n    getColumnMinWidth: (key: Key) =>\n      columnLayout.getColumnMinWidth(key),\n    getColumnMaxWidth: (key: Key) =>\n      columnLayout.getColumnMaxWidth(key),\n    tableState: state,\n    columnWidths\n  }), [\n    columnLayout,\n    columnWidths,\n    resizingColumn,\n    updateResizedColumns,\n    startResize,\n    endResize,\n    state\n  ]);\n}\n"], "names": [], "version": 3, "file": "useTableColumnResizeState.module.js.map"}
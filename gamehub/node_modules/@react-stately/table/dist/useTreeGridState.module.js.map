{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AAmCM,SAAS,0CAA4C,KAA4B;IACtF,IAAI,iBACF,gBAAgB,iCAChB,uBAAuB,mBACvB,eAAe,EACf,uBAAuB,gBAAgB,EACvC,8BAA8B,uBAAuB,6BACrD,yBAAyB,YACzB,QAAQ,EACT,GAAG;IAEJ,IAAI,CAAC,CAAA,GAAA,sBAAc,KACjB,MAAM,IAAI,MAAM;IAGlB,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,yBAAiB,EACrD,mBAAmB,sCAAgB,oBAAoB,WACvD,0BAA0B,sCAAgB,2BAA2B,IAAI,OACzE;IAGF,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;YAC3B,yBAAyB,2BAA2B,kBAAkB;YACtE,iBAAiB;2BACjB;YACA,SAAS,EAAE;QAEb,CAAA,GAAI;QAAC;QAAU;QAAyB;QAAe;KAAgB;IAEvE,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,wBAAgB,KAAQ,EAAE;IAC1D,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE,IAAM,QAAQ,KAAK,CAAC;YAAC,UAAU;QAA+B,GAAG,UAAU;QAAC;QAAS;QAAU;KAAQ;IAC3H,IAAI,qBAAqB,CAAA,GAAA,cAAM,EAAE;QAC/B,OAAO,iDAA8B,OAAO;qCAAC;6BAAyB;0BAAiB;QAAY;IACrG,GAAG;QAAC;QAAO;QAAyB;QAAiB;KAAa;IAElE,IAAI,WAAW,CAAC;QACd,gBAAgB,gCAAU,cAAc,KAAK;IAC/C;IAEA,IAAI,aAAa,CAAA,GAAA,cAAM,EAAE;QACvB,OAAO,IAAI,CAAA,GAAA,yCAAc,EAAE,mBAAmB,UAAU,EAAE,MAAM;IAClE,GAAG;QAAC;QAAS,mBAAmB,UAAU;KAAC;IAE3C,IAAI,aAAa,CAAA,GAAA,yCAAY,EAAE;QAAC,GAAG,KAAK;oBAAE;IAAU;IACpD,OAAO;QACL,GAAG,UAAU;QACb,QAAQ,mBAAmB,MAAM;QACjC,iBAAiB,mBAAmB,eAAe;sBACnD;QACA,WAAW;IACb;AACF;AAEA,SAAS,gCAAa,mBAAqC,EAAE,GAAQ,EAAE,UAAiC;IACtG,IAAI;IACJ,IAAI,wBAAwB,OAAO;QACjC,sBAAsB,IAAI,IAAI,WAAW,aAAa,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,CAAC,mBAAmB,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,eAAe,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG;QAChL,oBAAoB,MAAM,CAAC;IAC7B,OAAO;QACL,sBAAsB,IAAI,IAAI;QAC9B,IAAI,oBAAoB,GAAG,CAAC,MAC1B,oBAAoB,MAAM,CAAC;aAE3B,oBAAoB,GAAG,CAAC;IAE5B;IAEA,OAAO;AACT;AAEA,SAAS,sCAAgB,QAA+B;IACtD,IAAI,CAAC,UACH,OAAO,IAAI;IAGb,OAAO,aAAa,QAChB,QACA,IAAI,IAAI;AACd;AAcA,SAAS,iDAA8B,KAAK,EAAE,IAA+B;IAC3E,IAAI,gBACF,eAAe,IAAI,OACpB,GAAG;IAEJ,IAAI,OAA2B;IAC/B,IAAI,gBAA+B,EAAE;IACrC,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,kBAAiC,EAAE;IACvC,IAAI,SAAS,IAAI;IAEjB,IAAI,iBAAA,2BAAA,KAAM,uBAAuB,EAC/B;IAGF,IAAI,iBAAA,2BAAA,KAAM,eAAe,EACvB;IAGF,IAAI,eAA8B,EAAE;IACpC,IAAI,QAAQ,CAAC;QACX,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;gBACP,OAAO,GAAG,CAAC,KAAK,GAAG,EAAE;gBACrB;YACF,KAAK;gBACH,IAAI,CAAC,KAAK,aAAa,EACrB;gBAEF;YACF,KAAK;gBACH,aAAa,IAAI,CAAC;gBAClB;QACJ;QAEA,KAAK,IAAI,SAAS,KAAK,UAAU,CAC/B,MAAM;IAEV;IAEA,KAAK,IAAI,QAAQ,MAAO;QACtB,IAAI,KAAK,IAAI,KAAK,UAChB,gBAAgB,IAAI,CAAC;QAEvB,MAAM;IACR;IAEA,eAAe;IAEf,mKAAmK;IACnK,IAAI,iBAAiB;IACrB,IAAI,YAAY,CAAC,MAAmB;QAClC,iIAAiI;QACjI,4IAA4I;QAC5I,wDAAwD;QACxD,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,IAAI,aAA4B,EAAE;YAClC,KAAK,IAAI,SAAS,KAAK,UAAU,CAC/B,IAAI,MAAM,IAAI,KAAK,QAAQ;gBACzB,IAAI,YAAY;oBAAC,GAAG,KAAK;gBAAA;gBACzB,IAAI,UAAU,KAAK,GAAG,MAAM,aAC1B,UAAU,OAAO,GAAG;gBAEtB,WAAW,IAAI,CAAC;oBAAC,GAAG,SAAS;gBAAA;YAC/B;YAEF,IAAI,QAAqB;gBAAC,GAAG,IAAI;gBAAE,YAAY;gBAAY,WAAW,KAAM,GAAG;gBAAE,OAAO;gBAAG,OAAO;YAAgB;YAClH,cAAc,IAAI,CAAC;QACrB;QAEA,IAAI,WAAW,CAAC;QAEhB,yDAAyD;QACzD,IAAI,KAAK,IAAI,KAAK,iBAAiB,KAAK,IAAI,KAAK,UAC/C,QAAQ,CAAC,cAAc,GAAG;QAG5B,6GAA6G;QAC7G,8EAA8E;QAC9E,OAAO,MAAM,CAAC,MAAM;QACpB,OAAO,GAAG,CAAC,KAAK,GAAG,EAAE;QAErB,IAAI,WAA+B;QACnC,IAAI,WAAW;QACf,KAAK,IAAI,SAAS,KAAK,UAAU,CAC/B,IAAI,CAAE,CAAA,MAAM,IAAI,KAAK,UAAU,iBAAiB,SAAS,CAAC,aAAa,GAAG,CAAC,KAAK,GAAG,CAAA,GAAI;YACrF,IAAI,MAAM,SAAS,IAAI,MACrB,wIAAwI;YACxI,MAAM,SAAS,GAAG,KAAK,GAAG;YAG5B,IAAI,UAAU;gBACZ,SAAS,OAAO,GAAG,MAAM,GAAG;gBAC5B,MAAM,OAAO,GAAG,SAAS,GAAG;YAC9B,OACE,MAAM,OAAO,GAAG;YAGlB,IAAI,MAAM,IAAI,KAAK,QACjB,UAAU,OAAO;iBAEjB,0EAA0E;YAC1E,UAAU,OAAO,MAAM,KAAK;YAG9B,WAAW;QACb;QAGF,IAAI,UACF,SAAS,OAAO,GAAG;IAEvB;IAEA,IAAI,OAA2B;IAC/B,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,aAAa,OAAO,GAAI;QAC5C,UAAU,MAAqB;QAE/B,IAAI,MAAM;YACR,KAAK,OAAO,GAAG,KAAK,GAAG;YACvB,KAAK,OAAO,GAAG,KAAK,GAAG;QACzB,OACE,KAAK,OAAO,GAAG;QAGjB,OAAO;IACT;IAEA,IAAI,MACF,KAAK,OAAO,GAAG;IAGjB,OAAO;gBACL;yBACA;uBACA;QACA,YAAY;eAAI;YAAiB;gBAAC,GAAG,IAAI;gBAAG,YAAY;YAAa;SAAE;IACzE;AACF", "sources": ["packages/@react-stately/table/src/useTreeGridState.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CollectionBuilder} from '@react-stately/collections';\nimport {GridNode} from '@react-types/grid';\nimport {Key} from '@react-types/shared';\nimport {ReactElement, useMemo} from 'react';\nimport {TableCollection} from './TableCollection';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState, TableStateProps, useTableState} from './useTableState';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface TreeGridState<T> extends TableState<T> {\n  /** A set of keys for items that are expanded. */\n  expandedKeys: 'all' | Set<Key>,\n  /** Toggles the expanded state for a row by its key. */\n  toggleKey(key: Key): void,\n  /** The key map containing nodes representing the collection's tree grid structure. */\n  keyMap: Map<Key, GridNode<T>>,\n  /** The number of leaf columns provided by the user. */\n  userColumnCount: number\n}\n\nexport interface TreeGridStateProps<T> extends Omit<TableStateProps<T>, 'collection'> {\n  /** The currently expanded keys in the collection (controlled). */\n  UNSTABLE_expandedKeys?: 'all' | Iterable<Key>,\n  /** The initial expanded keys in the collection (uncontrolled). */\n  UNSTABLE_defaultExpandedKeys?: 'all' | Iterable<Key>,\n  /** Handler that is called when items are expanded or collapsed. */\n  UNSTABLE_onExpandedChange?: (keys: Set<Key>) => any\n}\n\n/**\n * Provides state management for a tree grid component. Handles building a collection\n * of columns and rows from props. In addition, it tracks and manages expanded rows, row selection, and sort order changes.\n */\nexport function UNSTABLE_useTreeGridState<T extends object>(props: TreeGridStateProps<T>): TreeGridState<T> {\n  let {\n    selectionMode = 'none',\n    showSelectionCheckboxes,\n    showDragButtons,\n    UNSTABLE_expandedKeys: propExpandedKeys,\n    UNSTABLE_defaultExpandedKeys: propDefaultExpandedKeys,\n    UNSTABLE_onExpandedChange,\n    children\n  } = props;\n\n  if (!tableNestedRows()) {\n    throw new Error('Feature flag for table nested rows must be enabled to use useTreeGridState.');\n  }\n\n  let [expandedKeys, setExpandedKeys] = useControlledState(\n    propExpandedKeys ? convertExpanded(propExpandedKeys) : undefined,\n    propDefaultExpandedKeys ? convertExpanded(propDefaultExpandedKeys) : new Set(),\n    UNSTABLE_onExpandedChange\n  );\n\n  let context = useMemo(() => ({\n    showSelectionCheckboxes: showSelectionCheckboxes && selectionMode !== 'none',\n    showDragButtons: showDragButtons,\n    selectionMode,\n    columns: []\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }), [children, showSelectionCheckboxes, selectionMode, showDragButtons]);\n\n  let builder = useMemo(() => new CollectionBuilder<T>(), []);\n  let nodes = useMemo(() => builder.build({children: children as ReactElement<any>[]}, context), [builder, children, context]);\n  let treeGridCollection = useMemo(() => {\n    return generateTreeGridCollection<T>(nodes, {showSelectionCheckboxes, showDragButtons, expandedKeys});\n  }, [nodes, showSelectionCheckboxes, showDragButtons, expandedKeys]);\n\n  let onToggle = (key: Key) => {\n    setExpandedKeys(toggleKey(expandedKeys, key, treeGridCollection));\n  };\n\n  let collection = useMemo(() => {\n    return new TableCollection(treeGridCollection.tableNodes, null, context);\n  }, [context, treeGridCollection.tableNodes]);\n\n  let tableState = useTableState({...props, collection});\n  return {\n    ...tableState,\n    keyMap: treeGridCollection.keyMap,\n    userColumnCount: treeGridCollection.userColumnCount,\n    expandedKeys,\n    toggleKey: onToggle\n  };\n}\n\nfunction toggleKey<T>(currentExpandedKeys: 'all' | Set<Key>, key: Key, collection: TreeGridCollection<T>): Set<Key> {\n  let updatedExpandedKeys: Set<Key>;\n  if (currentExpandedKeys === 'all') {\n    updatedExpandedKeys = new Set(collection.flattenedRows.filter(row => row.props.UNSTABLE_childItems || row.props.children.length > collection.userColumnCount).map(row => row.key));\n    updatedExpandedKeys.delete(key);\n  } else {\n    updatedExpandedKeys = new Set(currentExpandedKeys);\n    if (updatedExpandedKeys.has(key)) {\n      updatedExpandedKeys.delete(key);\n    } else {\n      updatedExpandedKeys.add(key);\n    }\n  }\n\n  return updatedExpandedKeys;\n}\n\nfunction convertExpanded(expanded: 'all' | Iterable<Key>): 'all' | Set<Key> {\n  if (!expanded) {\n    return new Set<Key>();\n  }\n\n  return expanded === 'all'\n    ? 'all'\n    : new Set(expanded);\n}\n\ninterface TreeGridCollectionOptions {\n  showSelectionCheckboxes?: boolean,\n  showDragButtons?: boolean,\n  expandedKeys: 'all' | Set<Key>\n}\n\ninterface TreeGridCollection<T> {\n  keyMap: Map<Key, GridNode<T>>,\n  tableNodes: GridNode<T>[],\n  flattenedRows: GridNode<T>[],\n  userColumnCount: number\n}\nfunction generateTreeGridCollection<T>(nodes, opts: TreeGridCollectionOptions): TreeGridCollection<T> {\n  let {\n    expandedKeys = new Set()\n  } = opts;\n\n  let body: GridNode<T> | null = null;\n  let flattenedRows: GridNode<T>[] = [];\n  let columnCount = 0;\n  let userColumnCount = 0;\n  let originalColumns: GridNode<T>[] = [];\n  let keyMap = new Map();\n\n  if (opts?.showSelectionCheckboxes) {\n    columnCount++;\n  }\n\n  if (opts?.showDragButtons) {\n    columnCount++;\n  }\n\n  let topLevelRows: GridNode<T>[] = [];\n  let visit = (node: GridNode<T>) => {\n    switch (node.type) {\n      case 'body':\n        body = node;\n        keyMap.set(body.key, body);\n        break;\n      case 'column':\n        if (!node.hasChildNodes) {\n          userColumnCount++;\n        }\n        break;\n      case 'item':\n        topLevelRows.push(node);\n        return;\n    }\n\n    for (let child of node.childNodes) {\n      visit(child);\n    }\n  };\n\n  for (let node of nodes) {\n    if (node.type === 'column') {\n      originalColumns.push(node);\n    }\n    visit(node);\n  }\n\n  columnCount += userColumnCount;\n\n  // Update each grid node in the treegrid table with values specific to a treegrid structure. Also store a set of flattened row nodes for TableCollection to consume\n  let globalRowCount = 0;\n  let visitNode = (node: GridNode<T>, i?: number) => {\n    // Clone row node and its children so modifications to the node for treegrid specific values aren't applied on the nodes provided\n    // to TableCollection. Index, level, and parent keys are all changed to reflect a flattened row structure rather than the treegrid structure\n    // values automatically calculated via CollectionBuilder\n    if (node.type === 'item') {\n      let childNodes: GridNode<T>[] = [];\n      for (let child of node.childNodes) {\n        if (child.type === 'cell') {\n          let cellClone = {...child};\n          if (cellClone.index + 1 === columnCount) {\n            cellClone.nextKey = null;\n          }\n          childNodes.push({...cellClone});\n        }\n      }\n      let clone: GridNode<T> = {...node, childNodes: childNodes, parentKey: body!.key, level: 1, index: globalRowCount++};\n      flattenedRows.push(clone);\n    }\n\n    let newProps = {};\n\n    // Assign indexOfType to cells and rows for aria-posinset\n    if (node.type !== 'placeholder' && node.type !== 'column') {\n      newProps['indexOfType'] = i;\n    }\n\n    // Use Object.assign instead of spread to preserve object reference for keyMap. Also ensures retrieving nodes\n    // via .childNodes returns the same object as the one found via keyMap look up\n    Object.assign(node, newProps);\n    keyMap.set(node.key, node);\n\n    let lastNode: GridNode<T> | null = null;\n    let rowIndex = 0;\n    for (let child of node.childNodes) {\n      if (!(child.type === 'item' && expandedKeys !== 'all' && !expandedKeys.has(node.key))) {\n        if (child.parentKey == null) {\n          // if child is a cell/expanded row/column and the parent key isn't already established by the collection, match child node to parent row\n          child.parentKey = node.key;\n        }\n\n        if (lastNode) {\n          lastNode.nextKey = child.key;\n          child.prevKey = lastNode.key;\n        } else {\n          child.prevKey = null;\n        }\n\n        if (child.type === 'item') {\n          visitNode(child, rowIndex++);\n        } else {\n          // We enforce that the cells come before rows so can just reuse cell index\n          visitNode(child, child.index);\n        }\n\n        lastNode = child;\n      }\n    }\n\n    if (lastNode) {\n      lastNode.nextKey = null;\n    }\n  };\n\n  let last: GridNode<T> | null = null;\n  for (let [i, node] of topLevelRows.entries()) {\n    visitNode(node as GridNode<T>, i);\n\n    if (last) {\n      last.nextKey = node.key;\n      node.prevKey = last.key;\n    } else {\n      node.prevKey = null;\n    }\n\n    last = node;\n  }\n\n  if (last) {\n    last.nextKey = null;\n  }\n\n  return {\n    keyMap,\n    userColumnCount,\n    flattenedRows,\n    tableNodes: [...originalColumns, {...body!, childNodes: flattenedRows}]\n  };\n}\n"], "names": [], "version": 3, "file": "useTreeGridState.module.js.map"}
{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;AAMD,SAAS,gCAAa,KAAwB;IAC5C,OAAO;AACT;AAEA,gCAAU,iBAAiB,GAAG,UAAU,kBAAqB,KAAwB;IACnF,IAAI,YAAC,QAAQ,SAAE,KAAK,EAAC,GAAG;IACxB,MAAM;QACJ,MAAM;QACN,eAAe;eACf;QACA,CAAC;YACC,IAAI,OAAO,aAAa,YAAY;gBAClC,IAAI,CAAC,OACH,MAAM,IAAI,MAAM;gBAGlB,KAAK,IAAI,QAAQ,MACf,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,UAAU;gBACZ;YAEJ,OAAO;gBACL,IAAI,QAA0B,EAAE;gBAChC,CAAA,GAAA,sCAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;oBAC/B,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,OAAO;YACT;QACF;IACF;AACF;AAEA;;;CAGC,GACD,oEAAoE;AACpE,IAAI,4CAAa", "sources": ["packages/@react-stately/table/src/TableBody.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {PartialNode} from '@react-stately/collections';\nimport React, {JSX, ReactElement} from 'react';\nimport {TableBodyProps} from '@react-types/table';\n\nfunction TableBody<T>(props: TableBodyProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nTableBody.getCollectionNode = function* getCollectionNode<T>(props: TableBodyProps<T>): Generator<PartialNode<T>> {\n  let {children, items} = props;\n  yield {\n    type: 'body',\n    hasChildNodes: true,\n    props,\n    *childNodes() {\n      if (typeof children === 'function') {\n        if (!items) {\n          throw new Error('props.children was a function but props.items is missing');\n        }\n\n        for (let item of items) {\n          yield {\n            type: 'item',\n            value: item,\n            renderer: children\n          };\n        }\n      } else {\n        let items: PartialNode<T>[] = [];\n        React.Children.forEach(children, item => {\n          items.push({\n            type: 'item',\n            element: item\n          });\n        });\n\n        yield* items;\n      }\n    }\n  };\n};\n\n/**\n * A TableBody is a container for the Row elements of a Table. Rows can be statically defined\n * as children, or generated dynamically using a function based on the data passed to the `items` prop.\n */\n// We don't want getCollectionNode to show up in the type definition\nlet _TableBody = TableBody as <T>(props: TableBodyProps<T>) => JSX.Element;\nexport {_TableBody as TableBody};\n"], "names": [], "version": 3, "file": "TableBody.main.js.map"}
{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;AAOD,SAAS,kCAAe,KAA0B;IAChD,OAAO;AACT;AAEA,kCAAY,iBAAiB,GAAG,UAAU,kBAAqB,KAA0B,EAAE,OAAoC;IAC7H,IAAI,YAAC,QAAQ,WAAE,OAAO,EAAC,GAAG;IAE1B,4DAA4D;IAC5D,QAAQ,OAAO,GAAG,EAAE;IAEpB,IAAI,OAAO,aAAa,YAAY;QAClC,IAAI,CAAC,SACH,MAAM,IAAI,MAAM;QAGlB,KAAK,IAAI,UAAU,QACjB,MAAM;YACJ,MAAM;YACN,OAAO;YACP,UAAU;QACZ;IAEJ,OAAO;QACL,IAAI,UAA4B,EAAE;QAClC,CAAA,GAAA,sCAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;YAC/B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,SAAS;YACX;QACF;QAEA,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,oEAAoE;AACpE,IAAI,4CAAe", "sources": ["packages/@react-stately/table/src/TableHeader.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CollectionBuilderContext} from './useTableState';\nimport {PartialNode} from '@react-stately/collections';\nimport React, {JSX, ReactElement} from 'react';\nimport {TableHeaderProps} from '@react-types/table';\n\nfunction TableHeader<T>(props: TableHeaderProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nTableHeader.getCollectionNode = function* getCollectionNode<T>(props: TableHeaderProps<T>, context: CollectionBuilderContext<T>): Generator<PartialNode<T>, void, any> {\n  let {children, columns} = props;\n\n  // Clear columns so they aren't double added in strict mode.\n  context.columns = [];\n\n  if (typeof children === 'function') {\n    if (!columns) {\n      throw new Error('props.children was a function but props.columns is missing');\n    }\n\n    for (let column of columns) {\n      yield {\n        type: 'column',\n        value: column,\n        renderer: children\n      };\n    }\n  } else {\n    let columns: PartialNode<T>[] = [];\n    React.Children.forEach(children, column => {\n      columns.push({\n        type: 'column',\n        element: column\n      });\n    });\n\n    yield* columns;\n  }\n};\n\n/**\n * A TableHeader is a container for the Column elements in a Table. Columns can be statically defined\n * as children, or generated dynamically using a function based on the data passed to the `columns` prop.\n */\n// We don't want getCollectionNode to show up in the type definition\nlet _TableHeader = TableHeader as <T>(props: TableHeaderProps<T>) => JSX.Element;\nexport {_TableHeader as TableHeader};\n"], "names": [], "version": 3, "file": "TableHeader.main.js.map"}
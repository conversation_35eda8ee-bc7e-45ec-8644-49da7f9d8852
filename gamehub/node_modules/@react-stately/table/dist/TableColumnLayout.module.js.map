{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAgBM,MAAM;IAYX,yHAAyH,GACzH,0CAA0C,OAA2B,EAAkD;QACrH,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK;YAC1B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,MACrB,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE;iBAEpB,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE;YAEtB,OAAO;QACT,GAAG;YAAC,IAAI;YAAO,IAAI;SAAM;IAC3B;IAEA,+EAA+E,GAC/E,iBAAiB,OAA2B,EAAE,kBAAwC,EAAE,mBAA0C,EAAE,iBAAwC,EAAwB;QAClM,OAAO,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA;YACzB,IAAI,oBAAoB,GAAG,CAAC,IAAI,GAAG,GACjC,OAAO;gBAAC,IAAI,GAAG;gBAAE,mBAAmB,GAAG,CAAC,IAAI,GAAG;aAAE;iBAEjD,OAAO;gBAAC,IAAI,GAAG;gBAAE,kBAAkB,GAAG,CAAC,IAAI,GAAG,EAAG,KAAK,CAAC,KAAK;aAAC;QAEjE;IACF;IAEA,oFAAoF,GACpF,6BAA6B,mBAA0C,EAAwB;QAC7F,OAAO,IAAI,IAAI,MAAM,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI;gBAC5B,uBAAA;gBAA1B,yBAAA;mBAAN;gBAAC;gBAAK,CAAA,OAAA,CAAA,0BAAA,IAAI,KAAK,CAAC,YAAY,cAAtB,qCAAA,2BAA0B,wBAAA,CAAA,QAAA,IAAI,EAAC,eAAe,cAApB,4CAAA,2BAAA,OAAuB,kBAAjD,kBAAA,OAAyD;aAAM;;IAEzE;IAEA,eAAe,GAAQ,EAAU;YACxB;QAAP,OAAO,CAAA,yBAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAtB,oCAAA,yBAA8B;IACvC;IAEA,kBAAkB,GAAQ,EAAU;YAC3B;QAAP,OAAO,CAAA,4BAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAzB,uCAAA,4BAAiC;IAC1C;IAEA,kBAAkB,GAAQ,EAAU;YAC3B;QAAP,OAAO,CAAA,4BAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAzB,uCAAA,4BAAiC;IAC1C;IAEA,kBAAkB,UAA8B,EAAE,kBAAwC,EAAE,GAAQ,EAAE,KAAa,EAAwB;QACzI,IAAI,mBAAmB,IAAI,CAAC,YAAY;QACxC,IAAI,SAAS;QACb,IAAI,YAAY,IAAI;QAEpB,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,KAAK,CAAC;QAE/F,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA;gBAMG,uBAEA;YAP5B,IAAI,OAAO,GAAG,KAAK,KAAK;gBACtB,UAAU,GAAG,CAAC,OAAO,GAAG,EAAE;gBAC1B,SAAS;YACX,OAAO,IAAI,QACT,2DAA2D;YAC3D,UAAU,GAAG,CAAC,OAAO,GAAG,EAAE,CAAA,wBAAA,iBAAiB,GAAG,CAAC,OAAO,GAAG,eAA/B,mCAAA,wBAAoC;iBAE9D,UAAU,GAAG,CAAC,OAAO,GAAG,EAAE,CAAA,sBAAA,OAAO,KAAK,CAAC,KAAK,cAAlB,iCAAA,sBAAsB,mBAAmB,GAAG,CAAC,OAAO,GAAG;QAErF;QAEA,OAAO;IACT;IAEA,kBAAkB,UAAkB,EAAE,UAA8B,EAAE,MAA4B,EAAoB;QACpH,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI;QAE3B,0CAA0C;QAC1C,IAAI,eAAe,CAAA,GAAA,yCAAmB,EACpC,YACA,WAAW,OAAO,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;gBAAC,GAAG,IAAI,KAAK;gBAAE,KAAK,IAAI,GAAG;YAAA,CAAA,IAC1D,QACA,CAAC,IAAM,IAAI,CAAC,eAAe,CAAC,WAAW,OAAO,CAAC,EAAE,GACjD,CAAC,IAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,OAAO,CAAC,EAAE;QAGtD,oEAAoE;QACpE,aAAa,OAAO,CAAC,CAAC,OAAO;YAC3B,IAAI,MAAM,WAAW,OAAO,CAAC,MAAM,CAAC,GAAG;YACvC,IAAI,SAAS,WAAW,OAAO,CAAC,MAAM;YACtC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK;gBACe;YAA1C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAA,GAAA,yCAAU,EAAE,CAAA,yBAAA,OAAO,KAAK,CAAC,QAAQ,cAArB,oCAAA,yBAAyB,IAAI,CAAC,kBAAkB,CAAC,SAAS;YACpG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAA,GAAA,yCAAU,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE;QACnE;QACA,OAAO,IAAI,CAAC,YAAY;IAC1B;IA5FA,YAAY,OAAoC,CAAE;aAJlD,eAAiC,IAAI;aACrC,kBAAoC,IAAI;aACxC,kBAAoC,IAAI;YAGf;QAAvB,IAAI,CAAC,eAAe,GAAG,CAAA,2BAAA,oBAAA,8BAAA,QAAS,eAAe,cAAxB,sCAAA,2BAA6B,IAAM;YAChC;QAA1B,IAAI,CAAC,kBAAkB,GAAG,CAAA,8BAAA,oBAAA,8BAAA,QAAS,kBAAkB,cAA3B,yCAAA,8BAAgC,IAAM;IAClE;AA0FF", "sources": ["packages/@react-stately/table/src/TableColumnLayout.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  calculateColumnSizes,\n  getMaxWidth,\n  getMinWidth\n} from './TableUtils';\nimport {ColumnSize, TableCollection} from '@react-types/table';\nimport {GridNode} from '@react-types/grid';\nimport {Key} from '@react-types/shared';\n\nexport interface TableColumnLayoutOptions<T> {\n  getDefaultWidth?: (column: GridNode<T>) => ColumnSize | null | undefined,\n  getDefaultMinWidth?: (column: GridNode<T>) => ColumnSize | null | undefined\n}\n\nexport class TableColumnLayout<T> {\n  getDefaultWidth: (column: GridNode<T>) => ColumnSize | null | undefined;\n  getDefaultMinWidth: (column: GridNode<T>) => ColumnSize | null | undefined;\n  columnWidths: Map<Key, number> = new Map();\n  columnMinWidths: Map<Key, number> = new Map();\n  columnMaxWidths: Map<Key, number> = new Map();\n\n  constructor(options: TableColumnLayoutOptions<T>) {\n    this.getDefaultWidth = options?.getDefaultWidth ?? (() => '1fr');\n    this.getDefaultMinWidth = options?.getDefaultMinWidth ?? (() => 75);\n  }\n\n  /** Takes an array of columns and splits it into 2 maps of columns with controlled and columns with uncontrolled widths. */\n  splitColumnsIntoControlledAndUncontrolled(columns: Array<GridNode<T>>): [Map<Key, GridNode<T>>, Map<Key, GridNode<T>>] {\n    return columns.reduce((acc, col) => {\n      if (col.props.width != null) {\n        acc[0].set(col.key, col);\n      } else {\n        acc[1].set(col.key, col);\n      }\n      return acc;\n    }, [new Map(), new Map()]);\n  }\n\n  /** Takes uncontrolled and controlled widths and joins them into a single Map. */\n  recombineColumns(columns: Array<GridNode<T>>, uncontrolledWidths: Map<Key, ColumnSize>, uncontrolledColumns: Map<Key, GridNode<T>>, controlledColumns: Map<Key, GridNode<T>>): Map<Key, ColumnSize> {\n    return new Map(columns.map(col => {\n      if (uncontrolledColumns.has(col.key)) {\n        return [col.key, uncontrolledWidths.get(col.key)];\n      } else {\n        return [col.key, controlledColumns.get(col.key)!.props.width];\n      }\n    }));\n  }\n\n  /** Used to make an initial Map of the uncontrolled widths based on default widths. */\n  getInitialUncontrolledWidths(uncontrolledColumns: Map<Key, GridNode<T>>): Map<Key, ColumnSize> {\n    return new Map(Array.from(uncontrolledColumns).map(([key, col]) =>\n      [key, col.props.defaultWidth ?? this.getDefaultWidth?.(col) ?? '1fr']\n    ));\n  }\n\n  getColumnWidth(key: Key): number {\n    return this.columnWidths.get(key) ?? 0;\n  }\n\n  getColumnMinWidth(key: Key): number {\n    return this.columnMinWidths.get(key) ?? 0;\n  }\n\n  getColumnMaxWidth(key: Key): number {\n    return this.columnMaxWidths.get(key) ?? 0;\n  }\n\n  resizeColumnWidth(collection: TableCollection<T>, uncontrolledWidths: Map<Key, ColumnSize>, col: Key, width: number): Map<Key, ColumnSize> {\n    let prevColumnWidths = this.columnWidths;\n    let freeze = true;\n    let newWidths = new Map<Key, ColumnSize>();\n\n    width = Math.max(this.getColumnMinWidth(col), Math.min(this.getColumnMaxWidth(col), Math.floor(width)));\n\n    collection.columns.forEach(column => {\n      if (column.key === col) {\n        newWidths.set(column.key, width);\n        freeze = false;\n      } else if (freeze) {\n        // freeze columns to the left to their previous pixel value\n        newWidths.set(column.key, prevColumnWidths.get(column.key) ?? 0);\n      } else {\n        newWidths.set(column.key, column.props.width ?? uncontrolledWidths.get(column.key));\n      }\n    });\n\n    return newWidths;\n  }\n\n  buildColumnWidths(tableWidth: number, collection: TableCollection<T>, widths: Map<Key, ColumnSize>): Map<Key, number> {\n    this.columnWidths = new Map();\n    this.columnMinWidths = new Map();\n    this.columnMaxWidths = new Map();\n\n    // initial layout or table/window resizing\n    let columnWidths = calculateColumnSizes(\n      tableWidth,\n      collection.columns.map(col => ({...col.props, key: col.key})),\n      widths,\n      (i) => this.getDefaultWidth(collection.columns[i]),\n      (i) => this.getDefaultMinWidth(collection.columns[i])\n    );\n\n    // columns going in will be the same order as the columns coming out\n    columnWidths.forEach((width, index) => {\n      let key = collection.columns[index].key;\n      let column = collection.columns[index];\n      this.columnWidths.set(key, width);\n      this.columnMinWidths.set(key, getMinWidth(column.props.minWidth ?? this.getDefaultMinWidth(column), tableWidth));\n      this.columnMaxWidths.set(key, getMaxWidth(column.props.maxWidth, tableWidth));\n    });\n    return this.columnWidths;\n  }\n}\n"], "names": [], "version": 3, "file": "TableColumnLayout.module.js.map"}
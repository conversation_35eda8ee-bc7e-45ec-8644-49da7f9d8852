{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAOD,SAAS,0BAAO,KAAkB;IAChC,OAAO;AACT;AAEA,0BAAI,iBAAiB,GAAG,UAAU,kBAAqB,KAAkB,EAAE,OAAoC;IAC7G,IAAI,YAAC,QAAQ,aAAE,SAAS,uBAAE,mBAAmB,EAAC,GAAG;IAEjD,MAAM;QACJ,MAAM;QACN,OAAO;mBACP;QACA,cAAc,KAAK,CAAC,aAAa;QACjC,eAAe;QACf,CAAC;YACC,sBAAsB;YACtB,IAAI,QAAQ,eAAe,EACzB,MAAM;gBACJ,MAAM;gBACN,KAAK;gBACL,OAAO;oBACL,kBAAkB;gBACpB;YACF;YAGF,IAAI,QAAQ,uBAAuB,IAAI,QAAQ,aAAa,KAAK,QAC/D,MAAM;gBACJ,MAAM;gBACN,KAAK;gBACL,OAAO;oBACL,iBAAiB;gBACnB;YACF;YAGF,IAAI,OAAO,aAAa,YAAY;gBAClC,KAAK,IAAI,UAAU,QAAQ,OAAO,CAChC,MAAM;oBACJ,MAAM;oBACN,SAAS,SAAS,OAAO,GAAG;oBAC5B,KAAK,OAAO,GAAG,CAAC,yDAAyD;gBAC3E;gBAGF,IAAI,qBACF,KAAK,IAAI,SAAS,oBAChB,kKAAkK;gBAClK,iIAAiI;gBACjI,MAAM;oBACJ,MAAM;oBACN,OAAO;gBACT;YAGN,OAAO;gBACL,IAAI,QAA0B,EAAE;gBAChC,IAAI,YAA8B,EAAE;gBACpC,IAAI,cAAc;gBAClB,CAAA,GAAA,YAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;oBAC/B,IAAI,KAAK,IAAI,KAAK,2BAAK;wBACrB,IAAI,MAAM,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,EACvC,MAAM,IAAI,MAAM;wBAGlB,UAAU,IAAI,CAAC;4BACb,MAAM;4BACN,SAAS;wBACX;oBACF,OAAO;wBACL,MAAM,IAAI,CAAC;4BACT,MAAM;4BACN,SAAS;wBACX;4BACe;wBAAf,eAAe,CAAA,sBAAA,KAAK,KAAK,CAAC,OAAO,cAAlB,iCAAA,sBAAsB;oBACvC;gBACF;gBAEA,IAAI,gBAAgB,QAAQ,OAAO,CAAC,MAAM,EACxC,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,YAAY,WAAW,EAAE,QAAQ,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;gBAGzH,OAAO;gBACP,OAAO;YACT;QACF;QACA,kBAAiB,UAAuC;YACtD,8CAA8C;YAC9C,OAAO,WAAW,OAAO,CAAC,MAAM,KAAK,QAAQ,OAAO,CAAC,MAAM,IACzD,WAAW,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,KAAK,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,KAClE,WAAW,uBAAuB,KAAK,QAAQ,uBAAuB,IACtE,WAAW,eAAe,KAAK,QAAQ,eAAe,IACtD,WAAW,aAAa,KAAK,QAAQ,aAAa;QACtD;IACF;AACF;AAEA;;;;CAIC,GACD,oEAAoE;AACpE,IAAI,4CAAO", "sources": ["packages/@react-stately/table/src/Row.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CollectionBuilderContext} from './useTableState';\nimport {PartialNode} from '@react-stately/collections';\nimport React, {JSX, ReactElement} from 'react';\nimport {RowProps} from '@react-types/table';\n\nfunction Row<T>(props: RowProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nRow.getCollectionNode = function* getCollectionNode<T>(props: RowProps<T>, context: CollectionBuilderContext<T>): Generator<PartialNode<T>> {\n  let {children, textValue, UNSTABLE_childItems} = props;\n\n  yield {\n    type: 'item',\n    props: props,\n    textValue,\n    'aria-label': props['aria-label'],\n    hasChildNodes: true,\n    *childNodes() {\n      // Process cells first\n      if (context.showDragButtons) {\n        yield {\n          type: 'cell',\n          key: 'header-drag', // this is combined with the row key by CollectionBuilder\n          props: {\n            isDragButtonCell: true\n          }\n        };\n      }\n\n      if (context.showSelectionCheckboxes && context.selectionMode !== 'none') {\n        yield {\n          type: 'cell',\n          key: 'header', // this is combined with the row key by CollectionBuilder\n          props: {\n            isSelectionCell: true\n          }\n        };\n      }\n\n      if (typeof children === 'function') {\n        for (let column of context.columns) {\n          yield {\n            type: 'cell',\n            element: children(column.key),\n            key: column.key // this is combined with the row key by CollectionBuilder\n          };\n        }\n\n        if (UNSTABLE_childItems) {\n          for (let child of UNSTABLE_childItems) {\n            // Note: in order to reuse the render function of TableBody for our child rows, we just need to yield a type and a value here. CollectionBuilder will then look up\n            // the parent renderer and use that to build the full node of this child row, using the value provided here to generate the cells\n            yield {\n              type: 'item',\n              value: child\n            };\n          }\n        }\n      } else {\n        let cells: PartialNode<T>[] = [];\n        let childRows: PartialNode<T>[] = [];\n        let columnCount = 0;\n        React.Children.forEach(children, node => {\n          if (node.type === Row) {\n            if (cells.length < context.columns.length) {\n              throw new Error('All of a Row\\'s child Cells must be positioned before any child Rows.');\n            }\n\n            childRows.push({\n              type: 'item',\n              element: node\n            });\n          } else {\n            cells.push({\n              type: 'cell',\n              element: node\n            });\n            columnCount += node.props.colSpan ?? 1;\n          }\n        });\n\n        if (columnCount !== context.columns.length) {\n          throw new Error(`Cell count must match column count. Found ${columnCount} cells and ${context.columns.length} columns.`);\n        }\n\n        yield* cells;\n        yield* childRows;\n      }\n    },\n    shouldInvalidate(newContext: CollectionBuilderContext<T>) {\n      // Invalidate all rows if the columns changed.\n      return newContext.columns.length !== context.columns.length ||\n        newContext.columns.some((c, i) => c.key !== context.columns[i].key) ||\n        newContext.showSelectionCheckboxes !== context.showSelectionCheckboxes ||\n        newContext.showDragButtons !== context.showDragButtons ||\n        newContext.selectionMode !== context.selectionMode;\n    }\n  };\n};\n\n/**\n * A Row represents a single item in a Table and contains Cell elements for each column.\n * Cells can be statically defined as children, or generated dynamically using a function\n * based on the columns defined in the TableHeader.\n */\n// We don't want getCollectionNode to show up in the type definition\nlet _Row = Row as <T>(props: RowProps<T>) => JSX.Element;\nexport {_Row as Row};\n"], "names": [], "version": 3, "file": "Row.module.js.map"}
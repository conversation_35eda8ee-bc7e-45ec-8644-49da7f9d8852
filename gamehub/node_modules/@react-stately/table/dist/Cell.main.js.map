{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAMD,SAAS,2BAAK,KAAgB;IAC5B,OAAO;AACT;AAEA,2BAAK,iBAAiB,GAAG,UAAU,kBAAqB,KAAgB;IACtE,IAAI,YAAC,QAAQ,EAAC,GAAG;IAEjB,IAAI,YAAY,MAAM,SAAS,IAAK,CAAA,OAAO,aAAa,WAAW,WAAW,EAAC,KAAM,KAAK,CAAC,aAAa,IAAI;IAC5G,MAAM;QACJ,MAAM;QACN,OAAO;QACP,UAAU;mBACV;QACA,cAAc,KAAK,CAAC,aAAa;QACjC,eAAe;IACjB;AACF;AAEA;;CAEC,GACD,oEAAoE;AACpE,IAAI,4CAAQ", "sources": ["packages/@react-stately/table/src/Cell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CellProps} from '@react-types/table';\nimport {JSX, ReactElement} from 'react';\nimport {PartialNode} from '@react-stately/collections';\n\nfunction Cell(props: CellProps): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nCell.getCollectionNode = function* getCollectionNode<T>(props: CellProps): Generator<PartialNode<T>> {\n  let {children} = props;\n\n  let textValue = props.textValue || (typeof children === 'string' ? children : '') || props['aria-label'] || '';\n  yield {\n    type: 'cell',\n    props: props,\n    rendered: children,\n    textValue,\n    'aria-label': props['aria-label'],\n    hasChildNodes: false\n  };\n};\n\n/**\n * A Cell represents the value of a single Column within a Table Row.\n */\n// We don't want getCollectionNode to show up in the type definition\nlet _Cell = Cell as (props: CellProps) => JSX.Element;\nexport {_Cell as Cell};\n"], "names": [], "version": 3, "file": "Cell.main.js.map"}
{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;AAQD,SAAS,6BAAU,KAAqB;IACtC,OAAO;AACT;AAEA,6BAAO,iBAAiB,GAAG,UAAU,kBAAqB,KAAqB,EAAE,OAAoC;IACnH,IAAI,SAAC,KAAK,YAAE,QAAQ,gBAAE,YAAY,EAAC,GAAG;IAEtC,IAAI,WAAW,SAAS;IACxB,IAAI,YAAY,MAAM,SAAS,IAAK,CAAA,OAAO,aAAa,WAAW,WAAW,EAAC,KAAM,KAAK,CAAC,aAAa;IAExG,IAAI,YAAY,MAAM;QACpB,MAAM;QACN,eAAe,CAAC,CAAC,gBAAiB,CAAC,CAAC,SAAS,CAAA,GAAA,sCAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;kBAC9E;mBACA;eACA;QACA,CAAC;YACC,IAAI,cACF,KAAK,IAAI,SAAS,aAChB,MAAM;gBACJ,MAAM;gBACN,OAAO;YACT;iBAEG,IAAI,OAAO;gBAChB,IAAI,eAAiC,EAAE;gBACvC,CAAA,GAAA,sCAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;oBAC/B,aAAa,IAAI,CAAC;wBAChB,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,OAAO;YACT;QACF;QACA,kBAAiB,UAAuC;YACtD,yCAAyC;YACzC,kFAAkF;YAClF,0EAA0E;YAC1E,cAAc;YACd,OAAO;QACT;IACF;IAEA,IAAI,gBAAgB,CAAC;QACnB,qEAAqE;QACrE,KAAK,IAAI,QAAQ,UACf,IAAI,CAAC,KAAK,aAAa,EACrB,QAAQ,OAAO,CAAC,IAAI,CAAC;IAG3B;IAEA,cAAc;AAChB;AAEA;;;;CAIC,GACD,oEAAoE;AACpE,IAAI,4CAAU", "sources": ["packages/@react-stately/table/src/Column.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CollectionBuilderContext} from './useTableState';\nimport {ColumnProps} from '@react-types/table';\nimport {GridNode} from '@react-types/grid';\nimport {PartialNode} from '@react-stately/collections';\nimport React, {JSX, ReactElement} from 'react';\n\nfunction Column<T>(props: ColumnProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nColumn.getCollectionNode = function* getCollectionNode<T>(props: ColumnProps<T>, context: CollectionBuilderContext<T>): Generator<PartialNode<T>, void, GridNode<T>[]> {\n  let {title, children, childColumns} = props;\n\n  let rendered = title || children;\n  let textValue = props.textValue || (typeof rendered === 'string' ? rendered : '') || props['aria-label'];\n\n  let fullNodes = yield {\n    type: 'column',\n    hasChildNodes: !!childColumns || (!!title && React.Children.count(children) > 0),\n    rendered,\n    textValue,\n    props,\n    *childNodes() {\n      if (childColumns) {\n        for (let child of childColumns) {\n          yield {\n            type: 'column',\n            value: child\n          };\n        }\n      } else if (title) {\n        let childColumns: PartialNode<T>[] = [];\n        React.Children.forEach(children, child => {\n          childColumns.push({\n            type: 'column',\n            element: child as ReactElement<ColumnProps<T>>\n          });\n        });\n\n        yield* childColumns;\n      }\n    },\n    shouldInvalidate(newContext: CollectionBuilderContext<T>) {\n      // This is a bit of a hack, but it works.\n      // If this method is called, then there's a cached version of this node available.\n      // But, we need to keep the list of columns in the new context up to date.\n      updateContext(newContext);\n      return false;\n    }\n  };\n\n  let updateContext = (context: CollectionBuilderContext<T>) => {\n    // register leaf columns on the context so that <Row> can access them\n    for (let node of fullNodes) {\n      if (!node.hasChildNodes) {\n        context.columns.push(node);\n      }\n    }\n  };\n\n  updateContext(context);\n};\n\n/**\n * A Column represents a field of each item within a Table. Columns may also contain nested\n * Column elements to represent column groups. Nested columns can be statically defined as\n * children, or dynamically generated using a function based on the `childColumns` prop.\n */\n// We don't want getCollectionNode to show up in the type definition\nlet _Column = Column as <T>(props: ColumnProps<T>) => JSX.Element;\nexport {_Column as Column};\n"], "names": [], "version": 3, "file": "Column.main.js.map"}
{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAaD,MAAM,8CAAwB,uBAAuB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;AACtF,IAAI,mDAA6B,uBAAuB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;AACzF,MAAO,gDAA0B,iDAC/B,mDAA6B,uBAAuB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;AAIhF,SAAS,0CAAmB,MAA6B,EAAE,WAA0B;IAC1F,IAAI,YAAY,MAAM,KAAK,GACzB,OAAO,EAAE;IAGX,IAAI,UAA2B,EAAE;IACjC,IAAI,OAAO,IAAI;IACf,KAAK,IAAI,UAAU,YAAa;QAC9B,IAAI,YAAY,OAAO,SAAS;QAChC,IAAI,MAAM;YAAC;SAAO;QAElB,MAAO,UAAW;YAChB,IAAI,SAAkC,OAAO,GAAG,CAAC;YACjD,IAAI,CAAC,QACH;YAGF,uDAAuD;YACvD,0DAA0D;YAC1D,6DAA6D;YAC7D,gEAAgE;YAChE,IAAI,KAAK,GAAG,CAAC,SAAS;oBACpB;;gBAAA,aAAA,UAAA,QAAO,sDAAP,QAAO,UAAY;gBACnB,OAAO,OAAO;gBACd,OAAO,OAAO,GAAG,OAAO,OAAO;gBAE/B,IAAI,UAAC,MAAM,SAAE,KAAK,EAAC,GAAG,KAAK,GAAG,CAAC;gBAC/B,IAAI,QAAQ,IAAI,MAAM,EACpB;gBAGF,IAAK,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,EAAE,IAClC,OAAO,MAAM,CAAC,GAAG,GAAG;gBAGtB,yBAAyB;gBACzB,IAAK,IAAI,IAAI,IAAI,MAAM,EAAE,IAAI,OAAO,MAAM,EAAE,IAC1C,qCAAqC;gBACrC,IAAI,MAAM,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GACjC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,GAAG;YAGlC,OAAO;gBACL,OAAO,OAAO,GAAG;gBACjB,OAAO,OAAO,GAAG;gBACjB,IAAI,IAAI,CAAC;gBACT,KAAK,GAAG,CAAC,QAAQ;oBAAC,QAAQ;oBAAK,OAAO,IAAI,MAAM,GAAG;gBAAC;YACtD;YAEA,YAAY,OAAO,SAAS;QAC9B;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,KAAK,GAAG,QAAQ,MAAM,GAAG;IAClC;IAEA,IAAI,YAAY,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;IACrD,IAAI,aAA8B,MAAM,WAAW,IAAI,CAAC,GAAG,GAAG,CAAC,IAAM,EAAE;IAEvE,6BAA6B;IAC7B,IAAI,WAAW;IACf,KAAK,IAAI,UAAU,QAAS;QAC1B,IAAI,IAAI,YAAY;QACpB,KAAK,IAAI,QAAQ,OAAQ;YACvB,IAAI,MAAM;gBACR,gEAAgE;gBAChE,IAAI,MAAM,UAAU,CAAC,EAAE;gBACvB,IAAI,YAAY,IAAI,MAAM,CAAC,CAAC,GAAG;wBAAW;2BAAL,IAAK,CAAA,CAAA,aAAA,EAAE,OAAO,cAAT,wBAAA,aAAa,CAAA;mBAAI;gBAC3D,IAAI,YAAY,UAAU;oBACxB,IAAI,cAA2B;wBAC7B,MAAM;wBACN,KAAK,iBAAiB,KAAK,GAAG;wBAC9B,SAAS,WAAW;wBACpB,SAAS,WAAW;wBACpB,OAAO;wBACP,OAAO;wBACP,UAAU;wBACV,OAAO;wBACP,eAAe;wBACf,YAAY,EAAE;wBACd,WAAW;oBACb;oBAEA,qCAAqC;oBACrC,IAAI,IAAI,MAAM,GAAG,GAAG;wBAClB,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,OAAO,GAAG,YAAY,GAAG;wBAC7C,YAAY,OAAO,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,GAAG;oBAC/C;oBAEA,IAAI,IAAI,CAAC;gBACX;gBAEA,IAAI,IAAI,MAAM,GAAG,GAAG;oBAClB,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,GAAG;oBACtC,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,GAAG;gBACxC;gBAEA,KAAK,KAAK,GAAG;gBACb,KAAK,QAAQ,GAAG;gBAChB,IAAI,IAAI,CAAC;YACX;YAEA;QACF;QAEA;IACF;IAEA,2EAA2E;IAC3E,IAAI,IAAI;IACR,KAAK,IAAI,OAAO,WAAY;QAC1B,IAAI,YAAY,IAAI,MAAM,CAAC,CAAC,GAAG;gBAAW;mBAAL,IAAK,CAAA,CAAA,aAAA,EAAE,OAAO,cAAT,wBAAA,aAAa,CAAA;WAAI;QAC3D,IAAI,YAAY,YAAY,MAAM,EAAE;YAClC,IAAI,cAA2B;gBAC7B,MAAM;gBACN,KAAK,iBAAiB,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,GAAG;gBAC7C,SAAS,YAAY,MAAM,GAAG;gBAC9B,SAAS,YAAY,MAAM,GAAG;gBAC9B,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,YAAY,EAAE;gBACd,WAAW;gBACX,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,CAAC,GAAG;YAClC;YAEA,IAAI,IAAI,CAAC;QACX;QAEA;IACF;IAEA,OAAO,WAAW,GAAG,CAAC,CAAC,YAAY;QACjC,IAAI,MAAmB;YACrB,MAAM;YACN,KAAK,eAAe;mBACpB;YACA,OAAO;YACP,UAAU;YACV,OAAO;YACP,eAAe;wBACf;YACA,WAAW;QACb;QAEA,OAAO;IACT;AACF;AAEO,MAAM,kDAA2B,CAAA,GAAA,qBAAa;IA2GnD,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAkC;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;IAC7B;IAEA,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,UAAiC;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACpB;QAAP,OAAO,CAAA,gBAAA,iBAAA,2BAAA,KAAM,OAAO,cAAb,2BAAA,gBAAiB;IAC1B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACpB;QAAP,OAAO,CAAA,gBAAA,iBAAA,2BAAA,KAAM,OAAO,cAAb,2BAAA,gBAAiB;IAC1B;IAEA,cAA0B;YACjB;YAAA;QAAP,OAAO,CAAA,qBAAA,gBAAA,CAAA,GAAA,mBAAW,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,eAAjC,oCAAA,cAAoC,GAAG,cAAvC,+BAAA,oBAA2C;IACpD;IAEA,aAAyB;YAChB;YAAA;QAAP,OAAO,CAAA,oBAAA,eAAA,CAAA,GAAA,kBAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,eAAhC,mCAAA,aAAmC,GAAG,cAAtC,8BAAA,mBAA0C;IACnD;IAEA,QAAQ,GAAQ,EAAsB;YAC7B;QAAP,OAAO,CAAA,mBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAhB,8BAAA,mBAAwB;IACjC;IAEA,GAAG,GAAW,EAAsB;QAClC,MAAM,OAAO;eAAI,IAAI,CAAC,OAAO;SAAG;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IAC/B;IAEA,YAAY,GAAQ,EAAyB;QAC3C,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,EACvB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU;QAG7B,OAAO,KAAK,CAAC,YAAY;IAC3B;IAEA,aAAa,GAAQ,EAAU;QAC7B,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,KACH,OAAO;QAGT,wCAAwC;QACxC,IAAI,IAAI,SAAS,EACf,OAAO,IAAI,SAAS;QAGtB,gEAAgE;QAChE,IAAI,sBAAsB,IAAI,CAAC,mBAAmB;QAClD,IAAI,qBAAqB;YACvB,IAAI,OAAiB,EAAE;YACvB,KAAK,IAAI,QAAQ,IAAI,UAAU,CAAE;gBAC/B,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;gBACrC,IAAI,oBAAoB,GAAG,CAAC,OAAO,GAAG,KAAK,KAAK,SAAS,EACvD,KAAK,IAAI,CAAC,KAAK,SAAS;gBAG1B,IAAI,KAAK,MAAM,KAAK,oBAAoB,IAAI,EAC1C;YAEJ;YAEA,OAAO,KAAK,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAjLA,YAAY,KAA4B,EAAE,IAAiC,EAAE,IAA4B,CAAE;QACzG,IAAI,sBAAgC,IAAI;QACxC,IAAI,OAA2B;QAC/B,IAAI,UAAyB,EAAE;QAC/B,+CAA+C;QAC/C,IAAI,iBAAA,2BAAA,KAAM,uBAAuB,EAAE;YACjC,IAAI,kBAA+B;gBACjC,MAAM;gBACN,KAAK;gBACL,OAAO;gBACP,WAAW;gBACX,OAAO;gBACP,OAAO,CAAA,iBAAA,2BAAA,KAAM,eAAe,IAAG,IAAI;gBACnC,eAAe;gBACf,UAAU;gBACV,YAAY,EAAE;gBACd,OAAO;oBACL,iBAAiB;gBACnB;YACF;YAEA,QAAQ,OAAO,CAAC;QAClB;QAEA,uCAAuC;QACvC,IAAI,iBAAA,2BAAA,KAAM,eAAe,EAAE;YACzB,IAAI,kBAA+B;gBACjC,MAAM;gBACN,KAAK;gBACL,OAAO;gBACP,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,UAAU;gBACV,YAAY,EAAE;gBACd,OAAO;oBACL,kBAAkB;gBACpB;YACF;YAEA,QAAQ,OAAO,CAAC;QAClB;QAEA,IAAI,OAAsB,EAAE;QAC5B,IAAI,eAAe,IAAI;QACvB,IAAI,QAAQ,CAAC;YACX,OAAQ,KAAK,IAAI;gBACf,KAAK;oBACH,OAAO;oBACP;gBACF,KAAK;oBACH,aAAa,GAAG,CAAC,KAAK,GAAG,EAAE;oBAC3B,IAAI,CAAC,KAAK,aAAa,EAAE;wBACvB,QAAQ,IAAI,CAAC;wBAEb,IAAI,KAAK,KAAK,CAAC,WAAW,EACxB,oBAAoB,GAAG,CAAC,KAAK,GAAG;oBAEpC;oBACA;gBACF,KAAK;oBACH,KAAK,IAAI,CAAC;oBACV,QAAQ,4BAA4B;YACxC;YACA,KAAK,IAAI,SAAS,KAAK,UAAU,CAC/B,MAAM;QAEV;QAEA,KAAK,IAAI,QAAQ,MACf,MAAM;QAGR,IAAI,aAAa,0CAAgB,cAAc;QAC/C,WAAW,OAAO,CAAC,CAAC,KAAK,IAAM,KAAK,MAAM,CAAC,GAAG,GAAG;QAEjD,KAAK,CAAC;YACJ,aAAa,QAAQ,MAAM;YAC3B,OAAO;YACP,WAAW,CAAA;gBACT,KAAK,MAAM,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC;gBACjC,OAAO;YACT;QACF,SAtFF,QAAgB;QAuFd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;eAAI,KAAM,UAAU;SAAC,CAAC,MAAM;QAEzC,8CAA8C;QAC9C,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,GAAG;YACvC,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;oBAAW,eAAmC;uBAApC,GAAC,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,gBAAgB,KAAI,GAAC,iBAAA,OAAO,KAAK,cAAZ,qCAAA,eAAc,eAAe;;YACvG,IAAI,KACF,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,GAAG;QAExC;IACF;AAgFF", "sources": ["packages/@react-stately/table/src/TableCollection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getFirstItem, getLastItem} from '@react-stately/collections';\nimport {GridCollection} from '@react-stately/grid';\nimport {GridNode} from '@react-types/grid';\nimport {TableCollection as ITableCollection} from '@react-types/table';\nimport {Key} from '@react-types/shared';\n\ninterface GridCollectionOptions {\n  showSelectionCheckboxes?: boolean,\n  showDragButtons?: boolean\n}\n\nconst ROW_HEADER_COLUMN_KEY = 'row-header-column-' + Math.random().toString(36).slice(2);\nlet ROW_HEADER_COLUMN_KEY_DRAG = 'row-header-column-' + Math.random().toString(36).slice(2);\nwhile (ROW_HEADER_COLUMN_KEY === ROW_HEADER_COLUMN_KEY_DRAG) {\n  ROW_HEADER_COLUMN_KEY_DRAG = 'row-header-column-' + Math.random().toString(36).slice(2);\n}\n\n/** @private */\nexport function buildHeaderRows<T>(keyMap: Map<Key, GridNode<T>>, columnNodes: GridNode<T>[]): GridNode<T>[] {\n  if (columnNodes.length === 0) {\n    return [];\n  }\n\n  let columns: GridNode<T>[][] = [];\n  let seen = new Map();\n  for (let column of columnNodes) {\n    let parentKey = column.parentKey;\n    let col = [column];\n\n    while (parentKey) {\n      let parent: GridNode<T> | undefined = keyMap.get(parentKey);\n      if (!parent) {\n        break;\n      }\n\n      // If we've already seen this parent, than it is shared\n      // with a previous column. If the current column is taller\n      // than the previous column, than we need to shift the parent\n      // in the previous column so it's level with the current column.\n      if (seen.has(parent)) {\n        parent.colSpan ??= 0;\n        parent.colSpan++;\n        parent.colspan = parent.colSpan;\n\n        let {column, index} = seen.get(parent);\n        if (index > col.length) {\n          break;\n        }\n\n        for (let i = index; i < col.length; i++) {\n          column.splice(i, 0, null);\n        }\n\n        // Adjust shifted indices\n        for (let i = col.length; i < column.length; i++) {\n          // eslint-disable-next-line max-depth\n          if (column[i] && seen.has(column[i])) {\n            seen.get(column[i]).index = i;\n          }\n        }\n      } else {\n        parent.colSpan = 1;\n        parent.colspan = 1;\n        col.push(parent);\n        seen.set(parent, {column: col, index: col.length - 1});\n      }\n\n      parentKey = parent.parentKey;\n    }\n\n    columns.push(col);\n    column.index = columns.length - 1;\n  }\n\n  let maxLength = Math.max(...columns.map(c => c.length));\n  let headerRows: GridNode<T>[][] = Array(maxLength).fill(0).map(() => []);\n\n  // Convert columns into rows.\n  let colIndex = 0;\n  for (let column of columns) {\n    let i = maxLength - 1;\n    for (let item of column) {\n      if (item) {\n        // Fill the space up until the current column with a placeholder\n        let row = headerRows[i];\n        let rowLength = row.reduce((p, c) => p + (c.colSpan ?? 1), 0);\n        if (rowLength < colIndex) {\n          let placeholder: GridNode<T> = {\n            type: 'placeholder',\n            key: 'placeholder-' + item.key,\n            colspan: colIndex - rowLength,\n            colSpan: colIndex - rowLength,\n            index: rowLength,\n            value: null,\n            rendered: null,\n            level: i,\n            hasChildNodes: false,\n            childNodes: [],\n            textValue: ''\n          };\n\n          // eslint-disable-next-line max-depth\n          if (row.length > 0) {\n            row[row.length - 1].nextKey = placeholder.key;\n            placeholder.prevKey = row[row.length - 1].key;\n          }\n\n          row.push(placeholder);\n        }\n\n        if (row.length > 0) {\n          row[row.length - 1].nextKey = item.key;\n          item.prevKey = row[row.length - 1].key;\n        }\n\n        item.level = i;\n        item.colIndex = colIndex;\n        row.push(item);\n      }\n\n      i--;\n    }\n\n    colIndex++;\n  }\n\n  // Add placeholders at the end of each row that is shorter than the maximum\n  let i = 0;\n  for (let row of headerRows) {\n    let rowLength = row.reduce((p, c) => p + (c.colSpan ?? 1), 0);\n    if (rowLength < columnNodes.length) {\n      let placeholder: GridNode<T> = {\n        type: 'placeholder',\n        key: 'placeholder-' + row[row.length - 1].key,\n        colSpan: columnNodes.length - rowLength,\n        colspan: columnNodes.length - rowLength,\n        index: rowLength,\n        value: null,\n        rendered: null,\n        level: i,\n        hasChildNodes: false,\n        childNodes: [],\n        textValue: '',\n        prevKey: row[row.length - 1].key\n      };\n\n      row.push(placeholder);\n    }\n\n    i++;\n  }\n\n  return headerRows.map((childNodes, index) => {\n    let row: GridNode<T> = {\n      type: 'headerrow',\n      key: 'headerrow-' + index,\n      index,\n      value: null,\n      rendered: null,\n      level: 0,\n      hasChildNodes: true,\n      childNodes,\n      textValue: ''\n    };\n\n    return row;\n  });\n}\n\nexport class TableCollection<T> extends GridCollection<T> implements ITableCollection<T> {\n  headerRows: GridNode<T>[];\n  columns: GridNode<T>[];\n  rowHeaderColumnKeys: Set<Key>;\n  body: GridNode<T>;\n  _size: number = 0;\n\n  constructor(nodes: Iterable<GridNode<T>>, prev?: ITableCollection<T> | null, opts?: GridCollectionOptions) {\n    let rowHeaderColumnKeys: Set<Key> = new Set();\n    let body: GridNode<T> | null = null;\n    let columns: GridNode<T>[] = [];\n    // Add cell for selection checkboxes if needed.\n    if (opts?.showSelectionCheckboxes) {\n      let rowHeaderColumn: GridNode<T> = {\n        type: 'column',\n        key: ROW_HEADER_COLUMN_KEY,\n        value: null,\n        textValue: '',\n        level: 0,\n        index: opts?.showDragButtons ? 1 : 0,\n        hasChildNodes: false,\n        rendered: null,\n        childNodes: [],\n        props: {\n          isSelectionCell: true\n        }\n      };\n\n      columns.unshift(rowHeaderColumn);\n    }\n\n    // Add cell for drag buttons if needed.\n    if (opts?.showDragButtons) {\n      let rowHeaderColumn: GridNode<T> = {\n        type: 'column',\n        key: ROW_HEADER_COLUMN_KEY_DRAG,\n        value: null,\n        textValue: '',\n        level: 0,\n        index: 0,\n        hasChildNodes: false,\n        rendered: null,\n        childNodes: [],\n        props: {\n          isDragButtonCell: true\n        }\n      };\n\n      columns.unshift(rowHeaderColumn);\n    }\n\n    let rows: GridNode<T>[] = [];\n    let columnKeyMap = new Map();\n    let visit = (node: GridNode<T>) => {\n      switch (node.type) {\n        case 'body':\n          body = node;\n          break;\n        case 'column':\n          columnKeyMap.set(node.key, node);\n          if (!node.hasChildNodes) {\n            columns.push(node);\n\n            if (node.props.isRowHeader) {\n              rowHeaderColumnKeys.add(node.key);\n            }\n          }\n          break;\n        case 'item':\n          rows.push(node);\n          return; // do not go into childNodes\n      }\n      for (let child of node.childNodes) {\n        visit(child);\n      }\n    };\n\n    for (let node of nodes) {\n      visit(node);\n    }\n\n    let headerRows = buildHeaderRows(columnKeyMap, columns) as GridNode<T>[];\n    headerRows.forEach((row, i) => rows.splice(i, 0, row));\n\n    super({\n      columnCount: columns.length,\n      items: rows,\n      visitNode: node => {\n        node.column = columns[node.index];\n        return node;\n      }\n    });\n    this.columns = columns;\n    this.rowHeaderColumnKeys = rowHeaderColumnKeys;\n    this.body = body!;\n    this.headerRows = headerRows;\n    this._size = [...body!.childNodes].length;\n\n    // Default row header column to the first one.\n    if (this.rowHeaderColumnKeys.size === 0) {\n      let col = this.columns.find(column => !column.props?.isDragButtonCell && !column.props?.isSelectionCell);\n      if (col) {\n        this.rowHeaderColumnKeys.add(col.key);\n      }\n    }\n  }\n\n  *[Symbol.iterator](): IterableIterator<GridNode<T>> {\n    yield* this.body.childNodes;\n  }\n\n  get size(): number {\n    return this._size;\n  }\n\n  getKeys(): IterableIterator<Key> {\n    return this.keyMap.keys();\n  }\n\n  getKeyBefore(key: Key): Key | null {\n    let node = this.keyMap.get(key);\n    return node?.prevKey ?? null;\n  }\n\n  getKeyAfter(key: Key): Key | null {\n    let node = this.keyMap.get(key);\n    return node?.nextKey ?? null;\n  }\n\n  getFirstKey(): Key | null {\n    return getFirstItem(this.body.childNodes)?.key ?? null;\n  }\n\n  getLastKey(): Key | null {\n    return getLastItem(this.body.childNodes)?.key ?? null;\n  }\n\n  getItem(key: Key): GridNode<T> | null {\n    return this.keyMap.get(key) ?? null;\n  }\n\n  at(idx: number): GridNode<T> | null {\n    const keys = [...this.getKeys()];\n    return this.getItem(keys[idx]);\n  }\n\n  getChildren(key: Key): Iterable<GridNode<T>> {\n    if (key === this.body.key) {\n      return this.body.childNodes;\n    }\n\n    return super.getChildren(key);\n  }\n\n  getTextValue(key: Key): string {\n    let row = this.getItem(key);\n    if (!row) {\n      return '';\n    }\n\n    // If the row has a textValue, use that.\n    if (row.textValue) {\n      return row.textValue;\n    }\n\n    // Otherwise combine the text of each of the row header columns.\n    let rowHeaderColumnKeys = this.rowHeaderColumnKeys;\n    if (rowHeaderColumnKeys) {\n      let text: string[] = [];\n      for (let cell of row.childNodes) {\n        let column = this.columns[cell.index];\n        if (rowHeaderColumnKeys.has(column.key) && cell.textValue) {\n          text.push(cell.textValue);\n        }\n\n        if (text.length === rowHeaderColumnKeys.size) {\n          break;\n        }\n      }\n\n      return text.join(' ');\n    }\n\n    return '';\n  }\n}\n"], "names": [], "version": 3, "file": "TableCollection.module.js.map"}
{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AAiDD,MAAM,gDAA0B;IAC9B,WAAW;IACX,YAAY;AACd;AAMO,SAAS,0CAAgC,KAAyB;IACvE,IAAI,CAAC,8BAA8B,8BAA8B,GAAG,CAAA,GAAA,eAAO,EAAE;IAC7E,IAAI,iBAAC,gBAAgB,iCAAQ,uBAAuB,mBAAE,eAAe,EAAC,GAAG;IAEzE,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;YAC3B,yBAAyB,2BAA2B,kBAAkB;YACtE,iBAAiB;2BACjB;YACA,SAAS,EAAE;QAEb,CAAA,GAAI;QAAC,MAAM,QAAQ;QAAE;QAAyB;QAAe;KAAgB;IAE7E,IAAI,aAAa,CAAA,GAAA,oBAAY,EAC3B,OACA,CAAA,GAAA,kBAAU,EAAE,CAAC,QAAU,IAAI,CAAA,GAAA,yCAAc,EAAE,OAAO,MAAM,UAAU;QAAC;KAAQ,GAC3E;IAEF,IAAI,gBAAC,YAAY,oBAAE,gBAAgB,EAAC,GAAG,CAAA,GAAA,mBAAW,EAAE;QAClD,GAAG,KAAK;oBACR;QACA,kBAAkB,MAAM,gBAAgB,IAAI;IAC9C;QAOkB;IALlB,OAAO;oBACL;sBACA;0BACA;QACA,yBAAyB,MAAM,uBAAuB,IAAI;QAC1D,gBAAgB,CAAA,wBAAA,MAAM,cAAc,cAApB,mCAAA,wBAAwB;QACxC,8BAA8B,WAAW,IAAI,KAAK,KAAK;uCACvD;QACA,MAAK,SAAc,EAAE,SAAsC;gBAG9B,uBAF3B;aAAA,sBAAA,MAAM,YAAY,cAAlB,0CAAA,yBAAA,OAAqB;gBACnB,QAAQ;gBACR,WAAW,sBAAA,uBAAA,YAAc,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,MAAM,MAAK,YACtD,6CAAuB,CAAC,MAAM,cAAc,CAAC,SAAS,CAAC,GACvD;YACN;QACF;IACF;AACF", "sources": ["packages/@react-stately/table/src/useTableState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {GridState, useGridState} from '@react-stately/grid';\nimport {TableCollection as ITableCollection, TableBodyProps, TableHeaderProps} from '@react-types/table';\nimport {Key, Node, SelectionMode, Sortable, SortDescriptor, SortDirection} from '@react-types/shared';\nimport {MultipleSelectionState, MultipleSelectionStateProps} from '@react-stately/selection';\nimport {ReactElement, useCallback, useMemo, useState} from 'react';\nimport {TableCollection} from './TableCollection';\nimport {useCollection} from '@react-stately/collections';\n\nexport interface TableState<T> extends GridState<T, ITableCollection<T>> {\n  /** A collection of rows and columns in the table. */\n  collection: ITableCollection<T>,\n  /** Whether the row selection checkboxes should be displayed. */\n  showSelectionCheckboxes: boolean,\n  /** The current sorted column and direction. */\n  sortDescriptor: SortDescriptor | null,\n  /** Calls the provided onSortChange handler with the provided column key and sort direction. */\n  sort(columnKey: Key, direction?: 'ascending' | 'descending'): void,\n  /** Whether keyboard navigation is disabled, such as when the arrow keys should be handled by a component within a cell. */\n  isKeyboardNavigationDisabled: boolean,\n  /** Set whether keyboard navigation is disabled, such as when the arrow keys should be handled by a component within a cell. */\n  setKeyboardNavigationDisabled: (val: boolean) => void\n}\n\nexport interface CollectionBuilderContext<T> {\n  showSelectionCheckboxes: boolean,\n  showDragButtons: boolean,\n  selectionMode: SelectionMode,\n  columns: Node<T>[]\n}\n\nexport interface TableStateProps<T> extends MultipleSelectionStateProps, Sortable {\n  /** The elements that make up the table. Includes the TableHeader, TableBody, Columns, and Rows. */\n  children?: [ReactElement<TableHeaderProps<T>>, ReactElement<TableBodyProps<T>>],\n  /** A list of row keys to disable. */\n  disabledKeys?: Iterable<Key>,\n  /** A pre-constructed collection to use instead of building one from items and children. */\n  collection?: ITableCollection<T>,\n  /** Whether the row selection checkboxes should be displayed. */\n  showSelectionCheckboxes?: boolean,\n  /** Whether the row drag button should be displayed.\n   * @private\n   */\n  showDragButtons?: boolean,\n  /** @private - do not use unless you know what you're doing. */\n  UNSAFE_selectionState?: MultipleSelectionState\n}\n\nconst OPPOSITE_SORT_DIRECTION = {\n  ascending: 'descending' as SortDirection,\n  descending: 'ascending' as SortDirection\n};\n\n/**\n * Provides state management for a table component. Handles building a collection\n * of columns and rows from props. In addition, it tracks row selection and manages sort order changes.\n */\nexport function useTableState<T extends object>(props: TableStateProps<T>): TableState<T> {\n  let [isKeyboardNavigationDisabled, setKeyboardNavigationDisabled] = useState(false);\n  let {selectionMode = 'none', showSelectionCheckboxes, showDragButtons} = props;\n\n  let context = useMemo(() => ({\n    showSelectionCheckboxes: showSelectionCheckboxes && selectionMode !== 'none',\n    showDragButtons: showDragButtons,\n    selectionMode,\n    columns: []\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }), [props.children, showSelectionCheckboxes, selectionMode, showDragButtons]);\n\n  let collection = useCollection<T, ITableCollection<T>>(\n    props,\n    useCallback((nodes) => new TableCollection(nodes, null, context), [context]),\n    context\n  );\n  let {disabledKeys, selectionManager} = useGridState({\n    ...props,\n    collection,\n    disabledBehavior: props.disabledBehavior || 'selection'\n  });\n\n  return {\n    collection,\n    disabledKeys,\n    selectionManager,\n    showSelectionCheckboxes: props.showSelectionCheckboxes || false,\n    sortDescriptor: props.sortDescriptor ?? null,\n    isKeyboardNavigationDisabled: collection.size === 0 || isKeyboardNavigationDisabled,\n    setKeyboardNavigationDisabled,\n    sort(columnKey: Key, direction?: 'ascending' | 'descending') {\n      props.onSortChange?.({\n        column: columnKey,\n        direction: direction ?? (props.sortDescriptor?.column === columnKey\n          ? OPPOSITE_SORT_DIRECTION[props.sortDescriptor.direction]\n          : 'ascending')\n      });\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useTableState.module.js.map"}
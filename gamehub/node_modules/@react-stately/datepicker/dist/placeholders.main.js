var $gtnqm$internationalizedstring = require("@internationalized/string");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "getPlaceholder", () => $e1e8ada727fae1a1$export$d3f5c5e0a5023fa0);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 
// These placeholders are based on the strings used by the <input type="date">
// implementations in Chrome and Firefox. Additional languages are supported
// here than React Spectrum's typical translations.
const $e1e8ada727fae1a1$var$placeholders = new (0, $gtnqm$internationalizedstring.LocalizedStringDictionary)({
    ach: {
        year: 'mwaka',
        month: 'dwe',
        day: 'nino'
    },
    af: {
        year: 'jjjj',
        month: 'mm',
        day: 'dd'
    },
    am: {
        year: "\u12D3\u12D3\u12D3\u12D3",
        month: "\u121A\u121C",
        day: "\u1240\u1240"
    },
    an: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    ar: {
        year: "\u0633\u0646\u0629",
        month: "\u0634\u0647\u0631",
        day: "\u064A\u0648\u0645"
    },
    ast: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    az: {
        year: 'iiii',
        month: 'aa',
        day: 'gg'
    },
    be: {
        year: "\u0433\u0433\u0433\u0433",
        month: "\u043C\u043C",
        day: "\u0434\u0434"
    },
    bg: {
        year: "\u0433\u0433\u0433\u0433",
        month: "\u043C\u043C",
        day: "\u0434\u0434"
    },
    bn: {
        year: 'yyyy',
        month: "\u09AE\u09BF\u09AE\u09BF",
        day: 'dd'
    },
    br: {
        year: 'bbbb',
        month: 'mm',
        day: 'dd'
    },
    bs: {
        year: 'gggg',
        month: 'mm',
        day: 'dd'
    },
    ca: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    cak: {
        year: 'jjjj',
        month: 'ii',
        day: "q'q'"
    },
    ckb: {
        year: "\u0633\u0627\u06B5",
        month: "\u0645\u0627\u0646\u06AF",
        day: "\u0695\u06C6\u0698"
    },
    cs: {
        year: 'rrrr',
        month: 'mm',
        day: 'dd'
    },
    cy: {
        year: 'bbbb',
        month: 'mm',
        day: 'dd'
    },
    da: {
        year: "\xe5\xe5\xe5\xe5",
        month: 'mm',
        day: 'dd'
    },
    de: {
        year: 'jjjj',
        month: 'mm',
        day: 'tt'
    },
    dsb: {
        year: 'llll',
        month: 'mm',
        day: "\u017A\u017A"
    },
    el: {
        year: "\u03B5\u03B5\u03B5\u03B5",
        month: "\u03BC\u03BC",
        day: "\u03B7\u03B7"
    },
    en: {
        year: 'yyyy',
        month: 'mm',
        day: 'dd'
    },
    eo: {
        year: 'jjjj',
        month: 'mm',
        day: 'tt'
    },
    es: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    et: {
        year: 'aaaa',
        month: 'kk',
        day: 'pp'
    },
    eu: {
        year: 'uuuu',
        month: 'hh',
        day: 'ee'
    },
    fa: {
        year: "\u0633\u0627\u0644",
        month: "\u0645\u0627\u0647",
        day: "\u0631\u0648\u0632"
    },
    ff: {
        year: 'hhhh',
        month: 'll',
        day: "\xf1\xf1"
    },
    fi: {
        year: 'vvvv',
        month: 'kk',
        day: 'pp'
    },
    fr: {
        year: 'aaaa',
        month: 'mm',
        day: 'jj'
    },
    fy: {
        year: 'jjjj',
        month: 'mm',
        day: 'dd'
    },
    ga: {
        year: 'bbbb',
        month: 'mm',
        day: 'll'
    },
    gd: {
        year: 'bbbb',
        month: 'mm',
        day: 'll'
    },
    gl: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    he: {
        year: "\u05E9\u05E0\u05D4",
        month: "\u05D7\u05D5\u05D3\u05E9",
        day: "\u05D9\u05D5\u05DD"
    },
    hr: {
        year: 'gggg',
        month: 'mm',
        day: 'dd'
    },
    hsb: {
        year: 'llll',
        month: 'mm',
        day: 'dd'
    },
    hu: {
        year: "\xe9\xe9\xe9\xe9",
        month: 'hh',
        day: 'nn'
    },
    ia: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    id: {
        year: 'tttt',
        month: 'bb',
        day: 'hh'
    },
    it: {
        year: 'aaaa',
        month: 'mm',
        day: 'gg'
    },
    ja: {
        year: "\u5E74",
        month: "\u6708",
        day: "\u65E5"
    },
    ka: {
        year: "\u10EC\u10EC\u10EC\u10EC",
        month: "\u10D7\u10D7",
        day: "\u10E0\u10E0"
    },
    kk: {
        year: "\u0436\u0436\u0436\u0436",
        month: "\u0430\u0430",
        day: "\u043A\u043A"
    },
    kn: {
        year: "\u0CB5\u0CB5\u0CB5\u0CB5",
        month: "\u0CAE\u0CBF\u0CAE\u0CC0",
        day: "\u0CA6\u0CBF\u0CA6\u0CBF"
    },
    ko: {
        year: "\uC5F0\uB3C4",
        month: "\uC6D4",
        day: "\uC77C"
    },
    lb: {
        year: 'jjjj',
        month: 'mm',
        day: 'dd'
    },
    lo: {
        year: "\u0E9B\u0E9B\u0E9B\u0E9B",
        month: "\u0E94\u0E94",
        day: "\u0EA7\u0EA7"
    },
    lt: {
        year: 'mmmm',
        month: 'mm',
        day: 'dd'
    },
    lv: {
        year: 'gggg',
        month: 'mm',
        day: 'dd'
    },
    meh: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    ml: {
        year: "\u0D35\u0D7C\u0D37\u0D02",
        month: "\u0D2E\u0D3E\u0D38\u0D02",
        day: "\u0D24\u0D40\u0D2F\u0D24\u0D3F"
    },
    ms: {
        year: 'tttt',
        month: 'mm',
        day: 'hh'
    },
    nl: {
        year: 'jjjj',
        month: 'mm',
        day: 'dd'
    },
    nn: {
        year: "\xe5\xe5\xe5\xe5",
        month: 'mm',
        day: 'dd'
    },
    no: {
        year: "\xe5\xe5\xe5\xe5",
        month: 'mm',
        day: 'dd'
    },
    oc: {
        year: 'aaaa',
        month: 'mm',
        day: 'jj'
    },
    pl: {
        year: 'rrrr',
        month: 'mm',
        day: 'dd'
    },
    pt: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    rm: {
        year: 'oooo',
        month: 'mm',
        day: 'dd'
    },
    ro: {
        year: 'aaaa',
        month: 'll',
        day: 'zz'
    },
    ru: {
        year: "\u0433\u0433\u0433\u0433",
        month: "\u043C\u043C",
        day: "\u0434\u0434"
    },
    sc: {
        year: 'aaaa',
        month: 'mm',
        day: 'dd'
    },
    scn: {
        year: 'aaaa',
        month: 'mm',
        day: 'jj'
    },
    sk: {
        year: 'rrrr',
        month: 'mm',
        day: 'dd'
    },
    sl: {
        year: 'llll',
        month: 'mm',
        day: 'dd'
    },
    sr: {
        year: "\u0433\u0433\u0433\u0433",
        month: "\u043C\u043C",
        day: "\u0434\u0434"
    },
    sv: {
        year: "\xe5\xe5\xe5\xe5",
        month: 'mm',
        day: 'dd'
    },
    szl: {
        year: 'rrrr',
        month: 'mm',
        day: 'dd'
    },
    tg: {
        year: "\u0441\u0441\u0441\u0441",
        month: "\u043C\u043C",
        day: "\u0440\u0440"
    },
    th: {
        year: "\u0E1B\u0E1B\u0E1B\u0E1B",
        month: "\u0E14\u0E14",
        day: "\u0E27\u0E27"
    },
    tr: {
        year: 'yyyy',
        month: 'aa',
        day: 'gg'
    },
    uk: {
        year: "\u0440\u0440\u0440\u0440",
        month: "\u043C\u043C",
        day: "\u0434\u0434"
    },
    'zh-CN': {
        year: "\u5E74",
        month: "\u6708",
        day: "\u65E5"
    },
    'zh-TW': {
        year: "\u5E74",
        month: "\u6708",
        day: "\u65E5"
    }
}, 'en');
function $e1e8ada727fae1a1$export$d3f5c5e0a5023fa0(field, value, locale) {
    // Use the actual placeholder value for the era and day period fields.
    if (field === 'era' || field === 'dayPeriod') return value;
    if (field === 'year' || field === 'month' || field === 'day') return $e1e8ada727fae1a1$var$placeholders.getStringForLocale(field, locale);
    // For time fields (e.g. hour, minute, etc.), use two dashes as the placeholder.
    return "\u2013\u2013";
}


//# sourceMappingURL=placeholders.main.js.map

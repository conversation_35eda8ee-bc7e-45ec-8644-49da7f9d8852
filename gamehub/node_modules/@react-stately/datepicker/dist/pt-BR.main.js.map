{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,QAAQ,CAAC,aAAa,CAAC;IAC7F,iBAAiB,CAAC,iDAA8C,CAAC;IACjE,kBAAkB,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC7E,mBAAmB,CAAC,iCAA8B,CAAC;AACrD", "sources": ["packages/@react-stately/datepicker/intl/pt-BR.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"O valor deve ser {maxValue} ou anterior.\",\n  \"rangeReversed\": \"A data inicial deve ser anterior à data final.\",\n  \"rangeUnderflow\": \"O valor deve ser {minValue} ou posterior.\",\n  \"unavailableDate\": \"Data selecionada indisponível.\"\n}\n"], "names": [], "version": 3, "file": "pt-BR.main.js.map"}
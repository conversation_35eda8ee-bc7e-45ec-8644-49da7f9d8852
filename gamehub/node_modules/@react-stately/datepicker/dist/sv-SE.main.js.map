{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,wBAAkB,EAAE,KAAK,QAAQ,CAAC,gBAAgB,CAAC;IACjG,iBAAiB,CAAC,+CAAyC,CAAC;IAC5D,kBAAkB,CAAC,OAAS,CAAC,wBAAkB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC9E,mBAAmB,CAAC,6CAAuC,CAAC;AAC9D", "sources": ["packages/@react-stately/datepicker/intl/sv-SE.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON><PERSON>rdet måste vara {maxValue} eller tidigare.\",\n  \"rangeReversed\": \"Startdatumet måste vara före slutdatumet.\",\n  \"rangeUnderflow\": \"Värdet måste vara {minValue} eller senare.\",\n  \"unavailableDate\": \"Det valda datumet är inte tillgängligt.\"\n}\n"], "names": [], "version": 3, "file": "sv-SE.main.js.map"}
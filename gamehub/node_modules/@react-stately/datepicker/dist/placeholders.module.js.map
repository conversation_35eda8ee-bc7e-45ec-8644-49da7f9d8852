{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAID,8EAA8E;AAC9E,4EAA4E;AAC5E,mDAAmD;AACnD,MAAM,qCAAe,IAAI,CAAA,GAAA,gCAAwB,EAAE;IACjD,KAAK;QAAC,MAAM;QAAS,OAAO;QAAO,KAAK;IAAM;IAC9C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAO,OAAO;QAAO,KAAK;IAAK;IAC1C,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAQ,KAAK;IAAI;IAC3C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAM;IAC5C,KAAK;QAAC,MAAM;QAAO,OAAO;QAAQ,KAAK;IAAK;IAC5C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAO,OAAO;QAAO,KAAK;IAAK;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAO,OAAO;QAAQ,KAAK;IAAK;IAC3C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAK,OAAO;QAAK,KAAK;IAAG;IACpC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAQ,KAAK;IAAM;IAC7C,IAAI;QAAC,MAAM;QAAM,OAAO;QAAK,KAAK;IAAG;IACrC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAQ,KAAK;IAAO;IAC9C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,KAAK;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IAC1C,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,IAAI;QAAC,MAAM;QAAQ,OAAO;QAAM,KAAK;IAAI;IACzC,SAAS;QAAC,MAAM;QAAK,OAAO;QAAK,KAAK;IAAG;IACzC,SAAS;QAAC,MAAM;QAAK,OAAO;QAAK,KAAK;IAAG;AAC3C,GAAG;AAEI,SAAS,0CAAe,KAAa,EAAE,KAAa,EAAE,MAAc;IACzE,sEAAsE;IACtE,IAAI,UAAU,SAAS,UAAU,aAC/B,OAAO;IAGT,IAAI,UAAU,UAAU,UAAU,WAAW,UAAU,OACrD,OAAO,mCAAa,kBAAkB,CAAC,OAAO;IAGhD,gFAAgF;IAChF,OAAO;AACT", "sources": ["packages/@react-stately/datepicker/src/placeholders.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {LocalizedStringDictionary} from '@internationalized/string';\n\n// These placeholders are based on the strings used by the <input type=\"date\">\n// implementations in Chrome and Firefox. Additional languages are supported\n// here than React Spectrum's typical translations.\nconst placeholders = new LocalizedStringDictionary({\n  ach: {year: 'mwaka', month: 'dwe', day: 'nino'},\n  af: {year: 'jjjj', month: 'mm', day: 'dd'},\n  am: {year: 'ዓዓዓዓ', month: 'ሚሜ', day: 'ቀቀ'},\n  an: {year: 'aaaa', month: 'mm', day: 'dd'},\n  ar: {year: 'سنة', month: 'شهر', day: 'يوم'},\n  ast: {year: 'aaaa', month: 'mm', day: 'dd'},\n  az: {year: 'iiii', month: 'aa', day: 'gg'},\n  be: {year: 'гггг', month: 'мм', day: 'дд'},\n  bg: {year: 'гггг', month: 'мм', day: 'дд'},\n  bn: {year: 'yyyy', month: 'মিমি', day: 'dd'},\n  br: {year: 'bbbb', month: 'mm', day: 'dd'},\n  bs: {year: 'gggg', month: 'mm', day: 'dd'},\n  ca: {year: 'aaaa', month: 'mm', day: 'dd'},\n  cak: {year: 'jjjj', month: 'ii', day: \"q'q'\"},\n  ckb: {year: 'ساڵ', month: 'مانگ', day: 'ڕۆژ'},\n  cs: {year: 'rrrr', month: 'mm', day: 'dd'},\n  cy: {year: 'bbbb', month: 'mm', day: 'dd'},\n  da: {year: 'åååå', month: 'mm', day: 'dd'},\n  de: {year: 'jjjj', month: 'mm', day: 'tt'},\n  dsb: {year: 'llll', month: 'mm', day: 'źź'},\n  el: {year: 'εεεε', month: 'μμ', day: 'ηη'},\n  en: {year: 'yyyy', month: 'mm', day: 'dd'},\n  eo: {year: 'jjjj', month: 'mm', day: 'tt'},\n  es: {year: 'aaaa', month: 'mm', day: 'dd'},\n  et: {year: 'aaaa', month: 'kk', day: 'pp'},\n  eu: {year: 'uuuu', month: 'hh', day: 'ee'},\n  fa: {year: 'سال', month: 'ماه', day: 'روز'},\n  ff: {year: 'hhhh', month: 'll', day: 'ññ'},\n  fi: {year: 'vvvv', month: 'kk', day: 'pp'},\n  fr: {year: 'aaaa', month: 'mm', day: 'jj'},\n  fy: {year: 'jjjj', month: 'mm', day: 'dd'},\n  ga: {year: 'bbbb', month: 'mm', day: 'll'},\n  gd: {year: 'bbbb', month: 'mm', day: 'll'},\n  gl: {year: 'aaaa', month: 'mm', day: 'dd'},\n  he: {year: 'שנה', month: 'חודש', day: 'יום'},\n  hr: {year: 'gggg', month: 'mm', day: 'dd'},\n  hsb: {year: 'llll', month: 'mm', day: 'dd'},\n  hu: {year: 'éééé', month: 'hh', day: 'nn'},\n  ia: {year: 'aaaa', month: 'mm', day: 'dd'},\n  id: {year: 'tttt', month: 'bb', day: 'hh'},\n  it: {year: 'aaaa', month: 'mm', day: 'gg'},\n  ja: {year: '年', month: '月', day: '日'},\n  ka: {year: 'წწწწ', month: 'თთ', day: 'რრ'},\n  kk: {year: 'жжжж', month: 'аа', day: 'кк'},\n  kn: {year: 'ವವವವ', month: 'ಮಿಮೀ', day: 'ದಿದಿ'},\n  ko: {year: '연도', month: '월', day: '일'},\n  lb: {year: 'jjjj', month: 'mm', day: 'dd'},\n  lo: {year: 'ປປປປ', month: 'ດດ', day: 'ວວ'},\n  lt: {year: 'mmmm', month: 'mm', day: 'dd'},\n  lv: {year: 'gggg', month: 'mm', day: 'dd'},\n  meh: {year: 'aaaa', month: 'mm', day: 'dd'},\n  ml: {year: 'വർഷം', month: 'മാസം', day: 'തീയതി'},\n  ms: {year: 'tttt', month: 'mm', day: 'hh'},\n  nl: {year: 'jjjj', month: 'mm', day: 'dd'},\n  nn: {year: 'åååå', month: 'mm', day: 'dd'},\n  no: {year: 'åååå', month: 'mm', day: 'dd'},\n  oc: {year: 'aaaa', month: 'mm', day: 'jj'},\n  pl: {year: 'rrrr', month: 'mm', day: 'dd'},\n  pt: {year: 'aaaa', month: 'mm', day: 'dd'},\n  rm: {year: 'oooo', month: 'mm', day: 'dd'},\n  ro: {year: 'aaaa', month: 'll', day: 'zz'},\n  ru: {year: 'гггг', month: 'мм', day: 'дд'},\n  sc: {year: 'aaaa', month: 'mm', day: 'dd'},\n  scn: {year: 'aaaa', month: 'mm', day: 'jj'},\n  sk: {year: 'rrrr', month: 'mm', day: 'dd'},\n  sl: {year: 'llll', month: 'mm', day: 'dd'},\n  sr: {year: 'гггг', month: 'мм', day: 'дд'},\n  sv: {year: 'åååå', month: 'mm', day: 'dd'},\n  szl: {year: 'rrrr', month: 'mm', day: 'dd'},\n  tg: {year: 'сссс', month: 'мм', day: 'рр'},\n  th: {year: 'ปปปป', month: 'ดด', day: 'วว'},\n  tr: {year: 'yyyy', month: 'aa', day: 'gg'},\n  uk: {year: 'рррр', month: 'мм', day: 'дд'},\n  'zh-CN': {year: '年', month: '月', day: '日'},\n  'zh-TW': {year: '年', month: '月', day: '日'}\n}, 'en');\n\nexport function getPlaceholder(field: string, value: string, locale: string): string {\n  // Use the actual placeholder value for the era and day period fields.\n  if (field === 'era' || field === 'dayPeriod') {\n    return value;\n  }\n\n  if (field === 'year' || field === 'month' || field === 'day') {\n    return placeholders.getStringForLocale(field, locale);\n  }\n\n  // For time fields (e.g. hour, minute, etc.), use two dashes as the placeholder.\n  return '––';\n}\n"], "names": [], "version": 3, "file": "placeholders.module.js.map"}
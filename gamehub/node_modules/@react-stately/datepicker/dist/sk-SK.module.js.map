{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,0BAAiB,EAAE,KAAK,QAAQ,CAAC,qBAAe,CAAC;IAC/F,iBAAiB,CAAC,6EAA+C,CAAC;IAClE,kBAAkB,CAAC,OAAS,CAAC,0BAAiB,EAAE,KAAK,QAAQ,CAAC,uBAAiB,CAAC;IAChF,mBAAmB,CAAC,qCAA4B,CAAC;AACnD", "sources": ["packages/@react-stately/datepicker/intl/sk-SK.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Hodnota musí byť {maxValue} alebo skoršia.\",\n  \"rangeReversed\": \"Dátum začiatku musí byť skorší ako dátum konca.\",\n  \"rangeUnderflow\": \"Hodnota musí byť {minValue} alebo neskoršia.\",\n  \"unavailableDate\": \"Vybratý dátum je nedostupný.\"\n}\n"], "names": [], "version": 3, "file": "sk-SK.module.js.map"}
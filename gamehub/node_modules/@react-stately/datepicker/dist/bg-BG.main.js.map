{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,yIAAuB,EAAE,KAAK,QAAQ,CAAC,0EAAc,CAAC;IACpG,iBAAiB,CAAC,4PAA0C,CAAC;IAC7D,kBAAkB,CAAC,OAAS,CAAC,yIAAuB,EAAE,KAAK,QAAQ,CAAC,0EAAc,CAAC;IACnF,mBAAmB,CAAC,sKAA4B,CAAC;AACnD", "sources": ["packages/@react-stately/datepicker/intl/bg-BG.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Стойността трябва да е {maxValue} или по-ранна.\",\n  \"rangeReversed\": \"Началната дата трябва да е преди крайната.\",\n  \"rangeUnderflow\": \"Стойността трябва да е {minValue} или по-късно.\",\n  \"unavailableDate\": \"Избраната дата не е налична.\"\n}\n"], "names": [], "version": 3, "file": "bg-BG.main.js.map"}
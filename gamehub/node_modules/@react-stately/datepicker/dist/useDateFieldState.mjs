import {convertValue as $35a22f14a1f04b11$export$61a490a80c552550, createPlaceholderDate as $35a22f14a1f04b11$export$66aa2b09de4b1ea5, getFormatOptions as $35a22f14a1f04b11$export$7e319ea407e63bc0, getValidationResult as $35a22f14a1f04b11$export$f18627323ab57ac0, useDefaultProps as $35a22f14a1f04b11$export$2440da353cedad43} from "./utils.mjs";
import {getPlaceholder as $3e3ed55ab2966714$export$d3f5c5e0a5023fa0} from "./placeholders.mjs";
import {DateFormatter as $g03ag$DateFormatter, isEqualCalendar as $g03ag$isEqualCalendar, toCalendar as $g03ag$toCalendar, GregorianCalendar as $g03ag$GregorianCalendar, getMinimumMonthInYear as $g03ag$getMinimumMonthInYear, getMinimumDayInMonth as $g03ag$getMinimumDayInMonth} from "@internationalized/date";
import {useFormValidationState as $g03ag$useFormValidationState} from "@react-stately/form";
import {useControlledState as $g03ag$useControlledState} from "@react-stately/utils";
import {useMemo as $g03ag$useMemo, useState as $g03ag$useState, useRef as $g03ag$useRef, useEffect as $g03ag$useEffect} from "react";

/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 





const $3c0fc76039f1c516$var$EDITABLE_SEGMENTS = {
    year: true,
    month: true,
    day: true,
    hour: true,
    minute: true,
    second: true,
    dayPeriod: true,
    era: true
};
const $3c0fc76039f1c516$var$PAGE_STEP = {
    year: 5,
    month: 2,
    day: 7,
    hour: 2,
    minute: 15,
    second: 15
};
const $3c0fc76039f1c516$var$TYPE_MAPPING = {
    // Node seems to convert everything to lowercase...
    dayperiod: 'dayPeriod',
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts#named_years
    relatedYear: 'year',
    yearName: 'literal',
    unknown: 'literal'
};
function $3c0fc76039f1c516$export$60e84778edff6d26(props) {
    let { locale: locale, createCalendar: createCalendar, hideTimeZone: hideTimeZone, isDisabled: isDisabled = false, isReadOnly: isReadOnly = false, isRequired: isRequired = false, minValue: minValue, maxValue: maxValue, isDateUnavailable: isDateUnavailable } = props;
    let v = props.value || props.defaultValue || props.placeholderValue || null;
    let [granularity, defaultTimeZone] = (0, $35a22f14a1f04b11$export$2440da353cedad43)(v, props.granularity);
    let timeZone = defaultTimeZone || 'UTC';
    // props.granularity must actually exist in the value if one is provided.
    if (v && !(granularity in v)) throw new Error('Invalid granularity ' + granularity + ' for value ' + v.toString());
    let defaultFormatter = (0, $g03ag$useMemo)(()=>new (0, $g03ag$DateFormatter)(locale), [
        locale
    ]);
    let calendar = (0, $g03ag$useMemo)(()=>createCalendar(defaultFormatter.resolvedOptions().calendar), [
        createCalendar,
        defaultFormatter
    ]);
    var _props_defaultValue;
    let [value, setDate] = (0, $g03ag$useControlledState)(props.value, (_props_defaultValue = props.defaultValue) !== null && _props_defaultValue !== void 0 ? _props_defaultValue : null, props.onChange);
    let calendarValue = (0, $g03ag$useMemo)(()=>{
        var _convertValue;
        return (_convertValue = (0, $35a22f14a1f04b11$export$61a490a80c552550)(value, calendar)) !== null && _convertValue !== void 0 ? _convertValue : null;
    }, [
        value,
        calendar
    ]);
    // We keep track of the placeholder date separately in state so that onChange is not called
    // until all segments are set. If the value === null (not undefined), then assume the component
    // is controlled, so use the placeholder as the value until all segments are entered so it doesn't
    // change from uncontrolled to controlled and emit a warning.
    let [placeholderDate, setPlaceholderDate] = (0, $g03ag$useState)(()=>(0, $35a22f14a1f04b11$export$66aa2b09de4b1ea5)(props.placeholderValue, granularity, calendar, defaultTimeZone));
    let val = calendarValue || placeholderDate;
    let showEra = calendar.identifier === 'gregory' && val.era === 'BC';
    let formatOpts = (0, $g03ag$useMemo)(()=>{
        var _props_maxGranularity;
        return {
            granularity: granularity,
            maxGranularity: (_props_maxGranularity = props.maxGranularity) !== null && _props_maxGranularity !== void 0 ? _props_maxGranularity : 'year',
            timeZone: defaultTimeZone,
            hideTimeZone: hideTimeZone,
            hourCycle: props.hourCycle,
            showEra: showEra,
            shouldForceLeadingZeros: props.shouldForceLeadingZeros
        };
    }, [
        props.maxGranularity,
        granularity,
        props.hourCycle,
        props.shouldForceLeadingZeros,
        defaultTimeZone,
        hideTimeZone,
        showEra
    ]);
    let opts = (0, $g03ag$useMemo)(()=>(0, $35a22f14a1f04b11$export$7e319ea407e63bc0)({}, formatOpts), [
        formatOpts
    ]);
    let dateFormatter = (0, $g03ag$useMemo)(()=>new (0, $g03ag$DateFormatter)(locale, opts), [
        locale,
        opts
    ]);
    let resolvedOptions = (0, $g03ag$useMemo)(()=>dateFormatter.resolvedOptions(), [
        dateFormatter
    ]);
    // Determine how many editable segments there are for validation purposes.
    // The result is cached for performance.
    let allSegments = (0, $g03ag$useMemo)(()=>dateFormatter.formatToParts(new Date()).filter((seg)=>$3c0fc76039f1c516$var$EDITABLE_SEGMENTS[seg.type]).reduce((p, seg)=>(p[$3c0fc76039f1c516$var$TYPE_MAPPING[seg.type] || seg.type] = true, p), {}), [
        dateFormatter
    ]);
    let [validSegments, setValidSegments] = (0, $g03ag$useState)(()=>props.value || props.defaultValue ? {
            ...allSegments
        } : {});
    let clearedSegment = (0, $g03ag$useRef)(null);
    // Reset placeholder when calendar changes
    let lastCalendar = (0, $g03ag$useRef)(calendar);
    (0, $g03ag$useEffect)(()=>{
        if (!(0, $g03ag$isEqualCalendar)(calendar, lastCalendar.current)) {
            lastCalendar.current = calendar;
            setPlaceholderDate((placeholder)=>Object.keys(validSegments).length > 0 ? (0, $g03ag$toCalendar)(placeholder, calendar) : (0, $35a22f14a1f04b11$export$66aa2b09de4b1ea5)(props.placeholderValue, granularity, calendar, defaultTimeZone));
        }
    }, [
        calendar,
        granularity,
        validSegments,
        defaultTimeZone,
        props.placeholderValue
    ]);
    // If there is a value prop, and some segments were previously placeholders, mark them all as valid.
    if (value && Object.keys(validSegments).length < Object.keys(allSegments).length) {
        validSegments = {
            ...allSegments
        };
        setValidSegments(validSegments);
    }
    // If the value is set to null and all segments are valid, reset the placeholder.
    if (value == null && Object.keys(validSegments).length === Object.keys(allSegments).length) {
        validSegments = {};
        setValidSegments(validSegments);
        setPlaceholderDate((0, $35a22f14a1f04b11$export$66aa2b09de4b1ea5)(props.placeholderValue, granularity, calendar, defaultTimeZone));
    }
    // If all segments are valid, use the date from state, otherwise use the placeholder date.
    let displayValue = calendarValue && Object.keys(validSegments).length >= Object.keys(allSegments).length ? calendarValue : placeholderDate;
    let setValue = (newValue)=>{
        if (props.isDisabled || props.isReadOnly) return;
        let validKeys = Object.keys(validSegments);
        let allKeys = Object.keys(allSegments);
        // if all the segments are completed or a timefield with everything but am/pm set the time, also ignore when am/pm cleared
        if (newValue == null) {
            setDate(null);
            setPlaceholderDate((0, $35a22f14a1f04b11$export$66aa2b09de4b1ea5)(props.placeholderValue, granularity, calendar, defaultTimeZone));
            setValidSegments({});
        } else if (validKeys.length >= allKeys.length || validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod && clearedSegment.current !== 'dayPeriod') {
            // The display calendar should not have any effect on the emitted value.
            // Emit dates in the same calendar as the original value, if any, otherwise gregorian.
            newValue = (0, $g03ag$toCalendar)(newValue, (v === null || v === void 0 ? void 0 : v.calendar) || new (0, $g03ag$GregorianCalendar)());
            setDate(newValue);
        } else setPlaceholderDate(newValue);
        clearedSegment.current = null;
    };
    let dateValue = (0, $g03ag$useMemo)(()=>displayValue.toDate(timeZone), [
        displayValue,
        timeZone
    ]);
    let segments = (0, $g03ag$useMemo)(()=>$3c0fc76039f1c516$var$processSegments(dateValue, validSegments, dateFormatter, resolvedOptions, displayValue, calendar, locale, granularity), [
        dateValue,
        validSegments,
        dateFormatter,
        resolvedOptions,
        displayValue,
        calendar,
        locale,
        granularity
    ]);
    // When the era field appears, mark it valid if the year field is already valid.
    // If the era field disappears, remove it from the valid segments.
    if (allSegments.era && validSegments.year && !validSegments.era) {
        validSegments.era = true;
        setValidSegments({
            ...validSegments
        });
    } else if (!allSegments.era && validSegments.era) {
        delete validSegments.era;
        setValidSegments({
            ...validSegments
        });
    }
    let markValid = (part)=>{
        validSegments[part] = true;
        if (part === 'year' && allSegments.era) validSegments.era = true;
        setValidSegments({
            ...validSegments
        });
    };
    let adjustSegment = (type, amount)=>{
        if (!validSegments[type]) {
            markValid(type);
            let validKeys = Object.keys(validSegments);
            let allKeys = Object.keys(allSegments);
            if (validKeys.length >= allKeys.length || validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod) setValue(displayValue);
        } else setValue($3c0fc76039f1c516$var$addSegment(displayValue, type, amount, resolvedOptions));
    };
    let builtinValidation = (0, $g03ag$useMemo)(()=>(0, $35a22f14a1f04b11$export$f18627323ab57ac0)(value, minValue, maxValue, isDateUnavailable, formatOpts), [
        value,
        minValue,
        maxValue,
        isDateUnavailable,
        formatOpts
    ]);
    let validation = (0, $g03ag$useFormValidationState)({
        ...props,
        value: value,
        builtinValidation: builtinValidation
    });
    let isValueInvalid = validation.displayValidation.isInvalid;
    let validationState = props.validationState || (isValueInvalid ? 'invalid' : null);
    var _props_maxGranularity;
    return {
        ...validation,
        value: calendarValue,
        dateValue: dateValue,
        calendar: calendar,
        setValue: setValue,
        segments: segments,
        dateFormatter: dateFormatter,
        validationState: validationState,
        isInvalid: isValueInvalid,
        granularity: granularity,
        maxGranularity: (_props_maxGranularity = props.maxGranularity) !== null && _props_maxGranularity !== void 0 ? _props_maxGranularity : 'year',
        isDisabled: isDisabled,
        isReadOnly: isReadOnly,
        isRequired: isRequired,
        increment (part) {
            adjustSegment(part, 1);
        },
        decrement (part) {
            adjustSegment(part, -1);
        },
        incrementPage (part) {
            adjustSegment(part, $3c0fc76039f1c516$var$PAGE_STEP[part] || 1);
        },
        decrementPage (part) {
            adjustSegment(part, -($3c0fc76039f1c516$var$PAGE_STEP[part] || 1));
        },
        setSegment (part, v) {
            markValid(part);
            setValue($3c0fc76039f1c516$var$setSegment(displayValue, part, v, resolvedOptions));
        },
        confirmPlaceholder () {
            if (props.isDisabled || props.isReadOnly) return;
            // Confirm the placeholder if only the day period is not filled in.
            let validKeys = Object.keys(validSegments);
            let allKeys = Object.keys(allSegments);
            if (validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod) {
                validSegments = {
                    ...allSegments
                };
                setValidSegments(validSegments);
                setValue(displayValue.copy());
            }
        },
        clearSegment (part) {
            delete validSegments[part];
            clearedSegment.current = part;
            setValidSegments({
                ...validSegments
            });
            let placeholder = (0, $35a22f14a1f04b11$export$66aa2b09de4b1ea5)(props.placeholderValue, granularity, calendar, defaultTimeZone);
            let value = displayValue;
            // Reset day period to default without changing the hour.
            if (part === 'dayPeriod' && 'hour' in displayValue && 'hour' in placeholder) {
                let isPM = displayValue.hour >= 12;
                let shouldBePM = placeholder.hour >= 12;
                if (isPM && !shouldBePM) value = displayValue.set({
                    hour: displayValue.hour - 12
                });
                else if (!isPM && shouldBePM) value = displayValue.set({
                    hour: displayValue.hour + 12
                });
            } else if (part === 'hour' && 'hour' in displayValue && displayValue.hour >= 12 && validSegments.dayPeriod) value = displayValue.set({
                hour: placeholder['hour'] + 12
            });
            else if (part in displayValue) value = displayValue.set({
                [part]: placeholder[part]
            });
            setDate(null);
            setValue(value);
        },
        formatValue (fieldOptions) {
            if (!calendarValue) return '';
            let formatOptions = (0, $35a22f14a1f04b11$export$7e319ea407e63bc0)(fieldOptions, formatOpts);
            let formatter = new (0, $g03ag$DateFormatter)(locale, formatOptions);
            return formatter.format(dateValue);
        },
        getDateFormatter (locale, formatOptions) {
            let newOptions = {
                ...formatOpts,
                ...formatOptions
            };
            let newFormatOptions = (0, $35a22f14a1f04b11$export$7e319ea407e63bc0)({}, newOptions);
            return new (0, $g03ag$DateFormatter)(locale, newFormatOptions);
        }
    };
}
function $3c0fc76039f1c516$var$processSegments(dateValue, validSegments, dateFormatter, resolvedOptions, displayValue, calendar, locale, granularity) {
    let timeValue = [
        'hour',
        'minute',
        'second'
    ];
    let segments = dateFormatter.formatToParts(dateValue);
    let processedSegments = [];
    for (let segment of segments){
        let type = $3c0fc76039f1c516$var$TYPE_MAPPING[segment.type] || segment.type;
        let isEditable = $3c0fc76039f1c516$var$EDITABLE_SEGMENTS[type];
        if (type === 'era' && calendar.getEras().length === 1) isEditable = false;
        let isPlaceholder = $3c0fc76039f1c516$var$EDITABLE_SEGMENTS[type] && !validSegments[type];
        let placeholder = $3c0fc76039f1c516$var$EDITABLE_SEGMENTS[type] ? (0, $3e3ed55ab2966714$export$d3f5c5e0a5023fa0)(type, segment.value, locale) : null;
        let dateSegment = {
            type: type,
            text: isPlaceholder ? placeholder : segment.value,
            ...$3c0fc76039f1c516$var$getSegmentLimits(displayValue, type, resolvedOptions),
            isPlaceholder: isPlaceholder,
            placeholder: placeholder,
            isEditable: isEditable
        };
        // There is an issue in RTL languages where time fields render (minute:hour) instead of (hour:minute).
        // To force an LTR direction on the time field since, we wrap the time segments in LRI (left-to-right) isolate unicode. See https://www.w3.org/International/questions/qa-bidi-unicode-controls.
        // These unicode characters will be added to the array of processed segments as literals and will mark the start and end of the embedded direction change. 
        if (type === 'hour') {
            // This marks the start of the embedded direction change. 
            processedSegments.push({
                type: 'literal',
                text: '\u2066',
                ...$3c0fc76039f1c516$var$getSegmentLimits(displayValue, 'literal', resolvedOptions),
                isPlaceholder: false,
                placeholder: '',
                isEditable: false
            });
            processedSegments.push(dateSegment);
            // This marks the end of the embedded direction change in the case that the granularity it set to "hour".
            if (type === granularity) processedSegments.push({
                type: 'literal',
                text: '\u2069',
                ...$3c0fc76039f1c516$var$getSegmentLimits(displayValue, 'literal', resolvedOptions),
                isPlaceholder: false,
                placeholder: '',
                isEditable: false
            });
        } else if (timeValue.includes(type) && type === granularity) {
            processedSegments.push(dateSegment);
            // This marks the end of the embedded direction change.
            processedSegments.push({
                type: 'literal',
                text: '\u2069',
                ...$3c0fc76039f1c516$var$getSegmentLimits(displayValue, 'literal', resolvedOptions),
                isPlaceholder: false,
                placeholder: '',
                isEditable: false
            });
        } else // We only want to "wrap" the unicode around segments that are hour, minute, or second. If they aren't, just process as normal. 
        processedSegments.push(dateSegment);
    }
    return processedSegments;
}
function $3c0fc76039f1c516$var$getSegmentLimits(date, type, options) {
    switch(type){
        case 'era':
            {
                let eras = date.calendar.getEras();
                return {
                    value: eras.indexOf(date.era),
                    minValue: 0,
                    maxValue: eras.length - 1
                };
            }
        case 'year':
            return {
                value: date.year,
                minValue: 1,
                maxValue: date.calendar.getYearsInEra(date)
            };
        case 'month':
            return {
                value: date.month,
                minValue: (0, $g03ag$getMinimumMonthInYear)(date),
                maxValue: date.calendar.getMonthsInYear(date)
            };
        case 'day':
            return {
                value: date.day,
                minValue: (0, $g03ag$getMinimumDayInMonth)(date),
                maxValue: date.calendar.getDaysInMonth(date)
            };
    }
    if ('hour' in date) switch(type){
        case 'dayPeriod':
            return {
                value: date.hour >= 12 ? 12 : 0,
                minValue: 0,
                maxValue: 12
            };
        case 'hour':
            if (options.hour12) {
                let isPM = date.hour >= 12;
                return {
                    value: date.hour,
                    minValue: isPM ? 12 : 0,
                    maxValue: isPM ? 23 : 11
                };
            }
            return {
                value: date.hour,
                minValue: 0,
                maxValue: 23
            };
        case 'minute':
            return {
                value: date.minute,
                minValue: 0,
                maxValue: 59
            };
        case 'second':
            return {
                value: date.second,
                minValue: 0,
                maxValue: 59
            };
    }
    return {};
}
function $3c0fc76039f1c516$var$addSegment(value, part, amount, options) {
    switch(part){
        case 'era':
        case 'year':
        case 'month':
        case 'day':
            return value.cycle(part, amount, {
                round: part === 'year'
            });
    }
    if ('hour' in value) switch(part){
        case 'dayPeriod':
            {
                let hours = value.hour;
                let isPM = hours >= 12;
                return value.set({
                    hour: isPM ? hours - 12 : hours + 12
                });
            }
        case 'hour':
        case 'minute':
        case 'second':
            return value.cycle(part, amount, {
                round: part !== 'hour',
                hourCycle: options.hour12 ? 12 : 24
            });
    }
    throw new Error('Unknown segment: ' + part);
}
function $3c0fc76039f1c516$var$setSegment(value, part, segmentValue, options) {
    switch(part){
        case 'day':
        case 'month':
        case 'year':
        case 'era':
            return value.set({
                [part]: segmentValue
            });
    }
    if ('hour' in value && typeof segmentValue === 'number') switch(part){
        case 'dayPeriod':
            {
                let hours = value.hour;
                let wasPM = hours >= 12;
                let isPM = segmentValue >= 12;
                if (isPM === wasPM) return value;
                return value.set({
                    hour: wasPM ? hours - 12 : hours + 12
                });
            }
        case 'hour':
            // In 12 hour time, ensure that AM/PM does not change
            if (options.hour12) {
                let hours = value.hour;
                let wasPM = hours >= 12;
                if (!wasPM && segmentValue === 12) segmentValue = 0;
                if (wasPM && segmentValue < 12) segmentValue += 12;
            }
        // fallthrough
        case 'minute':
        case 'second':
            return value.set({
                [part]: segmentValue
            });
    }
    throw new Error('Unknown segment: ' + part);
}


export {$3c0fc76039f1c516$export$60e84778edff6d26 as useDateFieldState};
//# sourceMappingURL=useDateFieldState.module.js.map

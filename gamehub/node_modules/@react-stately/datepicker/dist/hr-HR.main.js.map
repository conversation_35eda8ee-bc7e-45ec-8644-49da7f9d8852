{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,QAAQ,CAAC,YAAY,CAAC;IAChG,iBAAiB,CAAC,2DAA+C,CAAC;IAClE,kBAAkB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,QAAQ,CAAC,aAAa,CAAC;IAChF,mBAAmB,CAAC,6BAA6B,CAAC;AACpD", "sources": ["packages/@react-stately/datepicker/intl/hr-HR.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Vrijednost mora biti {maxValue} ili ranije.\",\n  \"rangeReversed\": \"Datum početka mora biti prije datuma završetka.\",\n  \"rangeUnderflow\": \"Vrijednost mora biti {minValue} ili kasnije.\",\n  \"unavailableDate\": \"Odabrani datum nije dostupan.\"\n}\n"], "names": [], "version": 3, "file": "hr-HR.main.js.map"}
var $cedfa43b4e2a5906$exports = {};
$cedfa43b4e2a5906$exports = {
    "rangeOverflow": (args)=>`El valor debe ser ${args.maxValue} o anterior.`,
    "rangeReversed": `La fecha de inicio debe ser anterior a la fecha de finalizaci\xf3n.`,
    "rangeUnderflow": (args)=>`El valor debe ser ${args.minValue} o posterior.`,
    "unavailableDate": `Fecha seleccionada no disponible.`
};


export {$cedfa43b4e2a5906$exports as default};
//# sourceMappingURL=es-ES.module.js.map

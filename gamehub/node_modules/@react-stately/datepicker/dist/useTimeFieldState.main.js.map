{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAuBM,SAAS,0CAAmD,KAA+B;IAChG,IAAI,oBACF,mBAAmB,IAAI,CAAA,GAAA,iCAAG,eAC1B,QAAQ,YACR,QAAQ,eACR,WAAW,YACX,QAAQ,EACT,GAAG;QAIF;IAFF,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,2CAAiB,EACvC,MAAM,KAAK,EACX,CAAA,sBAAA,MAAM,YAAY,cAAlB,iCAAA,sBAAsB,MACtB,MAAM,QAAQ;IAGhB,IAAI,IAAI,SAAS;IACjB,IAAI,MAAM,KAAK,SAAS,IAAI,IAAI;IAChC,IAAI,uBAAuB,MAAM,YAAY,IAAI,cAAc,MAAM,YAAY,GAAG,MAAM,YAAY,CAAC,QAAQ,GAAG;IAClH,IAAI,kBAAkB,CAAA,GAAA,oBAAM,EAAE;QAC5B,IAAI,gBAAgB,KAAK,cAAc,IAAI,EAAE,QAAQ,GAAG;QAExD,OAAO,AAAC,CAAA,iBAAiB,oBAAmB,KAAM,mBAAmB,CAAA,GAAA,oCAAM,EAAE,mCAAa,mBAAoB,iBAAiB,wBAAyB,mCAAa;IACvK,GAAG;QAAC;QAAkB;QAAG;KAAqB;IAC9C,IAAI,UAAU,CAAA,GAAA,oBAAM,EAAE,IAAM,mCAAa,UAAU,MAAM;QAAC;QAAU;KAAI;IACxE,IAAI,UAAU,CAAA,GAAA,oBAAM,EAAE,IAAM,mCAAa,UAAU,MAAM;QAAC;QAAU;KAAI;IAExE,IAAI,YAAY,CAAA,GAAA,oBAAM,EAAE,IAAM,SAAS,SAAS,QAAQ,CAAA,GAAA,mCAAK,EAAE,SAAS,OAAe;QAAC;KAAM;IAC9F,IAAI,WAAW,CAAA,GAAA,oBAAM,EAAE,IAAM,SAAS,OAAO,OAAO,mCAAa,QAAQ;QAAC;KAAM;IAChF,IAAI,WAAW,CAAA;QACb,SAAS,OAAO,uBAAuB,WAAW,YAAY,CAAA,GAAA,mCAAK,EAAE;IACvE;IAEA,IAAI,QAAQ,CAAA,GAAA,2CAAgB,EAAE;QAC5B,GAAG,KAAK;QACR,OAAO;QACP,cAAc;QACd,UAAU;QACV,UAAU;kBACV;QACA,aAAa,eAAe;QAC5B,gBAAgB;QAChB,kBAAkB,4BAAA,6BAAA,kBAAmB;QACrC,8CAA8C;QAC9C,gBAAgB,IAAM,IAAI,CAAA,GAAA,8CAAgB;QAC1C,UAAU,CAAA,GAAA,wBAAU,EAAE,IAAM,qBAAA,+BAAA,SAAW,QAAe;YAAC;YAAU;SAAM;IACzE;IAEA,OAAO;QACL,GAAG,KAAK;mBACR;IACF;AACF;AAEA,SAAS,mCAAa,KAAmC,EAAE,OAAkB,CAAA,GAAA,kCAAI,EAAE,CAAA,GAAA,6CAAe,IAAI;IACpG,IAAI,CAAC,OACH,OAAO;IAGT,IAAI,SAAS,OACX,OAAO;IAGT,OAAO,CAAA,GAAA,+CAAiB,EAAE,MAAM;AAClC", "sources": ["packages/@react-stately/datepicker/src/useTimeFieldState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DateFieldState, useDateFieldState} from '.';\nimport {DateValue, MappedTimeValue, TimePickerProps, TimeValue} from '@react-types/datepicker';\nimport {getLocalTimeZone, GregorianCalendar, Time, toCalendarDateTime, today, toTime, toZoned} from '@internationalized/date';\nimport {useCallback, useMemo} from 'react';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface TimeFieldStateOptions<T extends TimeValue = TimeValue> extends TimePickerProps<T> {\n  /** The locale to display and edit the value according to. */\n  locale: string\n}\n\nexport interface TimeFieldState extends DateFieldState {\n  /** The current time value. */\n  timeValue: Time\n}\n\n/**\n * Provides state management for a time field component.\n * A time field allows users to enter and edit time values using a keyboard.\n * Each part of a time value is displayed in an individually editable segment.\n */\nexport function useTimeFieldState<T extends TimeValue = TimeValue>(props: TimeFieldStateOptions<T>): TimeFieldState {\n  let {\n    placeholderValue = new Time(),\n    minValue,\n    maxValue,\n    granularity,\n    validate\n  } = props;\n\n  let [value, setValue] = useControlledState<TimeValue | null, MappedTimeValue<T> | null>(\n    props.value,\n    props.defaultValue ?? null,\n    props.onChange\n  );\n\n  let v = value || placeholderValue;\n  let day = v && 'day' in v ? v : undefined;\n  let defaultValueTimeZone = props.defaultValue && 'timeZone' in props.defaultValue ? props.defaultValue.timeZone : undefined;\n  let placeholderDate = useMemo(() => {\n    let valueTimeZone = v && 'timeZone' in v ? v.timeZone : undefined;\n\n    return (valueTimeZone || defaultValueTimeZone) && placeholderValue ? toZoned(convertValue(placeholderValue)!, valueTimeZone || defaultValueTimeZone!) : convertValue(placeholderValue);\n  }, [placeholderValue, v, defaultValueTimeZone]);\n  let minDate = useMemo(() => convertValue(minValue, day), [minValue, day]);\n  let maxDate = useMemo(() => convertValue(maxValue, day), [maxValue, day]);\n\n  let timeValue = useMemo(() => value && 'day' in value ? toTime(value) : value as Time, [value]);\n  let dateTime = useMemo(() => value == null ? null : convertValue(value), [value]);\n  let onChange = newValue => {\n    setValue(day || defaultValueTimeZone ? newValue : newValue && toTime(newValue));\n  };\n\n  let state = useDateFieldState({\n    ...props,\n    value: dateTime,\n    defaultValue: undefined,\n    minValue: minDate,\n    maxValue: maxDate,\n    onChange,\n    granularity: granularity || 'minute',\n    maxGranularity: 'hour',\n    placeholderValue: placeholderDate ?? undefined,\n    // Calendar should not matter for time fields.\n    createCalendar: () => new GregorianCalendar(),\n    validate: useCallback(() => validate?.(value as any), [validate, value])\n  });\n\n  return {\n    ...state,\n    timeValue\n  };\n}\n\nfunction convertValue(value: TimeValue | null | undefined, date: DateValue = today(getLocalTimeZone())) {\n  if (!value) {\n    return null;\n  }\n\n  if ('day' in value) {\n    return value;\n  }\n\n  return toCalendarDateTime(date, value);\n}\n"], "names": [], "version": 3, "file": "useTimeFieldState.main.js.map"}
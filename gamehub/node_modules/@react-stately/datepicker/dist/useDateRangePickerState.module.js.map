{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AAwEM,SAAS,yCAAyD,KAAqC;QAgF7F,cAAiF;IA/EhG,IAAI,eAAe,CAAA,GAAA,6BAAqB,EAAE;IAC1C,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,yBAAiB,EAA2D,MAAM,KAAK,EAAE,MAAM,YAAY,IAAI,MAAM,MAAM,QAAQ;IAC/K,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,eAAO,EAAgC,IAAM,mBAAmB;YAAC,OAAO;YAAM,KAAK;QAAI;IAErI,0DAA0D;IAC1D,IAAI,mBAAmB,QAAQ,iBAAiB,KAAK,IAAI,iBAAiB,GAAG,EAAE;QAC7E,mBAAmB;YAAC,OAAO;YAAM,KAAK;QAAI;QAC1C,oBAAoB;IACtB;IAEA,IAAI,QAAQ,mBAAmB;IAE/B,IAAI,WAAW,CAAC;QACd,oBAAoB,SAAS;YAAC,OAAO;YAAM,KAAK;QAAI;QACpD,IAAI,sCAAgB,QAClB,mBAAmB;aAEnB,mBAAmB;IAEvB;IAEA,IAAI,IAAK,CAAA,kBAAA,4BAAA,MAAO,KAAK,MAAI,kBAAA,4BAAA,MAAO,GAAG,KAAI,MAAM,gBAAgB,IAAI;IACjE,IAAI,CAAC,aAAa,gBAAgB,GAAG,CAAA,GAAA,yCAAc,EAAE,GAAG,MAAM,WAAW;IACzE,IAAI,UAAU,gBAAgB,UAAU,gBAAgB,YAAY,gBAAgB;QAC1D;IAA1B,IAAI,sBAAsB,CAAA,6BAAA,MAAM,mBAAmB,cAAzB,wCAAA,6BAA6B;IAEvD,IAAI,CAAC,WAAW,qBAAqB,GAAG,CAAA,GAAA,eAAO,EAAuC;IACtF,IAAI,CAAC,WAAW,qBAAqB,GAAG,CAAA,GAAA,eAAO,EAAuC;IAEtF,IAAI,SAAS,sCAAgB,QAAQ;QACnC,YAAY;QACZ,IAAI,UAAU,MAAM,KAAK,EACvB,YAAY;IAEhB;IAEA,IAAI,cAAc,CAAC,WAAsB;QACvC,SAAS;YACP,OAAO,cAAc,UAAU,KAAK,GAAG,UAAU,KAAK,CAAC,GAAG,CAAC,CAAA,GAAA,qBAAa,EAAE,UAAU,KAAK,KAAK,CAAA,GAAA,yBAAiB,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK;YACjJ,KAAK,cAAc,UAAU,GAAG,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,CAAA,GAAA,qBAAa,EAAE,UAAU,GAAG,KAAK,CAAA,GAAA,yBAAiB,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG;QACvI;QACA,qBAAqB;QACrB,qBAAqB;QACrB,WAAW,gBAAgB;IAC7B;IAEA,gGAAgG;IAChG,IAAI,eAAe,CAAC;QAClB,IAAI,cAAc,OAAO,wBAAwB,aAAa,wBAAwB;QACtF,IAAI;YACF,uGAAuG;YACvG,IAAI,sCAAgB,UAAW,CAAA,eAAgB,CAAA,sBAAA,gCAAA,UAAW,KAAK,MAAI,sBAAA,gCAAA,UAAW,GAAG,CAAA,GAC/E,YAAY,OAAO;gBACjB,OAAO,CAAA,sBAAA,gCAAA,UAAW,KAAK,KAAI,CAAA,GAAA,yCAAiB,EAAE,MAAM,gBAAgB;gBACpE,KAAK,CAAA,sBAAA,gCAAA,UAAW,GAAG,KAAI,CAAA,GAAA,yCAAiB,EAAE,MAAM,gBAAgB;YAClE;iBAEA,qBAAqB;eAElB,IAAI,sCAAgB,QAAQ;YACjC,SAAS;YACT,WAAW,gBAAgB;QAC7B,OACE,qBAAqB;QAGvB,IAAI,aACF,aAAa,OAAO,CAAC;IAEzB;IAEA,IAAI,eAAe,CAAC;QAClB,IAAI,sCAAgB,cAAc,sCAAgB,QAChD,YAAY,WAAW;aAEvB,qBAAqB;IAEzB;IAEA,IAAI,UAAU,CAAC,kBAAA,6BAAA,eAAA,MAAO,KAAK,cAAZ,mCAAA,aAAc,QAAQ,CAAC,UAAU,MAAK,aAAa,MAAM,KAAK,CAAC,GAAG,KAAK,QAAU,CAAA,kBAAA,6BAAA,aAAA,MAAO,GAAG,cAAV,iCAAA,WAAY,QAAQ,CAAC,UAAU,MAAK,aAAa,MAAM,GAAG,CAAC,GAAG,KAAK;IACnK,IAAI,aAAa,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;yBAC9B;YACA,UAAU;YACV,cAAc,MAAM,YAAY;YAChC,WAAW,MAAM,SAAS;YAC1B,yBAAyB,MAAM,uBAAuB;qBACtD;QACF,CAAA,GAAI;QAAC;QAAa,MAAM,SAAS;QAAE,MAAM,uBAAuB;QAAE;QAAiB,MAAM,YAAY;QAAE;KAAQ;IAE/G,IAAI,YAAC,QAAQ,YAAE,QAAQ,qBAAE,iBAAiB,EAAC,GAAG;IAC9C,IAAI,oBAAoB,CAAA,GAAA,cAAM,EAAE,IAAM,CAAA,GAAA,yCAAuB,EAC3D,OACA,UACA,UACA,mBACA,aACC;QAAC;QAAO;QAAU;QAAU;QAAmB;KAAW;IAE7D,IAAI,aAAa,CAAA,GAAA,6BAAqB,EAAE;QACtC,GAAG,KAAK;QACR,OAAO;QACP,MAAM,CAAA,GAAA,cAAM,EAAE,IAAM;gBAAC,MAAM,SAAS;gBAAE,MAAM,OAAO;aAAC,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,OAAO;YAAC,MAAM,SAAS;YAAE,MAAM,OAAO;SAAC;2BAC7G;IACF;IAEA,IAAI,iBAAiB,WAAW,iBAAiB,CAAC,SAAS;IAC3D,IAAI,kBAA0C,MAAM,eAAe,IAAK,CAAA,iBAAiB,YAAY,IAAG;IAExG,OAAO;QACL,GAAG,UAAU;eACb;kBACA;mBACA;mBACA;qBACA;iBACA;QACA,SAAQ,IAAI,EAAE,IAAI;gBAEkB,gBAEX;YAHvB,IAAI,SAAS,SACX,aAAa;gBAAC,OAAO;gBAAM,KAAK,CAAA,iBAAA,sBAAA,gCAAA,UAAW,GAAG,cAAd,4BAAA,iBAAkB;YAAI;iBAEtD,aAAa;gBAAC,OAAO,CAAA,mBAAA,sBAAA,gCAAA,UAAW,KAAK,cAAhB,8BAAA,mBAAoB;gBAAM,KAAK;YAAI;QAE5D;QACA,SAAQ,IAAI,EAAE,IAAI;gBAEkB,gBAEX;YAHvB,IAAI,SAAS,SACX,aAAa;gBAAC,OAAO;gBAAM,KAAK,CAAA,iBAAA,sBAAA,gCAAA,UAAW,GAAG,cAAd,4BAAA,iBAAkB;YAAI;iBAEtD,aAAa;gBAAC,OAAO,CAAA,mBAAA,sBAAA,gCAAA,UAAW,KAAK,cAAhB,8BAAA,mBAAoB;gBAAM,KAAK;YAAI;QAE5D;QACA,aAAY,IAAI,EAAE,QAAQ;gBAEU,YAEf;YAHnB,IAAI,SAAS,SACX,SAAS;gBAAC,OAAO;gBAAU,KAAK,CAAA,aAAA,kBAAA,4BAAA,MAAO,GAAG,cAAV,wBAAA,aAAc;YAAI;iBAElD,SAAS;gBAAC,OAAO,CAAA,eAAA,kBAAA,4BAAA,MAAO,KAAK,cAAZ,0BAAA,eAAgB;gBAAM,KAAK;YAAQ;QAExD;sBACA;sBACA;QACA,GAAG,YAAY;QACf,SAAQ,MAAM;YACZ,wGAAwG;YACxG,yGAAyG;YACzG,oCAAoC;YACpC,IAAI,CAAC,UAAU,CAAE,CAAA,CAAA,kBAAA,4BAAA,MAAO,KAAK,MAAI,kBAAA,4BAAA,MAAO,GAAG,CAAD,KAAM,sCAAgB,cAAc,SAC5E,YAAY,WAAW;gBACrB,OAAO,CAAA,sBAAA,gCAAA,UAAW,KAAK,KAAI,CAAA,GAAA,yCAAiB,EAAE,MAAM,gBAAgB;gBACpE,KAAK,CAAA,sBAAA,gCAAA,UAAW,GAAG,KAAI,CAAA,GAAA,yCAAiB,EAAE,MAAM,gBAAgB;YAClE;YAGF,aAAa,OAAO,CAAC;QACvB;yBACA;QACA,WAAW;QACX,aAAY,MAAM,EAAE,YAAY;YAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,GAAG,EACtC,OAAO;YAGT,IAAI,gBAAgB,cAAc,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvE,IAAI,mBAAmB,MAAM,WAAW,IAAK,CAAA,MAAM,KAAK,IAAI,YAAY,MAAM,KAAK,GAAG,WAAW,KAAI;YACrG,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,QAAQ,GAAG;YACjE,IAAI,iBAAiB,MAAM,WAAW,IAAK,CAAA,MAAM,GAAG,IAAI,YAAY,MAAM,GAAG,GAAG,WAAW,KAAI;YAE/F,IAAI,eAAe,CAAA,GAAA,yCAAe,EAAE,cAAc;gBAChD,aAAa;gBACb,UAAU;gBACV,cAAc,MAAM,YAAY;gBAChC,WAAW,MAAM,SAAS;gBAC1B,SAAS,AAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,UAAU,KAAK,aAAa,MAAM,KAAK,CAAC,GAAG,KAAK,QAC5E,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,KAAK,aAAa,MAAM,GAAG,CAAC,GAAG,KAAK;YACtE;YAEA,IAAI,YAAY,MAAM,KAAK,CAAC,MAAM,CAAC,iBAAiB;YACpD,IAAI,UAAU,MAAM,GAAG,CAAC,MAAM,CAAC,eAAe;YAE9C,IAAI,iBAAiB,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ;YAC/C,IAAI;YACJ,IAAI,kBAAkB,eAAe,qBAAqB,kBAAkB,MAAM,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,GAAG;gBAChH,2EAA2E;gBAC3E,oEAAoE;gBACpE,oGAAoG;gBACpG,IAAI;oBACF,IAAI,QAAQ,eAAe,kBAAkB,CAAC,WAAW;oBAEzD,wEAAwE;oBACxE,2DAA2D;oBAC3D,IAAI,iBAAiB;oBACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,IAAI,OAAO,KAAK,CAAC,EAAE;wBACnB,IAAI,KAAK,MAAM,KAAK,YAAY,KAAK,IAAI,KAAK,WAC5C,iBAAiB;6BACZ,IAAI,KAAK,MAAM,KAAK,YACzB;oBAEJ;oBAEA,2DAA2D;oBAC3D,IAAI,QAAQ;oBACZ,IAAI,MAAM;oBACV,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,IAAI,IAAI,gBACN,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK;6BAClB,IAAI,IAAI,gBACb,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK;oBAEzB;oBAEA,OAAO;+BAAC;6BAAO;oBAAG;gBACpB,EAAE,OAAM;gBACN,SAAS;gBACX;gBAEA,eAAe;YACjB,OAAO;gBACL,IAAI,aAAa,CAAA,GAAA,yCAAe,EAAE,cAAc;oBAC9C,aAAa;oBACb,UAAU;oBACV,cAAc,MAAM,YAAY;oBAChC,WAAW,MAAM,SAAS;gBAC5B;gBAEA,eAAe,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ;YAC3C;YAEA,OAAO;gBACL,OAAO,eAAe,MAAM,CAAC;gBAC7B,KAAK,aAAa,MAAM,CAAC;YAC3B;QACF;QACA,kBAAiB,MAAM,EAAE,aAA+B;YACtD,IAAI,aAAa;gBAAC,GAAG,UAAU;gBAAE,GAAG,aAAa;YAAA;YACjD,IAAI,mBAAmB,CAAA,GAAA,yCAAe,EAAE,CAAC,GAAG;YAC5C,OAAO,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ;QACnC;IACF;AACF;AAEA,SAAS,sCAAmB,KAAkC;IAC5D,OAAO,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI,QAAQ,MAAM,GAAG,IAAI;AAC9C", "sources": ["packages/@react-stately/datepicker/src/useDateRangePickerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n\nimport {DateFormatter, toCalendarDate, toCalendarDateTime} from '@internationalized/date';\nimport {DateRange, DateRangePickerProps, DateValue, Granularity, MappedDateValue, TimeValue} from '@react-types/datepicker';\nimport {FieldOptions, FormatterOptions, getFormatOptions, getPlaceholderTime, getRangeValidationResult, useDefaultProps} from './utils';\nimport {FormValidationState, useFormValidationState} from '@react-stately/form';\nimport {OverlayTriggerState, useOverlayTriggerState} from '@react-stately/overlays';\nimport {RangeValue, ValidationState} from '@react-types/shared';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo, useState} from 'react';\n\nexport interface DateRangePickerStateOptions<T extends DateValue = DateValue> extends DateRangePickerProps<T> {\n  /**\n   * Determines whether the date picker popover should close automatically when a date is selected.\n   * @default true\n   */\n  shouldCloseOnSelect?: boolean | (() => boolean)\n}\n\ntype TimeRange = RangeValue<TimeValue>;\nexport interface DateRangePickerState extends OverlayTriggerState, FormValidationState {\n  /** The currently selected date range. */\n  value: RangeValue<DateValue | null>,\n  /** Sets the selected date range. */\n  setValue(value: DateRange | null): void,\n  /**\n   * The date portion of the selected range. This may be set prior to `value` if the user has\n   * selected a date range but has not yet selected a time range.\n   */\n  dateRange: RangeValue<DateValue | null> | null,\n  /** Sets the date portion of the selected range. */\n  setDateRange(value: DateRange): void,\n  /**\n   * The time portion of the selected range. This may be set prior to `value` if the user has\n   * selected a time range but has not yet selected a date range.\n   */\n  timeRange: RangeValue<TimeValue | null> | null,\n  /** Sets the time portion of the selected range. */\n  setTimeRange(value: TimeRange): void,\n  /** Sets the date portion of either the start or end of the selected range. */\n  setDate(part: 'start' | 'end', value: DateValue | null): void,\n  /** Sets the time portion of either the start or end of the selected range. */\n  setTime(part: 'start' | 'end', value: TimeValue | null): void,\n  /** Sets the date and time of either the start or end of the selected range. */\n  setDateTime(part: 'start' | 'end', value: DateValue | null): void,\n  /** The granularity for the field, based on the `granularity` prop and current value. */\n  granularity: Granularity,\n  /** Whether the date range picker supports selecting times, according to the `granularity` prop and current value. */\n  hasTime: boolean,\n  /** Whether the calendar popover is currently open. */\n  isOpen: boolean,\n  /** Sets whether the calendar popover is open. */\n  setOpen(isOpen: boolean): void,\n  /**\n   * The current validation state of the date range picker, based on the `validationState`, `minValue`, and `maxValue` props.\n   * @deprecated Use `isInvalid` instead.\n   */\n  validationState: ValidationState | null,\n  /** Whether the date range picker is invalid, based on the `isInvalid`, `minValue`, and `maxValue` props. */\n  isInvalid: boolean,\n  /** Formats the selected range using the given options. */\n  formatValue(locale: string, fieldOptions: FieldOptions): {start: string, end: string} | null,\n  /** Gets a formatter based on state's props. */\n  getDateFormatter(locale: string, formatOptions: FormatterOptions): DateFormatter\n}\n\n/**\n * Provides state management for a date range picker component.\n * A date range picker combines two DateFields and a RangeCalendar popover to allow\n * users to enter or select a date and time range.\n */\nexport function useDateRangePickerState<T extends DateValue = DateValue>(props: DateRangePickerStateOptions<T>): DateRangePickerState {\n  let overlayState = useOverlayTriggerState(props);\n  let [controlledValue, setControlledValue] = useControlledState<DateRange | null, RangeValue<MappedDateValue<T>> | null>(props.value, props.defaultValue || null, props.onChange);\n  let [placeholderValue, setPlaceholderValue] = useState<RangeValue<DateValue | null>>(() => controlledValue || {start: null, end: null});\n\n  // Reset the placeholder if the value prop is set to null.\n  if (controlledValue == null && placeholderValue.start && placeholderValue.end) {\n    placeholderValue = {start: null, end: null};\n    setPlaceholderValue(placeholderValue);\n  }\n\n  let value = controlledValue || placeholderValue;\n\n  let setValue = (value: RangeValue<DateValue | null> | null) => {\n    setPlaceholderValue(value || {start: null, end: null});\n    if (isCompleteRange(value)) {\n      setControlledValue(value);\n    } else {\n      setControlledValue(null);\n    }\n  };\n\n  let v = (value?.start || value?.end || props.placeholderValue || null);\n  let [granularity, defaultTimeZone] = useDefaultProps(v, props.granularity);\n  let hasTime = granularity === 'hour' || granularity === 'minute' || granularity === 'second';\n  let shouldCloseOnSelect = props.shouldCloseOnSelect ?? true;\n\n  let [dateRange, setSelectedDateRange] = useState<RangeValue<DateValue | null> | null>(null);\n  let [timeRange, setSelectedTimeRange] = useState<RangeValue<TimeValue | null> | null>(null);\n\n  if (value && isCompleteRange(value)) {\n    dateRange = value;\n    if ('hour' in value.start) {\n      timeRange = value as TimeRange;\n    }\n  }\n\n  let commitValue = (dateRange: DateRange, timeRange: TimeRange) => {\n    setValue({\n      start: 'timeZone' in timeRange.start ? timeRange.start.set(toCalendarDate(dateRange.start)) : toCalendarDateTime(dateRange.start, timeRange.start),\n      end: 'timeZone' in timeRange.end ? timeRange.end.set(toCalendarDate(dateRange.end)) : toCalendarDateTime(dateRange.end, timeRange.end)\n    });\n    setSelectedDateRange(null);\n    setSelectedTimeRange(null);\n    validation.commitValidation();\n  };\n\n  // Intercept setValue to make sure the Time section is not changed by date selection in Calendar\n  let setDateRange = (range: RangeValue<DateValue | null>) => {\n    let shouldClose = typeof shouldCloseOnSelect === 'function' ? shouldCloseOnSelect() : shouldCloseOnSelect;\n    if (hasTime) {\n      // Set a placeholder time if the popover is closing so we don't leave the field in an incomplete state.\n      if (isCompleteRange(range) && (shouldClose || (timeRange?.start && timeRange?.end))) {\n        commitValue(range, {\n          start: timeRange?.start || getPlaceholderTime(props.placeholderValue),\n          end: timeRange?.end || getPlaceholderTime(props.placeholderValue)\n        });\n      } else {\n        setSelectedDateRange(range);\n      }\n    } else if (isCompleteRange(range)) {\n      setValue(range);\n      validation.commitValidation();\n    } else {\n      setSelectedDateRange(range);\n    }\n\n    if (shouldClose) {\n      overlayState.setOpen(false);\n    }\n  };\n\n  let setTimeRange = (range: RangeValue<TimeValue | null>) => {\n    if (isCompleteRange(dateRange) && isCompleteRange(range)) {\n      commitValue(dateRange, range);\n    } else {\n      setSelectedTimeRange(range);\n    }\n  };\n\n  let showEra = (value?.start?.calendar.identifier === 'gregory' && value.start.era === 'BC') || (value?.end?.calendar.identifier === 'gregory' && value.end.era === 'BC');\n  let formatOpts = useMemo(() => ({\n    granularity,\n    timeZone: defaultTimeZone,\n    hideTimeZone: props.hideTimeZone,\n    hourCycle: props.hourCycle,\n    shouldForceLeadingZeros: props.shouldForceLeadingZeros,\n    showEra\n  }), [granularity, props.hourCycle, props.shouldForceLeadingZeros, defaultTimeZone, props.hideTimeZone, showEra]);\n\n  let {minValue, maxValue, isDateUnavailable} = props;\n  let builtinValidation = useMemo(() => getRangeValidationResult(\n    value,\n    minValue,\n    maxValue,\n    isDateUnavailable,\n    formatOpts\n  ), [value, minValue, maxValue, isDateUnavailable, formatOpts]);\n\n  let validation = useFormValidationState({\n    ...props,\n    value: controlledValue as RangeValue<MappedDateValue<T>> | null,\n    name: useMemo(() => [props.startName, props.endName].filter(n => n != null), [props.startName, props.endName]),\n    builtinValidation\n  });\n\n  let isValueInvalid = validation.displayValidation.isInvalid;\n  let validationState: ValidationState | null = props.validationState || (isValueInvalid ? 'invalid' : null);\n\n  return {\n    ...validation,\n    value,\n    setValue,\n    dateRange,\n    timeRange,\n    granularity,\n    hasTime,\n    setDate(part, date) {\n      if (part === 'start') {\n        setDateRange({start: date, end: dateRange?.end ?? null});\n      } else {\n        setDateRange({start: dateRange?.start ?? null, end: date});\n      }\n    },\n    setTime(part, time) {\n      if (part === 'start') {\n        setTimeRange({start: time, end: timeRange?.end ?? null});\n      } else {\n        setTimeRange({start: timeRange?.start ?? null, end: time});\n      }\n    },\n    setDateTime(part, dateTime) {\n      if (part === 'start') {\n        setValue({start: dateTime, end: value?.end ?? null});\n      } else {\n        setValue({start: value?.start ?? null, end: dateTime});\n      }\n    },\n    setDateRange,\n    setTimeRange,\n    ...overlayState,\n    setOpen(isOpen) {\n      // Commit the selected date range when the calendar is closed. Use a placeholder time if one wasn't set.\n      // If only the time range was set and not the date range, don't commit. The state will be preserved until\n      // the user opens the popover again.\n      if (!isOpen && !(value?.start && value?.end) && isCompleteRange(dateRange) && hasTime) {\n        commitValue(dateRange, {\n          start: timeRange?.start || getPlaceholderTime(props.placeholderValue),\n          end: timeRange?.end || getPlaceholderTime(props.placeholderValue)\n        });\n      }\n\n      overlayState.setOpen(isOpen);\n    },\n    validationState,\n    isInvalid: isValueInvalid,\n    formatValue(locale, fieldOptions) {\n      if (!value || !value.start || !value.end) {\n        return null;\n      }\n\n      let startTimeZone = 'timeZone' in value.start ? value.start.timeZone : undefined;\n      let startGranularity = props.granularity || (value.start && 'minute' in value.start ? 'minute' : 'day');\n      let endTimeZone = 'timeZone' in value.end ? value.end.timeZone : undefined;\n      let endGranularity = props.granularity || (value.end && 'minute' in value.end ? 'minute' : 'day');\n\n      let startOptions = getFormatOptions(fieldOptions, {\n        granularity: startGranularity,\n        timeZone: startTimeZone,\n        hideTimeZone: props.hideTimeZone,\n        hourCycle: props.hourCycle,\n        showEra: (value.start.calendar.identifier === 'gregory' && value.start.era === 'BC') ||\n          (value.end.calendar.identifier === 'gregory' && value.end.era === 'BC')\n      });\n\n      let startDate = value.start.toDate(startTimeZone || 'UTC');\n      let endDate = value.end.toDate(endTimeZone || 'UTC');\n\n      let startFormatter = new DateFormatter(locale, startOptions);\n      let endFormatter: Intl.DateTimeFormat;\n      if (startTimeZone === endTimeZone && startGranularity === endGranularity && value.start.compare(value.end) !== 0) {\n        // Use formatRange, as it results in shorter output when some of the fields\n        // are shared between the start and end dates (e.g. the same month).\n        // Formatting will fail if the end date is before the start date. Fall back below when that happens.\n        try {\n          let parts = startFormatter.formatRangeToParts(startDate, endDate);\n\n          // Find the separator between the start and end date. This is determined\n          // by finding the last shared literal before the end range.\n          let separatorIndex = -1;\n          for (let i = 0; i < parts.length; i++) {\n            let part = parts[i];\n            if (part.source === 'shared' && part.type === 'literal') {\n              separatorIndex = i;\n            } else if (part.source === 'endRange') {\n              break;\n            }\n          }\n\n          // Now we can combine the parts into start and end strings.\n          let start = '';\n          let end = '';\n          for (let i = 0; i < parts.length; i++) {\n            if (i < separatorIndex) {\n              start += parts[i].value;\n            } else if (i > separatorIndex) {\n              end += parts[i].value;\n            }\n          }\n\n          return {start, end};\n        } catch {\n          // ignore\n        }\n\n        endFormatter = startFormatter;\n      } else {\n        let endOptions = getFormatOptions(fieldOptions, {\n          granularity: endGranularity,\n          timeZone: endTimeZone,\n          hideTimeZone: props.hideTimeZone,\n          hourCycle: props.hourCycle\n        });\n\n        endFormatter = new DateFormatter(locale, endOptions);\n      }\n\n      return {\n        start: startFormatter.format(startDate),\n        end: endFormatter.format(endDate)\n      };\n    },\n    getDateFormatter(locale, formatOptions: FormatterOptions) {\n      let newOptions = {...formatOpts, ...formatOptions};\n      let newFormatOptions = getFormatOptions({}, newOptions);\n      return new DateFormatter(locale, newFormatOptions);\n    }\n  };\n}\n\nfunction isCompleteRange<T>(value: RangeValue<T | null> | null): value is RangeValue<T> {\n  return value?.start != null && value.end != null;\n}\n"], "names": [], "version": 3, "file": "useDateRangePickerState.module.js.map"}
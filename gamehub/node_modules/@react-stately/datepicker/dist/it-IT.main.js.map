{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IACnG,iBAAiB,CAAC,4DAA4D,CAAC;IAC/E,kBAAkB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IAClF,mBAAmB,CAAC,iCAAiC,CAAC;AACxD", "sources": ["packages/@react-stately/datepicker/intl/it-IT.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Il valore deve essere {maxValue} o precedente.\",\n  \"rangeReversed\": \"La data di inizio deve essere antecedente alla data di fine.\",\n  \"rangeUnderflow\": \"Il valore deve essere {minValue} o successivo.\",\n  \"unavailableDate\": \"Data selezionata non disponibile.\"\n}\n"], "names": [], "version": 3, "file": "it-IT.main.js.map"}
{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,yBAAmB,EAAE,KAAK,QAAQ,CAAC,gBAAa,CAAC;IAC/F,iBAAiB,CAAC,mDAA0C,CAAC;IAC7D,kBAAkB,CAAC,OAAS,CAAC,yBAAmB,EAAE,KAAK,QAAQ,CAAC,gBAAa,CAAC;IAC9E,mBAAmB,CAAC,iCAA8B,CAAC;AACrD", "sources": ["packages/@react-stately/datepicker/intl/et-EE.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Väärtus peab olema {maxValue} või varasem.\",\n  \"rangeReversed\": \"Algus<PERSON>up<PERSON>ev peab olema enne lõ<PERSON>.\",\n  \"rangeUnderflow\": \"Väärtus peab olema {minValue} või hilisem.\",\n  \"unavailableDate\": \"<PERSON><PERSON><PERSON> kuup<PERSON>ev pole saadaval.\"\n}\n"], "names": [], "version": 3, "file": "et-EE.main.js.map"}
{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,oLAA8B,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;IAC9F,iBAAiB,CAAC,mTAAiD,CAAC;IACpE,kBAAkB,CAAC,OAAS,CAAC,2LAA+B,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;IAC9E,mBAAmB,CAAC,oKAA0B,CAAC;AACjD", "sources": ["packages/@react-stately/datepicker/intl/ru-RU.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Значение должно быть не позже {maxValue}.\",\n  \"rangeReversed\": \"Дата начала должна предшествовать дате окончания.\",\n  \"rangeUnderflow\": \"Значение должно быть не раньше {minValue}.\",\n  \"unavailableDate\": \"Выбранная дата недоступна.\"\n}\n"], "names": [], "version": 3, "file": "ru-RU.module.js.map"}
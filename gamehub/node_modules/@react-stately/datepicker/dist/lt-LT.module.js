var $8f855bffe63f6eca$exports = {};
$8f855bffe63f6eca$exports = {
    "rangeOverflow": (args)=>`<PERSON><PERSON>\u{161}m\u{117} turi b\u{16B}ti ${args.maxValue} arba ankstesn\u{117}.`,
    "rangeReversed": `Prad\u{17E}ios data turi b\u{16B}ti ankstesn\u{117} nei pabaigos data.`,
    "rangeUnderflow": (args)=>`<PERSON>ik\u{161}m\u{117} turi b\u{16B}ti ${args.minValue} arba naujesn\u{117}.`,
    "unavailableDate": `Pasirinkta data nepasiekiama.`
};


export {$8f855bffe63f6eca$exports as default};
//# sourceMappingURL=lt-LT.module.js.map

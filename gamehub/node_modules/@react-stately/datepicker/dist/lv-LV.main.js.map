{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,0CAAkB,EAAE,KAAK,QAAQ,CAAC,mBAAa,CAAC;IAC9F,iBAAiB,CAAC,6DAA2C,CAAC;IAC9D,kBAAkB,CAAC,OAAS,CAAC,0CAAkB,EAAE,KAAK,QAAQ,CAAC,yBAAa,CAAC;IAC7E,mBAAmB,CAAC,qCAA+B,CAAC;AACtD", "sources": ["packages/@react-stately/datepicker/intl/lv-LV.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Vērtībai ir jābūt {maxValue} vai agrākai.\",\n  \"rangeReversed\": \"<PERSON><PERSON>ku<PERSON> datumam ir jābūt pirms beigu datuma.\",\n  \"rangeUnderflow\": \"Vērtībai ir jābūt {minValue} vai vēlākai.\",\n  \"unavailableDate\": \"Atlasītais datums nav pieejams.\"\n}\n"], "names": [], "version": 3, "file": "lv-LV.main.js.map"}
var $27f5cd2291ca8a02$exports = {};
$27f5cd2291ca8a02$exports = {
    "rangeOverflow": (args)=>`O valor deve ser ${args.maxValue} ou anterior.`,
    "rangeReversed": `A data inicial deve ser anterior \xe0 data final.`,
    "rangeUnderflow": (args)=>`O valor deve ser ${args.minValue} ou posterior.`,
    "unavailableDate": `Data selecionada indispon\xedvel.`
};


export {$27f5cd2291ca8a02$exports as default};
//# sourceMappingURL=pt-BR.module.js.map

{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,wBAAkB,EAAE,KAAK,QAAQ,CAAC,iBAAiB,CAAC;IAClG,iBAAiB,CAAC,2CAAqC,CAAC;IACxD,kBAAkB,CAAC,OAAS,CAAC,wBAAkB,EAAE,KAAK,QAAQ,CAAC,aAAa,CAAC;IAC7E,mBAAmB,CAAC,uCAAoC,CAAC;AAC3D", "sources": ["packages/@react-stately/datepicker/intl/da-DK.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Værdien skal være {maxValue} eller tidligere.\",\n  \"rangeReversed\": \"Startdatoen skal være før slutdatoen.\",\n  \"rangeUnderflow\": \"Værdien skal være {minValue} eller nyere.\",\n  \"unavailableDate\": \"Den valgte dato er ikke tilgængelig.\"\n}\n"], "names": [], "version": 3, "file": "da-DK.main.js.map"}
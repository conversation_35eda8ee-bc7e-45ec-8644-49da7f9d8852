{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,oBAAc,CAAC;IAChG,iBAAiB,CAAC,yDAA6C,CAAC;IAChE,kBAAkB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,mBAAa,CAAC;IAC9E,mBAAmB,CAAC,0BAA0B,CAAC;AACjD", "sources": ["packages/@react-stately/datepicker/intl/sl-SI.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Vrednost mora biti {maxValue} ali stare<PERSON>.\",\n  \"rangeReversed\": \"Začetni datum mora biti pred končnim datumom.\",\n  \"rangeUnderflow\": \"Vrednost mora biti {minValue} ali novej<PERSON>.\",\n  \"unavailableDate\": \"<PERSON>zbrani datum ni na voljo.\"\n}\n"], "names": [], "version": 3, "file": "sl-SI.main.js.map"}
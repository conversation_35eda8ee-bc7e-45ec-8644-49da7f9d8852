{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,QAAQ,CAAC,wBAAqB,CAAC;IACpG,iBAAiB,CAAC,2DAA4C,CAAC;IAC/D,kBAAkB,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,QAAQ,CAAC,+BAAsB,CAAC;IACpF,mBAAmB,CAAC,gEAAwC,CAAC;AAC/D", "sources": ["packages/@react-stately/datepicker/intl/fi-FI.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON><PERSON><PERSON> on oltava {maxValue} tai sitä aikaisempi.\",\n  \"rangeReversed\": \"Aloituspäivän on oltava ennen lopetuspäivää.\",\n  \"rangeUnderflow\": \"<PERSON><PERSON><PERSON> on oltava {minValue} tai sitä myöhäisempi.\",\n  \"unavailableDate\": \"Valittu päivämäärä ei ole käytettävissä.\"\n}\n"], "names": [], "version": 3, "file": "fi-FI.main.js.map"}
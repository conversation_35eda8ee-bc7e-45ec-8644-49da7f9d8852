{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,iCAAK,EAAE,KAAK,QAAQ,CAAC,iDAAO,CAAC;IAC3E,iBAAiB,CAAC,wGAAa,CAAC;IAChC,kBAAkB,CAAC,OAAS,CAAC,iCAAK,EAAE,KAAK,QAAQ,CAAC,iDAAO,CAAC;IAC1D,mBAAmB,CAAC,gEAAQ,CAAC;AAC/B", "sources": ["packages/@react-stately/datepicker/intl/zh-CN.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"值必须是 {maxValue} 或更早日期。\",\n  \"rangeReversed\": \"开始日期必须早于结束日期。\",\n  \"rangeUnderflow\": \"值必须是 {minValue} 或更晚日期。\",\n  \"unavailableDate\": \"所选日期不可用。\"\n}\n"], "names": [], "version": 3, "file": "zh-CN.module.js.map"}
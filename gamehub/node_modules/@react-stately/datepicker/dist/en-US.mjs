var $22195056771860be$exports = {};
$22195056771860be$exports = {
    "rangeUnderflow": (args)=>`Value must be ${args.minValue} or later.`,
    "rangeOverflow": (args)=>`Value must be ${args.maxValue} or earlier.`,
    "rangeReversed": `Start date must be before end date.`,
    "unavailableDate": `Selected date unavailable.`
};


export {$22195056771860be$exports as default};
//# sourceMappingURL=en-US.module.js.map

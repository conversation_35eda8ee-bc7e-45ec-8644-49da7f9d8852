{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,iBAAG,EAAE,KAAK,QAAQ,CAAC,yGAAc,CAAC;IAChF,iBAAiB,CAAC,wKAAqB,CAAC;IACxC,kBAAkB,CAAC,OAAS,CAAC,iBAAG,EAAE,KAAK,QAAQ,CAAC,yGAAc,CAAC;IAC/D,mBAAmB,CAAC,wHAAe,CAAC;AACtC", "sources": ["packages/@react-stately/datepicker/intl/ja-JP.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"値は {maxValue} 以下にする必要があります。\",\n  \"rangeReversed\": \"開始日は終了日より前にする必要があります。\",\n  \"rangeUnderflow\": \"値は {minValue} 以上にする必要があります。\",\n  \"unavailableDate\": \"選択した日付は使用できません。\"\n}\n"], "names": [], "version": 3, "file": "ja-JP.main.js.map"}
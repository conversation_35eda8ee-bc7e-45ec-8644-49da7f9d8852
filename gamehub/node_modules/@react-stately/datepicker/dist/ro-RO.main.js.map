{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,8BAAwB,EAAE,KAAK,QAAQ,CAAC,sBAAgB,CAAC;IACvG,iBAAiB,CAAC,mFAA2D,CAAC;IAC9E,kBAAkB,CAAC,OAAS,CAAC,8BAAwB,EAAE,KAAK,QAAQ,CAAC,sBAAgB,CAAC;IACtF,mBAAmB,CAAC,+CAAmC,CAAC;AAC1D", "sources": ["packages/@react-stately/datepicker/intl/ro-RO.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Valoarea trebuie să fie {maxValue} sau anterioară.\",\n  \"rangeReversed\": \"Data de început trebuie să fie anterioară datei de sfârșit.\",\n  \"rangeUnderflow\": \"Valoarea trebuie să fie {minValue} sau ulterioară.\",\n  \"unavailableDate\": \"Data selectată nu este disponibilă.\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.main.js.map"}
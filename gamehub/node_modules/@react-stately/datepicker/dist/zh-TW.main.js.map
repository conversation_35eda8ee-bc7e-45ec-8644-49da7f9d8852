{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,iCAAK,EAAE,KAAK,QAAQ,CAAC,iCAAK,CAAC;IACzE,iBAAiB,CAAC,gHAAc,CAAC;IACjC,kBAAkB,CAAC,OAAS,CAAC,iCAAK,EAAE,KAAK,QAAQ,CAAC,iCAAK,CAAC;IACxD,mBAAmB,CAAC,wEAAS,CAAC;AAChC", "sources": ["packages/@react-stately/datepicker/intl/zh-TW.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"值必須是 {maxValue} 或更早。\",\n  \"rangeReversed\": \"開始日期必須在結束日期之前。\",\n  \"rangeUnderflow\": \"值必須是 {minValue} 或更晚。\",\n  \"unavailableDate\": \"所選日期無法使用。\"\n}\n"], "names": [], "version": 3, "file": "zh-TW.main.js.map"}
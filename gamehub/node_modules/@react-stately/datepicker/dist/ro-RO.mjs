var $fa5f88e804986547$exports = {};
$fa5f88e804986547$exports = {
    "rangeOverflow": (args)=>`Valoarea trebuie s\u{103} fie ${args.maxValue} sau anterioar\u{103}.`,
    "rangeReversed": `Data de \xeenceput trebuie s\u{103} fie anterioar\u{103} datei de sf\xe2r\u{219}it.`,
    "rangeUnderflow": (args)=>`Valoarea trebuie s\u{103} fie ${args.minValue} sau ulterioar\u{103}.`,
    "unavailableDate": `Data selectat\u{103} nu este disponibil\u{103}.`
};


export {$fa5f88e804986547$exports as default};
//# sourceMappingURL=ro-RO.module.js.map

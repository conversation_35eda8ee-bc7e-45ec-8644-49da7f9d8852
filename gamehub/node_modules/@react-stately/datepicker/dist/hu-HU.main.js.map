{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,kBAAY,EAAE,KAAK,QAAQ,CAAC,gCAA6B,CAAC;IACxG,iBAAiB,CAAC,oFAA4D,CAAC;IAC/E,kBAAkB,CAAC,OAAS,CAAC,kBAAY,EAAE,KAAK,QAAQ,CAAC,sCAA6B,CAAC;IACvF,mBAAmB,CAAC,kDAAmC,CAAC;AAC1D", "sources": ["packages/@react-stately/datepicker/intl/hu-HU.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON>z értéknek {maxValue} vagy kor<PERSON>bbinak kell lennie.\",\n  \"rangeReversed\": \"A kezdő dátumnak a befejező dátumnál korábbinak kell lennie.\",\n  \"rangeUnderflow\": \"<PERSON>z értéknek {minValue} vagy k<PERSON>őbbinek kell lennie.\",\n  \"unavailableDate\": \"A kiválasztott dátum nem érhető el.\"\n}\n"], "names": [], "version": 3, "file": "hu-HU.main.js.map"}
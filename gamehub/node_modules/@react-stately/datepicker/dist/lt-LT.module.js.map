{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,oCAAkB,EAAE,KAAK,QAAQ,CAAC,sBAAgB,CAAC;IACjG,iBAAiB,CAAC,sEAAoD,CAAC;IACvE,kBAAkB,CAAC,OAAS,CAAC,oCAAkB,EAAE,KAAK,QAAQ,CAAC,qBAAe,CAAC;IAC/E,mBAAmB,CAAC,6BAA6B,CAAC;AACpD", "sources": ["packages/@react-stately/datepicker/intl/lt-LT.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti {maxValue} arba ankstesnė.\",\n  \"rangeReversed\": \"Pradžios data turi būti ankstesnė nei pabaigos data.\",\n  \"rangeUnderflow\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti {minValue} arba naujesnė.\",\n  \"unavailableDate\": \"Pasirinkta data nepasiekiama.\"\n}\n"], "names": [], "version": 3, "file": "lt-LT.module.js.map"}
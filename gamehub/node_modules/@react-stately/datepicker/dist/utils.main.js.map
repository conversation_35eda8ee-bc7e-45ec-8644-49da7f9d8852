{"mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAWD,MAAM,mCAAa,IAAI,CAAA,GAAA,wDAAwB,EAAE,CAAA,GAAA,mDAAW;AAE5D,SAAS;IACP,8GAA8G;IAC9G,qEAAqE;IACrE,aAAa;IACb,OAAO,AAAC,OAAO,cAAc,eAAgB,CAAA,UAAU,QAAQ,IAAI,UAAU,YAAY,AAAD,KAAO;AACjG;AAEO,SAAS,0CACd,KAAuB,EACvB,QAAsC,EACtC,QAAsC,EACtC,iBAA0D,EAC1D,OAAyB;IAEzB,IAAI,gBAAgB,SAAS,QAAQ,YAAY,QAAQ,MAAM,OAAO,CAAC,YAAY;IACnF,IAAI,iBAAiB,SAAS,QAAQ,YAAY,QAAQ,MAAM,OAAO,CAAC,YAAY;IACpF,IAAI,gBAAgB,AAAC,SAAS,SAAQ,8BAAA,wCAAA,kBAAoB,WAAW;IACrE,IAAI,YAAY,iBAAiB,kBAAkB;IACnD,IAAI,SAAmB,EAAE;IAEzB,IAAI,WAAW;QACb,IAAI,SAAS;QACb,IAAI,UAAU,CAAA,GAAA,wDAAwB,EAAE,6BAA6B,CAAC,gCAAgC;QACtG,IAAI,YAAY,IAAI,CAAA,GAAA,uDAAuB,EAAE,QAAQ;QACrD,IAAI,gBAAgB,IAAI,CAAA,GAAA,0CAAY,EAAE,QAAQ,0CAAiB,CAAC,GAAG;QACnE,IAAI,WAAW,cAAc,eAAe,GAAG,QAAQ;QAEvD,IAAI,kBAAkB,YAAY,MAChC,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC,kBAAkB;YAAC,UAAU,cAAc,MAAM,CAAC,SAAS,MAAM,CAAC;QAAU;QAG3G,IAAI,iBAAiB,YAAY,MAC/B,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC,iBAAiB;YAAC,UAAU,cAAc,MAAM,CAAC,SAAS,MAAM,CAAC;QAAU;QAG1G,IAAI,eACF,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC;IAEjC;IAEA,OAAO;mBACL;QACA,kBAAkB;QAClB,mBAAmB;YACjB,UAAU;YACV,aAAa;YACb,iBAAiB;2BACjB;4BACA;YACA,cAAc;YACd,SAAS;YACT,UAAU;YACV,cAAc;YACd,cAAc;YACd,OAAO,CAAC;QACV;IACF;AACF;AAEO,SAAS,0CACd,KAA0C,EAC1C,QAAsC,EACtC,QAAsC,EACtC,iBAA0D,EAC1D,OAAyB;QAGvB;IADF,IAAI,kBAAkB,0CACpB,CAAA,eAAA,kBAAA,4BAAA,MAAO,KAAK,cAAZ,0BAAA,eAAgB,MAChB,UACA,UACA,mBACA;QAIA;IADF,IAAI,gBAAgB,0CAClB,CAAA,aAAA,kBAAA,4BAAA,MAAO,GAAG,cAAV,wBAAA,aAAc,MACd,UACA,UACA,mBACA;IAGF,IAAI,SAAS,CAAA,GAAA,uCAAc,EAAE,iBAAiB;IAC9C,IAAI,CAAA,kBAAA,4BAAA,MAAO,GAAG,KAAI,QAAQ,MAAM,KAAK,IAAI,QAAQ,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,GAAG;QACnF,IAAI,UAAU,CAAA,GAAA,wDAAwB,EAAE,6BAA6B,CAAC,gCAAgC;QACtG,SAAS,CAAA,GAAA,uCAAc,EAAE,QAAQ;YAC/B,WAAW;YACX,kBAAkB;gBAAC,QAAQ,kBAAkB,CAAC,iBAAiB;aAAa;YAC5E,mBAAmB;gBACjB,GAAG,CAAA,GAAA,4CAAmB,CAAC;gBACvB,gBAAgB;gBAChB,eAAe;gBACf,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAaA,MAAM,8CAAsC;IAC1C,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEA,MAAM,gDAAwC;IAC5C,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,SAAS,0CACd,YAA0B,EAC1B,OAAyB;IAEzB,IAAI,sBAAsB,QAAQ,uBAAuB,GAAG,gDAA0B;IACtF,eAAe;QAAC,GAAG,mBAAmB;QAAE,GAAG,YAAY;IAAA;IACvD,IAAI,cAAc,QAAQ,WAAW,IAAI;IACzC,IAAI,OAAO,OAAO,IAAI,CAAC;QACK;IAA5B,IAAI,WAAW,KAAK,OAAO,CAAC,CAAA,0BAAA,QAAQ,cAAc,cAAtB,qCAAA,0BAA0B;IACtD,IAAI,WAAW,GACb,WAAW;IAGb,IAAI,SAAS,KAAK,OAAO,CAAC;IAC1B,IAAI,SAAS,GACX,SAAS;IAGX,IAAI,WAAW,QACb,MAAM,IAAI,MAAM;IAGlB,IAAI,OAAmC,KAAK,KAAK,CAAC,UAAU,SAAS,GAAG,MAAM,CAAC,CAAC,MAAM;QACpF,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI;QAC7B,OAAO;IACT,GAAG,CAAC;IAEJ,IAAI,QAAQ,SAAS,IAAI,MACvB,KAAK,MAAM,GAAG,QAAQ,SAAS,KAAK;IAGtC,KAAK,QAAQ,GAAG,QAAQ,QAAQ,IAAI;IAEpC,IAAI,UAAU,gBAAgB,UAAU,gBAAgB,YAAY,gBAAgB;IACpF,IAAI,WAAW,QAAQ,QAAQ,IAAI,CAAC,QAAQ,YAAY,EACtD,KAAK,YAAY,GAAG;IAGtB,IAAI,QAAQ,OAAO,IAAI,aAAa,GAClC,KAAK,GAAG,GAAG;IAGb,OAAO;AACT;AAEO,SAAS,0CAAmB,gBAA8C;IAC/E,IAAI,oBAAoB,UAAU,kBAChC,OAAO;IAGT,OAAO,IAAI,CAAA,GAAA,iCAAG;AAChB;AAEO,SAAS,0CAAa,KAAmC,EAAE,QAAkB;IAClF,IAAI,UAAU,MACZ,OAAO;IAGT,IAAI,CAAC,OACH,OAAO;IAGT,OAAO,CAAA,GAAA,uCAAS,EAAE,OAAO;AAC3B;AAGO,SAAS,0CAAsB,gBAA8C,EAAE,WAAmB,EAAE,QAAkB,EAAE,QAA4B;IACzJ,IAAI,kBACF,OAAO,0CAAa,kBAAkB;IAGxC,IAAI,OAAO,CAAA,GAAA,uCAAS,EAAE,CAAA,GAAA,gCAAE,EAAE,qBAAA,sBAAA,WAAY,CAAA,GAAA,6CAAe,KAAK,GAAG,CAAC;QAC5D,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,aAAa;IACf,IAAI;IAEJ,IAAI,gBAAgB,UAAU,gBAAgB,WAAW,gBAAgB,OACvE,OAAO,CAAA,GAAA,2CAAa,EAAE;IAGxB,IAAI,CAAC,UACH,OAAO,CAAA,GAAA,+CAAiB,EAAE;IAG5B,OAAO;AACT;AAEO,SAAS,0CAAgB,CAAmB,EAAE,WAAoC;IACvF,6GAA6G;IAC7G,IAAI,kBAAmB,KAAK,cAAc,IAAI,EAAE,QAAQ,GAAG;IAC3D,IAAI,qBAAmC,KAAK,YAAY,IAAI,WAAW;IAEvE,yEAAyE;IACzE,IAAI,KAAK,eAAe,CAAE,CAAA,eAAe,CAAA,GACvC,MAAM,IAAI,MAAM,yBAAyB,cAAc,gBAAgB,EAAE,QAAQ;IAGnF,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qBAAO,EAAqC;QAAC;QAAoB;KAAgB;IAEjH,kEAAkE;IAClE,IAAI,KAAM,CAAA,SAAS,CAAC,EAAE,KAAK,sBAAsB,SAAS,CAAC,EAAE,KAAK,eAAc,GAC9E,aAAa;QAAC;QAAoB;KAAgB;IAGpD,IAAI,CAAC,aACH,cAAc,IAAI,qBAAqB,SAAS,CAAC,EAAE;IAGrD,IAAI,WAAW,IAAI,kBAAkB,SAAS,CAAC,EAAE;IACjD,OAAO;QAAC;QAAa;KAAS;AAChC", "sources": ["packages/@react-stately/datepicker/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Calendar, DateFormatter, getLocalTimeZone, now, Time, toCalendar, toCalendarDate, toCalendarDateTime} from '@internationalized/date';\nimport {DatePickerProps, DateValue, Granularity, TimeValue} from '@react-types/datepicker';\n// @ts-ignore\nimport i18nMessages from '../intl/*.json';\nimport {LocalizedStringDictionary, LocalizedStringFormatter} from '@internationalized/string';\nimport {mergeValidation, VALID_VALIDITY_STATE} from '@react-stately/form';\nimport {RangeValue, ValidationResult} from '@react-types/shared';\nimport {useState} from 'react';\n\nconst dictionary = new LocalizedStringDictionary(i18nMessages);\n\nfunction getLocale() {\n  // Match browser language setting here, NOT react-aria's I18nProvider, so that we match other browser-provided\n  // validation messages, which to not respect our provider's language.\n  // @ts-ignore\n  return (typeof navigator !== 'undefined' && (navigator.language || navigator.userLanguage)) || 'en-US';\n}\n\nexport function getValidationResult(\n  value: DateValue | null,\n  minValue: DateValue | null | undefined,\n  maxValue: DateValue | null | undefined,\n  isDateUnavailable: ((v: DateValue) => boolean) | undefined,\n  options: FormatterOptions\n): ValidationResult {\n  let rangeOverflow = value != null && maxValue != null && value.compare(maxValue) > 0;\n  let rangeUnderflow = value != null && minValue != null && value.compare(minValue) < 0;\n  let isUnavailable = (value != null && isDateUnavailable?.(value)) || false;\n  let isInvalid = rangeOverflow || rangeUnderflow || isUnavailable;\n  let errors: string[] = [];\n\n  if (isInvalid) {\n    let locale = getLocale();\n    let strings = LocalizedStringDictionary.getGlobalDictionaryForPackage('@react-stately/datepicker') || dictionary;\n    let formatter = new LocalizedStringFormatter(locale, strings);\n    let dateFormatter = new DateFormatter(locale, getFormatOptions({}, options));\n    let timeZone = dateFormatter.resolvedOptions().timeZone;\n\n    if (rangeUnderflow && minValue != null) {\n      errors.push(formatter.format('rangeUnderflow', {minValue: dateFormatter.format(minValue.toDate(timeZone))}));\n    }\n\n    if (rangeOverflow && maxValue != null) {\n      errors.push(formatter.format('rangeOverflow', {maxValue: dateFormatter.format(maxValue.toDate(timeZone))}));\n    }\n\n    if (isUnavailable) {\n      errors.push(formatter.format('unavailableDate'));\n    }\n  }\n\n  return {\n    isInvalid,\n    validationErrors: errors,\n    validationDetails: {\n      badInput: isUnavailable,\n      customError: false,\n      patternMismatch: false,\n      rangeOverflow,\n      rangeUnderflow,\n      stepMismatch: false,\n      tooLong: false,\n      tooShort: false,\n      typeMismatch: false,\n      valueMissing: false,\n      valid: !isInvalid\n    }\n  };\n}\n\nexport function getRangeValidationResult(\n  value: RangeValue<DateValue | null> | null,\n  minValue: DateValue | null | undefined,\n  maxValue: DateValue | null | undefined,\n  isDateUnavailable: ((v: DateValue) => boolean) | undefined,\n  options: FormatterOptions\n): ValidationResult {\n  let startValidation = getValidationResult(\n    value?.start ?? null,\n    minValue,\n    maxValue,\n    isDateUnavailable,\n    options\n  );\n\n  let endValidation = getValidationResult(\n    value?.end ?? null,\n    minValue,\n    maxValue,\n    isDateUnavailable,\n    options\n  );\n\n  let result = mergeValidation(startValidation, endValidation);\n  if (value?.end != null && value.start != null && value.end.compare(value.start) < 0) {\n    let strings = LocalizedStringDictionary.getGlobalDictionaryForPackage('@react-stately/datepicker') || dictionary;\n    result = mergeValidation(result, {\n      isInvalid: true,\n      validationErrors: [strings.getStringForLocale('rangeReversed', getLocale())],\n      validationDetails: {\n        ...VALID_VALIDITY_STATE,\n        rangeUnderflow: true,\n        rangeOverflow: true,\n        valid: false\n      }\n    });\n  }\n\n  return result;\n}\n\nexport type FieldOptions = Pick<Intl.DateTimeFormatOptions, 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second'>;\nexport interface FormatterOptions {\n  timeZone?: string,\n  hideTimeZone?: boolean,\n  granularity?: DatePickerProps<any>['granularity'],\n  maxGranularity?: 'year' | 'month' | DatePickerProps<any>['granularity'],\n  hourCycle?: 12 | 24,\n  showEra?: boolean,\n  shouldForceLeadingZeros?: boolean\n}\n\nconst DEFAULT_FIELD_OPTIONS: FieldOptions = {\n  year: 'numeric',\n  month: 'numeric',\n  day: 'numeric',\n  hour: 'numeric',\n  minute: '2-digit',\n  second: '2-digit'\n};\n\nconst TWO_DIGIT_FIELD_OPTIONS: FieldOptions = {\n  year: 'numeric',\n  month: '2-digit',\n  day: '2-digit',\n  hour: '2-digit',\n  minute: '2-digit',\n  second: '2-digit'\n};\n\nexport function getFormatOptions(\n  fieldOptions: FieldOptions,\n  options: FormatterOptions\n): Intl.DateTimeFormatOptions {\n  let defaultFieldOptions = options.shouldForceLeadingZeros ? TWO_DIGIT_FIELD_OPTIONS : DEFAULT_FIELD_OPTIONS;\n  fieldOptions = {...defaultFieldOptions, ...fieldOptions};\n  let granularity = options.granularity || 'minute';\n  let keys = Object.keys(fieldOptions);\n  let startIdx = keys.indexOf(options.maxGranularity ?? 'year');\n  if (startIdx < 0) {\n    startIdx = 0;\n  }\n\n  let endIdx = keys.indexOf(granularity);\n  if (endIdx < 0) {\n    endIdx = 2;\n  }\n\n  if (startIdx > endIdx) {\n    throw new Error('maxGranularity must be greater than granularity');\n  }\n\n  let opts: Intl.DateTimeFormatOptions = keys.slice(startIdx, endIdx + 1).reduce((opts, key) => {\n    opts[key] = fieldOptions[key];\n    return opts;\n  }, {});\n\n  if (options.hourCycle != null) {\n    opts.hour12 = options.hourCycle === 12;\n  }\n\n  opts.timeZone = options.timeZone || 'UTC';\n\n  let hasTime = granularity === 'hour' || granularity === 'minute' || granularity === 'second';\n  if (hasTime && options.timeZone && !options.hideTimeZone) {\n    opts.timeZoneName = 'short';\n  }\n\n  if (options.showEra && startIdx === 0) {\n    opts.era = 'short';\n  }\n\n  return opts;\n}\n\nexport function getPlaceholderTime(placeholderValue: DateValue | null | undefined): TimeValue {\n  if (placeholderValue && 'hour' in placeholderValue) {\n    return placeholderValue;\n  }\n\n  return new Time();\n}\n\nexport function convertValue(value: DateValue | null | undefined, calendar: Calendar): DateValue | null | undefined {\n  if (value === null) {\n    return null;\n  }\n\n  if (!value) {\n    return undefined;\n  }\n\n  return toCalendar(value, calendar);\n}\n\n\nexport function createPlaceholderDate(placeholderValue: DateValue | null | undefined, granularity: string, calendar: Calendar, timeZone: string | undefined): DateValue {\n  if (placeholderValue) {\n    return convertValue(placeholderValue, calendar)!;\n  }\n\n  let date = toCalendar(now(timeZone ?? getLocalTimeZone()).set({\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  }), calendar);\n\n  if (granularity === 'year' || granularity === 'month' || granularity === 'day') {\n    return toCalendarDate(date);\n  }\n\n  if (!timeZone) {\n    return toCalendarDateTime(date);\n  }\n\n  return date;\n}\n\nexport function useDefaultProps(v: DateValue | null, granularity: Granularity | undefined): [Granularity, string | undefined] {\n  // Compute default granularity and time zone from the value. If the value becomes null, keep the last values.\n  let defaultTimeZone = (v && 'timeZone' in v ? v.timeZone : undefined);\n  let defaultGranularity: Granularity = (v && 'minute' in v ? 'minute' : 'day');\n\n  // props.granularity must actually exist in the value if one is provided.\n  if (v && granularity && !(granularity in v)) {\n    throw new Error('Invalid granularity ' + granularity + ' for value ' + v.toString());\n  }\n\n  let [lastValue, setLastValue] = useState<[Granularity, string | undefined]>([defaultGranularity, defaultTimeZone]);\n\n  // If the granularity or time zone changed, update the last value.\n  if (v && (lastValue[0] !== defaultGranularity || lastValue[1] !== defaultTimeZone)) {\n    setLastValue([defaultGranularity, defaultTimeZone]);\n  }\n\n  if (!granularity) {\n    granularity = v ? defaultGranularity : lastValue[0];\n  }\n\n  let timeZone = v ? defaultTimeZone : lastValue[1];\n  return [granularity, timeZone];\n}\n"], "names": [], "version": 3, "file": "utils.main.js.map"}
var $e046fdccd69bea8e$exports = {};
$e046fdccd69bea8e$exports = {
    "rangeOverflow": (args)=>`O valor tem de ser ${args.maxValue} ou anterior.`,
    "rangeReversed": `A data de in\xedcio deve ser anterior \xe0 data de fim.`,
    "rangeUnderflow": (args)=>`O valor tem de ser ${args.minValue} ou posterior.`,
    "unavailableDate": `Data selecionada indispon\xedvel.`
};


export {$e046fdccd69bea8e$exports as default};
//# sourceMappingURL=pt-PT.module.js.map

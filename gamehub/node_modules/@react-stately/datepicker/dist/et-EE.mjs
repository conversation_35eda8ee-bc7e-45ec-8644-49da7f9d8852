var $eeeaa8daee3601c7$exports = {};
$eeeaa8daee3601c7$exports = {
    "rangeOverflow": (args)=>`V\xe4\xe4rtus peab olema ${args.maxValue} v\xf5i varasem.`,
    "rangeReversed": `Alguskuup\xe4ev peab olema enne l\xf5ppkuup\xe4eva.`,
    "rangeUnderflow": (args)=>`V\xe4\xe4rtus peab olema ${args.minValue} v\xf5i hilisem.`,
    "unavailableDate": `Valitud kuup\xe4ev pole saadaval.`
};


export {$eeeaa8daee3601c7$exports as default};
//# sourceMappingURL=et-EE.module.js.map

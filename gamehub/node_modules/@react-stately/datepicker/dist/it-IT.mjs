var $4120bdb1d03484db$exports = {};
$4120bdb1d03484db$exports = {
    "rangeOverflow": (args)=>`Il valore deve essere ${args.maxValue} o precedente.`,
    "rangeReversed": `La data di inizio deve essere antecedente alla data di fine.`,
    "rangeUnderflow": (args)=>`Il valore deve essere ${args.minValue} o successivo.`,
    "unavailableDate": `Data selezionata non disponibile.`
};


export {$4120bdb1d03484db$exports as default};
//# sourceMappingURL=it-IT.module.js.map

var $929bc63f7705a78f$exports = {};
$929bc63f7705a78f$exports = {
    "rangeOverflow": (args)=>`La valeur doit \xeatre ${args.maxValue} ou ant\xe9rieure.`,
    "rangeReversed": `La date de d\xe9but doit \xeatre ant\xe9rieure \xe0 la date de fin.`,
    "rangeUnderflow": (args)=>`La valeur doit \xeatre ${args.minValue} ou ult\xe9rieure.`,
    "unavailableDate": `La date s\xe9lectionn\xe9e n\u{2019}est pas disponible.`
};


export {$929bc63f7705a78f$exports as default};
//# sourceMappingURL=fr-FR.module.js.map

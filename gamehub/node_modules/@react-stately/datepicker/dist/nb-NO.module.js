var $dd1a9a73340c793e$exports = {};
$dd1a9a73340c793e$exports = {
    "rangeOverflow": (args)=>`<PERSON>en m\xe5 v\xe6re ${args.maxValue} eller tidligere.`,
    "rangeReversed": `Startdatoen m\xe5 v\xe6re f\xf8r sluttdatoen.`,
    "rangeUnderflow": (args)=>`Verdien m\xe5 v\xe6re ${args.minValue} eller senere.`,
    "unavailableDate": `Valgt dato utilgjengelig.`
};


export {$dd1a9a73340c793e$exports as default};
//# sourceMappingURL=nb-NO.module.js.map

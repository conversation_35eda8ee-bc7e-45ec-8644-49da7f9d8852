var $6f5745e389c346d7$exports = {};
$6f5745e389c346d7$exports = {
    "rangeOverflow": (args)=>`Der Wert muss ${args.maxValue} oder fr\xfcher sein.`,
    "rangeReversed": `Das Anfangsdatum muss vor dem Enddatum liegen.`,
    "rangeUnderflow": (args)=>`Der Wert muss ${args.minValue} oder sp\xe4ter sein.`,
    "unavailableDate": `Das ausgew\xe4hlte Datum ist nicht verf\xfcgbar.`
};


export {$6f5745e389c346d7$exports as default};
//# sourceMappingURL=de-DE.module.js.map

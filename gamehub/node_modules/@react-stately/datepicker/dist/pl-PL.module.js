var $c85829aebb0bc94b$exports = {};
$c85829aebb0bc94b$exports = {
    "rangeOverflow": (args)=>`Warto\u{15B}\u{107} musi mie\u{107} warto\u{15B}\u{107} ${args.maxValue} lub wcze\u{15B}niejsz\u{105}.`,
    "rangeReversed": `Data rozpocz\u{119}cia musi by\u{107} wcze\u{15B}niejsza ni\u{17C} data zako\u{144}czenia.`,
    "rangeUnderflow": (args)=>`Warto\u{15B}\u{107} musi mie\u{107} warto\u{15B}\u{107} ${args.minValue} lub p\xf3\u{17A}niejsz\u{105}.`,
    "unavailableDate": `Wybrana data jest niedost\u{119}pna.`
};


export {$c85829aebb0bc94b$exports as default};
//# sourceMappingURL=pl-PL.module.js.map

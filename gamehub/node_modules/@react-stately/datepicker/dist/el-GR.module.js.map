{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,mIAAuB,EAAE,KAAK,QAAQ,CAAC,gFAAc,CAAC;IACpG,iBAAiB,CAAC,mZAAmE,CAAC;IACtF,kBAAkB,CAAC,OAAS,CAAC,mIAAuB,EAAE,KAAK,QAAQ,CAAC,qGAAiB,CAAC;IACtF,mBAAmB,CAAC,gRAA4C,CAAC;AACnE", "sources": ["packages/@react-stately/datepicker/intl/el-GR.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"Η τιμή πρέπει να είναι {maxValue} ή παλαιότερη.\",\n  \"rangeReversed\": \"Η ημερομηνία έναρξης πρέπει να είναι πριν από την ημερομηνία λήξης.\",\n  \"rangeUnderflow\": \"Η τιμή πρέπει να είναι {minValue} ή μεταγενέστερη.\",\n  \"unavailableDate\": \"Η επιλεγμένη ημερομηνία δεν είναι διαθέσιμη.\"\n}\n"], "names": [], "version": 3, "file": "el-GR.module.js.map"}
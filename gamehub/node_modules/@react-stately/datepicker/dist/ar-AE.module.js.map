{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,6GAAmB,EAAE,KAAK,QAAQ,CAAC,4DAAY,CAAC;IAC9F,iBAAiB,CAAC,6PAA2C,CAAC;IAC9D,kBAAkB,CAAC,OAAS,CAAC,6GAAmB,EAAE,KAAK,QAAQ,CAAC,4DAAY,CAAC;IAC7E,mBAAmB,CAAC,qKAA2B,CAAC;AAClD", "sources": ["packages/@react-stately/datepicker/intl/ar-AE.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"يجب أن تكون القيمة {maxValue} أو قبل ذلك.\",\n  \"rangeReversed\": \"تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء.\",\n  \"rangeUnderflow\": \"يجب أن تكون القيمة {minValue} أو بعد ذلك.\",\n  \"unavailableDate\": \"البيانات المحددة غير متاحة.\"\n}\n"], "names": [], "version": 3, "file": "ar-AE.module.js.map"}
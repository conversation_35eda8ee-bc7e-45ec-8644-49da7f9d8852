{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,uBAAoB,EAAE,KAAK,QAAQ,CAAC,kBAAe,CAAC;IAClG,iBAAiB,CAAC,mEAAuD,CAAC;IAC1E,kBAAkB,CAAC,OAAS,CAAC,uBAAoB,EAAE,KAAK,QAAQ,CAAC,kBAAe,CAAC;IACjF,mBAAmB,CAAC,uDAA0C,CAAC;AACjE", "sources": ["packages/@react-stately/datepicker/intl/fr-FR.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"La valeur doit être {maxValue} ou antérieure.\",\n  \"rangeReversed\": \"La date de début doit être antérieure à la date de fin.\",\n  \"rangeUnderflow\": \"La valeur doit être {minValue} ou ultérieure.\",\n  \"unavailableDate\": \"La date sélectionnée n’est pas disponible.\"\n}\n"], "names": [], "version": 3, "file": "fr-FR.main.js.map"}
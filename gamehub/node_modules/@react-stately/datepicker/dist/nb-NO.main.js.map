{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,sBAAgB,EAAE,KAAK,QAAQ,CAAC,iBAAiB,CAAC;IAChG,iBAAiB,CAAC,6CAAoC,CAAC;IACvD,kBAAkB,CAAC,OAAS,CAAC,sBAAgB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC5E,mBAAmB,CAAC,yBAAyB,CAAC;AAChD", "sources": ["packages/@react-stately/datepicker/intl/nb-NO.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON>en må være {maxValue} eller tidligere.\",\n  \"rangeReversed\": \"Startdatoen må være før sluttdatoen.\",\n  \"rangeUnderflow\": \"Verdien må være {minValue} eller senere.\",\n  \"unavailableDate\": \"Valgt dato utilgjengelig.\"\n}\n"], "names": [], "version": 3, "file": "nb-NO.main.js.map"}
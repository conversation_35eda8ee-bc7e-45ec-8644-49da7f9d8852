{"mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AA+DM,SAAS,0CAAoD,KAAgC;IAClG,IAAI,eAAe,CAAA,GAAA,kDAAqB,EAAE;IAC1C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,2CAAiB,EAA+C,MAAM,KAAK,EAAE,MAAM,YAAY,IAAI,MAAM,MAAM,QAAQ;IAE/I,IAAI,IAAK,SAAS,MAAM,gBAAgB,IAAI;IAC5C,IAAI,CAAC,aAAa,gBAAgB,GAAG,CAAA,GAAA,yCAAc,EAAE,GAAG,MAAM,WAAW;IACzE,IAAI,YAAY,SAAS,OAAO,MAAM,MAAM,CAAC,4BAAA,6BAAA,kBAAmB,SAAS;IACzE,IAAI,UAAU,gBAAgB,UAAU,gBAAgB,YAAY,gBAAgB;QAC1D;IAA1B,IAAI,sBAAsB,CAAA,6BAAA,MAAM,mBAAmB,cAAzB,wCAAA,6BAA6B;IAEvD,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qBAAO,EAAoB;IACjE,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qBAAO,EAAoB;IAEjE,IAAI,OAAO;QACT,eAAe;QACf,IAAI,UAAU,OACZ,eAAe;IAEnB;IAEA,yEAAyE;IACzE,IAAI,KAAK,CAAE,CAAA,eAAe,CAAA,GACxB,MAAM,IAAI,MAAM,yBAAyB,cAAc,gBAAgB,EAAE,QAAQ;IAGnF,IAAI,UAAU,CAAA,kBAAA,4BAAA,MAAO,QAAQ,CAAC,UAAU,MAAK,aAAa,MAAM,GAAG,KAAK;IACxE,IAAI,aAAa,CAAA,GAAA,oBAAM,EAAE,IAAO,CAAA;yBAC9B;YACA,UAAU;YACV,cAAc,MAAM,YAAY;YAChC,WAAW,MAAM,SAAS;YAC1B,yBAAyB,MAAM,uBAAuB;qBACtD;QACF,CAAA,GAAI;QAAC;QAAa,MAAM,SAAS;QAAE,MAAM,uBAAuB;QAAE;QAAiB,MAAM,YAAY;QAAE;KAAQ;IAE/G,IAAI,YAAC,QAAQ,YAAE,QAAQ,qBAAE,iBAAiB,EAAC,GAAG;IAC9C,IAAI,oBAAoB,CAAA,GAAA,oBAAM,EAAE,IAAM,CAAA,GAAA,6CAAkB,EACtD,OACA,UACA,UACA,mBACA,aACC;QAAC;QAAO;QAAU;QAAU;QAAmB;KAAW;IAE7D,IAAI,aAAa,CAAA,GAAA,8CAAqB,EAAE;QACtC,GAAG,KAAK;QACR,OAAO;2BACP;IACF;IAEA,IAAI,iBAAiB,WAAW,iBAAiB,CAAC,SAAS;IAC3D,IAAI,kBAA0C,MAAM,eAAe,IAAK,CAAA,iBAAiB,YAAY,IAAG;IAExG,IAAI,cAAc,CAAC,MAAiB;QAClC,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,CAAA,GAAA,2CAAa,EAAE,SAAS,CAAA,GAAA,+CAAiB,EAAE,MAAM;QACxF,gBAAgB;QAChB,gBAAgB;QAChB,WAAW,gBAAgB;IAC7B;IAEA,gGAAgG;IAChG,IAAI,aAAa,CAAC;QAChB,IAAI,cAAc,OAAO,wBAAwB,aAAa,wBAAwB;QACtF,IAAI;YACF,IAAI,gBAAgB,aAClB,YAAY,UAAU,gBAAgB,CAAA,GAAA,4CAAiB,EAAE,MAAM,YAAY,IAAI,MAAM,gBAAgB;iBAErG,gBAAgB;eAEb;YACL,SAAS;YACT,WAAW,gBAAgB;QAC7B;QAEA,IAAI,aACF,aAAa,OAAO,CAAC;IAEzB;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,gBAAgB,UAClB,YAAY,cAAc;aAE1B,gBAAgB;IAEpB;IAEA,OAAO;QACL,GAAG,UAAU;eACb;kBACA;QACA,WAAW;QACX,WAAW;QACX,cAAc;QACd,cAAc;qBACd;iBACA;QACA,GAAG,YAAY;QACf,SAAQ,MAAM;YACZ,kGAAkG;YAClG,6FAA6F;YAC7F,oCAAoC;YACpC,IAAI,CAAC,UAAU,CAAC,SAAS,gBAAgB,SACvC,YAAY,cAAc,gBAAgB,CAAA,GAAA,4CAAiB,EAAE,MAAM,YAAY,IAAI,MAAM,gBAAgB;YAG3G,aAAa,OAAO,CAAC;QACvB;yBACA;QACA,WAAW;QACX,aAAY,MAAM,EAAE,YAAY;YAC9B,IAAI,CAAC,WACH,OAAO;YAGT,IAAI,gBAAgB,CAAA,GAAA,0CAAe,EAAE,cAAc;YACnD,IAAI,YAAY,IAAI,CAAA,GAAA,0CAAY,EAAE,QAAQ;YAC1C,OAAO,UAAU,MAAM,CAAC;QAC1B;QACA,kBAAiB,MAAM,EAAE,aAA+B;YACtD,IAAI,aAAa;gBAAC,GAAG,UAAU;gBAAE,GAAG,aAAa;YAAA;YACjD,IAAI,mBAAmB,CAAA,GAAA,0CAAe,EAAE,CAAC,GAAG;YAC5C,OAAO,IAAI,CAAA,GAAA,0CAAY,EAAE,QAAQ;QACnC;IACF;AACF", "sources": ["packages/@react-stately/datepicker/src/useDatePickerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, DateFormatter, toCalendarDate, toCalendarDateTime} from '@internationalized/date';\nimport {DatePickerProps, DateValue, Granularity, MappedDateValue, TimeValue} from '@react-types/datepicker';\nimport {FieldOptions, FormatterOptions, getFormatOptions, getPlaceholderTime, getValidationResult, useDefaultProps} from './utils';\nimport {FormValidationState, useFormValidationState} from '@react-stately/form';\nimport {OverlayTriggerState, useOverlayTriggerState} from '@react-stately/overlays';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo, useState} from 'react';\nimport {ValidationState} from '@react-types/shared';\n\nexport interface DatePickerStateOptions<T extends DateValue> extends DatePickerProps<T> {\n  /**\n   * Determines whether the date picker popover should close automatically when a date is selected.\n   * @default true\n   */\n  shouldCloseOnSelect?: boolean | (() => boolean)\n}\n\nexport interface DatePickerState extends OverlayTriggerState, FormValidationState {\n  /** The currently selected date. */\n  value: DateValue | null,\n  /** Sets the selected date. */\n  setValue(value: DateValue | null): void,\n  /**\n   * The date portion of the value. This may be set prior to `value` if the user has\n   * selected a date but has not yet selected a time.\n   */\n  dateValue: DateValue | null,\n  /** Sets the date portion of the value. */\n  setDateValue(value: DateValue): void,\n  /**\n   * The time portion of the value. This may be set prior to `value` if the user has\n   * selected a time but has not yet selected a date.\n   */\n  timeValue: TimeValue | null,\n  /** Sets the time portion of the value. */\n  setTimeValue(value: TimeValue): void,\n  /** The granularity for the field, based on the `granularity` prop and current value. */\n  granularity: Granularity,\n  /** Whether the date picker supports selecting a time, according to the `granularity` prop and current value. */\n  hasTime: boolean,\n  /** Whether the calendar popover is currently open. */\n  isOpen: boolean,\n  /** Sets whether the calendar popover is open. */\n  setOpen(isOpen: boolean): void,\n  /**\n   * The current validation state of the date picker, based on the `validationState`, `minValue`, and `maxValue` props.\n   * @deprecated Use `isInvalid` instead.\n   */\n  validationState: ValidationState | null,\n  /** Whether the date picker is invalid, based on the `isInvalid`, `minValue`, and `maxValue` props. */\n  isInvalid: boolean,\n  /** Formats the selected value using the given options. */\n  formatValue(locale: string, fieldOptions: FieldOptions): string,\n  /** Gets a formatter based on state's props. */\n  getDateFormatter(locale: string, formatOptions: FormatterOptions): DateFormatter\n}\n\n/**\n * Provides state management for a date picker component.\n * A date picker combines a DateField and a Calendar popover to allow users to enter or select a date and time value.\n */\nexport function useDatePickerState<T extends DateValue = DateValue>(props: DatePickerStateOptions<T>): DatePickerState {\n  let overlayState = useOverlayTriggerState(props);\n  let [value, setValue] = useControlledState<DateValue | null, MappedDateValue<T> | null>(props.value, props.defaultValue || null, props.onChange);\n\n  let v = (value || props.placeholderValue || null);\n  let [granularity, defaultTimeZone] = useDefaultProps(v, props.granularity);\n  let dateValue = value != null ? value.toDate(defaultTimeZone ?? 'UTC') : null;\n  let hasTime = granularity === 'hour' || granularity === 'minute' || granularity === 'second';\n  let shouldCloseOnSelect = props.shouldCloseOnSelect ?? true;\n\n  let [selectedDate, setSelectedDate] = useState<DateValue | null>(null);\n  let [selectedTime, setSelectedTime] = useState<TimeValue | null>(null);\n\n  if (value) {\n    selectedDate = value;\n    if ('hour' in value) {\n      selectedTime = value;\n    }\n  }\n\n  // props.granularity must actually exist in the value if one is provided.\n  if (v && !(granularity in v)) {\n    throw new Error('Invalid granularity ' + granularity + ' for value ' + v.toString());\n  }\n\n  let showEra = value?.calendar.identifier === 'gregory' && value.era === 'BC';\n  let formatOpts = useMemo(() => ({\n    granularity,\n    timeZone: defaultTimeZone,\n    hideTimeZone: props.hideTimeZone,\n    hourCycle: props.hourCycle,\n    shouldForceLeadingZeros: props.shouldForceLeadingZeros,\n    showEra\n  }), [granularity, props.hourCycle, props.shouldForceLeadingZeros, defaultTimeZone, props.hideTimeZone, showEra]);\n\n  let {minValue, maxValue, isDateUnavailable} = props;\n  let builtinValidation = useMemo(() => getValidationResult(\n    value,\n    minValue,\n    maxValue,\n    isDateUnavailable,\n    formatOpts\n  ), [value, minValue, maxValue, isDateUnavailable, formatOpts]);\n\n  let validation = useFormValidationState({\n    ...props,\n    value: value as MappedDateValue<T> | null,\n    builtinValidation\n  });\n\n  let isValueInvalid = validation.displayValidation.isInvalid;\n  let validationState: ValidationState | null = props.validationState || (isValueInvalid ? 'invalid' : null);\n\n  let commitValue = (date: DateValue, time: TimeValue) => {\n    setValue('timeZone' in time ? time.set(toCalendarDate(date)) : toCalendarDateTime(date, time));\n    setSelectedDate(null);\n    setSelectedTime(null);\n    validation.commitValidation();\n  };\n\n  // Intercept setValue to make sure the Time section is not changed by date selection in Calendar\n  let selectDate = (newValue: CalendarDate) => {\n    let shouldClose = typeof shouldCloseOnSelect === 'function' ? shouldCloseOnSelect() : shouldCloseOnSelect;\n    if (hasTime) {\n      if (selectedTime || shouldClose) {\n        commitValue(newValue, selectedTime || getPlaceholderTime(props.defaultValue || props.placeholderValue));\n      } else {\n        setSelectedDate(newValue);\n      }\n    } else {\n      setValue(newValue);\n      validation.commitValidation();\n    }\n\n    if (shouldClose) {\n      overlayState.setOpen(false);\n    }\n  };\n\n  let selectTime = (newValue: TimeValue) => {\n    if (selectedDate && newValue) {\n      commitValue(selectedDate, newValue);\n    } else {\n      setSelectedTime(newValue);\n    }\n  };\n\n  return {\n    ...validation,\n    value,\n    setValue,\n    dateValue: selectedDate,\n    timeValue: selectedTime,\n    setDateValue: selectDate,\n    setTimeValue: selectTime,\n    granularity,\n    hasTime,\n    ...overlayState,\n    setOpen(isOpen) {\n      // Commit the selected date when the calendar is closed. Use a placeholder time if one wasn't set.\n      // If only the time was set and not the date, don't commit. The state will be preserved until\n      // the user opens the popover again.\n      if (!isOpen && !value && selectedDate && hasTime) {\n        commitValue(selectedDate, selectedTime || getPlaceholderTime(props.defaultValue || props.placeholderValue));\n      }\n\n      overlayState.setOpen(isOpen);\n    },\n    validationState,\n    isInvalid: isValueInvalid,\n    formatValue(locale, fieldOptions) {\n      if (!dateValue) {\n        return '';\n      }\n\n      let formatOptions = getFormatOptions(fieldOptions, formatOpts);\n      let formatter = new DateFormatter(locale, formatOptions);\n      return formatter.format(dateValue);\n    },\n    getDateFormatter(locale, formatOptions: FormatterOptions) {\n      let newOptions = {...formatOpts, ...formatOptions};\n      let newFormatOptions = getFormatOptions({}, newOptions);\n      return new DateFormatter(locale, newFormatOptions);\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useDatePickerState.main.js.map"}
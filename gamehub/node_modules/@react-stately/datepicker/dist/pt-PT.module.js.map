{"mappings": ";AAAA,4BAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,aAAa,CAAC;IAC/F,iBAAiB,CAAC,uDAAiD,CAAC;IACpE,kBAAkB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC/E,mBAAmB,CAAC,iCAA8B,CAAC;AACrD", "sources": ["packages/@react-stately/datepicker/intl/pt-PT.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"O valor tem de ser {maxValue} ou anterior.\",\n  \"rangeReversed\": \"A data de início deve ser anterior à data de fim.\",\n  \"rangeUnderflow\": \"O valor tem de ser {minValue} ou posterior.\",\n  \"unavailableDate\": \"Data selecionada indisponível.\"\n}\n"], "names": [], "version": 3, "file": "pt-PT.module.js.map"}
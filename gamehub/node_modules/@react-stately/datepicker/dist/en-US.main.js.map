{"mappings": "AAAA,iBAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,cAAc,EAAE,KAAK,QAAQ,CAAC,UAAU,CAAC;IACxF,iBAAiB,CAAC,OAAS,CAAC,cAAc,EAAE,KAAK,QAAQ,CAAC,YAAY,CAAC;IACvE,iBAAiB,CAAC,mCAAmC,CAAC;IACtD,mBAAmB,CAAC,0BAA0B,CAAC;AACjD", "sources": ["packages/@react-stately/datepicker/intl/en-US.json"], "sourcesContent": ["{\n  \"rangeUnderflow\": \"Value must be {minValue} or later.\",\n  \"rangeOverflow\": \"Value must be {maxValue} or earlier.\",\n  \"rangeReversed\": \"Start date must be before end date.\",\n  \"unavailableDate\": \"Selected date unavailable.\"\n}\n"], "names": [], "version": 3, "file": "en-US.main.js.map"}
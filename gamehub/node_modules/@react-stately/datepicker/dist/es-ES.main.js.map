{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,YAAY,CAAC;IAC7F,iBAAiB,CAAC,mEAAgE,CAAC;IACnF,kBAAkB,CAAC,OAAS,CAAC,kBAAkB,EAAE,KAAK,QAAQ,CAAC,aAAa,CAAC;IAC7E,mBAAmB,CAAC,iCAAiC,CAAC;AACxD", "sources": ["packages/@react-stately/datepicker/intl/es-ES.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"El valor debe ser {maxValue} o anterior.\",\n  \"rangeReversed\": \"La fecha de inicio debe ser anterior a la fecha de finalización.\",\n  \"rangeUnderflow\": \"El valor debe ser {minValue} o posterior.\",\n  \"unavailableDate\": \"Fecha seleccionada no disponible.\"\n}\n"], "names": [], "version": 3, "file": "es-ES.main.js.map"}
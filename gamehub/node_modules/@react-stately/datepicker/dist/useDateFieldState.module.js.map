{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AA0FD,MAAM,0CAAoB;IACxB,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,KAAK;AACP;AAEA,MAAM,kCAAY;IAChB,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEA,MAAM,qCAAe;IACnB,mDAAmD;IACnD,WAAW;IACX,iIAAiI;IACjI,aAAa;IACb,UAAU;IACV,SAAS;AACX;AAwBO,SAAS,0CAAmD,KAA+B;IAChG,IAAI,UACF,MAAM,kBACN,cAAc,gBACd,YAAY,cACZ,aAAa,mBACb,aAAa,mBACb,aAAa,iBACb,QAAQ,YACR,QAAQ,qBACR,iBAAiB,EAClB,GAAG;IAEJ,IAAI,IAAsB,MAAM,KAAK,IAAI,MAAM,YAAY,IAAI,MAAM,gBAAgB,IAAI;IACzF,IAAI,CAAC,aAAa,gBAAgB,GAAG,CAAA,GAAA,yCAAc,EAAE,GAAG,MAAM,WAAW;IACzE,IAAI,WAAW,mBAAmB;IAElC,yEAAyE;IACzE,IAAI,KAAK,CAAE,CAAA,eAAe,CAAA,GACxB,MAAM,IAAI,MAAM,yBAAyB,cAAc,gBAAgB,EAAE,QAAQ;IAGnF,IAAI,mBAAmB,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,oBAAY,EAAE,SAAS;QAAC;KAAO;IACxE,IAAI,WAAW,CAAA,GAAA,cAAM,EAAE,IAAM,eAAe,iBAAiB,eAAe,GAAG,QAAQ,GAAyB;QAAC;QAAgB;KAAiB;QAIhJ;IAFF,IAAI,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,yBAAiB,EACtC,MAAM,KAAK,EACX,CAAA,sBAAA,MAAM,YAAY,cAAlB,iCAAA,sBAAsB,MACtB,MAAM,QAAQ;IAGhB,IAAI,gBAAgB,CAAA,GAAA,cAAM,EAAE;YAAM;eAAA,CAAA,gBAAA,CAAA,GAAA,yCAAW,EAAE,OAAO,uBAApB,2BAAA,gBAAiC;OAAM;QAAC;QAAO;KAAS;IAE1F,2FAA2F;IAC3F,+FAA+F;IAC/F,kGAAkG;IAClG,6DAA6D;IAC7D,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,eAAO,EACjD,IAAM,CAAA,GAAA,yCAAoB,EAAE,MAAM,gBAAgB,EAAE,aAAa,UAAU;IAG7E,IAAI,MAAM,iBAAiB;IAC3B,IAAI,UAAU,SAAS,UAAU,KAAK,aAAa,IAAI,GAAG,KAAK;IAC/D,IAAI,aAAa,CAAA,GAAA,cAAM,EAAE;YAEP;eAFc;yBAC9B;YACA,gBAAgB,CAAA,wBAAA,MAAM,cAAc,cAApB,mCAAA,wBAAwB;YACxC,UAAU;0BACV;YACA,WAAW,MAAM,SAAS;qBAC1B;YACA,yBAAyB,MAAM,uBAAuB;QACxD;OAAI;QAAC,MAAM,cAAc;QAAE;QAAa,MAAM,SAAS;QAAE,MAAM,uBAAuB;QAAE;QAAiB;QAAc;KAAQ;IAC/H,IAAI,OAAO,CAAA,GAAA,cAAM,EAAE,IAAM,CAAA,GAAA,yCAAe,EAAE,CAAC,GAAG,aAAa;QAAC;KAAW;IAEvE,IAAI,gBAAgB,CAAA,GAAA,cAAM,EAAE,IAAM,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ,OAAO;QAAC;QAAQ;KAAK;IACjF,IAAI,kBAAkB,CAAA,GAAA,cAAM,EAAE,IAAM,cAAc,eAAe,IAAI;QAAC;KAAc;IAEpF,0EAA0E;IAC1E,wCAAwC;IACxC,IAAI,cAAiD,CAAA,GAAA,cAAM,EAAE,IAC3D,cAAc,aAAa,CAAC,IAAI,QAC7B,MAAM,CAAC,CAAA,MAAO,uCAAiB,CAAC,IAAI,IAAI,CAAC,EACzC,MAAM,CAAC,CAAC,GAAG,MAAS,CAAA,CAAC,CAAC,kCAAY,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,MAAM,CAAA,GAAI,CAAC,IAC1E;QAAC;KAAc;IAEjB,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,eAAO,EAC7C,IAAM,MAAM,KAAK,IAAI,MAAM,YAAY,GAAG;YAAC,GAAG,WAAW;QAAA,IAAI,CAAC;IAGhE,IAAI,iBAAiB,CAAA,GAAA,aAAK,EAAiB;IAE3C,0CAA0C;IAC1C,IAAI,eAAe,CAAA,GAAA,aAAK,EAAE;IAC1B,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,CAAC,CAAA,GAAA,sBAAc,EAAE,UAAU,aAAa,OAAO,GAAG;YACpD,aAAa,OAAO,GAAG;YACvB,mBAAmB,CAAA,cACjB,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,IAChC,CAAA,GAAA,iBAAS,EAAE,aAAa,YACxB,CAAA,GAAA,yCAAoB,EAAE,MAAM,gBAAgB,EAAE,aAAa,UAAU;QAE7E;IACF,GAAG;QAAC;QAAU;QAAa;QAAe;QAAiB,MAAM,gBAAgB;KAAC;IAElF,oGAAoG;IACpG,IAAI,SAAS,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,OAAO,IAAI,CAAC,aAAa,MAAM,EAAE;QAChF,gBAAgB;YAAC,GAAG,WAAW;QAAA;QAC/B,iBAAiB;IACnB;IAEA,iFAAiF;IACjF,IAAI,SAAS,QAAQ,OAAO,IAAI,CAAC,eAAe,MAAM,KAAK,OAAO,IAAI,CAAC,aAAa,MAAM,EAAE;QAC1F,gBAAgB,CAAC;QACjB,iBAAiB;QACjB,mBAAmB,CAAA,GAAA,yCAAoB,EAAE,MAAM,gBAAgB,EAAE,aAAa,UAAU;IAC1F;IAEA,0FAA0F;IAC1F,IAAI,eAAe,iBAAiB,OAAO,IAAI,CAAC,eAAe,MAAM,IAAI,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,gBAAgB;IAC3H,IAAI,WAAW,CAAC;QACd,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,EACtC;QAEF,IAAI,YAAY,OAAO,IAAI,CAAC;QAC5B,IAAI,UAAU,OAAO,IAAI,CAAC;QAE1B,0HAA0H;QAC1H,IAAI,YAAY,MAAM;YACpB,QAAQ;YACR,mBAAmB,CAAA,GAAA,yCAAoB,EAAE,MAAM,gBAAgB,EAAE,aAAa,UAAU;YACxF,iBAAiB,CAAC;QACpB,OAAO,IAAI,UAAU,MAAM,IAAI,QAAQ,MAAM,IAAK,UAAU,MAAM,KAAK,QAAQ,MAAM,GAAG,KAAK,YAAY,SAAS,IAAI,CAAC,cAAc,SAAS,IAAI,eAAe,OAAO,KAAK,aAAc;YACzL,wEAAwE;YACxE,sFAAsF;YACtF,WAAW,CAAA,GAAA,iBAAS,EAAE,UAAU,CAAA,cAAA,wBAAA,EAAG,QAAQ,KAAI,IAAI,CAAA,GAAA,wBAAgB;YACnE,QAAQ;QACV,OACE,mBAAmB;QAErB,eAAe,OAAO,GAAG;IAC3B;IAEA,IAAI,YAAY,CAAA,GAAA,cAAM,EAAE,IAAM,aAAa,MAAM,CAAC,WAAW;QAAC;QAAc;KAAS;IACrF,IAAI,WAAW,CAAA,GAAA,cAAM,EAAE,IACrB,sCAAgB,WAAW,eAAe,eAAe,iBAAiB,cAAc,UAAU,QAAQ,cAC1G;QAAC;QAAW;QAAe;QAAe;QAAiB;QAAc;QAAU;QAAQ;KAAY;IAEzG,gFAAgF;IAChF,kEAAkE;IAClE,IAAI,YAAY,GAAG,IAAI,cAAc,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE;QAC/D,cAAc,GAAG,GAAG;QACpB,iBAAiB;YAAC,GAAG,aAAa;QAAA;IACpC,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,cAAc,GAAG,EAAE;QAChD,OAAO,cAAc,GAAG;QACxB,iBAAiB;YAAC,GAAG,aAAa;QAAA;IACpC;IAEA,IAAI,YAAY,CAAC;QACf,aAAa,CAAC,KAAK,GAAG;QACtB,IAAI,SAAS,UAAU,YAAY,GAAG,EACpC,cAAc,GAAG,GAAG;QAEtB,iBAAiB;YAAC,GAAG,aAAa;QAAA;IACpC;IAEA,IAAI,gBAAgB,CAAC,MAAoC;QACvD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;YACxB,UAAU;YACV,IAAI,YAAY,OAAO,IAAI,CAAC;YAC5B,IAAI,UAAU,OAAO,IAAI,CAAC;YAC1B,IAAI,UAAU,MAAM,IAAI,QAAQ,MAAM,IAAK,UAAU,MAAM,KAAK,QAAQ,MAAM,GAAG,KAAK,YAAY,SAAS,IAAI,CAAC,cAAc,SAAS,EACrI,SAAS;QAEb,OACE,SAAS,iCAAW,cAAc,MAAM,QAAQ;IAEpD;IAEA,IAAI,oBAAoB,CAAA,GAAA,cAAM,EAAE,IAAM,CAAA,GAAA,yCAAkB,EACtD,OACA,UACA,UACA,mBACA,aACC;QAAC;QAAO;QAAU;QAAU;QAAmB;KAAW;IAE7D,IAAI,aAAa,CAAA,GAAA,6BAAqB,EAAE;QACtC,GAAG,KAAK;QACR,OAAO;2BACP;IACF;IAEA,IAAI,iBAAiB,WAAW,iBAAiB,CAAC,SAAS;IAC3D,IAAI,kBAA0C,MAAM,eAAe,IAAK,CAAA,iBAAiB,YAAY,IAAG;QAatF;IAXlB,OAAO;QACL,GAAG,UAAU;QACb,OAAO;mBACP;kBACA;kBACA;kBACA;uBACA;yBACA;QACA,WAAW;qBACX;QACA,gBAAgB,CAAA,wBAAA,MAAM,cAAc,cAApB,mCAAA,wBAAwB;oBACxC;oBACA;oBACA;QACA,WAAU,IAAI;YACZ,cAAc,MAAM;QACtB;QACA,WAAU,IAAI;YACZ,cAAc,MAAM;QACtB;QACA,eAAc,IAAI;YAChB,cAAc,MAAM,+BAAS,CAAC,KAAK,IAAI;QACzC;QACA,eAAc,IAAI;YAChB,cAAc,MAAM,CAAE,CAAA,+BAAS,CAAC,KAAK,IAAI,CAAA;QAC3C;QACA,YAAW,IAAI,EAAE,CAAkB;YACjC,UAAU;YACV,SAAS,iCAAW,cAAc,MAAM,GAAG;QAC7C;QACA;YACE,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,EACtC;YAGF,mEAAmE;YACnE,IAAI,YAAY,OAAO,IAAI,CAAC;YAC5B,IAAI,UAAU,OAAO,IAAI,CAAC;YAC1B,IAAI,UAAU,MAAM,KAAK,QAAQ,MAAM,GAAG,KAAK,YAAY,SAAS,IAAI,CAAC,cAAc,SAAS,EAAE;gBAChG,gBAAgB;oBAAC,GAAG,WAAW;gBAAA;gBAC/B,iBAAiB;gBACjB,SAAS,aAAa,IAAI;YAC5B;QACF;QACA,cAAa,IAAI;YACf,OAAO,aAAa,CAAC,KAAK;YAC1B,eAAe,OAAO,GAAG;YACzB,iBAAiB;gBAAC,GAAG,aAAa;YAAA;YAElC,IAAI,cAAc,CAAA,GAAA,yCAAoB,EAAE,MAAM,gBAAgB,EAAE,aAAa,UAAU;YACvF,IAAI,QAAQ;YAEZ,yDAAyD;YACzD,IAAI,SAAS,eAAe,UAAU,gBAAgB,UAAU,aAAa;gBAC3E,IAAI,OAAO,aAAa,IAAI,IAAI;gBAChC,IAAI,aAAa,YAAY,IAAI,IAAI;gBACrC,IAAI,QAAQ,CAAC,YACX,QAAQ,aAAa,GAAG,CAAC;oBAAC,MAAM,aAAa,IAAI,GAAG;gBAAE;qBACjD,IAAI,CAAC,QAAQ,YAClB,QAAQ,aAAa,GAAG,CAAC;oBAAC,MAAM,aAAa,IAAI,GAAG;gBAAE;YAE1D,OAAO,IAAI,SAAS,UAAU,UAAU,gBAAgB,aAAa,IAAI,IAAI,MAAM,cAAc,SAAS,EACxG,QAAQ,aAAa,GAAG,CAAC;gBAAC,MAAM,WAAW,CAAC,OAAO,GAAG;YAAE;iBACnD,IAAI,QAAQ,cACjB,QAAQ,aAAa,GAAG,CAAC;gBAAC,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK;YAAA;YAGrD,QAAQ;YACR,SAAS;QACX;QACA,aAAY,YAA0B;YACpC,IAAI,CAAC,eACH,OAAO;YAGT,IAAI,gBAAgB,CAAA,GAAA,yCAAe,EAAE,cAAc;YACnD,IAAI,YAAY,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ;YAC1C,OAAO,UAAU,MAAM,CAAC;QAC1B;QACA,kBAAiB,MAAM,EAAE,aAA+B;YACtD,IAAI,aAAa;gBAAC,GAAG,UAAU;gBAAE,GAAG,aAAa;YAAA;YACjD,IAAI,mBAAmB,CAAA,GAAA,yCAAe,EAAE,CAAC,GAAG;YAC5C,OAAO,IAAI,CAAA,GAAA,oBAAY,EAAE,QAAQ;QACnC;IACF;AACF;AAEA,SAAS,sCAAgB,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW;IAC5H,IAAI,YAAY;QAAC;QAAQ;QAAU;KAAS;IAC5C,IAAI,WAAW,cAAc,aAAa,CAAC;IAC3C,IAAI,oBAAmC,EAAE;IACzC,KAAK,IAAI,WAAW,SAAU;QAC5B,IAAI,OAAO,kCAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI;QACrD,IAAI,aAAa,uCAAiB,CAAC,KAAK;QACxC,IAAI,SAAS,SAAS,SAAS,OAAO,GAAG,MAAM,KAAK,GAClD,aAAa;QAGf,IAAI,gBAAgB,uCAAiB,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK;QACnE,IAAI,cAAc,uCAAiB,CAAC,KAAK,GAAG,CAAA,GAAA,yCAAa,EAAE,MAAM,QAAQ,KAAK,EAAE,UAAU;QAE1F,IAAI,cAAc;kBAChB;YACA,MAAM,gBAAgB,cAAc,QAAQ,KAAK;YACjD,GAAG,uCAAiB,cAAc,MAAM,gBAAgB;2BACxD;yBACA;wBACA;QACF;QAEA,sGAAsG;QACtG,gMAAgM;QAChM,2JAA2J;QAC3J,IAAI,SAAS,QAAQ;YACnB,0DAA0D;YAC1D,kBAAkB,IAAI,CAAC;gBACrB,MAAM;gBACN,MAAM;gBACN,GAAG,uCAAiB,cAAc,WAAW,gBAAgB;gBAC7D,eAAe;gBACf,aAAa;gBACb,YAAY;YACd;YACA,kBAAkB,IAAI,CAAC;YACvB,yGAAyG;YACzG,IAAI,SAAS,aACX,kBAAkB,IAAI,CAAC;gBACrB,MAAM;gBACN,MAAM;gBACN,GAAG,uCAAiB,cAAc,WAAW,gBAAgB;gBAC7D,eAAe;gBACf,aAAa;gBACb,YAAY;YACd;QAEJ,OAAO,IAAI,UAAU,QAAQ,CAAC,SAAS,SAAS,aAAa;YAC3D,kBAAkB,IAAI,CAAC;YACvB,uDAAuD;YACvD,kBAAkB,IAAI,CAAC;gBACrB,MAAM;gBACN,MAAM;gBACN,GAAG,uCAAiB,cAAc,WAAW,gBAAgB;gBAC7D,eAAe;gBACf,aAAa;gBACb,YAAY;YACd;QACF,OACE,gIAAgI;QAChI,kBAAkB,IAAI,CAAC;IAE3B;IAEA,OAAO;AACT;AAEA,SAAS,uCAAiB,IAAe,EAAE,IAAY,EAAE,OAA2C;IAClG,OAAQ;QACN,KAAK;YAAO;gBACV,IAAI,OAAO,KAAK,QAAQ,CAAC,OAAO;gBAChC,OAAO;oBACL,OAAO,KAAK,OAAO,CAAC,KAAK,GAAG;oBAC5B,UAAU;oBACV,UAAU,KAAK,MAAM,GAAG;gBAC1B;YACF;QACA,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,IAAI;gBAChB,UAAU;gBACV,UAAU,KAAK,QAAQ,CAAC,aAAa,CAAC;YACxC;QACF,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,UAAU,CAAA,GAAA,4BAAoB,EAAE;gBAChC,UAAU,KAAK,QAAQ,CAAC,eAAe,CAAC;YAC1C;QACF,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,GAAG;gBACf,UAAU,CAAA,GAAA,2BAAmB,EAAE;gBAC/B,UAAU,KAAK,QAAQ,CAAC,cAAc,CAAC;YACzC;IACJ;IAEA,IAAI,UAAU,MACZ,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;gBAC9B,UAAU;gBACV,UAAU;YACZ;QACF,KAAK;YACH,IAAI,QAAQ,MAAM,EAAE;gBAClB,IAAI,OAAO,KAAK,IAAI,IAAI;gBACxB,OAAO;oBACL,OAAO,KAAK,IAAI;oBAChB,UAAU,OAAO,KAAK;oBACtB,UAAU,OAAO,KAAK;gBACxB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,IAAI;gBAChB,UAAU;gBACV,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,MAAM;gBAClB,UAAU;gBACV,UAAU;YACZ;QACF,KAAK;YACH,OAAO;gBACL,OAAO,KAAK,MAAM;gBAClB,UAAU;gBACV,UAAU;YACZ;IACJ;IAGF,OAAO,CAAC;AACV;AAEA,SAAS,iCAAW,KAAgB,EAAE,IAAY,EAAE,MAAc,EAAE,OAA2C;IAC7G,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,MAAM,QAAQ;gBAAC,OAAO,SAAS;YAAM;IAC5D;IAEA,IAAI,UAAU,OACZ,OAAQ;QACN,KAAK;YAAa;gBAChB,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,OAAO,SAAS;gBACpB,OAAO,MAAM,GAAG,CAAC;oBAAC,MAAM,OAAO,QAAQ,KAAK,QAAQ;gBAAE;YACxD;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,MAAM,QAAQ;gBAC/B,OAAO,SAAS;gBAChB,WAAW,QAAQ,MAAM,GAAG,KAAK;YACnC;IACJ;IAGF,MAAM,IAAI,MAAM,sBAAsB;AACxC;AAEA,SAAS,iCAAW,KAAgB,EAAE,IAAY,EAAE,YAA6B,EAAE,OAA2C;IAC5H,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,MAAM,GAAG,CAAC;gBAAC,CAAC,KAAK,EAAE;YAAY;IAC1C;IAEA,IAAI,UAAU,SAAS,OAAO,iBAAiB,UAC7C,OAAQ;QACN,KAAK;YAAa;gBAChB,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,QAAQ,SAAS;gBACrB,IAAI,OAAO,gBAAgB;gBAC3B,IAAI,SAAS,OACX,OAAO;gBAET,OAAO,MAAM,GAAG,CAAC;oBAAC,MAAM,QAAQ,QAAQ,KAAK,QAAQ;gBAAE;YACzD;QACA,KAAK;YACH,qDAAqD;YACrD,IAAI,QAAQ,MAAM,EAAE;gBAClB,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,QAAQ,SAAS;gBACrB,IAAI,CAAC,SAAS,iBAAiB,IAC7B,eAAe;gBAEjB,IAAI,SAAS,eAAe,IAC1B,gBAAgB;YAEpB;QACA,cAAc;QAChB,KAAK;QACL,KAAK;YACH,OAAO,MAAM,GAAG,CAAC;gBAAC,CAAC,KAAK,EAAE;YAAY;IAC1C;IAGF,MAAM,IAAI,MAAM,sBAAsB;AACxC", "sources": ["packages/@react-stately/datepicker/src/useDateFieldState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Calendar, CalendarIdentifier, DateFormatter, getMinimumDayInMonth, getMinimumMonthInYear, GregorianCalendar, isEqualCalendar, toCalendar} from '@internationalized/date';\nimport {convertValue, createPlaceholderDate, FieldOptions, FormatterOptions, getFormatOptions, getValidationResult, useDefaultProps} from './utils';\nimport {DatePickerProps, DateValue, Granularity, MappedDateValue} from '@react-types/datepicker';\nimport {FormValidationState, useFormValidationState} from '@react-stately/form';\nimport {getPlaceholder} from './placeholders';\nimport {useControlledState} from '@react-stately/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\nimport {ValidationState} from '@react-types/shared';\n\nexport type SegmentType = 'era' | 'year' | 'month' | 'day' |  'hour' | 'minute' | 'second' | 'dayPeriod' | 'literal' | 'timeZoneName';\nexport interface DateSegment {\n  /** The type of segment. */\n  type: SegmentType,\n  /** The formatted text for the segment. */\n  text: string,\n  /** The numeric value for the segment, if applicable. */\n  value?: number,\n  /** The minimum numeric value for the segment, if applicable. */\n  minValue?: number,\n  /** The maximum numeric value for the segment, if applicable. */\n  maxValue?: number,\n  /** Whether the value is a placeholder. */\n  isPlaceholder: boolean,\n  /** A placeholder string for the segment. */\n  placeholder: string,\n  /** Whether the segment is editable. */\n  isEditable: boolean\n}\n\nexport interface DateFieldState extends FormValidationState {\n  /** The current field value. */\n  value: DateValue | null,\n  /** The current value, converted to a native JavaScript `Date` object.  */\n  dateValue: Date,\n  /** The calendar system currently in use. */\n  calendar: Calendar,\n  /** Sets the field's value. */\n  setValue(value: DateValue | null): void,\n  /** A list of segments for the current value. */\n  segments: DateSegment[],\n  /** A date formatter configured for the current locale and format. */\n  dateFormatter: DateFormatter,\n  /**\n   * The current validation state of the date field, based on the `validationState`, `minValue`, and `maxValue` props.\n   * @deprecated Use `isInvalid` instead.\n   */\n  validationState: ValidationState | null,\n  /** Whether the date field is invalid, based on the `isInvalid`, `minValue`, and `maxValue` props. */\n  isInvalid: boolean,\n  /** The granularity for the field, based on the `granularity` prop and current value. */\n  granularity: Granularity,\n  /** The maximum date or time unit that is displayed in the field. */\n  maxGranularity: 'year' | 'month' | Granularity,\n  /** Whether the field is disabled. */\n  isDisabled: boolean,\n  /** Whether the field is read only. */\n  isReadOnly: boolean,\n  /** Whether the field is required. */\n  isRequired: boolean,\n  /** Increments the given segment. Upon reaching the minimum or maximum value, the value wraps around to the opposite limit. */\n  increment(type: SegmentType): void,\n  /** Decrements the given segment. Upon reaching the minimum or maximum value, the value wraps around to the opposite limit. */\n  decrement(type: SegmentType): void,\n  /**\n   * Increments the given segment by a larger amount, rounding it to the nearest increment.\n   * The amount to increment by depends on the field, for example 15 minutes, 7 days, and 5 years.\n   * Upon reaching the minimum or maximum value, the value wraps around to the opposite limit.\n   */\n  incrementPage(type: SegmentType): void,\n  /**\n   * Decrements the given segment by a larger amount, rounding it to the nearest increment.\n   * The amount to decrement by depends on the field, for example 15 minutes, 7 days, and 5 years.\n   * Upon reaching the minimum or maximum value, the value wraps around to the opposite limit.\n   */\n  decrementPage(type: SegmentType): void,\n  /** Sets the value of the given segment. */\n  setSegment(type: 'era', value: string): void,\n  setSegment(type: SegmentType, value: number): void,\n  /** Updates the remaining unfilled segments with the placeholder value. */\n  confirmPlaceholder(): void,\n  /** Clears the value of the given segment, reverting it to the placeholder. */\n  clearSegment(type: SegmentType): void,\n  /** Formats the current date value using the given options. */\n  formatValue(fieldOptions: FieldOptions): string,\n  /** Gets a formatter based on state's props. */\n  getDateFormatter(locale: string, formatOptions: FormatterOptions): DateFormatter\n}\n\nconst EDITABLE_SEGMENTS = {\n  year: true,\n  month: true,\n  day: true,\n  hour: true,\n  minute: true,\n  second: true,\n  dayPeriod: true,\n  era: true\n};\n\nconst PAGE_STEP = {\n  year: 5,\n  month: 2,\n  day: 7,\n  hour: 2,\n  minute: 15,\n  second: 15\n};\n\nconst TYPE_MAPPING = {\n  // Node seems to convert everything to lowercase...\n  dayperiod: 'dayPeriod',\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts#named_years\n  relatedYear: 'year',\n  yearName: 'literal', // not editable\n  unknown: 'literal'\n};\n\nexport interface DateFieldStateOptions<T extends DateValue = DateValue> extends DatePickerProps<T> {\n  /**\n   * The maximum unit to display in the date field.\n   * @default 'year'\n   */\n  maxGranularity?: 'year' | 'month' | Granularity,\n  /** The locale to display and edit the value according to. */\n  locale: string,\n  /**\n   * A function that creates a [Calendar](../internationalized/date/Calendar.html)\n   * object for a given calendar identifier. Such a function may be imported from the\n   * `@internationalized/date` package, or manually implemented to include support for\n   * only certain calendars.\n   */\n  createCalendar: (name: CalendarIdentifier) => Calendar\n}\n\n/**\n * Provides state management for a date field component.\n * A date field allows users to enter and edit date and time values using a keyboard.\n * Each part of a date value is displayed in an individually editable segment.\n */\nexport function useDateFieldState<T extends DateValue = DateValue>(props: DateFieldStateOptions<T>): DateFieldState {\n  let {\n    locale,\n    createCalendar,\n    hideTimeZone,\n    isDisabled = false,\n    isReadOnly = false,\n    isRequired = false,\n    minValue,\n    maxValue,\n    isDateUnavailable\n  } = props;\n\n  let v: DateValue | null = props.value || props.defaultValue || props.placeholderValue || null;\n  let [granularity, defaultTimeZone] = useDefaultProps(v, props.granularity);\n  let timeZone = defaultTimeZone || 'UTC';\n\n  // props.granularity must actually exist in the value if one is provided.\n  if (v && !(granularity in v)) {\n    throw new Error('Invalid granularity ' + granularity + ' for value ' + v.toString());\n  }\n\n  let defaultFormatter = useMemo(() => new DateFormatter(locale), [locale]);\n  let calendar = useMemo(() => createCalendar(defaultFormatter.resolvedOptions().calendar as CalendarIdentifier), [createCalendar, defaultFormatter]);\n\n  let [value, setDate] = useControlledState<DateValue | null, MappedDateValue<T> | null>(\n    props.value,\n    props.defaultValue ?? null,\n    props.onChange\n  );\n\n  let calendarValue = useMemo(() => convertValue(value, calendar) ?? null, [value, calendar]);\n\n  // We keep track of the placeholder date separately in state so that onChange is not called\n  // until all segments are set. If the value === null (not undefined), then assume the component\n  // is controlled, so use the placeholder as the value until all segments are entered so it doesn't\n  // change from uncontrolled to controlled and emit a warning.\n  let [placeholderDate, setPlaceholderDate] = useState(\n    () => createPlaceholderDate(props.placeholderValue, granularity, calendar, defaultTimeZone)\n  );\n\n  let val = calendarValue || placeholderDate;\n  let showEra = calendar.identifier === 'gregory' && val.era === 'BC';\n  let formatOpts = useMemo(() => ({\n    granularity,\n    maxGranularity: props.maxGranularity ?? 'year',\n    timeZone: defaultTimeZone,\n    hideTimeZone,\n    hourCycle: props.hourCycle,\n    showEra,\n    shouldForceLeadingZeros: props.shouldForceLeadingZeros\n  }), [props.maxGranularity, granularity, props.hourCycle, props.shouldForceLeadingZeros, defaultTimeZone, hideTimeZone, showEra]);\n  let opts = useMemo(() => getFormatOptions({}, formatOpts), [formatOpts]);\n\n  let dateFormatter = useMemo(() => new DateFormatter(locale, opts), [locale, opts]);\n  let resolvedOptions = useMemo(() => dateFormatter.resolvedOptions(), [dateFormatter]);\n\n  // Determine how many editable segments there are for validation purposes.\n  // The result is cached for performance.\n  let allSegments: Partial<typeof EDITABLE_SEGMENTS> = useMemo(() =>\n    dateFormatter.formatToParts(new Date())\n      .filter(seg => EDITABLE_SEGMENTS[seg.type])\n      .reduce((p, seg) => (p[TYPE_MAPPING[seg.type] || seg.type] = true, p), {})\n  , [dateFormatter]);\n\n  let [validSegments, setValidSegments] = useState<Partial<typeof EDITABLE_SEGMENTS>>(\n    () => props.value || props.defaultValue ? {...allSegments} : {}\n  );\n\n  let clearedSegment = useRef<string | null>(null);\n\n  // Reset placeholder when calendar changes\n  let lastCalendar = useRef(calendar);\n  useEffect(() => {\n    if (!isEqualCalendar(calendar, lastCalendar.current)) {\n      lastCalendar.current = calendar;\n      setPlaceholderDate(placeholder =>\n        Object.keys(validSegments).length > 0\n          ? toCalendar(placeholder, calendar)\n          : createPlaceholderDate(props.placeholderValue, granularity, calendar, defaultTimeZone)\n      );\n    }\n  }, [calendar, granularity, validSegments, defaultTimeZone, props.placeholderValue]);\n\n  // If there is a value prop, and some segments were previously placeholders, mark them all as valid.\n  if (value && Object.keys(validSegments).length < Object.keys(allSegments).length) {\n    validSegments = {...allSegments};\n    setValidSegments(validSegments);\n  }\n\n  // If the value is set to null and all segments are valid, reset the placeholder.\n  if (value == null && Object.keys(validSegments).length === Object.keys(allSegments).length) {\n    validSegments = {};\n    setValidSegments(validSegments);\n    setPlaceholderDate(createPlaceholderDate(props.placeholderValue, granularity, calendar, defaultTimeZone));\n  }\n\n  // If all segments are valid, use the date from state, otherwise use the placeholder date.\n  let displayValue = calendarValue && Object.keys(validSegments).length >= Object.keys(allSegments).length ? calendarValue : placeholderDate;\n  let setValue = (newValue: DateValue) => {\n    if (props.isDisabled || props.isReadOnly) {\n      return;\n    }\n    let validKeys = Object.keys(validSegments);\n    let allKeys = Object.keys(allSegments);\n\n    // if all the segments are completed or a timefield with everything but am/pm set the time, also ignore when am/pm cleared\n    if (newValue == null) {\n      setDate(null);\n      setPlaceholderDate(createPlaceholderDate(props.placeholderValue, granularity, calendar, defaultTimeZone));\n      setValidSegments({});\n    } else if (validKeys.length >= allKeys.length || (validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod && clearedSegment.current !== 'dayPeriod')) {\n      // The display calendar should not have any effect on the emitted value.\n      // Emit dates in the same calendar as the original value, if any, otherwise gregorian.\n      newValue = toCalendar(newValue, v?.calendar || new GregorianCalendar());\n      setDate(newValue);\n    } else {\n      setPlaceholderDate(newValue);\n    }\n    clearedSegment.current = null;\n  };\n\n  let dateValue = useMemo(() => displayValue.toDate(timeZone), [displayValue, timeZone]);\n  let segments = useMemo(() => \n    processSegments(dateValue, validSegments, dateFormatter, resolvedOptions, displayValue, calendar, locale, granularity), \n    [dateValue, validSegments, dateFormatter, resolvedOptions, displayValue, calendar, locale, granularity]);\n\n  // When the era field appears, mark it valid if the year field is already valid.\n  // If the era field disappears, remove it from the valid segments.\n  if (allSegments.era && validSegments.year && !validSegments.era) {\n    validSegments.era = true;\n    setValidSegments({...validSegments});\n  } else if (!allSegments.era && validSegments.era) {\n    delete validSegments.era;\n    setValidSegments({...validSegments});\n  }\n\n  let markValid = (part: Intl.DateTimeFormatPartTypes) => {\n    validSegments[part] = true;\n    if (part === 'year' && allSegments.era) {\n      validSegments.era = true;\n    }\n    setValidSegments({...validSegments});\n  };\n\n  let adjustSegment = (type: Intl.DateTimeFormatPartTypes, amount: number) => {\n    if (!validSegments[type]) {\n      markValid(type);\n      let validKeys = Object.keys(validSegments);\n      let allKeys = Object.keys(allSegments);\n      if (validKeys.length >= allKeys.length || (validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod)) {\n        setValue(displayValue);\n      }\n    } else {\n      setValue(addSegment(displayValue, type, amount, resolvedOptions));\n    }\n  };\n\n  let builtinValidation = useMemo(() => getValidationResult(\n    value,\n    minValue,\n    maxValue,\n    isDateUnavailable,\n    formatOpts\n  ), [value, minValue, maxValue, isDateUnavailable, formatOpts]);\n\n  let validation = useFormValidationState({\n    ...props,\n    value: value as MappedDateValue<T> | null,\n    builtinValidation\n  });\n\n  let isValueInvalid = validation.displayValidation.isInvalid;\n  let validationState: ValidationState | null = props.validationState || (isValueInvalid ? 'invalid' : null);\n\n  return {\n    ...validation,\n    value: calendarValue,\n    dateValue,\n    calendar,\n    setValue,\n    segments,\n    dateFormatter,\n    validationState,\n    isInvalid: isValueInvalid,\n    granularity,\n    maxGranularity: props.maxGranularity ?? 'year',\n    isDisabled,\n    isReadOnly,\n    isRequired,\n    increment(part) {\n      adjustSegment(part, 1);\n    },\n    decrement(part) {\n      adjustSegment(part, -1);\n    },\n    incrementPage(part) {\n      adjustSegment(part, PAGE_STEP[part] || 1);\n    },\n    decrementPage(part) {\n      adjustSegment(part, -(PAGE_STEP[part] || 1));\n    },\n    setSegment(part, v: string | number) {\n      markValid(part);\n      setValue(setSegment(displayValue, part, v, resolvedOptions));\n    },\n    confirmPlaceholder() {\n      if (props.isDisabled || props.isReadOnly) {\n        return;\n      }\n\n      // Confirm the placeholder if only the day period is not filled in.\n      let validKeys = Object.keys(validSegments);\n      let allKeys = Object.keys(allSegments);\n      if (validKeys.length === allKeys.length - 1 && allSegments.dayPeriod && !validSegments.dayPeriod) {\n        validSegments = {...allSegments};\n        setValidSegments(validSegments);\n        setValue(displayValue.copy());\n      }\n    },\n    clearSegment(part) {\n      delete validSegments[part];\n      clearedSegment.current = part;\n      setValidSegments({...validSegments});\n\n      let placeholder = createPlaceholderDate(props.placeholderValue, granularity, calendar, defaultTimeZone);\n      let value = displayValue;\n\n      // Reset day period to default without changing the hour.\n      if (part === 'dayPeriod' && 'hour' in displayValue && 'hour' in placeholder) {\n        let isPM = displayValue.hour >= 12;\n        let shouldBePM = placeholder.hour >= 12;\n        if (isPM && !shouldBePM) {\n          value = displayValue.set({hour: displayValue.hour - 12});\n        } else if (!isPM && shouldBePM) {\n          value = displayValue.set({hour: displayValue.hour + 12});\n        }\n      } else if (part === 'hour' && 'hour' in displayValue && displayValue.hour >= 12 && validSegments.dayPeriod) {\n        value = displayValue.set({hour: placeholder['hour'] + 12});\n      } else if (part in displayValue) {\n        value = displayValue.set({[part]: placeholder[part]});\n      }\n\n      setDate(null);\n      setValue(value);\n    },\n    formatValue(fieldOptions: FieldOptions) {\n      if (!calendarValue) {\n        return '';\n      }\n\n      let formatOptions = getFormatOptions(fieldOptions, formatOpts);\n      let formatter = new DateFormatter(locale, formatOptions);\n      return formatter.format(dateValue);\n    },\n    getDateFormatter(locale, formatOptions: FormatterOptions) {\n      let newOptions = {...formatOpts, ...formatOptions};\n      let newFormatOptions = getFormatOptions({}, newOptions);\n      return new DateFormatter(locale, newFormatOptions);\n    }\n  };\n}\n\nfunction processSegments(dateValue, validSegments, dateFormatter, resolvedOptions, displayValue, calendar, locale, granularity) : DateSegment[] {\n  let timeValue = ['hour', 'minute', 'second'];\n  let segments = dateFormatter.formatToParts(dateValue);\n  let processedSegments: DateSegment[] = [];\n  for (let segment of segments) {\n    let type = TYPE_MAPPING[segment.type] || segment.type;\n    let isEditable = EDITABLE_SEGMENTS[type];\n    if (type === 'era' && calendar.getEras().length === 1) {\n      isEditable = false;\n    }\n\n    let isPlaceholder = EDITABLE_SEGMENTS[type] && !validSegments[type];\n    let placeholder = EDITABLE_SEGMENTS[type] ? getPlaceholder(type, segment.value, locale) : null;\n\n    let dateSegment = {\n      type,\n      text: isPlaceholder ? placeholder : segment.value,\n      ...getSegmentLimits(displayValue, type, resolvedOptions),\n      isPlaceholder,\n      placeholder,\n      isEditable\n    } as DateSegment;\n\n    // There is an issue in RTL languages where time fields render (minute:hour) instead of (hour:minute).\n    // To force an LTR direction on the time field since, we wrap the time segments in LRI (left-to-right) isolate unicode. See https://www.w3.org/International/questions/qa-bidi-unicode-controls.\n    // These unicode characters will be added to the array of processed segments as literals and will mark the start and end of the embedded direction change. \n    if (type === 'hour') {\n      // This marks the start of the embedded direction change. \n      processedSegments.push({\n        type: 'literal',\n        text: '\\u2066',\n        ...getSegmentLimits(displayValue, 'literal', resolvedOptions),\n        isPlaceholder: false,\n        placeholder: '',\n        isEditable: false\n      });\n      processedSegments.push(dateSegment);\n      // This marks the end of the embedded direction change in the case that the granularity it set to \"hour\".\n      if (type === granularity) {\n        processedSegments.push({\n          type: 'literal',\n          text: '\\u2069',\n          ...getSegmentLimits(displayValue, 'literal', resolvedOptions),\n          isPlaceholder: false,\n          placeholder: '',\n          isEditable: false\n        });\n      }\n    } else if (timeValue.includes(type) && type === granularity) {\n      processedSegments.push(dateSegment);\n      // This marks the end of the embedded direction change.\n      processedSegments.push({\n        type: 'literal',\n        text: '\\u2069',\n        ...getSegmentLimits(displayValue, 'literal', resolvedOptions),\n        isPlaceholder: false,\n        placeholder: '',\n        isEditable: false\n      });\n    } else {\n      // We only want to \"wrap\" the unicode around segments that are hour, minute, or second. If they aren't, just process as normal. \n      processedSegments.push(dateSegment);\n    }\n  }\n\n  return processedSegments;\n}\n\nfunction getSegmentLimits(date: DateValue, type: string, options: Intl.ResolvedDateTimeFormatOptions) {\n  switch (type) {\n    case 'era': {\n      let eras = date.calendar.getEras();\n      return {\n        value: eras.indexOf(date.era),\n        minValue: 0,\n        maxValue: eras.length - 1\n      };\n    }\n    case 'year':\n      return {\n        value: date.year,\n        minValue: 1,\n        maxValue: date.calendar.getYearsInEra(date)\n      };\n    case 'month':\n      return {\n        value: date.month,\n        minValue: getMinimumMonthInYear(date),\n        maxValue: date.calendar.getMonthsInYear(date)\n      };\n    case 'day':\n      return {\n        value: date.day,\n        minValue: getMinimumDayInMonth(date),\n        maxValue: date.calendar.getDaysInMonth(date)\n      };\n  }\n\n  if ('hour' in date) {\n    switch (type) {\n      case 'dayPeriod':\n        return {\n          value: date.hour >= 12 ? 12 : 0,\n          minValue: 0,\n          maxValue: 12\n        };\n      case 'hour':\n        if (options.hour12) {\n          let isPM = date.hour >= 12;\n          return {\n            value: date.hour,\n            minValue: isPM ? 12 : 0,\n            maxValue: isPM ? 23 : 11\n          };\n        }\n\n        return {\n          value: date.hour,\n          minValue: 0,\n          maxValue: 23\n        };\n      case 'minute':\n        return {\n          value: date.minute,\n          minValue: 0,\n          maxValue: 59\n        };\n      case 'second':\n        return {\n          value: date.second,\n          minValue: 0,\n          maxValue: 59\n        };\n    }\n  }\n\n  return {};\n}\n\nfunction addSegment(value: DateValue, part: string, amount: number, options: Intl.ResolvedDateTimeFormatOptions) {\n  switch (part) {\n    case 'era':\n    case 'year':\n    case 'month':\n    case 'day':\n      return value.cycle(part, amount, {round: part === 'year'});\n  }\n\n  if ('hour' in value) {\n    switch (part) {\n      case 'dayPeriod': {\n        let hours = value.hour;\n        let isPM = hours >= 12;\n        return value.set({hour: isPM ? hours - 12 : hours + 12});\n      }\n      case 'hour':\n      case 'minute':\n      case 'second':\n        return value.cycle(part, amount, {\n          round: part !== 'hour',\n          hourCycle: options.hour12 ? 12 : 24\n        });\n    }\n  }\n\n  throw new Error('Unknown segment: ' + part);\n}\n\nfunction setSegment(value: DateValue, part: string, segmentValue: number | string, options: Intl.ResolvedDateTimeFormatOptions) {\n  switch (part) {\n    case 'day':\n    case 'month':\n    case 'year':\n    case 'era':\n      return value.set({[part]: segmentValue});\n  }\n\n  if ('hour' in value && typeof segmentValue === 'number') {\n    switch (part) {\n      case 'dayPeriod': {\n        let hours = value.hour;\n        let wasPM = hours >= 12;\n        let isPM = segmentValue >= 12;\n        if (isPM === wasPM) {\n          return value;\n        }\n        return value.set({hour: wasPM ? hours - 12 : hours + 12});\n      }\n      case 'hour':\n        // In 12 hour time, ensure that AM/PM does not change\n        if (options.hour12) {\n          let hours = value.hour;\n          let wasPM = hours >= 12;\n          if (!wasPM && segmentValue === 12) {\n            segmentValue = 0;\n          }\n          if (wasPM && segmentValue < 12) {\n            segmentValue += 12;\n          }\n        }\n        // fallthrough\n      case 'minute':\n      case 'second':\n        return value.set({[part]: segmentValue});\n    }\n  }\n\n  throw new Error('Unknown segment: ' + part);\n}\n"], "names": [], "version": 3, "file": "useDateFieldState.module.js.map"}
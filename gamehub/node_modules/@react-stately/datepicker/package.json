{"name": "@react-stately/datepicker", "version": "3.14.2", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@internationalized/date": "^3.8.2", "@internationalized/string": "^3.2.7", "@react-stately/form": "^3.1.5", "@react-stately/overlays": "^3.6.17", "@react-stately/utils": "^3.10.7", "@react-types/datepicker": "^3.12.2", "@react-types/shared": "^3.30.0", "@swc/helpers": "^0.5.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "publishConfig": {"access": "public"}, "gitHead": "265b4d7f107905ee1c6e87a8af1613ab440a6849"}
{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAEM,MAAM;IAYX;;GAEC,GACD,OAAc;QACZ,OAAO,IAAI,0CAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACjC;IAEA;;GAEC,GACD,OAAO,KAAY,EAAW;QAC5B,OAAO,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC;IACjD;IAEA;;GAEC,GACD,WAAoB;QAClB,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK;IACpC;IAxBA,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,CAAE;QACxB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;IACX;AAsBF", "sources": ["packages/@react-stately/virtualizer/src/Point.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport class Point {\n  /** The x-coordinate of the point. */\n  x: number;\n\n  /** The y-coordinate of the point. */\n  y: number;\n\n  constructor(x = 0, y = 0) {\n    this.x = x;\n    this.y = y;\n  }\n\n  /**\n   * Returns a copy of this point.\n   */\n  copy(): Point {\n    return new Point(this.x, this.y);\n  }\n\n  /**\n   * Checks if two points are equal.\n   */\n  equals(point: Point): boolean {\n    return this.x === point.x && this.y === point.y;\n  }\n\n  /**\n   * Returns true if this point is the origin.\n   */\n  isOrigin(): boolean {\n    return this.x === 0 && this.y === 0;\n  }\n}\n"], "names": [], "version": 3, "file": "Point.main.js.map"}
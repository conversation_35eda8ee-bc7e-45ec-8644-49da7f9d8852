{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAUM,MAAM;IAoBX;;GAEC,GACD,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;IAC7B;IAEA;;GAEC,GACD,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;IACjC;IAEA;;GAEC,GACD,IAAI,UAAiB;QACnB,OAAO,IAAI,CAAA,GAAA,yCAAI,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACjC;IAEA;;GAEC,GACD,IAAI,WAAkB;QACpB,OAAO,IAAI,CAAA,GAAA,yCAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACpC;IAEA;;GAEC,GACD,IAAI,aAAoB;QACtB,OAAO,IAAI,CAAA,GAAA,yCAAI,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI;IACpC;IAEA;;GAEC,GACD,IAAI,cAAqB;QACvB,OAAO,IAAI,CAAA,GAAA,yCAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;IACvC;IAEA;;;GAGC,GACD,WAAW,IAAU,EAAW;QAC9B,OAAO,IAAI,CAAC,IAAI,GAAG,KACZ,KAAK,IAAI,GAAG,KACZ,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,IAC7B,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAC7B,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,MAAM,IAC9B,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;IACvC;IAEA;;;GAGC,GACD,aAAa,IAAU,EAAW;QAChC,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAChB,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAChB,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IACtB,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;IAC/B;IAEA;;;GAGC,GACD,cAAc,KAAY,EAAW;QACnC,OAAO,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,IACjB,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,IACjB,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,IACpB,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC;IAC7B;IAEA;;;;GAIC,GACD,gBAAgB,IAAU,EAAqB;QAC7C,KAAK,IAAI,OAAO;YAAC;YAAW;YAAY;YAAc;SAAc,CAAE;YACpE,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,IAAI,GAC9B,OAAO;QAEX;QAEA,OAAO;IACT;IAEA,OAAO,IAAU,EAAW;QAC1B,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,IACjB,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,IACjB,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,IACzB,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM;IACpC;IAEA,YAAY,KAAmB,EAAW;QACxC,OAAO,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,IAClB,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC;IAC3B;IAEA,WAAW,IAAiB,EAAW;QACrC,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,IACzB,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM;IACpC;IAEA;;GAEC,GACD,MAAM,KAAW,EAAQ;QACvB,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;QAChC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;QAChC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI;QAC9C,IAAI,SAAS,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI;QAC/C,OAAO,IAAI,0CAAK,GAAG,GAAG,OAAO;IAC/B;IAEA;;;GAGC,GACD,aAAa,KAAW,EAAQ;QAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QACnB,OAAO,IAAI,0CAAK,GAAG,GAAG,GAAG;QAG3B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;QAChC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;QAChC,OAAO,IAAI,0CACT,GACA,GACA,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,GAClC,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI;IAEtC;IAEA;;GAEC,GACD,OAAa;QACX,OAAO,IAAI,0CAAK,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;IACzD;IA9JA,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAE;QAC/C,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAChB;AA0JF", "sources": ["packages/@react-stately/virtualizer/src/Rect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Point} from './Point';\nimport {Size} from './Size';\n\nexport type RectCorner = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';\n\n/**\n * Represents a rectangle.\n */\nexport class Rect {\n  /** The x-coordinate of the rectangle. */\n  x: number;\n\n  /** The y-coordinate of the rectangle. */\n  y: number;\n\n  /** The width of the rectangle. */\n  width: number;\n\n  /** The height of the rectangle. */\n  height: number;\n\n  constructor(x = 0, y = 0, width = 0, height = 0) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n\n  /**\n   * The maximum x-coordinate in the rectangle.\n   */\n  get maxX(): number {\n    return this.x + this.width;\n  }\n\n  /**\n   * The maximum y-coordinate in the rectangle.\n   */\n  get maxY(): number {\n    return this.y + this.height;\n  }\n\n  /**\n   * The area of the rectangle.\n   */\n  get area(): number {\n    return this.width * this.height;\n  }\n\n  /**\n   * The top left corner of the rectangle.\n   */\n  get topLeft(): Point {\n    return new Point(this.x, this.y);\n  }\n\n  /**\n   * The top right corner of the rectangle.\n   */\n  get topRight(): Point {\n    return new Point(this.maxX, this.y);\n  }\n\n  /**\n   * The bottom left corner of the rectangle.\n   */\n  get bottomLeft(): Point {\n    return new Point(this.x, this.maxY);\n  }\n\n  /**\n   * The bottom right corner of the rectangle.\n   */\n  get bottomRight(): Point {\n    return new Point(this.maxX, this.maxY);\n  }\n\n  /**\n   * Returns whether this rectangle intersects another rectangle.\n   * @param rect - The rectangle to check.\n   */\n  intersects(rect: Rect): boolean {\n    return this.area > 0 \n        && rect.area > 0 \n        && this.x <= rect.x + rect.width\n        && rect.x <= this.x + this.width\n        && this.y <= rect.y + rect.height\n        && rect.y <= this.y + this.height;\n  }\n\n  /**\n   * Returns whether this rectangle fully contains another rectangle.\n   * @param rect - The rectangle to check.\n   */\n  containsRect(rect: Rect): boolean {\n    return this.x <= rect.x\n        && this.y <= rect.y\n        && this.maxX >= rect.maxX\n        && this.maxY >= rect.maxY;\n  }\n\n  /**\n   * Returns whether the rectangle contains the given point.\n   * @param point - The point to check.\n   */\n  containsPoint(point: Point): boolean {\n    return this.x <= point.x\n        && this.y <= point.y\n        && this.maxX >= point.x\n        && this.maxY >= point.y;\n  }\n\n  /**\n   * Returns the first corner of this rectangle (from top to bottom, left to right)\n   * that is contained in the given rectangle, or null of the rectangles do not intersect.\n   * @param rect - The rectangle to check.\n   */\n  getCornerInRect(rect: Rect): RectCorner | null {\n    for (let key of ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']) {\n      if (rect.containsPoint(this[key])) {\n        return key as RectCorner;\n      }\n    }\n\n    return null;\n  }\n\n  equals(rect: Rect): boolean {\n    return rect.x === this.x\n        && rect.y === this.y\n        && rect.width === this.width\n        && rect.height === this.height;\n  }\n\n  pointEquals(point: Point | Rect): boolean {\n    return this.x === point.x\n        && this.y === point.y;\n  }\n\n  sizeEquals(size: Size | Rect): boolean {\n    return this.width === size.width\n        && this.height === size.height;\n  }\n\n  /**\n   * Returns the union of this Rect and another.\n   */\n  union(other: Rect): Rect {\n    let x = Math.min(this.x, other.x);\n    let y = Math.min(this.y, other.y);\n    let width = Math.max(this.maxX, other.maxX) - x;\n    let height = Math.max(this.maxY, other.maxY) - y;\n    return new Rect(x, y, width, height);\n  }\n\n  /**\n   * Returns the intersection of this Rect with another.\n   * If the rectangles do not intersect, an all zero Rect is returned.\n   */\n  intersection(other: Rect): Rect {\n    if (!this.intersects(other)) {\n      return new Rect(0, 0, 0, 0);\n    }\n\n    let x = Math.max(this.x, other.x);\n    let y = Math.max(this.y, other.y);\n    return new Rect(\n      x,\n      y,\n      Math.min(this.maxX, other.maxX) - x,\n      Math.min(this.maxY, other.maxY) - y\n    );\n  }\n\n  /**\n   * Returns a copy of this rectangle.\n   */\n  copy(): Rect {\n    return new Rect(this.x, this.y, this.width, this.height);\n  }\n}\n"], "names": [], "version": 3, "file": "Rect.module.js.map"}
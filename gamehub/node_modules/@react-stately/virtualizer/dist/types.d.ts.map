{"mappings": ";AAYA;IACE,qCAAqC;IACrC,CAAC,EAAE,MAAM,CAAC;IAEV,qCAAqC;IACrC,CAAC,EAAE,MAAM,CAAC;gBAEE,CAAC,SAAI,EAAE,CAAC,SAAI;IAKxB;;OAEG;IACH,IAAI,IAAI,KAAK;IAIb;;OAEG;IACH,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI7B;;OAEG;IACH,QAAQ,IAAI,OAAO;CAGpB;AChCD;IACE,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;gBAEH,KAAK,SAAI,EAAE,MAAM,SAAI;IAKjC;;OAEG;IACH,IAAI,IAAI,IAAI;IAIZ;;OAEG;IACH,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,OAAO;IAK5B;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;CACF;AC3BD,yBAAyB,SAAS,GAAG,UAAU,GAAG,YAAY,GAAG,aAAa,CAAC;AAE/E;;GAEG;AACH;IACE,yCAAyC;IACzC,CAAC,EAAE,MAAM,CAAC;IAEV,yCAAyC;IACzC,CAAC,EAAE,MAAM,CAAC;IAEV,kCAAkC;IAClC,KAAK,EAAE,MAAM,CAAC;IAEd,mCAAmC;IACnC,MAAM,EAAE,MAAM,CAAC;gBAEH,CAAC,SAAI,EAAE,CAAC,SAAI,EAAE,KAAK,SAAI,EAAE,MAAM,SAAI;IAO/C;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,KAAK,CAEnB;IAED;;OAEG;IACH,IAAI,QAAQ,IAAI,KAAK,CAEpB;IAED;;OAEG;IACH,IAAI,UAAU,IAAI,KAAK,CAEtB;IAED;;OAEG;IACH,IAAI,WAAW,IAAI,KAAK,CAEvB;IAED;;;OAGG;IACH,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAS/B;;;OAGG;IACH,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAOjC;;;OAGG;IACH,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAOpC;;;;OAIG;IACH,eAAe,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU,GAAG,IAAI;IAU9C,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO;IAO3B,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,GAAG,OAAO;IAKzC,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,OAAO;IAKtC;;OAEG;IACH,KAAK,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI;IAQxB;;;OAGG;IACH,YAAY,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI;IAe/B;;OAEG;IACH,IAAI,IAAI,IAAI;CAGb;ACjLD;;;;;GAKG;AACH;IACE;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,GAAG,EAAE,GAAG,CAAC;IAET;;OAEG;IACH,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC;IAEtB;;OAEG;IACH,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC;IAEpB;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IAEX;;;;;OAKG;IACH,aAAa,EAAE,OAAO,CAAC;IAEvB;;;OAGG;IACH,QAAQ,EAAE,OAAO,CAAC;IAElB;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IAEzB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,aAAa,EAAE,OAAO,CAAC;IAEvB;;;;OAIG;gBACS,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAc9C;;OAEG;IACH,IAAI,IAAI,UAAU;CAYnB;ACnGD;;;GAGG;AACH,0BAA0B,CAAC,SAAS,MAAM,EAAE,CAAC;IAC3C,8CAA8C;IAC9C,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IAE/B,0DAA0D;IAC1D,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAE9B,kFAAkF;IAClF,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;IAElB,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;IAEnB,QAAQ,EAAE,MAAM,CAAC;IACjB,GAAG,EAAE,GAAG,CAAC;IAET,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;gBAElC,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM;IAW5D;;OAEG;IACH,eAAe,IAAI,IAAI;IAMvB,eAAe,CAAC,SAAS,EAAE,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;IAanD,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI;CASzC;AAQD,wBAAuB,CAAC,SAAS,MAAM,EAAE,CAAC,CAAE,SAAQ,aAAa,CAAC,EAAE,CAAC,CAAC;IACpE,MAAM,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEf,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM;CAIzF;AG1ED,6BAA6B,CAAC,SAAS,MAAM,EAAE,CAAC;IAC9C,QAAQ,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAC1B,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;CAClB;AAED;;;;;;;;;;;;;;;GAeG;AACH,0BAAyB,CAAC,SAAS,MAAM,EAAE,CAAC;IAC1C;;;OAGG;IACH,QAAQ,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAEpC,8CAA8C;IAC9C,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IACnC,2DAA2D;IAC3D,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAC3B,0CAA0C;IAC1C,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC;IAC3B,uCAAuC;IACvC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC;IAC3B,mGAAmG;IACnG,QAAQ,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBASrB,OAAO,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC;IAe7C,mEAAmE;IACnE,cAAc,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO;IA0DjC;;OAEG;IACH,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,GAAG,IAAI;IAqCpC,qBAAqB,IAAI,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC;IA4E7C,2DAA2D;IAC3D,MAAM,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;IAiF/D,cAAc,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS;IAIxD,UAAU,CAAC,OAAO,EAAE,mBAAmB,GAAG,IAAI;IAI9C,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;CAY3C;ACvVD;;;;;;;;;GASG;AACH,OAAO,QAAQ,cAAc,CAAC,SAAS,MAAM,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAE,YAAW,cAAc;IAC3F,2DAA2D;IAC3D,WAAW,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAQ;IAE/C;;;;OAIG;IACH,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU,EAAE;IAExD;;;;OAIG;IACH,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,GAAG,UAAU,GAAG,IAAI;IAEnD;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,IAAI;IAE/B;;;;;OAKG;IACH,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,OAAO;IAMvD;;;;OAIG;IACH,6BAA6B,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,GAAG,OAAO;IAIpE;;;;;OAKG;IACH,MAAM,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC,GAAG,IAAI;IAEzD;;OAEG;IACH,cAAc,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,GAAG,OAAO;IAE9C;;OAEG;IACH,uBAAuB,CAAC,CAAC,MAAM,EAAE,cAAc,GAAG,UAAU;IAE5D,eAAe;IACf,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;IAIlC,eAAe;IACf,cAAc,IAAI,IAAI;CAGvB;ACpFD,qCAAqC,CAAC,GAAG,GAAG;IAC1C,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,aAAa,CAAC,EAAE,CAAC,CAAA;CAClB;AAED,8BAAqC,CAAC,SAAS,MAAM,EAAE,CAAC;IACtD,cAAc,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;IACjC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IAC/C,UAAU,CAAC,GAAG,EAAE,mBAAmB,GAAG,IAAI,CAAA;CAC3C;AAED,mCAA0C,CAAC,SAAS,MAAM,EAAE,CAAC,GAAG,GAAG;IACjE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAClB,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAC1B,aAAa,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAChC,WAAW,EAAE,IAAI,CAAC;IAClB,mBAAmB,EAAE,mBAAmB,CAAC;IACzC,WAAW,EAAE,OAAO,CAAC;IACrB,aAAa,CAAC,EAAE,CAAC,CAAA;CAClB;ACjBD,2BAA2B,CAAC,SAAS,MAAM,EAAE,CAAC,EAAE,CAAC;IAC/C,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IAC/C,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAClB,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAC1B,mBAAmB,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;IACtC,aAAa,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAChC,aAAa,CAAC,EAAE,CAAC,CAAA;CAClB;AAED,kCAAkC,CAAC,SAAS,MAAM,EAAE,CAAC;IACnD,YAAY,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACnC,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;IACrC,WAAW,EAAE,IAAI,CAAC;IAClB,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,WAAW,EAAE,OAAO,CAAC;IACrB,cAAc,EAAE,MAAM,IAAI,CAAC;IAC3B,YAAY,EAAE,MAAM,IAAI,CAAA;CACzB;AAED,oCAAoC,CAAC,SAAS,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAwEzH", "sources": ["packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/Point.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/Size.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/Rect.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/LayoutInfo.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/ReusableView.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/utils.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/OverscanManager.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/Virtualizer.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/Layout.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/types.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/useVirtualizerState.ts", "packages/@react-stately/virtualizer/src/packages/@react-stately/virtualizer/src/index.ts", "packages/@react-stately/virtualizer/src/index.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type {InvalidationContext} from './types';\nexport type {VirtualizerState} from './useVirtualizerState';\nexport type {RectCorner} from './Rect';\n\nexport {Layout} from './Layout';\nexport {LayoutInfo} from './LayoutInfo';\nexport {Point} from './Point';\nexport {Rect} from './Rect';\nexport {Size} from './Size';\nexport {ReusableView} from './ReusableView';\nexport {useVirtualizerState} from './useVirtualizerState';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}
{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AA+BM,SAAS,0CAAkD,IAA+B;IAC/F,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,eAAO,EAAE,IAAI,CAAA,GAAA,yCAAG,EAAE,GAAG,GAAG,GAAG;IAC/D,IAAI,CAAC,aAAa,aAAa,GAAG,CAAA,GAAA,eAAO,EAAE;IAC3C,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,eAAO,EAAuB,CAAC;IACnF,IAAI,qBAAqB,CAAA,GAAA,aAAK,EAAE;IAChC,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,eAAO,EAAE,IAAM,IAAI,CAAA,GAAA,yCAAU,EAAQ;YACvD,YAAY,KAAK,UAAU;YAC3B,QAAQ,KAAK,MAAM;YACnB,UAAU;gBACR,gBAAe,IAAI;oBACjB,eAAe;oBACf,mBAAmB,OAAO,GAAG;gBAC/B;gBACA,2DAA2D;gBAC3D,YAAY,KAAK,UAAU;gBAC3B,YAAY;YACd;QACF;IAEA,wEAAwE;IACxE,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,GAAG;YAC7B,KAAK,mBAAmB,CAAC;QAC3B;IACF;IAEA,IAAI,4BAA4B,CAAA,GAAA,cAAM,EAAE;QACtC,IAAI,KAAK,aAAa,IAAI,MACxB,OAAO;YAAC,GAAG,mBAAmB;YAAE,eAAe,KAAK,aAAa;QAAA;QAEnE,OAAO;IACT,GAAG;QAAC;QAAqB,KAAK,aAAa;KAAC;IAE5C,IAAI,eAAe,YAAY,MAAM,CAAC;QACpC,QAAQ,KAAK,MAAM;QACnB,YAAY,KAAK,UAAU;QAC3B,eAAe,KAAK,aAAa;QACjC,eAAe,KAAK,aAAa;qBACjC;QACA,qBAAqB;qBACrB;IACF;IAEA,IAAI,cAAc,YAAY,WAAW;IAEzC,IAAI,iBAAiB,CAAA,GAAA,kBAAU,EAAE;QAC/B,aAAa;IACf,GAAG,EAAE;IACL,IAAI,eAAe,CAAA,GAAA,kBAAU,EAAE;QAC7B,aAAa;IACf,GAAG,EAAE;IAEL,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE,IAAO,CAAA;yBACzB;0BACA;4BACA;yBACA;yBACA;4BACA;0BACA;QACF,CAAA,GAAI;QACF;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;AACT", "sources": ["packages/@react-stately/virtualizer/src/useVirtualizerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Key} from '@react-types/shared';\nimport {InvalidationContext} from './types';\nimport {Layout} from './Layout';\nimport {Rect} from './Rect';\nimport {ReusableView} from './ReusableView';\nimport {Size} from './Size';\nimport {useCallback, useMemo, useRef, useState} from 'react';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {Virtualizer} from './Virtualizer';\n\ninterface VirtualizerProps<T extends object, V, O> {\n  renderView(type: string, content: T | null): V,\n  layout: Layout<T>,\n  collection: Collection<T>,\n  onVisibleRectChange(rect: Rect): void,\n  persistedKeys?: Set<Key> | null,\n  layoutOptions?: O\n}\n\nexport interface VirtualizerState<T extends object, V> {\n  visibleViews: ReusableView<T, V>[],\n  setVisibleRect: (rect: Rect) => void,\n  contentSize: Size,\n  virtualizer: Virtualizer<T, V>,\n  isScrolling: boolean,\n  startScrolling: () => void,\n  endScrolling: () => void\n}\n\nexport function useVirtualizerState<T extends object, V, O = any>(opts: VirtualizerProps<T, V, O>): VirtualizerState<T, V> {\n  let [visibleRect, setVisibleRect] = useState(new Rect(0, 0, 0, 0));\n  let [isScrolling, setScrolling] = useState(false);\n  let [invalidationContext, setInvalidationContext] = useState<InvalidationContext>({});\n  let visibleRectChanged = useRef(false);\n  let [virtualizer] = useState(() => new Virtualizer<T, V>({\n    collection: opts.collection,\n    layout: opts.layout,\n    delegate: {\n      setVisibleRect(rect) {\n        setVisibleRect(rect);\n        visibleRectChanged.current = true;\n      },\n      // TODO: should changing these invalidate the entire cache?\n      renderView: opts.renderView,\n      invalidate: setInvalidationContext\n    }\n  }));\n\n  // onVisibleRectChange must be called from an effect, not during render.\n  useLayoutEffect(() => {\n    if (visibleRectChanged.current) {\n      visibleRectChanged.current = false;\n      opts.onVisibleRectChange(visibleRect);\n    }\n  });\n\n  let mergedInvalidationContext = useMemo(() => {\n    if (opts.layoutOptions != null) {\n      return {...invalidationContext, layoutOptions: opts.layoutOptions};\n    }\n    return invalidationContext;\n  }, [invalidationContext, opts.layoutOptions]);\n\n  let visibleViews = virtualizer.render({\n    layout: opts.layout,\n    collection: opts.collection,\n    persistedKeys: opts.persistedKeys,\n    layoutOptions: opts.layoutOptions,\n    visibleRect,\n    invalidationContext: mergedInvalidationContext,\n    isScrolling\n  });\n\n  let contentSize = virtualizer.contentSize;\n\n  let startScrolling = useCallback(() => {\n    setScrolling(true);\n  }, []);\n  let endScrolling = useCallback(() => {\n    setScrolling(false);\n  }, []);\n\n  let state = useMemo(() => ({\n    virtualizer,\n    visibleViews,\n    setVisibleRect,\n    contentSize,\n    isScrolling,\n    startScrolling,\n    endScrolling\n  }), [\n    virtualizer,\n    visibleViews,\n    setVisibleRect,\n    contentSize,\n    isScrolling,\n    startScrolling,\n    endScrolling\n  ]);\n\n  return state;\n}\n"], "names": [], "version": 3, "file": "useVirtualizerState.module.js.map"}
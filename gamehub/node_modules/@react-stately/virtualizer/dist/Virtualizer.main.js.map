{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAmCM,MAAM;IAwCX,iEAAiE,GACjE,eAAe,GAAQ,EAAW;QAChC,mEAAmE;QACnE,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MACzB,OAAO;QAGT,wEAAwE;QACxE,KAAK,IAAI,KAAK,IAAI,CAAC,aAAa,CAC9B,MAAO,KAAK,KAAM;YAChB,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YAC3C,IAAI,CAAC,cAAc,WAAW,SAAS,IAAI,MACzC;YAGF,IAAI,WAAW,SAAS;YAExB,IAAI,MAAM,KACR,OAAO;QAEX;QAGF,OAAO;IACT;IAEQ,cAAc,UAAsB,EAAkC;QAC5E,OAAO,WAAW,SAAS,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,SAAS,IAAI,IAAI,CAAC,SAAS;IACrG;IAEQ,gBAAgB,UAAsB,EAAmB;QAC/D,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC;QACpC,IAAI,OAAO,WAAW,eAAe,CAAC,WAAW,IAAI;QACrD,KAAK,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,CAAC;QACjB,OAAO;IACT;IAEQ,YAAY,YAAgC,EAAE;QACpD,IAAI,aAAa,UAAU,EAAE;YAC3B,IAAI,QAAC,IAAI,OAAE,GAAG,WAAE,OAAO,EAAC,GAAG,aAAa,UAAU;YAClD,aAAa,OAAO,GAAG,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1D,aAAa,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,aAAa,OAAO;QACxE;IACF;IAEQ,eAAe,IAAY,EAAE,OAAiB,EAAE;QACtD,IAAI,SAAS,WAAW,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW;QACpE,IAAI,UAAU,MACZ,OAAO;QAGT,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM;QAC9C,IAAI,SACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS;QAErC,OAAO;IACT;IAEA;;GAEC,GACD,WAAW,KAAY,EAAc;QACnC,IAAI,OAAO,IAAI,CAAA,GAAA,8BAAG,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG;QACzC,IAAI,cAAc,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;QAE3E,yDAAyD;QACzD,kEAAkE;QAClE,KAAK,IAAI,cAAc,YAAa;YAClC,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,OAC7B,OAAO,WAAW,GAAG;QAEzB;QAEA,OAAO;IACT;IAEQ,SAAS,UAA+B,CAAC,CAAC,EAAE;QAClD,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACnB,AAAC,IAAI,CAAmB,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;QAEhE,6BAA6B;QAC7B,6CAA6C;QAC7C,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,IAAI,iBAAiB,QAAQ,cAAc,GAAG,IAAI,YAAY,CAAC;QAC/D,IAAI,iBAAiB,QAAQ,cAAc,GAAG,IAAI,YAAY,CAAC;QAC/D,iBAAiB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,YAAY,KAAK,EAAE;QAClF,iBAAiB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,YAAY,MAAM,EAAE;QAEpF,IAAI,mBAAmB,YAAY,CAAC,IAAI,mBAAmB,YAAY,CAAC,EAAE;YACxE,kDAAkD;YAClD,IAAI,OAAO,IAAI,CAAA,GAAA,8BAAG,EAAE,gBAAgB,gBAAgB,YAAY,KAAK,EAAE,YAAY,MAAM;YACzF,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC/B,OACE,IAAI,CAAC,cAAc;IAEvB;IAEA,wBAA8C;QAC5C,IAAI,YAAY,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,GAAG,CAAC,OAAO;QACvE,IAAI,sBAAsB,aAAa,OAAO,gBAAgB,eAAe,OAAO,mBAAmB,CAAC,YAAY,SAAS,EAAE,QAAQ,CAAC;QACxI,IAAI,uBAAuB,aAAa,OAAO,gBAAgB,eAAe,OAAO,mBAAmB,CAAC,YAAY,SAAS,EAAE,QAAQ,CAAC;QAEzI,IAAI;QACJ,IAAI,aAAa,CAAE,CAAA,uBAAuB,oBAAmB,GAC3D,OAAO,IAAI,CAAA,GAAA,8BAAG,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;aAErE,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;QAEjD,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;QACpD,IAAI,MAAM,IAAI;QACd,KAAK,IAAI,cAAc,YACrB,IAAI,GAAG,CAAC,WAAW,GAAG,EAAE;QAG1B,OAAO;IACT;IAEQ,iBAAiB;QACvB,IAAI,qBAAqB,IAAI,CAAC,qBAAqB;QAEnD,IAAI,UAAU,IAAI;QAClB,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,aAAa,CAAE;YAC1C,IAAI,aAAa,mBAAmB,GAAG,CAAC;YACxC,oFAAoF;YACpF,IAAI,CAAC,cAAc,KAAK,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa;gBACjE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC1B,KAAK,MAAM,CAAC,UAAU,CAAC;gBACvB,QAAQ,GAAG,CAAC,OAAO,6CAA6C;YAClE;QACF;QAEA,KAAK,IAAI,CAAC,KAAK,WAAW,IAAI,mBAAoB;YAChD,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC,eAAe,CAAC;gBAC5B,KAAK,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACzB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK;gBAC5B,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,KAAK,UAAU,GAAG;gBAElB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,GAAG;gBACjD,IAAI,KAAK,OAAO,KAAK,MAAM;oBACzB,IAAI,KAAK,OAAO,IAAI,MAClB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,OAAO;oBAE3C,IAAI,CAAC,WAAW,CAAC;gBACnB;YACF;QACF;QAEA,wEAAwE;QACxE,6EAA6E;QAC7E,yEAAyE;QACzE,oFAAoF;QACpF,KAAK,IAAI,QAAQ,QAAS;YACxB,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5B,KAAK,MAAM,CAAC,aAAa,CAAC,KAAK;QACjC;QAEA,0EAA0E;QAC1E,wEAAwE;QACxE,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,YAAY,EACpB,uEAAuE;QACvE,KAAK,IAAI,OAAO,mBAAmB,IAAI,GAAI;YACzC,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YAClC,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5B,KAAK,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC3B;IAEJ;IAEA,yDAAyD,GACzD,OAAO,IAAiC,EAAwB;QAC9D,IAAI,cAA6B,IAAI;QACrC,IAAI,cAAc;QAClB,IAAI,gBAAgB;QACpB,IAAI,cAAc;QAClB,IAAI,kBAAkB;QACtB,IAAI,uBAAuB;QAC3B,IAAI,cAAc;QAElB,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC,UAAU,EAAE;YACvC,YAAY,UAAU,GAAG,KAAK,UAAU;YACxC,cAAc;QAChB;QAEA,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,IAAI,EAAE;YACnE,IAAI,IAAI,CAAC,MAAM,EACb,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG;YAG5B,KAAK,MAAM,CAAC,WAAW,GAAG,IAAI;YAC9B,YAAY,MAAM,GAAG,KAAK,MAAM;YAChC,cAAc;QAChB;QAEA,IAAI,KAAK,aAAa,IAAI,CAAC,CAAA,GAAA,oCAAS,EAAE,KAAK,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG;YAC7E,YAAY,aAAa,GAAG,KAAK,aAAa;YAC9C,cAAc;QAChB;QAEA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,WAAW,GAAG;YAC9C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,WAAW;YACrD,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,WAAW;YAEtF,IAAI,kBAAkB;gBACpB,gBAAgB,CAAC,KAAK,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW;gBAC9D,cAAc,CAAC,KAAK,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW;gBAC3D,cAAc;YAChB,OACE,cAAc;YAGhB,YAAY,WAAW,GAAG,KAAK,WAAW;QAC5C;QAEA,IAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAC1D,IAAI,KAAK,mBAAmB,EAAE;gBAC5B,gBAAA,cAAgB,KAAK,mBAAmB,CAAC,WAAW,IAAI;gBACxD,kBAAA,gBAAkB,KAAK,mBAAmB,CAAC,aAAa,IAAI;gBAC5D,oBAAA,kBAAoB,KAAK,mBAAmB,CAAC,eAAe,IAAI;gBAChE,yBAAA,uBAAyB,KAAK,mBAAmB,CAAC,aAAa,IAAI,QAC9D,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAAI,QAC3C,KAAK,mBAAmB,CAAC,aAAa,KAAK,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAClF,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,aAAa;gBAC9H,gBAAA,cAAgB,mBAAmB,eAAe,iBAAiB;YACrE;YACA,IAAI,CAAC,oBAAoB,GAAG,KAAK,mBAAmB;QACtD;QAEA,IAAI,KAAK,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE;YAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW;YACpC,IAAI,CAAC,KAAK,WAAW,EACnB,+CAA+C;YAC/C,cAAc;QAElB;QAEA,IAAI,aACF,IAAI,CAAC,QAAQ,CAAC;2BACZ;yBACA;6BACA;kCACA;YACA,eAAe,IAAI,CAAC,oBAAoB,CAAC,aAAa;QACxD;aACK,IAAI,aACT,IAAI,CAAC,cAAc;QAGrB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;IAC3C;IAEA,eAAe,GAAQ,EAAkC;QACvD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;IAChC;IAEA,WAAW,OAA4B,EAAQ;QAC7C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;IAC3B;IAEA,eAAe,GAAQ,EAAE,IAAU,EAAQ;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAC7B;QAGF,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK;QAC9C,IAAI,SACF,IAAI,CAAC,UAAU,CAAC;YACd,iBAAiB;QACnB;IAEJ;IAnSA,YAAY,OAAiC,CAAE;QAC7C,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;QACpC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA,GAAA,8BAAG;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA,GAAA,8BAAG;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA,GAAA,kCAAO,EAAE,IAAI;QAClC,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,oBAAoB,GAAG,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA,GAAA,yCAAc;IAC5C;AAuRF", "sources": ["packages/@react-stately/virtualizer/src/Virtualizer.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ChildView, ReusableView, RootView} from './ReusableView';\nimport {Collection, Key} from '@react-types/shared';\nimport {InvalidationContext, Mutable, VirtualizerDelegate, VirtualizerRenderOptions} from './types';\nimport {isSetEqual} from './utils';\nimport {Layout} from './Layout';\nimport {LayoutInfo} from './LayoutInfo';\nimport {OverscanManager} from './OverscanManager';\nimport {Point} from './Point';\nimport {Rect} from './Rect';\nimport {Size} from './Size';\n\ninterface VirtualizerOptions<T extends object, V> {\n  delegate: VirtualizerDelegate<T, V>,\n  collection: Collection<T>,\n  layout: Layout<T>\n}\n\n/**\n * The Virtualizer class renders a scrollable collection of data using customizable layouts.\n * It supports very large collections by only rendering visible views to the DOM, reusing\n * them as you scroll. Virtualizer can present any type of view, including non-item views\n * such as section headers and footers.\n *\n * Virtualizer uses `Layout` objects to compute what views should be visible, and how\n * to position and style them. This means that virtualizer can have its items arranged in\n * a stack, a grid, a circle, or any other layout you can think of. The layout can be changed\n * dynamically at runtime as well.\n *\n * Layouts produce information on what views should appear in the virtualizer, but do not create\n * the views themselves directly. It is the responsibility of the `VirtualizerDelegate` object\n * to render elements for each layout info. The virtualizer manages a set of `ReusableView` objects,\n * which are reused as the user scrolls by swapping their content with cached elements returned by the delegate.\n */\nexport class Virtualizer<T extends object, V> {\n  /**\n   * The virtualizer delegate. The delegate is used by the virtualizer\n   * to create and configure views.\n   */\n  delegate: VirtualizerDelegate<T, V>;\n\n  /** The current content of the virtualizer. */\n  readonly collection: Collection<T>;\n  /** The layout object that determines the visible views. */\n  readonly layout: Layout<T>;\n  /** The size of the scrollable content. */\n  readonly contentSize: Size;\n  /** The currently visible rectangle. */\n  readonly visibleRect: Rect;\n  /** The set of persisted keys that are always present in the DOM, even if not currently in view. */\n  readonly persistedKeys: Set<Key>;\n\n  private _visibleViews: Map<Key, ChildView<T, V>>;\n  private _renderedContent: WeakMap<T, V>;\n  private _rootView: RootView<T, V>;\n  private _isScrolling: boolean;\n  private _invalidationContext: InvalidationContext;\n  private _overscanManager: OverscanManager;\n\n  constructor(options: VirtualizerOptions<T, V>) {\n    this.delegate = options.delegate;\n    this.collection = options.collection;\n    this.layout = options.layout;\n    this.contentSize = new Size;\n    this.visibleRect = new Rect;\n    this.persistedKeys = new Set();\n    this._visibleViews = new Map();\n    this._renderedContent = new WeakMap();\n    this._rootView = new RootView(this);\n    this._isScrolling = false;\n    this._invalidationContext = {};\n    this._overscanManager = new OverscanManager();\n  }\n\n  /** Returns whether the given key, or an ancestor, is persisted. */\n  isPersistedKey(key: Key): boolean {\n    // Quick check if the key is directly in the set of persisted keys.\n    if (this.persistedKeys.has(key)) {\n      return true;\n    }\n\n    // If not, check if the key is an ancestor of any of the persisted keys.\n    for (let k of this.persistedKeys) {\n      while (k != null) {\n        let layoutInfo = this.layout.getLayoutInfo(k);\n        if (!layoutInfo || layoutInfo.parentKey == null) {\n          break;\n        }\n\n        k = layoutInfo.parentKey;\n\n        if (k === key) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  private getParentView(layoutInfo: LayoutInfo): ReusableView<T, V> | undefined {\n    return layoutInfo.parentKey != null ? this._visibleViews.get(layoutInfo.parentKey) : this._rootView;\n  }\n\n  private getReusableView(layoutInfo: LayoutInfo): ChildView<T, V> {\n    let parentView = this.getParentView(layoutInfo)!;\n    let view = parentView.getReusableView(layoutInfo.type);\n    view.layoutInfo = layoutInfo;\n    this._renderView(view);\n    return view;\n  }\n\n  private _renderView(reusableView: ReusableView<T, V>) {\n    if (reusableView.layoutInfo) {\n      let {type, key, content} = reusableView.layoutInfo;\n      reusableView.content = content || this.collection.getItem(key);\n      reusableView.rendered = this._renderContent(type, reusableView.content);\n    }\n  }\n\n  private _renderContent(type: string, content: T | null) {\n    let cached = content != null ? this._renderedContent.get(content) : null;\n    if (cached != null) {\n      return cached;\n    }\n\n    let rendered = this.delegate.renderView(type, content);\n    if (content) {\n      this._renderedContent.set(content, rendered);\n    }\n    return rendered;\n  }\n\n  /**\n   * Returns the key for the item view currently at the given point.\n   */\n  keyAtPoint(point: Point): Key | null {\n    let rect = new Rect(point.x, point.y, 1, 1);\n    let layoutInfos = rect.area === 0 ? [] : this.layout.getVisibleLayoutInfos(rect);\n\n    // Layout may return multiple layout infos in the case of\n    // persisted keys, so find the first one that actually intersects.\n    for (let layoutInfo of layoutInfos) {\n      if (layoutInfo.rect.intersects(rect)) {\n        return layoutInfo.key;\n      }\n    }\n\n    return null;\n  }\n\n  private relayout(context: InvalidationContext = {}) {\n    // Update the layout\n    this.layout.update(context);\n    (this as Mutable<this>).contentSize = this.layout.getContentSize();\n\n    // Constrain scroll position.\n    // If the content changed, scroll to the top.\n    let visibleRect = this.visibleRect;\n    let contentOffsetX = context.contentChanged ? 0 : visibleRect.x;\n    let contentOffsetY = context.contentChanged ? 0 : visibleRect.y;\n    contentOffsetX = Math.max(0, Math.min(this.contentSize.width - visibleRect.width, contentOffsetX));\n    contentOffsetY = Math.max(0, Math.min(this.contentSize.height - visibleRect.height, contentOffsetY));\n\n    if (contentOffsetX !== visibleRect.x || contentOffsetY !== visibleRect.y) {\n      // If the offset changed, trigger a new re-render.\n      let rect = new Rect(contentOffsetX, contentOffsetY, visibleRect.width, visibleRect.height);\n      this.delegate.setVisibleRect(rect);\n    } else {\n      this.updateSubviews();\n    }\n  }\n\n  getVisibleLayoutInfos(): Map<Key, LayoutInfo> {\n    let isTestEnv = process.env.NODE_ENV === 'test' && !process.env.VIRT_ON;\n    let isClientWidthMocked = isTestEnv && typeof HTMLElement !== 'undefined' && Object.getOwnPropertyNames(HTMLElement.prototype).includes('clientWidth');\n    let isClientHeightMocked = isTestEnv && typeof HTMLElement !== 'undefined' && Object.getOwnPropertyNames(HTMLElement.prototype).includes('clientHeight');\n\n    let rect: Rect;\n    if (isTestEnv && !(isClientWidthMocked && isClientHeightMocked)) {\n      rect = new Rect(0, 0, this.contentSize.width, this.contentSize.height);\n    } else {\n      rect = this._overscanManager.getOverscannedRect();\n    }\n    let layoutInfos = this.layout.getVisibleLayoutInfos(rect);\n    let map = new Map;\n    for (let layoutInfo of layoutInfos) {\n      map.set(layoutInfo.key, layoutInfo);\n    }\n\n    return map;\n  }\n\n  private updateSubviews() {\n    let visibleLayoutInfos = this.getVisibleLayoutInfos();\n\n    let removed = new Set<ChildView<T, V>>();\n    for (let [key, view] of this._visibleViews) {\n      let layoutInfo = visibleLayoutInfos.get(key);\n      // If a view's parent changed, treat it as a delete and re-create in the new parent.\n      if (!layoutInfo || view.parent !== this.getParentView(layoutInfo)) {\n        this._visibleViews.delete(key);\n        view.parent.reuseChild(view);\n        removed.add(view); // Defer removing in case we reuse this view.\n      }\n    }\n\n    for (let [key, layoutInfo] of visibleLayoutInfos) {\n      let view = this._visibleViews.get(key);\n      if (!view) {\n        view = this.getReusableView(layoutInfo);\n        view.parent.children.add(view);\n        this._visibleViews.set(key, view);\n        removed.delete(view);\n      } else {\n        view.layoutInfo = layoutInfo;\n\n        let item = this.collection.getItem(layoutInfo.key);\n        if (view.content !== item) {\n          if (view.content != null) {\n            this._renderedContent.delete(view.content);\n          }\n          this._renderView(view);\n        }\n      }\n    }\n\n    // The remaining views in `removed` were not reused to render new items.\n    // They should be removed from the DOM. We also clear the reusable view queue\n    // here since there's no point holding onto views that have been removed.\n    // Doing so hurts performance in the future when reusing elements due to FIFO order.\n    for (let view of removed) {\n      view.parent.children.delete(view);\n      view.parent.reusableViews.clear();\n    }\n\n    // Reordering DOM nodes is costly, so we defer this until scrolling stops.\n    // DOM order does not affect visual order (due to absolute positioning),\n    // but does matter for assistive technology users.\n    if (!this._isScrolling) {\n      // Layout infos must be in topological order (parents before children).\n      for (let key of visibleLayoutInfos.keys()) {\n        let view = this._visibleViews.get(key)!;\n        view.parent.children.delete(view);\n        view.parent.children.add(view);\n      }\n    }\n  }\n\n  /** Performs layout and updates visible views as needed. */\n  render(opts: VirtualizerRenderOptions<T>): ReusableView<T, V>[] {\n    let mutableThis: Mutable<this> = this;\n    let needsLayout = false;\n    let offsetChanged = false;\n    let sizeChanged = false;\n    let itemSizeChanged = false;\n    let layoutOptionsChanged = false;\n    let needsUpdate = false;\n\n    if (opts.collection !== this.collection) {\n      mutableThis.collection = opts.collection;\n      needsLayout = true;\n    }\n\n    if (opts.layout !== this.layout || this.layout.virtualizer !== this) {\n      if (this.layout) {\n        this.layout.virtualizer = null;\n      }\n\n      opts.layout.virtualizer = this;\n      mutableThis.layout = opts.layout;\n      needsLayout = true;\n    }\n\n    if (opts.persistedKeys && !isSetEqual(opts.persistedKeys, this.persistedKeys)) {\n      mutableThis.persistedKeys = opts.persistedKeys;\n      needsUpdate = true;\n    }\n\n    if (!this.visibleRect.equals(opts.visibleRect)) {\n      this._overscanManager.setVisibleRect(opts.visibleRect);\n      let shouldInvalidate = this.layout.shouldInvalidate(opts.visibleRect, this.visibleRect);\n\n      if (shouldInvalidate) {\n        offsetChanged = !opts.visibleRect.pointEquals(this.visibleRect);\n        sizeChanged = !opts.visibleRect.sizeEquals(this.visibleRect);\n        needsLayout = true;\n      } else {\n        needsUpdate = true;\n      }\n\n      mutableThis.visibleRect = opts.visibleRect;\n    }\n\n    if (opts.invalidationContext !== this._invalidationContext) {\n      if (opts.invalidationContext) {\n        sizeChanged ||= opts.invalidationContext.sizeChanged || false;\n        offsetChanged ||= opts.invalidationContext.offsetChanged || false;\n        itemSizeChanged ||= opts.invalidationContext.itemSizeChanged || false;\n        layoutOptionsChanged ||= opts.invalidationContext.layoutOptions != null\n          && this._invalidationContext.layoutOptions != null\n          && opts.invalidationContext.layoutOptions !== this._invalidationContext.layoutOptions\n          && this.layout.shouldInvalidateLayoutOptions(opts.invalidationContext.layoutOptions, this._invalidationContext.layoutOptions);\n        needsLayout ||= itemSizeChanged || sizeChanged || offsetChanged || layoutOptionsChanged;\n      }\n      this._invalidationContext = opts.invalidationContext;\n    }\n\n    if (opts.isScrolling !== this._isScrolling) {\n      this._isScrolling = opts.isScrolling;\n      if (!opts.isScrolling) {\n        // Update to fix the DOM order after scrolling.\n        needsUpdate = true;\n      }\n    }\n\n    if (needsLayout) {\n      this.relayout({\n        offsetChanged,\n        sizeChanged,\n        itemSizeChanged,\n        layoutOptionsChanged,\n        layoutOptions: this._invalidationContext.layoutOptions\n      });\n    } else if (needsUpdate) {\n      this.updateSubviews();\n    }\n\n    return Array.from(this._rootView.children);\n  }\n\n  getVisibleView(key: Key): ReusableView<T, V> | undefined {\n    return this._visibleViews.get(key);\n  }\n\n  invalidate(context: InvalidationContext): void {\n    this.delegate.invalidate(context);\n  }\n\n  updateItemSize(key: Key, size: Size): void {\n    if (!this.layout.updateItemSize) {\n      return;\n    }\n\n    let changed = this.layout.updateItemSize(key, size);\n    if (changed) {\n      this.invalidate({\n        itemSizeChanged: true\n      });\n    }\n  }\n}\n"], "names": [], "version": 3, "file": "Virtualizer.main.js.map"}
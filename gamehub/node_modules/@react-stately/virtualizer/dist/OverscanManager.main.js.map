{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAKM,MAAM;IAKX,eAAe,IAAU,EAAQ;QAC/B,IAAI,OAAO,YAAY,GAAG,KAAK,IAAI,CAAC,SAAS;QAC7C,IAAI,OAAO,KAAK;YACd,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,OAAO,GAC1C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,AAAC,CAAA,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,AAAD,IAAK;YAGpD,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,OAAO,GAC1C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,AAAC,CAAA,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,AAAD,IAAK;QAEtD;QAEA,IAAI,CAAC,SAAS,GAAG,YAAY,GAAG;QAChC,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA,qBAA2B;QACzB,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC,IAAI;QAEvC,IAAI,YAAY,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QAC1C,YAAY,MAAM,IAAI;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GACpB,YAAY,CAAC,IAAI;QAGnB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG;YACzB,IAAI,YAAY,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;YACzC,YAAY,KAAK,IAAI;YACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GACpB,YAAY,CAAC,IAAI;QAErB;QAEA,OAAO;IACT;;aAtCQ,YAAY;aACZ,WAAW,IAAI,CAAA,GAAA,+BAAI,EAAE,GAAG;aACxB,cAAc,IAAI,CAAA,GAAA,8BAAG;;AAqC/B", "sources": ["packages/@react-stately/virtualizer/src/OverscanManager.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Point} from './Point';\nimport {Rect} from './Rect';\n\nexport class OverscanManager {\n  private startTime = 0;\n  private velocity = new Point(0, 0);\n  private visibleRect = new Rect();\n\n  setVisibleRect(rect: Rect): void {\n    let time = performance.now() - this.startTime;\n    if (time < 500) {\n      if (rect.x !== this.visibleRect.x && time > 0) {\n        this.velocity.x = (rect.x - this.visibleRect.x) / time;\n      }\n\n      if (rect.y !== this.visibleRect.y && time > 0) {\n        this.velocity.y = (rect.y - this.visibleRect.y) / time;\n      }\n    }\n\n    this.startTime = performance.now();\n    this.visibleRect = rect;\n  }\n\n  getOverscannedRect(): Rect {\n    let overscanned = this.visibleRect.copy();\n\n    let overscanY = this.visibleRect.height / 3;\n    overscanned.height += overscanY;\n    if (this.velocity.y < 0) {\n      overscanned.y -= overscanY;\n    }\n\n    if (this.velocity.x !== 0) {\n      let overscanX = this.visibleRect.width / 3;\n      overscanned.width += overscanX;\n      if (this.velocity.x < 0) {\n        overscanned.x -= overscanX;\n      }\n    }\n\n    return overscanned;\n  }\n}\n"], "names": [], "version": 3, "file": "OverscanManager.main.js.map"}
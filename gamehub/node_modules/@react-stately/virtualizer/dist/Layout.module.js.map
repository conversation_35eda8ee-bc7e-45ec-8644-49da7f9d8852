{"mappings": "AAAA;;;;;;;;;;CAUC,GAmBM,MAAe;IAuBpB;;;;;GAKC,GACD,iBAAiB,OAAa,EAAE,OAAa,EAAW;QACtD,+CAA+C;QAC/C,OAAO,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAC/B,QAAQ,MAAM,KAAK,QAAQ,MAAM;IAC1C;IAEA;;;;GAIC,GACD,8BAA8B,UAAa,EAAE,UAAa,EAAW;QACnE,OAAO,eAAe;IACxB;IAEA;;;;;GAKC,GACD,OAAO,mBAA2C,EAAQ,CAAC;IAY3D,aAAa,GACb,YAAY,GAAQ,EAAe;YAC1B;YAAA;QAAP,OAAO,CAAA,4BAAA,sBAAA,IAAI,CAAC,aAAa,CAAC,kBAAnB,0CAAA,oBAAyB,IAAI,cAA7B,sCAAA,2BAAiC;IAC1C;IAEA,aAAa,GACb,iBAAuB;QACrB,OAAO,IAAI,CAAC,WAAW,CAAE,WAAW;IACtC;;QArEA,yDAAyD,QACzD,cAA0C;;AAqE5C", "sources": ["packages/@react-stately/virtualizer/src/Layout.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {InvalidationContext} from './types';\nimport {ItemDropTarget, Key, LayoutDelegate, Node} from '@react-types/shared';\nimport {LayoutInfo} from './LayoutInfo';\nimport {Rect} from './Rect';\nimport {Size} from './Size';\nimport {Virtualizer} from './Virtualizer';\n\n/**\n * Virtualizer supports arbitrary layout objects, which compute what items are visible, and how\n * to position and style them. However, layouts do not render items directly. Instead,\n * layouts produce lightweight LayoutInfo objects which describe various properties of an item,\n * such as its position and size. The Virtualizer is then responsible for creating the actual\n * views as needed, based on this layout information.\n *\n * Every layout extends from the Layout abstract base class. Layouts must implement the `getVisibleLayoutInfos`,\n * `getLayoutInfo`, and `getContentSize` methods. All other methods can be optionally overridden to implement custom behavior.\n */\nexport abstract class Layout<T extends object = Node<any>, O = any> implements LayoutDelegate {\n  /** The Virtualizer the layout is currently attached to. */\n  virtualizer: Virtualizer<T, any> | null = null;\n\n  /**\n   * Returns an array of `LayoutInfo` objects which are inside the given rectangle.\n   * Should be implemented by subclasses.\n   * @param rect The rectangle that should contain the returned LayoutInfo objects.\n   */\n  abstract getVisibleLayoutInfos(rect: Rect): LayoutInfo[];\n\n  /**\n   * Returns a `LayoutInfo` for the given key.\n   * Should be implemented by subclasses.\n   * @param key The key of the LayoutInfo to retrieve.\n   */\n  abstract getLayoutInfo(key: Key): LayoutInfo | null;\n\n  /**\n   * Returns size of the content. By default, it returns virtualizer's size.\n   */\n  abstract getContentSize(): Size;  \n\n  /**\n   * Returns whether the layout should invalidate in response to\n   * visible rectangle changes. By default, it only invalidates\n   * when the virtualizer's size changes. Return true always\n   * to make the layout invalidate while scrolling (e.g. sticky headers).\n   */\n  shouldInvalidate(newRect: Rect, oldRect: Rect): boolean {\n    // By default, invalidate when the size changes\n    return newRect.width !== oldRect.width\n        || newRect.height !== oldRect.height;\n  }\n\n  /**\n   * Returns whether the layout should invalidate when the layout options change.\n   * By default it invalidates when the object identity changes. Override this\n   * method to optimize layout updates based on specific option changes.\n   */\n  shouldInvalidateLayoutOptions(newOptions: O, oldOptions: O): boolean {\n    return newOptions !== oldOptions;\n  }\n\n  /**\n   * This method allows the layout to perform any pre-computation\n   * it needs to in order to prepare LayoutInfos for retrieval.\n   * Called by the virtualizer before `getVisibleLayoutInfos`\n   * or `getLayoutInfo` are called.\n   */\n  update(invalidationContext: InvalidationContext<O>): void {} // eslint-disable-line @typescript-eslint/no-unused-vars\n\n  /**\n   * Updates the size of the given item.\n   */\n  updateItemSize?(key: Key, size: Size): boolean;\n\n  /**\n   * Returns a `LayoutInfo` for the given drop target.\n   */\n  getDropTargetLayoutInfo?(target: ItemDropTarget): LayoutInfo;\n\n  /** @private */\n  getItemRect(key: Key): Rect | null {\n    return this.getLayoutInfo(key)?.rect ?? null;\n  }\n\n  /** @private */\n  getVisibleRect(): Rect {\n    return this.virtualizer!.visibleRect;\n  }\n}\n"], "names": [], "version": 3, "file": "Layout.module.js.map"}
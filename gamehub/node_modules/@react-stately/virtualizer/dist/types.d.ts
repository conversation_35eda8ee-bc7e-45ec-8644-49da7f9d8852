import { Key, Collection, ItemDropTarget, LayoutDelegate, Node } from "@react-types/shared";
export class Point {
    /** The x-coordinate of the point. */
    x: number;
    /** The y-coordinate of the point. */
    y: number;
    constructor(x?: number, y?: number);
    /**
     * Returns a copy of this point.
     */
    copy(): Point;
    /**
     * Checks if two points are equal.
     */
    equals(point: Point): boolean;
    /**
     * Returns true if this point is the origin.
     */
    isOrigin(): boolean;
}
export class Size {
    width: number;
    height: number;
    constructor(width?: number, height?: number);
    /**
     * Returns a copy of this size.
     */
    copy(): Size;
    /**
     * Returns whether this size is equal to another one.
     */
    equals(other: Size): boolean;
    /**
     * The total area of the Size.
     */
    get area(): number;
}
export type RectCorner = 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
/**
 * Represents a rectangle.
 */
export class Rect {
    /** The x-coordinate of the rectangle. */
    x: number;
    /** The y-coordinate of the rectangle. */
    y: number;
    /** The width of the rectangle. */
    width: number;
    /** The height of the rectangle. */
    height: number;
    constructor(x?: number, y?: number, width?: number, height?: number);
    /**
     * The maximum x-coordinate in the rectangle.
     */
    get maxX(): number;
    /**
     * The maximum y-coordinate in the rectangle.
     */
    get maxY(): number;
    /**
     * The area of the rectangle.
     */
    get area(): number;
    /**
     * The top left corner of the rectangle.
     */
    get topLeft(): Point;
    /**
     * The top right corner of the rectangle.
     */
    get topRight(): Point;
    /**
     * The bottom left corner of the rectangle.
     */
    get bottomLeft(): Point;
    /**
     * The bottom right corner of the rectangle.
     */
    get bottomRight(): Point;
    /**
     * Returns whether this rectangle intersects another rectangle.
     * @param rect - The rectangle to check.
     */
    intersects(rect: Rect): boolean;
    /**
     * Returns whether this rectangle fully contains another rectangle.
     * @param rect - The rectangle to check.
     */
    containsRect(rect: Rect): boolean;
    /**
     * Returns whether the rectangle contains the given point.
     * @param point - The point to check.
     */
    containsPoint(point: Point): boolean;
    /**
     * Returns the first corner of this rectangle (from top to bottom, left to right)
     * that is contained in the given rectangle, or null of the rectangles do not intersect.
     * @param rect - The rectangle to check.
     */
    getCornerInRect(rect: Rect): RectCorner | null;
    equals(rect: Rect): boolean;
    pointEquals(point: Point | Rect): boolean;
    sizeEquals(size: Size | Rect): boolean;
    /**
     * Returns the union of this Rect and another.
     */
    union(other: Rect): Rect;
    /**
     * Returns the intersection of this Rect with another.
     * If the rectangles do not intersect, an all zero Rect is returned.
     */
    intersection(other: Rect): Rect;
    /**
     * Returns a copy of this rectangle.
     */
    copy(): Rect;
}
/**
 * Instances of this lightweight class are created by `Layout` subclasses
 * to represent each item in the `Virtualizer`. LayoutInfo objects describe
 * various properties of an item, such as its position and size, and style information.
 * The virtualizer uses this information when creating actual DOM elements to display.
 */
export class LayoutInfo {
    /**
     * The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.
     */
    type: string;
    /**
     * A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.
     */
    key: Key;
    /**
     * The key for a parent LayoutInfo, if any.
     */
    parentKey: Key | null;
    /**
     * Content for this item if it was generated by the layout rather than coming from the Collection.
     */
    content: any | null;
    /**
     * The rectangle describing the size and position of this element.
     */
    rect: Rect;
    /**
     * Whether the size is estimated. `false` by default.
     * Items with estimated sizes will be measured the first time they are added to the DOM.
     * The estimated size is used to calculate the size and position of the scrollbar.
     * @default false
     */
    estimatedSize: boolean;
    /**
     * Whether the layout info sticks to the viewport when scrolling.
     * @default false
     */
    isSticky: boolean;
    /**
     * The element's opacity.
     * @default 1
     */
    opacity: number;
    /**
     * A CSS transform string to apply to the element. `null` by default.
     */
    transform: string | null;
    /**
     * The z-index of the element. 0 by default.
     */
    zIndex: number;
    /**
     * Whether the element allows its contents to overflow its container.
     * @default false
     */
    allowOverflow: boolean;
    /**
     * @param type The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.
     * @param key A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.
     * @param rect The rectangle describing the size and position of this element.
     */
    constructor(type: string, key: Key, rect: Rect);
    /**
     * Returns a copy of the LayoutInfo.
     */
    copy(): LayoutInfo;
}
/**
 * `Virtualizer` creates instances of the `ReusableView` class to
 * represent views currently being displayed.
 */
export class ReusableView<T extends object, V> {
    /** The Virtualizer this view is a part of. */
    virtualizer: Virtualizer<T, V>;
    /** The LayoutInfo this view is currently representing. */
    layoutInfo: LayoutInfo | null;
    /** The content currently being displayed by this view, set by the virtualizer. */
    content: T | null;
    rendered: V | null;
    viewType: string;
    key: Key;
    children: Set<ChildView<T, V>>;
    reusableViews: Map<string, ChildView<T, V>[]>;
    constructor(virtualizer: Virtualizer<T, V>, viewType: string);
    /**
     * Prepares the view for reuse. Called just before the view is removed from the DOM.
     */
    prepareForReuse(): void;
    getReusableView(reuseType: string): ChildView<T, V>;
    reuseChild(child: ChildView<T, V>): void;
}
declare class ChildView<T extends object, V> extends ReusableView<T, V> {
    parent: ReusableView<T, V>;
    constructor(virtualizer: Virtualizer<T, V>, parent: ReusableView<T, V>, viewType: string);
}
interface VirtualizerOptions<T extends object, V> {
    delegate: VirtualizerDelegate<T, V>;
    collection: Collection<T>;
    layout: Layout<T>;
}
/**
 * The Virtualizer class renders a scrollable collection of data using customizable layouts.
 * It supports very large collections by only rendering visible views to the DOM, reusing
 * them as you scroll. Virtualizer can present any type of view, including non-item views
 * such as section headers and footers.
 *
 * Virtualizer uses `Layout` objects to compute what views should be visible, and how
 * to position and style them. This means that virtualizer can have its items arranged in
 * a stack, a grid, a circle, or any other layout you can think of. The layout can be changed
 * dynamically at runtime as well.
 *
 * Layouts produce information on what views should appear in the virtualizer, but do not create
 * the views themselves directly. It is the responsibility of the `VirtualizerDelegate` object
 * to render elements for each layout info. The virtualizer manages a set of `ReusableView` objects,
 * which are reused as the user scrolls by swapping their content with cached elements returned by the delegate.
 */
declare class Virtualizer<T extends object, V> {
    /**
     * The virtualizer delegate. The delegate is used by the virtualizer
     * to create and configure views.
     */
    delegate: VirtualizerDelegate<T, V>;
    /** The current content of the virtualizer. */
    readonly collection: Collection<T>;
    /** The layout object that determines the visible views. */
    readonly layout: Layout<T>;
    /** The size of the scrollable content. */
    readonly contentSize: Size;
    /** The currently visible rectangle. */
    readonly visibleRect: Rect;
    /** The set of persisted keys that are always present in the DOM, even if not currently in view. */
    readonly persistedKeys: Set<Key>;
    constructor(options: VirtualizerOptions<T, V>);
    /** Returns whether the given key, or an ancestor, is persisted. */
    isPersistedKey(key: Key): boolean;
    /**
     * Returns the key for the item view currently at the given point.
     */
    keyAtPoint(point: Point): Key | null;
    getVisibleLayoutInfos(): Map<Key, LayoutInfo>;
    /** Performs layout and updates visible views as needed. */
    render(opts: VirtualizerRenderOptions<T>): ReusableView<T, V>[];
    getVisibleView(key: Key): ReusableView<T, V> | undefined;
    invalidate(context: InvalidationContext): void;
    updateItemSize(key: Key, size: Size): void;
}
/**
 * Virtualizer supports arbitrary layout objects, which compute what items are visible, and how
 * to position and style them. However, layouts do not render items directly. Instead,
 * layouts produce lightweight LayoutInfo objects which describe various properties of an item,
 * such as its position and size. The Virtualizer is then responsible for creating the actual
 * views as needed, based on this layout information.
 *
 * Every layout extends from the Layout abstract base class. Layouts must implement the `getVisibleLayoutInfos`,
 * `getLayoutInfo`, and `getContentSize` methods. All other methods can be optionally overridden to implement custom behavior.
 */
export abstract class Layout<T extends object = Node<any>, O = any> implements LayoutDelegate {
    /** The Virtualizer the layout is currently attached to. */
    virtualizer: Virtualizer<T, any> | null;
    /**
     * Returns an array of `LayoutInfo` objects which are inside the given rectangle.
     * Should be implemented by subclasses.
     * @param rect The rectangle that should contain the returned LayoutInfo objects.
     */
    abstract getVisibleLayoutInfos(rect: Rect): LayoutInfo[];
    /**
     * Returns a `LayoutInfo` for the given key.
     * Should be implemented by subclasses.
     * @param key The key of the LayoutInfo to retrieve.
     */
    abstract getLayoutInfo(key: Key): LayoutInfo | null;
    /**
     * Returns size of the content. By default, it returns virtualizer's size.
     */
    abstract getContentSize(): Size;
    /**
     * Returns whether the layout should invalidate in response to
     * visible rectangle changes. By default, it only invalidates
     * when the virtualizer's size changes. Return true always
     * to make the layout invalidate while scrolling (e.g. sticky headers).
     */
    shouldInvalidate(newRect: Rect, oldRect: Rect): boolean;
    /**
     * Returns whether the layout should invalidate when the layout options change.
     * By default it invalidates when the object identity changes. Override this
     * method to optimize layout updates based on specific option changes.
     */
    shouldInvalidateLayoutOptions(newOptions: O, oldOptions: O): boolean;
    /**
     * This method allows the layout to perform any pre-computation
     * it needs to in order to prepare LayoutInfos for retrieval.
     * Called by the virtualizer before `getVisibleLayoutInfos`
     * or `getLayoutInfo` are called.
     */
    update(invalidationContext: InvalidationContext<O>): void;
    /**
     * Updates the size of the given item.
     */
    updateItemSize?(key: Key, size: Size): boolean;
    /**
     * Returns a `LayoutInfo` for the given drop target.
     */
    getDropTargetLayoutInfo?(target: ItemDropTarget): LayoutInfo;
    /** @private */
    getItemRect(key: Key): Rect | null;
    /** @private */
    getVisibleRect(): Rect;
}
export interface InvalidationContext<O = any> {
    contentChanged?: boolean;
    offsetChanged?: boolean;
    sizeChanged?: boolean;
    itemSizeChanged?: boolean;
    layoutOptionsChanged?: boolean;
    layoutOptions?: O;
}
interface VirtualizerDelegate<T extends object, V> {
    setVisibleRect(rect: Rect): void;
    renderView(type: string, content: T | null): V;
    invalidate(ctx: InvalidationContext): void;
}
interface VirtualizerRenderOptions<T extends object, O = any> {
    layout: Layout<T>;
    collection: Collection<T>;
    persistedKeys?: Set<Key> | null;
    visibleRect: Rect;
    invalidationContext: InvalidationContext;
    isScrolling: boolean;
    layoutOptions?: O;
}
interface VirtualizerProps<T extends object, V, O> {
    renderView(type: string, content: T | null): V;
    layout: Layout<T>;
    collection: Collection<T>;
    onVisibleRectChange(rect: Rect): void;
    persistedKeys?: Set<Key> | null;
    layoutOptions?: O;
}
export interface VirtualizerState<T extends object, V> {
    visibleViews: ReusableView<T, V>[];
    setVisibleRect: (rect: Rect) => void;
    contentSize: Size;
    virtualizer: Virtualizer<T, V>;
    isScrolling: boolean;
    startScrolling: () => void;
    endScrolling: () => void;
}
export function useVirtualizerState<T extends object, V, O = any>(opts: VirtualizerProps<T, V, O>): VirtualizerState<T, V>;

//# sourceMappingURL=types.d.ts.map

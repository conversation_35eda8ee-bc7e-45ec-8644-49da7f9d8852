{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAEM,MAAM;IASX;;GAEC,GACD,OAAa;QACX,OAAO,IAAI,0CAAK,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;IACzC;IAEA;;GAEC,GACD,OAAO,KAAW,EAAW;QAC3B,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,IAC1B,IAAI,CAAC,MAAM,KAAK,MAAM,MAAM;IACrC;IAEA;;GAEC,GACD,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;IACjC;IAzBA,YAAY,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAE;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,OAAO;QAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,QAAQ;IACjC;AAuBF", "sources": ["packages/@react-stately/virtualizer/src/Size.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport class Size {\n  width: number;\n  height: number;\n\n  constructor(width = 0, height = 0) {\n    this.width = Math.max(width, 0);\n    this.height = Math.max(height, 0);\n  }\n\n  /**\n   * Returns a copy of this size.\n   */\n  copy(): Size {\n    return new Size(this.width, this.height);\n  }\n\n  /**\n   * Returns whether this size is equal to another one.\n   */\n  equals(other: Size): boolean {\n    return this.width === other.width\n        && this.height === other.height;\n  }\n\n  /**\n   * The total area of the Size.\n   */\n  get area(): number {\n    return this.width * this.height;\n  }\n}\n"], "names": [], "version": 3, "file": "Size.main.js.map"}
{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAED,wCAAwC,GACjC,SAAS,0CAAc,CAAS,EAAE,CAAS;IAChD,IAAI,MAAM,GACR,OAAO;IAGT,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EACnB,OAAO;IAGT,KAAK,IAAI,OAAO,EAAG;QACjB,IAAI,CAAC,EAAE,GAAG,CAAC,MACT,OAAO;IAEX;IAEA,OAAO;AACT", "sources": ["packages/@react-stately/virtualizer/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/** Returns whether two sets are equal. */\nexport function isSetEqual<T>(a: Set<T>, b: Set<T>): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (let key of a) {\n    if (!b.has(key)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "names": [], "version": 3, "file": "utils.main.js.map"}
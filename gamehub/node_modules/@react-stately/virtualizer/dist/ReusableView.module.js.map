{"mappings": "AAAA;;;;;;;;;;CAUC,GAMD,IAAI,4BAAM;AAMH,MAAM;IA6BX;;GAEC,GACD,kBAAwB;QACtB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,gBAAgB,SAAiB,EAAmB;QAClD,4FAA4F;QAC5F,oGAAoG;QACpG,iGAAiG;QACjG,sEAAsE;QACtE,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,OAAO,YAAY,SAAS,MAAM,GAAG,IACrC,SAAS,KAAK,KACd,IAAI,0CAAgB,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;QAEhD,OAAO;IACT;IAEA,WAAW,KAAsB,EAAQ;QACvC,MAAM,eAAe;QACrB,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,QAAQ;QACpD,IAAI,CAAC,UAAU;YACb,WAAW,EAAE;YACb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,QAAQ,EAAE;QACzC;QACA,SAAS,IAAI,CAAC;IAChB;IAzCA,YAAY,WAA8B,EAAE,QAAgB,CAAE;QAC5D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG,EAAE;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;IAClB;AAiCF;AAEO,MAAM,kDAAsC;IACjD,YAAY,WAA8B,CAAE;QAC1C,KAAK,CAAC,aAAa;IACrB;AACF;AAEO,MAAM,kDAAuC;IAGlD,YAAY,WAA8B,EAAE,MAA0B,EAAE,QAAgB,CAAE;QACxF,KAAK,CAAC,aAAa;QACnB,IAAI,CAAC,MAAM,GAAG;IAChB;AACF", "sources": ["packages/@react-stately/virtualizer/src/ReusableView.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {LayoutInfo} from './LayoutInfo';\nimport {Virtualizer} from './Virtualizer';\n\nlet KEY = 0;\n\n/**\n * `Virtualizer` creates instances of the `ReusableView` class to\n * represent views currently being displayed.\n */\nexport class ReusableView<T extends object, V> {\n  /** The Virtualizer this view is a part of. */\n  virtualizer: Virtualizer<T, V>;\n\n  /** The LayoutInfo this view is currently representing. */\n  layoutInfo: LayoutInfo | null;\n\n  /** The content currently being displayed by this view, set by the virtualizer. */\n  content: T | null;\n\n  rendered: V | null;\n\n  viewType: string;\n  key: Key;\n\n  children: Set<ChildView<T, V>>;\n  reusableViews: Map<string, ChildView<T, V>[]>;\n\n  constructor(virtualizer: Virtualizer<T, V>, viewType: string) {\n    this.virtualizer = virtualizer;\n    this.key = ++KEY;\n    this.viewType = viewType;\n    this.children = new Set();\n    this.reusableViews = new Map();\n    this.layoutInfo = null;\n    this.content = null;\n    this.rendered = null;\n  }\n\n  /**\n   * Prepares the view for reuse. Called just before the view is removed from the DOM.\n   */\n  prepareForReuse(): void {\n    this.content = null;\n    this.rendered = null;\n    this.layoutInfo = null;\n  }\n\n  getReusableView(reuseType: string): ChildView<T, V> {\n    // Reusable view queue should be FIFO so that DOM order remains consistent during scrolling.\n    // For example, cells within a row should remain in the same order even if the row changes contents.\n    // The cells within a row are removed from their parent in order. If the row is reused, the cells\n    // should be reused in the new row in the same order they were before.\n    let reusable = this.reusableViews.get(reuseType);\n    let view = reusable && reusable.length > 0\n      ? reusable.shift()!\n      : new ChildView<T, V>(this.virtualizer, this, reuseType);\n\n    return view;\n  }\n\n  reuseChild(child: ChildView<T, V>): void {\n    child.prepareForReuse();\n    let reusable = this.reusableViews.get(child.viewType);\n    if (!reusable) {\n      reusable = [];\n      this.reusableViews.set(child.viewType, reusable);\n    }\n    reusable.push(child);\n  }\n}\n\nexport class RootView<T extends object, V> extends ReusableView<T, V> {\n  constructor(virtualizer: Virtualizer<T, V>) {\n    super(virtualizer, 'root');\n  }\n}\n\nexport class ChildView<T extends object, V> extends ReusableView<T, V> {\n  parent: ReusableView<T, V>;\n\n  constructor(virtualizer: Virtualizer<T, V>, parent: ReusableView<T, V>, viewType: string) {\n    super(virtualizer, viewType);\n    this.parent = parent;\n  }\n}\n"], "names": [], "version": 3, "file": "ReusableView.module.js.map"}
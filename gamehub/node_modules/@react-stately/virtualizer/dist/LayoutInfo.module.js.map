{"mappings": "AAAA;;;;;;;;;;CAUC,GAWM,MAAM;IAiFX;;GAEC,GACD,OAAmB;QACjB,IAAI,MAAM,IAAI,0CAAW,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;QAC5D,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;QACtC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO;QAC1B,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;QAC9B,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO;QAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;QACxB,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;QACtC,OAAO;IACT;IAjCA;;;;GAIC,GACD,YAAY,IAAY,EAAE,GAAQ,EAAE,IAAU,CAAE;QAC9C,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG;IACvB;AAiBF", "sources": ["packages/@react-stately/virtualizer/src/LayoutInfo.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {Rect} from './Rect';\n\n/**\n * Instances of this lightweight class are created by `Layout` subclasses\n * to represent each item in the `Virtualizer`. LayoutInfo objects describe\n * various properties of an item, such as its position and size, and style information.\n * The virtualizer uses this information when creating actual DOM elements to display.\n */\nexport class LayoutInfo {\n  /**\n   * The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.\n   */\n  type: string;\n\n  /**\n   * A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.\n   */\n  key: Key;\n\n  /**\n   * The key for a parent LayoutInfo, if any.\n   */\n  parentKey: Key | null;\n\n  /** \n   * Content for this item if it was generated by the layout rather than coming from the Collection.\n   */\n  content: any | null;\n\n  /**\n   * The rectangle describing the size and position of this element.\n   */\n  rect: Rect;\n\n  /**\n   * Whether the size is estimated. `false` by default.\n   * Items with estimated sizes will be measured the first time they are added to the DOM.\n   * The estimated size is used to calculate the size and position of the scrollbar.\n   * @default false\n   */\n  estimatedSize: boolean;\n\n  /**\n   * Whether the layout info sticks to the viewport when scrolling.\n   * @default false\n   */\n  isSticky: boolean;\n\n  /**\n   * The element's opacity.\n   * @default 1\n   */\n  opacity: number;\n\n  /**\n   * A CSS transform string to apply to the element. `null` by default.\n   */\n  transform: string | null;\n\n  /**\n   * The z-index of the element. 0 by default.\n   */\n  zIndex: number;\n\n  /**\n   * Whether the element allows its contents to overflow its container.\n   * @default false\n   */\n  allowOverflow: boolean;\n\n  /**\n   * @param type The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.\n   * @param key A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.\n   * @param rect The rectangle describing the size and position of this element.\n   */\n  constructor(type: string, key: Key, rect: Rect) {\n    this.type = type;\n    this.key = key;\n    this.parentKey = null;\n    this.content = null;\n    this.rect = rect;\n    this.estimatedSize = false;\n    this.isSticky = false;\n    this.opacity = 1;\n    this.transform = null;\n    this.zIndex = 0;\n    this.allowOverflow = false;\n  }\n\n  /**\n   * Returns a copy of the LayoutInfo.\n   */\n  copy(): LayoutInfo {\n    let res = new LayoutInfo(this.type, this.key, this.rect.copy());\n    res.estimatedSize = this.estimatedSize;\n    res.opacity = this.opacity;\n    res.transform = this.transform;\n    res.parentKey = this.parentKey;\n    res.content = this.content;\n    res.isSticky = this.isSticky;\n    res.zIndex = this.zIndex;\n    res.allowOverflow = this.allowOverflow;\n    return res;\n  }\n}\n"], "names": [], "version": 3, "file": "LayoutInfo.module.js.map"}
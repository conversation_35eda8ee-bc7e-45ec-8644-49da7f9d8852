/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */

import {Key} from '@react-types/shared';
import {Rect} from './Rect';

/**
 * Instances of this lightweight class are created by `Layout` subclasses
 * to represent each item in the `Virtualizer`. LayoutInfo objects describe
 * various properties of an item, such as its position and size, and style information.
 * The virtualizer uses this information when creating actual DOM elements to display.
 */
export class LayoutInfo {
  /**
   * The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.
   */
  type: string;

  /**
   * A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.
   */
  key: Key;

  /**
   * The key for a parent LayoutInfo, if any.
   */
  parentKey: Key | null;

  /** 
   * Content for this item if it was generated by the layout rather than coming from the Collection.
   */
  content: any | null;

  /**
   * The rectangle describing the size and position of this element.
   */
  rect: Rect;

  /**
   * Whether the size is estimated. `false` by default.
   * Items with estimated sizes will be measured the first time they are added to the DOM.
   * The estimated size is used to calculate the size and position of the scrollbar.
   * @default false
   */
  estimatedSize: boolean;

  /**
   * Whether the layout info sticks to the viewport when scrolling.
   * @default false
   */
  isSticky: boolean;

  /**
   * The element's opacity.
   * @default 1
   */
  opacity: number;

  /**
   * A CSS transform string to apply to the element. `null` by default.
   */
  transform: string | null;

  /**
   * The z-index of the element. 0 by default.
   */
  zIndex: number;

  /**
   * Whether the element allows its contents to overflow its container.
   * @default false
   */
  allowOverflow: boolean;

  /**
   * @param type The type of element represented by this LayoutInfo. Should match the `type` of the corresponding collection node.
   * @param key A unique key for this LayoutInfo. Should match the `key` of the corresponding collection node.
   * @param rect The rectangle describing the size and position of this element.
   */
  constructor(type: string, key: Key, rect: Rect) {
    this.type = type;
    this.key = key;
    this.parentKey = null;
    this.content = null;
    this.rect = rect;
    this.estimatedSize = false;
    this.isSticky = false;
    this.opacity = 1;
    this.transform = null;
    this.zIndex = 0;
    this.allowOverflow = false;
  }

  /**
   * Returns a copy of the LayoutInfo.
   */
  copy(): LayoutInfo {
    let res = new LayoutInfo(this.type, this.key, this.rect.copy());
    res.estimatedSize = this.estimatedSize;
    res.opacity = this.opacity;
    res.transform = this.transform;
    res.parentKey = this.parentKey;
    res.content = this.content;
    res.isSticky = this.isSticky;
    res.zIndex = this.zIndex;
    res.allowOverflow = this.allowOverflow;
    return res;
  }
}

{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAaM,SAAS,0CAAU,IAAe,EAAE,QAA2B,EAAE,QAA2B;IACjG,OAAO,AAAC,YAAY,QAAQ,KAAK,OAAO,CAAC,YAAY,KAClD,YAAY,QAAQ,KAAK,OAAO,CAAC,YAAY;AAClD;AAEO,SAAS,0CAAY,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC9I,IAAI,eAA6B,CAAC;IAClC,IAAK,IAAI,OAAO,SAAU;QACxB,YAAY,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG;QAC/C,IAAI,YAAY,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI,GAAG,MAAM,GACjD,YAAY,CAAC,IAAI;IAErB;IAEA,IAAI,UAAU,yCAAW,MAAM,UAAU,QAAQ,QAAQ,CAAC;IAC1D,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,yCAAW,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC7I,yCAAyC;IACzC,IAAI,UAAU;IACd,IAAI,SAAS,KAAK,EAChB,UAAU,CAAA,GAAA,kBAAU,EAAE;SACjB,IAAI,SAAS,MAAM,EACxB,UAAU,CAAA,GAAA,mBAAW,EAAE;SAClB,IAAI,SAAS,KAAK,EACvB,UAAU,CAAA,GAAA,kBAAU,EAAE,MAAM;IAG9B,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,0CAAS,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC3I,IAAI,IAAI;QAAC,GAAG,QAAQ;IAAA;IACpB,oCAAoC;IACpC,IAAI,EAAE,IAAI,EACR,EAAE,IAAI;SACD,IAAI,EAAE,KAAK,EAChB,EAAE,KAAK;SACF,IAAI,EAAE,MAAM,EACjB,EAAE,MAAM;SACH,IAAI,EAAE,KAAK,EAChB,EAAE,KAAK;IAGT,IAAI,UAAU,yCAAW,MAAM,UAAU,QAAQ,QAAQ,CAAC;IAC1D,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,0CACd,IAAkB,EAClB,OAAqB,EACrB,QAAsB,EACtB,MAAc,EACd,QAA2B,EAC3B,QAA2B;IAC3B,IAAI,YAAY,KAAK,OAAO,CAAC,aAAa,GAAG;QAC3C,IAAI,UAAU,CAAA,GAAA,cAAM,EAClB,SACA,yCAAW,CAAA,GAAA,qBAAa,EAAE,WAAW,UAAU;QAEjD,IAAI,SACF,UAAU;IAEd;IAEA,IAAI,YAAY,KAAK,OAAO,CAAC,aAAa,GAAG;QAC3C,IAAI,UAAU,CAAA,GAAA,cAAM,EAClB,SACA,0CAAS,CAAA,GAAA,qBAAa,EAAE,WAAW,UAAU;QAE/C,IAAI,SACF,UAAU;IAEd;IAEA,OAAO;AACT;AAEO,SAAS,0CAAe,IAAkB,EAAE,QAA2B,EAAE,QAA2B;IACzG,IAAI,UAAU;QACZ,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,MAAM,CAAA,GAAA,qBAAa,EAAE;QAC3C,IAAI,SACF,OAAO;IAEX;IAEA,IAAI,UAAU;QACZ,IAAI,UAAU,CAAA,GAAA,cAAM,EAAE,MAAM,CAAA,GAAA,qBAAa,EAAE;QAC3C,IAAI,SACF,OAAO;IAEX;IAEA,OAAO;AACT;AAEO,SAAS,0CAAsB,IAAkB,EAAE,QAAmB,EAAE,iBAAmD;IAChI,IAAI,CAAC,mBACH,OAAO;IAGT,MAAO,KAAK,OAAO,CAAC,aAAa,KAAK,kBAAkB,MACtD,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM;IAAC;IAG/B,IAAI,KAAK,OAAO,CAAC,aAAa,GAC5B,OAAO;IAET,OAAO;AACT", "sources": ["packages/@react-stately/calendar/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nimport {\n  CalendarDate,\n  DateDuration,\n  maxDate,\n  minDate,\n  startOfMonth,\n  startOfWeek,\n  startOfYear,\n  toCalendarDate\n} from '@internationalized/date';\nimport {DateValue} from '@react-types/calendar';\n\nexport function isInvalid(date: DateValue, minValue?: DateValue | null, maxValue?: DateValue | null): boolean {\n  return (minValue != null && date.compare(minValue) < 0) ||\n    (maxValue != null && date.compare(maxValue) > 0);\n}\n\nexport function alignCenter(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  let halfDuration: DateDuration = {};\n  for (let key in duration) {\n    halfDuration[key] = Math.floor(duration[key] / 2);\n    if (halfDuration[key] > 0 && duration[key] % 2 === 0) {\n      halfDuration[key]--;\n    }\n  }\n\n  let aligned = alignStart(date, duration, locale).subtract(halfDuration);\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function alignStart(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  // align to the start of the largest unit\n  let aligned = date;\n  if (duration.years) {\n    aligned = startOfYear(date);\n  } else if (duration.months) {\n    aligned = startOfMonth(date);\n  } else if (duration.weeks) {\n    aligned = startOfWeek(date, locale);\n  }\n\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function alignEnd(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  let d = {...duration};\n  // subtract 1 from the smallest unit\n  if (d.days) {\n    d.days--;\n  } else if (d.weeks) {\n    d.weeks--;\n  } else if (d.months) {\n    d.months--;\n  } else if (d.years) {\n    d.years--;\n  }\n\n  let aligned = alignStart(date, duration, locale).subtract(d);\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function constrainStart(\n  date: CalendarDate,\n  aligned: CalendarDate,\n  duration: DateDuration,\n  locale: string,\n  minValue?: DateValue | null,\n  maxValue?: DateValue | null): CalendarDate {\n  if (minValue && date.compare(minValue) >= 0) {\n    let newDate = maxDate(\n      aligned,\n      alignStart(toCalendarDate(minValue), duration, locale)\n    );\n    if (newDate) {\n      aligned = newDate;\n    }\n  }\n\n  if (maxValue && date.compare(maxValue) <= 0) {\n    let newDate = minDate(\n      aligned,\n      alignEnd(toCalendarDate(maxValue), duration, locale)\n    );\n    if (newDate) {\n      aligned = newDate;\n    }\n  }\n\n  return aligned;\n}\n\nexport function constrainValue(date: CalendarDate, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  if (minValue) {\n    let newDate = maxDate(date, toCalendarDate(minValue));\n    if (newDate) {\n      date = newDate;\n    }\n  }\n\n  if (maxValue) {\n    let newDate = minDate(date, toCalendarDate(maxValue));\n    if (newDate) {\n      date = newDate;\n    }\n  }\n\n  return date;\n}\n\nexport function previousAvailableDate(date: CalendarDate, minValue: DateValue, isDateUnavailable?: (date: CalendarDate) => boolean): CalendarDate | null {\n  if (!isDateUnavailable) {\n    return date;\n  }\n\n  while (date.compare(minValue) >= 0 && isDateUnavailable(date)) {\n    date = date.subtract({days: 1});\n  }\n\n  if (date.compare(minValue) >= 0) {\n    return date;\n  }\n  return null;\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}
import { Collection, Key, Node, CollectionStateBase, LayoutDelegate, SingleSelection } from "@react-types/shared";
import { MultipleSelectionStateProps, SelectionManager } from "@react-stately/selection";
export class ListCollection<T> implements Collection<Node<T>> {
    constructor(nodes: Iterable<Node<T>>);
    [Symbol.iterator](): IterableIterator<Node<T>>;
    get size(): number;
    getKeys(): IterableIterator<Key>;
    getKeyBefore(key: Key): Key | null;
    getKeyAfter(key: Key): Key | null;
    getFirstKey(): Key | null;
    getLastKey(): Key | null;
    getItem(key: Key): Node<T> | null;
    at(idx: number): Node<T> | null;
    getChildren(key: Key): Iterable<Node<T>>;
}
export interface ListProps<T> extends CollectionStateBase<T>, MultipleSelectionStateProps {
    /** Filter function to generate a filtered list of nodes. */
    filter?: (nodes: Iterable<Node<T>>) => Iterable<Node<T>>;
    /** @private */
    suppressTextValueWarning?: boolean;
    /**
     * A delegate object that provides layout information for items in the collection.
     * This can be used to override the behavior of shift selection.
     */
    layoutDelegate?: LayoutDelegate;
}
export interface ListState<T> {
    /** A collection of items in the list. */
    collection: Collection<Node<T>>;
    /** A set of items that are disabled. */
    disabledKeys: Set<Key>;
    /** A selection manager to read and update multiple selection state. */
    selectionManager: SelectionManager;
}
/**
 * Provides state management for list-like components. Handles building a collection
 * of items from props, and manages multiple selection state.
 */
export function useListState<T extends object>(props: ListProps<T>): ListState<T>;
/**
 * Filters a collection using the provided filter function and returns a new ListState.
 */
export function UNSTABLE_useFilteredListState<T extends object>(state: ListState<T>, filter: ((nodeValue: string) => boolean) | null | undefined): ListState<T>;
export interface SingleSelectListProps<T> extends CollectionStateBase<T>, Omit<SingleSelection, 'disallowEmptySelection'> {
    /** Filter function to generate a filtered list of nodes. */
    filter?: (nodes: Iterable<Node<T>>) => Iterable<Node<T>>;
    /** @private */
    suppressTextValueWarning?: boolean;
}
export interface SingleSelectListState<T> extends ListState<T> {
    /** The key for the currently selected item. */
    readonly selectedKey: Key | null;
    /** Sets the selected key. */
    setSelectedKey(key: Key | null): void;
    /** The value of the currently selected item. */
    readonly selectedItem: Node<T> | null;
}
/**
 * Provides state management for list-like components with single selection.
 * Handles building a collection of items from props, and manages selection state.
 */
export function useSingleSelectListState<T extends object>(props: SingleSelectListProps<T>): SingleSelectListState<T>;

//# sourceMappingURL=types.d.ts.map

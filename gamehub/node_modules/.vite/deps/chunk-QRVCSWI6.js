import {
  LinkIcon
} from "./chunk-5IPK33QS.js";
import {
  $f7dceffc5ad7768b$export$4e328f61c538687f,
  useDOMRef,
  useProviderContext
} from "./chunk-NHQQQ5IV.js";
import {
  $3ef42575df84b30b$export$9d1611c77c2fe928,
  $65484d02dcb7eb3e$export$457c3d6518dd4c6f,
  $ea8dcbcb9ea1b556$export$7e924b3091a3bd18,
  $ea8dcbcb9ea1b556$export$9a302a45f65d0572,
  $ea8dcbcb9ea1b556$export$efa8c9099e530235,
  $f645667febf57a63$export$4c014de7c8940b4c,
  $f6c31cce2adf654f$export$45712eceda6fad21
} from "./chunk-EWPKFISU.js";
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-5K7QB4ZL.js";
import {
  dataAttr,
  link,
  linkAnchorClasses,
  objectToDeps
} from "./chunk-CMXIESOC.js";
import {
  require_jsx_runtime
} from "./chunk-HVLLINLV.js";
import {
  require_react
} from "./chunk-HKLPI2XQ.js";
import {
  __toESM
} from "./chunk-ZS7NZCD4.js";

// node_modules/@heroui/link/dist/chunk-7LH7ZARU.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var LinkIcon2 = () => (0, import_jsx_runtime.jsxs)(
  "svg",
  {
    "aria-hidden": "true",
    className: "flex mx-1 text-current self-center",
    fill: "none",
    height: "1em",
    shapeRendering: "geometricPrecision",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "1.5",
    viewBox: "0 0 24 24",
    width: "1em",
    children: [
      (0, import_jsx_runtime.jsx)("path", { d: "M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6" }),
      (0, import_jsx_runtime.jsx)("path", { d: "M15 3h6v6" }),
      (0, import_jsx_runtime.jsx)("path", { d: "M10 14L21 3" })
    ]
  }
);

// node_modules/@heroui/use-aria-link/dist/index.mjs
function useAriaLink(props, ref) {
  let {
    elementType = "a",
    onPress,
    onPressStart,
    onPressEnd,
    onClick,
    isDisabled,
    ...otherProps
  } = props;
  let linkProps = {};
  if (elementType !== "a") {
    linkProps = {
      role: "link",
      tabIndex: !isDisabled ? 0 : void 0
    };
  }
  let { focusableProps } = $f645667febf57a63$export$4c014de7c8940b4c(props, ref);
  let { pressProps, isPressed } = $f6c31cce2adf654f$export$45712eceda6fad21({
    onClick,
    onPress,
    onPressStart,
    onPressEnd,
    isDisabled,
    ref
  });
  let domProps = $65484d02dcb7eb3e$export$457c3d6518dd4c6f(otherProps, { labelable: true, isLink: elementType === "a" });
  let interactionHandlers = $3ef42575df84b30b$export$9d1611c77c2fe928(focusableProps, pressProps);
  let router = $ea8dcbcb9ea1b556$export$9a302a45f65d0572();
  let routerLinkProps = $ea8dcbcb9ea1b556$export$7e924b3091a3bd18(props);
  return {
    isPressed,
    // Used to indicate press state for visual
    linkProps: $3ef42575df84b30b$export$9d1611c77c2fe928(domProps, routerLinkProps, {
      ...interactionHandlers,
      ...linkProps,
      "aria-disabled": isDisabled || void 0,
      "aria-current": props["aria-current"],
      onClick: (e) => {
        var _a;
        (_a = pressProps.onClick) == null ? void 0 : _a.call(pressProps, e);
        if (!router.isNative && e.currentTarget instanceof HTMLAnchorElement && e.currentTarget.href && // If props are applied to a router Link component, it may have already prevented default.
        !e.isDefaultPrevented() && $ea8dcbcb9ea1b556$export$efa8c9099e530235(e.currentTarget, e) && props.href) {
          e.preventDefault();
          router.open(e.currentTarget, e, props.href, props.routerOptions);
        }
      }
    })
  };
}

// node_modules/@heroui/link/dist/chunk-SGLWUJCW.mjs
var import_react = __toESM(require_react(), 1);
function useLink(originalProps) {
  var _a, _b, _c, _d;
  const globalContext = useProviderContext();
  const [props, variantProps] = mapPropsVariants(originalProps, link.variantKeys);
  const {
    ref,
    as,
    children,
    anchorIcon,
    isExternal = false,
    showAnchorIcon = false,
    autoFocus = false,
    className,
    onPress,
    onPressStart,
    onPressEnd,
    onClick,
    ...otherProps
  } = props;
  const Component = as || "a";
  const domRef = useDOMRef(ref);
  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const { linkProps } = useAriaLink(
    {
      ...otherProps,
      onPress,
      onPressStart,
      onPressEnd,
      // @ts-ignore React Aria Link does accept onClick as a prop but it's not in the types
      onClick,
      isDisabled: originalProps.isDisabled,
      elementType: `${as}`
    },
    domRef
  );
  const { isFocused, isFocusVisible, focusProps } = $f7dceffc5ad7768b$export$4e328f61c538687f({
    autoFocus
  });
  if (isExternal) {
    otherProps.rel = (_c = otherProps.rel) != null ? _c : "noopener noreferrer";
    otherProps.target = (_d = otherProps.target) != null ? _d : "_blank";
  }
  const styles = (0, import_react.useMemo)(
    () => link({
      ...variantProps,
      disableAnimation,
      className
    }),
    [objectToDeps(variantProps), disableAnimation, className]
  );
  const getLinkProps = (0, import_react.useCallback)(() => {
    return {
      ref: domRef,
      className: styles,
      "data-focus": dataAttr(isFocused),
      "data-disabled": dataAttr(originalProps.isDisabled),
      "data-focus-visible": dataAttr(isFocusVisible),
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(focusProps, linkProps, otherProps)
    };
  }, [styles, isFocused, isFocusVisible, focusProps, linkProps, otherProps]);
  return { Component, children, anchorIcon, showAnchorIcon, getLinkProps };
}

// node_modules/@heroui/link/dist/chunk-T45N425O.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var Link = forwardRef((props, ref) => {
  const {
    Component,
    children,
    showAnchorIcon,
    anchorIcon = (0, import_jsx_runtime2.jsx)(LinkIcon, { className: linkAnchorClasses }),
    getLinkProps
  } = useLink({
    ref,
    ...props
  });
  return (0, import_jsx_runtime2.jsx)(Component, { ...getLinkProps(), children: (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, { children: [
    children,
    showAnchorIcon && anchorIcon
  ] }) });
});
Link.displayName = "HeroUI.Link";
var link_default = Link;

export {
  LinkIcon2 as LinkIcon,
  useLink,
  link_default
};
//# sourceMappingURL=chunk-QRVCSWI6.js.map

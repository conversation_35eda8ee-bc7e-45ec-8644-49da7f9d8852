{"version": 3, "sources": ["../../@heroui/kbd/dist/chunk-33JVVCIB.mjs", "../../@heroui/kbd/dist/chunk-ZWTE5ZFD.mjs", "../../@heroui/kbd/dist/chunk-EZX35FZF.mjs"], "sourcesContent": ["// src/use-kbd.ts\nimport { mapPropsVariants } from \"@heroui/system-rsc\";\nimport { kbd } from \"@heroui/theme\";\nimport { clsx, objectToDeps } from \"@heroui/shared-utils\";\nimport { useMemo } from \"react\";\nfunction useKbd(originalProps) {\n  const [props, variantProps] = mapPropsVariants(originalProps, kbd.variantKeys);\n  const { as, children, className, keys, title, classNames, ...otherProps } = props;\n  const Component = as || \"kbd\";\n  const slots = useMemo(\n    () => kbd({\n      ...variantProps\n    }),\n    [objectToDeps(variantProps)]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const keysToRender = typeof keys === \"string\" ? [keys] : Array.isArray(keys) ? keys : [];\n  const getKbdProps = (props2 = {}) => ({\n    ...otherProps,\n    ...props2,\n    className: clsx(slots.base({ class: clsx(baseStyles, props2.className) }))\n  });\n  return { Component, slots, classNames, title, children, keysToRender, getKbdProps };\n}\n\nexport {\n  useKbd\n};\n", "// src/utils.ts\nvar kbdKeysMap = {\n  command: \"\\u2318\",\n  shift: \"\\u21E7\",\n  ctrl: \"\\u2303\",\n  option: \"\\u2325\",\n  enter: \"\\u21B5\",\n  delete: \"\\u232B\",\n  escape: \"\\u238B\",\n  tab: \"\\u21E5\",\n  capslock: \"\\u21EA\",\n  up: \"\\u2191\",\n  right: \"\\u2192\",\n  down: \"\\u2193\",\n  left: \"\\u2190\",\n  pageup: \"\\u21DE\",\n  pagedown: \"\\u21DF\",\n  home: \"\\u2196\",\n  end: \"\\u2198\",\n  help: \"?\",\n  space: \"\\u2423\",\n  fn: \"Fn\",\n  win: \"\\u2318\",\n  alt: \"\\u2325\"\n};\nvar kbdKeysLabelMap = {\n  command: \"Command\",\n  shift: \"Shift\",\n  ctrl: \"Control\",\n  option: \"Option\",\n  enter: \"Enter\",\n  delete: \"Delete\",\n  escape: \"Escape\",\n  tab: \"Tab\",\n  capslock: \"Caps Lock\",\n  up: \"Up\",\n  right: \"Right\",\n  down: \"Down\",\n  left: \"Left\",\n  pageup: \"Page Up\",\n  pagedown: \"Page Down\",\n  home: \"Home\",\n  end: \"End\",\n  help: \"Help\",\n  space: \"Space\",\n  fn: \"Fn\",\n  win: \"Win\",\n  alt: \"Alt\"\n};\n\nexport {\n  kbdKeysMap,\n  kbdKeysLabelMap\n};\n", "import {\n  useKbd\n} from \"./chunk-33JVVCIB.mjs\";\nimport {\n  kbdKeysLabelMap,\n  kbdKeysMap\n} from \"./chunk-ZWTE5ZFD.mjs\";\n\n// src/kbd.tsx\nimport { useMemo } from \"react\";\nimport { forwardRef } from \"@heroui/system-rsc\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Kbd = forwardRef((props, ref) => {\n  const { Component, children, slots, classNames, keysToRender, getKbdProps } = useKbd({\n    ...props\n  });\n  const keysContent = useMemo(() => {\n    return keysToRender.map((key) => /* @__PURE__ */ jsx(\n      \"abbr\",\n      {\n        className: slots.abbr({ class: classNames == null ? void 0 : classNames.abbr }),\n        title: kbdKeysLabelMap[key],\n        children: kbdKeysMap[key]\n      },\n      key\n    ));\n  }, [keysToRender]);\n  return /* @__PURE__ */ jsxs(Component, { ref, ...getKbdProps(), children: [\n    keysContent,\n    children && /* @__PURE__ */ jsx(\"span\", { className: slots.content({ class: classNames == null ? void 0 : classNames.content }), children })\n  ] });\n});\nKbd.displayName = \"HeroUI.Kbd\";\nvar kbd_default = Kbd;\n\nexport {\n  kbd_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,mBAAwB;AACxB,SAAS,OAAO,eAAe;AAC7B,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,IAAI,WAAW;AAC7E,QAAM,EAAE,IAAI,UAAU,WAAW,MAAM,OAAO,YAAY,GAAG,WAAW,IAAI;AAC5E,QAAM,YAAY,MAAM;AACxB,QAAM,YAAQ;AAAA,IACZ,MAAM,IAAI;AAAA,MACR,GAAG;AAAA,IACL,CAAC;AAAA,IACD,CAAC,aAAa,YAAY,CAAC;AAAA,EAC7B;AACA,QAAM,aAAa,KAAK,cAAc,OAAO,SAAS,WAAW,MAAM,SAAS;AAChF,QAAM,eAAe,OAAO,SAAS,WAAW,CAAC,IAAI,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC;AACvF,QAAM,cAAc,CAAC,SAAS,CAAC,OAAO;AAAA,IACpC,GAAG;AAAA,IACH,GAAG;AAAA,IACH,WAAW,KAAK,MAAM,KAAK,EAAE,OAAO,KAAK,YAAY,OAAO,SAAS,EAAE,CAAC,CAAC;AAAA,EAC3E;AACA,SAAO,EAAE,WAAW,OAAO,YAAY,OAAO,UAAU,cAAc,YAAY;AACpF;;;ACtBA,IAAI,aAAa;AAAA,EACf,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAI,kBAAkB;AAAA,EACpB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AACP;;;ACvCA,IAAAA,gBAAwB;AAExB,yBAA0B;AAC1B,IAAI,MAAM,WAAW,CAAC,OAAO,QAAQ;AACnC,QAAM,EAAE,WAAW,UAAU,OAAO,YAAY,cAAc,YAAY,IAAI,OAAO;AAAA,IACnF,GAAG;AAAA,EACL,CAAC;AACD,QAAM,kBAAc,uBAAQ,MAAM;AAChC,WAAO,aAAa,IAAI,CAAC,YAAwB;AAAA,MAC/C;AAAA,MACA;AAAA,QACE,WAAW,MAAM,KAAK,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,KAAK,CAAC;AAAA,QAC9E,OAAO,gBAAgB,GAAG;AAAA,QAC1B,UAAU,WAAW,GAAG;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,YAAY,CAAC;AACjB,aAAuB,yBAAK,WAAW,EAAE,KAAK,GAAG,YAAY,GAAG,UAAU;AAAA,IACxE;AAAA,IACA,gBAA4B,wBAAI,QAAQ,EAAE,WAAW,MAAM,QAAQ,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,QAAQ,CAAC,GAAG,SAAS,CAAC;AAAA,EAC7I,EAAE,CAAC;AACL,CAAC;AACD,IAAI,cAAc;AAClB,IAAI,cAAc;", "names": ["import_react"]}
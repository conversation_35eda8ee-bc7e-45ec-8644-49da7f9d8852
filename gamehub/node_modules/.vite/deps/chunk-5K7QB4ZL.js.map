{"version": 3, "sources": ["../../@heroui/system-rsc/dist/chunk-YFAKJTDR.mjs", "../../@heroui/system-rsc/dist/chunk-Z7BOKSG7.mjs", "../../@heroui/system-rsc/node_modules/clsx/dist/clsx.m.js"], "sourcesContent": ["// src/utils.ts\nimport { forwardRef as baseForwardRef } from \"react\";\nfunction forwardRef(component) {\n  return baseForwardRef(component);\n}\nvar toIterator = (obj) => {\n  return {\n    ...obj,\n    [Symbol.iterator]: function() {\n      const keys = Object.keys(this);\n      let index = 0;\n      return {\n        next: () => {\n          if (index >= keys.length) {\n            return { done: true };\n          }\n          const key = keys[index];\n          const value = this[key];\n          index++;\n          return { value: { key, value }, done: false };\n        }\n      };\n    }\n  };\n};\nvar mapPropsVariants = (props, variantKeys, removeVariantProps = true) => {\n  if (!variantKeys) {\n    return [props, {}];\n  }\n  const picked = variantKeys.reduce((acc, key) => {\n    if (key in props) {\n      return { ...acc, [key]: props[key] };\n    } else {\n      return acc;\n    }\n  }, {});\n  if (removeVariantProps) {\n    const omitted = Object.keys(props).filter((key) => !variantKeys.includes(key)).reduce((acc, key) => ({ ...acc, [key]: props[key] }), {});\n    return [omitted, picked];\n  } else {\n    return [props, picked];\n  }\n};\nvar mapPropsVariantsWithCommon = (originalProps, variantKeys, commonKeys) => {\n  const props = Object.keys(originalProps).filter((key) => !variantKeys.includes(key) || (commonKeys == null ? void 0 : commonKeys.includes(key))).reduce((acc, key) => ({ ...acc, [key]: originalProps[key] }), {});\n  const variants = variantKeys.reduce(\n    (acc, key) => ({ ...acc, [key]: originalProps[key] }),\n    {}\n  );\n  return [props, variants];\n};\nvar isHeroUIEl = (component) => {\n  var _a, _b, _c;\n  return !!((_c = (_b = (_a = component.type) == null ? void 0 : _a.render) == null ? void 0 : _b.displayName) == null ? void 0 : _c.includes(\"HeroUI\"));\n};\n\nexport {\n  forwardRef,\n  toIterator,\n  mapPropsVariants,\n  mapPropsVariantsWithCommon,\n  isHeroUIEl\n};\n", "import {\n  mapPropsVariants\n} from \"./chunk-YFAKJTDR.mjs\";\n\n// src/extend-variants.js\nimport * as React from \"react\";\nimport { tv } from \"@heroui/theme\";\nimport clsx from \"clsx\";\nfunction getSlots(variants) {\n  return variants ? Object.values(variants).flatMap(Object.values).reduce((acc, slot) => {\n    if (typeof slot === \"object\" && slot !== null && !(slot instanceof String)) {\n      Object.keys(slot).forEach((key) => {\n        if (!acc.hasOwnProperty(key)) {\n          acc[key] = \"\";\n        }\n      });\n    }\n    return acc;\n  }, {}) : {};\n}\nfunction getClassNamesWithProps({\n  props = {},\n  variants,\n  slots,\n  defaultVariants,\n  compoundVariants,\n  hasSlots,\n  opts\n}) {\n  var _a, _b, _c;\n  const keys = [];\n  if (defaultVariants && typeof defaultVariants === \"object\") {\n    for (const key in defaultVariants) {\n      const value = defaultVariants[key];\n      const propValue = props == null ? void 0 : props[key];\n      if (propValue && propValue !== value) {\n        keys.push(key);\n      }\n    }\n  }\n  const customTv = tv(\n    {\n      variants,\n      defaultVariants: defaultVariants && typeof defaultVariants === \"object\" ? (\n        // Do not apply default variants when the props variant is different\n        Object.keys(defaultVariants).filter((k) => !keys.includes(k)).reduce((o, k) => {\n          o[k] = defaultVariants[k];\n          return o;\n        }, [])\n      ) : defaultVariants,\n      compoundVariants,\n      ...hasSlots && { slots }\n    },\n    {\n      twMerge: (_a = opts.twMerge) != null ? _a : true,\n      twMergeConfig: (_b = opts.twMergeConfig) != null ? _b : {}\n    }\n  );\n  const [baseProps, variantProps] = mapPropsVariants(props, customTv.variantKeys, false);\n  const newProps = { ...defaultVariants, ...baseProps };\n  let classNames = {};\n  const result = customTv(variantProps);\n  if (!hasSlots) {\n    newProps.className = clsx(result, props.className);\n  } else {\n    Object.entries(result).forEach(([key, value]) => {\n      const slotResult = value();\n      if (typeof slotResult === \"string\") {\n        classNames[key] = slotResult;\n      }\n    });\n    Object.entries((_c = props.classNames) != null ? _c : {}).forEach(([key, value]) => {\n      classNames[key] = clsx(classNames[key], value);\n    });\n  }\n  if (Object.keys(classNames).length !== 0) {\n    newProps.classNames = classNames;\n  }\n  return newProps;\n}\nfunction extendVariants(BaseComponent, styles = {}, opts = {}) {\n  const { variants, defaultVariants, compoundVariants } = styles || {};\n  const slots = getSlots(variants);\n  const hasSlots = typeof slots === \"object\" && Object.keys(slots).length !== 0;\n  const ForwardedComponent = React.forwardRef((originalProps = {}, ref) => {\n    const newProps = React.useMemo(\n      () => getClassNamesWithProps(\n        {\n          slots,\n          variants,\n          compoundVariants,\n          props: originalProps,\n          defaultVariants,\n          hasSlots,\n          opts\n        },\n        [originalProps]\n      )\n    );\n    return React.createElement(BaseComponent, { ...originalProps, ...newProps, ref });\n  });\n  if (BaseComponent.getCollectionNode) {\n    ForwardedComponent.getCollectionNode = (itemProps) => {\n      const newProps = getClassNamesWithProps({\n        slots,\n        variants,\n        compoundVariants,\n        props: itemProps,\n        defaultVariants,\n        hasSlots,\n        opts\n      });\n      return BaseComponent.getCollectionNode({ ...itemProps, ...newProps });\n    };\n  }\n  ForwardedComponent.displayName = `Extended(${BaseComponent.displayName || BaseComponent.name})`;\n  return ForwardedComponent;\n}\n\nexport {\n  extendVariants\n};\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "mappings": ";;;;;;;;;;;AACA,mBAA6C;AAC7C,SAAS,WAAW,WAAW;AAC7B,aAAO,aAAAA,YAAe,SAAS;AACjC;AACA,IAAI,aAAa,CAAC,QAAQ;AACxB,SAAO;AAAA,IACL,GAAG;AAAA,IACH,CAAC,OAAO,QAAQ,GAAG,WAAW;AAC5B,YAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,UAAI,QAAQ;AACZ,aAAO;AAAA,QACL,MAAM,MAAM;AACV,cAAI,SAAS,KAAK,QAAQ;AACxB,mBAAO,EAAE,MAAM,KAAK;AAAA,UACtB;AACA,gBAAM,MAAM,KAAK,KAAK;AACtB,gBAAM,QAAQ,KAAK,GAAG;AACtB;AACA,iBAAO,EAAE,OAAO,EAAE,KAAK,MAAM,GAAG,MAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,CAAC,OAAO,aAAa,qBAAqB,SAAS;AACxE,MAAI,CAAC,aAAa;AAChB,WAAO,CAAC,OAAO,CAAC,CAAC;AAAA,EACnB;AACA,QAAM,SAAS,YAAY,OAAO,CAAC,KAAK,QAAQ;AAC9C,QAAI,OAAO,OAAO;AAChB,aAAO,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,IACrC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,oBAAoB;AACtB,UAAM,UAAU,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,SAAS,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC;AACvI,WAAO,CAAC,SAAS,MAAM;AAAA,EACzB,OAAO;AACL,WAAO,CAAC,OAAO,MAAM;AAAA,EACvB;AACF;AACA,IAAI,6BAA6B,CAAC,eAAe,aAAa,eAAe;AAC3E,QAAM,QAAQ,OAAO,KAAK,aAAa,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,SAAS,GAAG,MAAM,cAAc,OAAO,SAAS,WAAW,SAAS,GAAG,EAAE,EAAE,OAAO,CAAC,KAAK,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,cAAc,GAAG,EAAE,IAAI,CAAC,CAAC;AACjN,QAAM,WAAW,YAAY;AAAA,IAC3B,CAAC,KAAK,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,cAAc,GAAG,EAAE;AAAA,IACnD,CAAC;AAAA,EACH;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AACA,IAAI,aAAa,CAAC,cAAc;AAC9B,MAAI,IAAI,IAAI;AACZ,SAAO,CAAC,GAAG,MAAM,MAAM,KAAK,UAAU,SAAS,OAAO,SAAS,GAAG,WAAW,OAAO,SAAS,GAAG,gBAAgB,OAAO,SAAS,GAAG,SAAS,QAAQ;AACtJ;;;ACjDA,YAAuB;;;ACLvB,SAAS,EAAE,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE;AAAG,MAAG,YAAU,OAAO,KAAG,YAAU,OAAO;AAAE,SAAG;AAAA,WAAU,YAAU,OAAO;AAAE,QAAG,MAAM,QAAQ,CAAC;AAAE,WAAI,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,UAAE,CAAC,MAAI,IAAE,EAAE,EAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA;AAAQ,WAAI,KAAK;AAAE,UAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAQ,SAAS,OAAM;AAAC,WAAQ,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU;AAAQ,KAAC,IAAE,UAAU,GAAG,OAAK,IAAE,EAAE,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAC,IAAO,iBAAQ;;;ADQjX,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW,OAAO,OAAO,QAAQ,EAAE,QAAQ,OAAO,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS;AACrF,QAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,EAAE,gBAAgB,SAAS;AAC1E,aAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AACjC,YAAI,CAAC,IAAI,eAAe,GAAG,GAAG;AAC5B,cAAI,GAAG,IAAI;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,IAAI,CAAC;AACZ;AACA,SAAS,uBAAuB;AAAA,EAC9B,QAAQ,CAAC;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,IAAI,IAAI;AACZ,QAAM,OAAO,CAAC;AACd,MAAI,mBAAmB,OAAO,oBAAoB,UAAU;AAC1D,eAAW,OAAO,iBAAiB;AACjC,YAAM,QAAQ,gBAAgB,GAAG;AACjC,YAAM,YAAY,SAAS,OAAO,SAAS,MAAM,GAAG;AACpD,UAAI,aAAa,cAAc,OAAO;AACpC,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,QAAM,WAAW;AAAA,IACf;AAAA,MACE;AAAA,MACA,iBAAiB,mBAAmB,OAAO,oBAAoB;AAAA;AAAA,QAE7D,OAAO,KAAK,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM;AAC7E,YAAE,CAAC,IAAI,gBAAgB,CAAC;AACxB,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAAA,UACH;AAAA,MACJ;AAAA,MACA,GAAG,YAAY,EAAE,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,MACE,UAAU,KAAK,KAAK,YAAY,OAAO,KAAK;AAAA,MAC5C,gBAAgB,KAAK,KAAK,kBAAkB,OAAO,KAAK,CAAC;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,CAAC,WAAW,YAAY,IAAI,iBAAiB,OAAO,SAAS,aAAa,KAAK;AACrF,QAAM,WAAW,EAAE,GAAG,iBAAiB,GAAG,UAAU;AACpD,MAAI,aAAa,CAAC;AAClB,QAAM,SAAS,SAAS,YAAY;AACpC,MAAI,CAAC,UAAU;AACb,aAAS,YAAY,eAAK,QAAQ,MAAM,SAAS;AAAA,EACnD,OAAO;AACL,WAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,YAAM,aAAa,MAAM;AACzB,UAAI,OAAO,eAAe,UAAU;AAClC,mBAAW,GAAG,IAAI;AAAA,MACpB;AAAA,IACF,CAAC;AACD,WAAO,SAAS,KAAK,MAAM,eAAe,OAAO,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAClF,iBAAW,GAAG,IAAI,eAAK,WAAW,GAAG,GAAG,KAAK;AAAA,IAC/C,CAAC;AAAA,EACH;AACA,MAAI,OAAO,KAAK,UAAU,EAAE,WAAW,GAAG;AACxC,aAAS,aAAa;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,eAAe,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG;AAC7D,QAAM,EAAE,UAAU,iBAAiB,iBAAiB,IAAI,UAAU,CAAC;AACnE,QAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAM,WAAW,OAAO,UAAU,YAAY,OAAO,KAAK,KAAK,EAAE,WAAW;AAC5E,QAAM,qBAA2B,iBAAW,CAAC,gBAAgB,CAAC,GAAG,QAAQ;AACvE,UAAM,WAAiB;AAAA,MACrB,MAAM;AAAA,QACJ;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,CAAC,aAAa;AAAA,MAChB;AAAA,IACF;AACA,WAAa,oBAAc,eAAe,EAAE,GAAG,eAAe,GAAG,UAAU,IAAI,CAAC;AAAA,EAClF,CAAC;AACD,MAAI,cAAc,mBAAmB;AACnC,uBAAmB,oBAAoB,CAAC,cAAc;AACpD,YAAM,WAAW,uBAAuB;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,cAAc,kBAAkB,EAAE,GAAG,WAAW,GAAG,SAAS,CAAC;AAAA,IACtE;AAAA,EACF;AACA,qBAAmB,cAAc,YAAY,cAAc,eAAe,cAAc,IAAI;AAC5F,SAAO;AACT;", "names": ["baseForwardRef"]}
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-C4VLZYLP.js";
import {
  code,
  objectToDeps
} from "./chunk-B4Z3JF7O.js";
import "./chunk-XDJEYPHN.js";
import {
  require_jsx_runtime
} from "./chunk-6HCJQXVG.js";
import {
  require_react
} from "./chunk-XEXUAUZA.js";
import {
  __toESM
} from "./chunk-LQ2VYIYD.js";

// node_modules/@heroui/code/dist/chunk-UDFNFZDA.mjs
var import_react = __toESM(require_react(), 1);
function useCode(originalProps) {
  const [props, variantProps] = mapPropsVariants(originalProps, code.variantKeys);
  const { as, children, className, ...otherProps } = props;
  const Component = as || "code";
  const styles = (0, import_react.useMemo)(
    () => code({
      ...variantProps,
      className
    }),
    [objectToDeps(variantProps), className]
  );
  const getCodeProps = () => {
    return {
      className: styles,
      ...otherProps
    };
  };
  return { Component, children, getCodeProps };
}

// node_modules/@heroui/code/dist/chunk-C3KKIFEX.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Code = forwardRef((props, ref) => {
  const { Component, children, getCodeProps } = useCode({ ...props });
  return (0, import_jsx_runtime.jsx)(Component, { ref, ...getCodeProps(), children });
});
Code.displayName = "HeroUI.Code";
var code_default = Code;
export {
  code_default as Code,
  useCode
};
//# sourceMappingURL=@heroui_code.js.map

{"version": 3, "sources": ["../../@react-aria/utils/dist/packages/@react-aria/utils/src/useLayoutEffect.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useEffectEvent.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useValueEffect.ts", "../../@react-aria/ssr/dist/packages/@react-aria/ssr/src/SSRProvider.tsx", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useId.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/chain.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/domHelpers.ts", "../../@react-stately/flags/dist/packages/@react-stately/flags/src/index.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/shadowdom/DOMFunctions.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/shadowdom/ShadowTreeWalker.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/mergeProps.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/mergeRefs.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/filterDOMProps.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/focusWithoutScrolling.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/platform.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/openLink.tsx", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/runAfterTransition.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useGlobalListeners.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useLabels.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useObjectRef.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useResizeObserver.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useSyncRef.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/isScrollable.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/getScrollParent.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useViewportSize.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/isVirtualEvent.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/useFormReset.ts", "../../@react-aria/utils/dist/packages/@react-aria/utils/src/isFocusable.ts", "../../@react-stately/utils/dist/packages/@react-stately/utils/src/useControlledState.ts", "../../@react-stately/utils/dist/packages/@react-stately/utils/src/number.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/utils.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/textSelection.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/context.ts", "../../@swc/helpers/esm/_class_apply_descriptor_get.js", "../../@swc/helpers/esm/_class_extract_field_descriptor.js", "../../@swc/helpers/esm/_class_private_field_get.js", "../../@swc/helpers/esm/_check_private_redeclaration.js", "../../@swc/helpers/esm/_class_private_field_init.js", "../../@swc/helpers/esm/_class_apply_descriptor_set.js", "../../@swc/helpers/esm/_class_private_field_set.js", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/usePress.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusVisible.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/focusSafely.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocus.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/createEventHandler.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useKeyboard.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusable.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/Pressable.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/PressResponder.tsx", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useFocusWithin.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useHover.ts", "../../@react-aria/interactions/dist/packages/@react-aria/interactions/src/useInteractOutside.ts", "../../@react-aria/visually-hidden/dist/packages/@react-aria/visually-hidden/src/VisuallyHidden.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Dispatch, MutableRefObject, useRef, useState} from 'react';\nimport {useEffectEvent, useLayoutEffect} from './';\n\ntype SetValueAction<S> = (prev: S) => Generator<any, void, unknown>;\n\n// This hook works like `useState`, but when setting the value, you pass a generator function\n// that can yield multiple values. Each yielded value updates the state and waits for the next\n// layout effect, then continues the generator. This allows sequential updates to state to be\n// written linearly.\nexport function useValueEffect<S>(defaultValue: S | (() => S)): [S, Dispatch<SetValueAction<S>>] {\n  let [value, setValue] = useState(defaultValue);\n  let effect: MutableRefObject<Generator<S> | null> = useRef<Generator<S> | null>(null);\n\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = useEffectEvent(() => {\n    if (!effect.current) {\n      return;\n    }\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) {\n      nextRef();\n    } else {\n      setValue(newValue.value);\n    }\n  });\n\n  useLayoutEffect(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) {\n      nextRef();\n    }\n  });\n\n  let queue = useEffectEvent(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n\n  return [value, queue];\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n", "export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n", "// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n", "// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nimport {nodeContains} from './DOMFunctions';\nimport {shadowDOM} from '@react-stately/flags';\n\nexport class ShadowTreeWalker implements TreeWalker {\n  public readonly filter: NodeFilter | null;\n  public readonly root: Node;\n  public readonly whatToShow: number;\n\n  private _doc: Document;\n  private _walkerStack: Array<TreeWalker> = [];\n  private _currentNode: Node;\n  private _currentSetFor: Set<TreeWalker> = new Set();\n\n  constructor(\n      doc: Document,\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null\n    ) {\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter ?? null;\n    this.whatToShow = whatToShow ?? NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n\n    this._walkerStack.unshift(\n      doc.createTreeWalker(root, whatToShow, this._acceptNode)\n    );\n\n    const shadowRoot = (root as Element).shadowRoot;\n\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(\n        shadowRoot,\n        this.whatToShow,\n        {acceptNode: this._acceptNode}\n      );\n\n      this._walkerStack.unshift(walker);\n    }\n  }\n\n  private _acceptNode = (node: Node): number => {\n    if (node.nodeType === Node.ELEMENT_NODE) {\n      const shadowRoot = (node as Element).shadowRoot;\n\n      if (shadowRoot) {\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        this._walkerStack.unshift(walker);\n\n        return NodeFilter.FILTER_ACCEPT;\n      } else {\n        if (typeof this.filter === 'function') {\n          return this.filter(node);\n        } else if (this.filter?.acceptNode) {\n          return this.filter.acceptNode(node);\n        } else if (this.filter === null) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n    }\n\n    return NodeFilter.FILTER_SKIP;\n  };\n\n  public get currentNode(): Node {\n    return this._currentNode;\n  }\n\n  public set currentNode(node: Node) {\n    if (!nodeContains(this.root, node)) {\n      throw new Error(\n        'Cannot set currentNode to a node that is not contained by the root node.'\n      );\n    }\n\n    const walkers: TreeWalker[] = [];\n    let curNode: Node | null | undefined = node;\n    let currentWalkerCurrentNode = node;\n\n    this._currentNode = node;\n\n    while (curNode && curNode !== this.root) {\n      if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n        const shadowRoot = curNode as ShadowRoot;\n\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        walkers.push(walker);\n\n        walker.currentNode = currentWalkerCurrentNode;\n\n        this._currentSetFor.add(walker);\n\n        curNode = currentWalkerCurrentNode = shadowRoot.host;\n      } else {\n        curNode = curNode.parentNode;\n      }\n    }\n\n    const walker = this._doc.createTreeWalker(\n      this.root,\n      this.whatToShow,\n      {acceptNode: this._acceptNode}\n    );\n\n    walkers.push(walker);\n\n    walker.currentNode = currentWalkerCurrentNode;\n\n    this._currentSetFor.add(walker);\n\n    this._walkerStack = walkers;\n  }\n\n  public get doc(): Document {\n    return this._doc;\n  }\n\n  public firstChild(): Node | null {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!nodeContains(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public lastChild(): Node | null {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public nextNode(): Node | null {\n    const nextNode = this._walkerStack[0].nextNode();\n\n    if (nextNode) {\n      const shadowRoot = (nextNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(nextNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(nextNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (nextNode) {\n        this.currentNode = nextNode;\n      }\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n  public previousNode(): Node | null {\n    const currentWalker = this._walkerStack[0];\n\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) {\n            this.currentNode = newNode;\n          }\n          return newNode;\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    }\n\n    const previousNode = currentWalker.previousNode();\n\n    if (previousNode) {\n      const shadowRoot = (previousNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(previousNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(previousNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) {\n            this.currentNode = previousNode;\n          }\n          return previousNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (previousNode) {\n        this.currentNode = previousNode;\n      }\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.previousNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n    /**\n     * @deprecated\n     */\n  public nextSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public previousSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public parentNode(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n}\n\n/**\n * ShadowDOM safe version of document.createTreeWalker.\n */\nexport function createShadowTreeWalker(\n    doc: Document,\n    root: Node,\n    whatToShow?: number,\n    filter?: NodeFilter | null\n): TreeWalker {\n  if (shadowDOM()) {\n    return new ShadowTreeWalker(doc, root, whatToShow, filter);\n  }\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps, opts: Options = {}): DOMProps & AriaLabelingProps {\n  let {labelable, isLink, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  return (\n    window.navigator['userAgentData']?.brands.some((brand: {brand: string, version: string}) => re.test(brand.brand))\n  ) ||\n  re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n  \n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {focusWithoutScrolling, isMac, isWebKit} from './index';\nimport {Href, LinkDOMProps, RouterOptions} from '@react-types/shared';\nimport {isFirefox, isIPad} from './platform';\nimport React, {createContext, DOMAttributes, JSX, ReactNode, useContext, useMemo} from 'react';\n\ninterface Router {\n  isNative: boolean,\n  open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref: (href: Href) => string\n}\n\nconst RouterContext = createContext<Router>({\n  isNative: true,\n  open: openSyntheticLink,\n  useHref: (href) => href\n});\n\ninterface RouterProviderProps {\n  navigate: (path: Href, routerOptions: RouterOptions | undefined) => void,\n  useHref?: (href: Href) => string,\n  children: ReactNode\n}\n\n/**\n * A RouterProvider accepts a `navigate` function from a framework or client side router,\n * and provides it to all nested React Aria links to enable client side navigation.\n */\nexport function RouterProvider(props: RouterProviderProps): JSX.Element {\n  let {children, navigate, useHref} = props;\n\n  let ctx = useMemo(() => ({\n    isNative: false,\n    open: (target: Element, modifiers: Modifiers, href: Href, routerOptions: RouterOptions | undefined) => {\n      getSyntheticLink(target, link => {\n        if (shouldClientNavigate(link, modifiers)) {\n          navigate(href, routerOptions);\n        } else {\n          openLink(link, modifiers);\n        }\n      });\n    },\n    useHref: useHref || ((href) => href)\n  }), [navigate, useHref]);\n\n  return (\n    <RouterContext.Provider value={ctx}>\n      {children}\n    </RouterContext.Provider>\n  );\n}\n\nexport function useRouter(): Router {\n  return useContext(RouterContext);\n}\n\ninterface Modifiers {\n  metaKey?: boolean,\n  ctrlKey?: boolean,\n  altKey?: boolean,\n  shiftKey?: boolean\n}\n\nexport function shouldClientNavigate(link: HTMLAnchorElement, modifiers: Modifiers): boolean {\n  // Use getAttribute here instead of link.target. Firefox will default link.target to \"_parent\" when inside an iframe.\n  let target = link.getAttribute('target');\n  return (\n    (!target || target === '_self') &&\n    link.origin === location.origin &&\n    !link.hasAttribute('download') &&\n    !modifiers.metaKey && // open in new tab (mac)\n    !modifiers.ctrlKey && // open in new tab (windows)\n    !modifiers.altKey && // download\n    !modifiers.shiftKey\n  );\n}\n\nexport function openLink(target: HTMLAnchorElement, modifiers: Modifiers, setOpening = true): void {\n  let {metaKey, ctrlKey, altKey, shiftKey} = modifiers;\n\n  // Firefox does not recognize keyboard events as a user action by default, and the popup blocker\n  // will prevent links with target=\"_blank\" from opening. However, it does allow the event if the\n  // Command/Control key is held, which opens the link in a background tab. This seems like the best we can do.\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=257870 and https://bugzilla.mozilla.org/show_bug.cgi?id=746640.\n  if (isFirefox() && window.event?.type?.startsWith('key') && target.target === '_blank') {\n    if (isMac()) {\n      metaKey = true;\n    } else {\n      ctrlKey = true;\n    }\n  }\n\n  // WebKit does not support firing click events with modifier keys, but does support keyboard events.\n  // https://github.com/WebKit/WebKit/blob/c03d0ac6e6db178f90923a0a63080b5ca210d25f/Source/WebCore/html/HTMLAnchorElement.cpp#L184\n  let event = isWebKit() && isMac() && !isIPad() && process.env.NODE_ENV !== 'test'\n    // @ts-ignore - keyIdentifier is a non-standard property, but it's what webkit expects\n    ? new KeyboardEvent('keydown', {keyIdentifier: 'Enter', metaKey, ctrlKey, altKey, shiftKey})\n    : new MouseEvent('click', {metaKey, ctrlKey, altKey, shiftKey, bubbles: true, cancelable: true});\n  (openLink as any).isOpening = setOpening;\n  focusWithoutScrolling(target);\n  target.dispatchEvent(event);\n  (openLink as any).isOpening = false;\n}\n// https://github.com/parcel-bundler/parcel/issues/8724\n(openLink as any).isOpening = false;\n\nfunction getSyntheticLink(target: Element, open: (link: HTMLAnchorElement) => void) {\n  if (target instanceof HTMLAnchorElement) {\n    open(target);\n  } else if (target.hasAttribute('data-href')) {\n    let link = document.createElement('a');\n    link.href = target.getAttribute('data-href')!;\n    if (target.hasAttribute('data-target')) {\n      link.target = target.getAttribute('data-target')!;\n    }\n    if (target.hasAttribute('data-rel')) {\n      link.rel = target.getAttribute('data-rel')!;\n    }\n    if (target.hasAttribute('data-download')) {\n      link.download = target.getAttribute('data-download')!;\n    }\n    if (target.hasAttribute('data-ping')) {\n      link.ping = target.getAttribute('data-ping')!;\n    }\n    if (target.hasAttribute('data-referrer-policy')) {\n      link.referrerPolicy = target.getAttribute('data-referrer-policy')!;\n    }\n    target.appendChild(link);\n    open(link);\n    target.removeChild(link);\n  }\n}\n\nfunction openSyntheticLink(target: Element, modifiers: Modifiers) {\n  getSyntheticLink(target, link => openLink(link, modifiers));\n}\n\nexport function useSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  let router = useRouter();\n  const href = router.useHref(props.href ?? '');\n  return {\n    'data-href': props.href ? href : undefined,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\n/** @deprecated - For backward compatibility. */\nexport function getSyntheticLinkProps(props: LinkDOMProps): DOMAttributes<HTMLElement> {\n  return {\n    'data-href': props.href,\n    'data-target': props.target,\n    'data-rel': props.rel,\n    'data-download': props.download,\n    'data-ping': props.ping,\n    'data-referrer-policy': props.referrerPolicy\n  } as DOMAttributes<HTMLElement>;\n}\n\nexport function useLinkProps(props?: LinkDOMProps): LinkDOMProps {\n  let router = useRouter();\n  const href = router.useHref(props?.href ?? '');\n  return {\n    href: props?.href ? href : undefined,\n    target: props?.target,\n    rel: props?.rel,\n    download: props?.download,\n    ping: props?.ping,\n    referrerPolicy: props?.referrerPolicy\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {useId} from './useId';\n\n/**\n * Merges aria-label and aria-labelledby into aria-labelledby when both exist.\n * @param props - Aria label props.\n * @param defaultLabel - Default value for aria-label when not present.\n */\nexport function useLabels(props: DOMProps & AriaLabelingProps, defaultLabel?: string): DOMProps & AriaLabelingProps {\n  let {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = useId(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) {\n    labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  }\n\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) {\n    label = defaultLabel;\n  }\n\n  return {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\n", "/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n", "\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\nfunction hasResizeObserver() {\n  return typeof window.ResizeObserver !== 'undefined';\n}\n\ntype useResizeObserverOptionsType<T> = {\n  ref: RefObject<T | undefined | null> | undefined,\n  box?: ResizeObserverBoxOptions,\n  onResize: () => void\n}\n\nexport function useResizeObserver<T extends Element>(options: useResizeObserverOptionsType<T>): void {\n  const {ref, box, onResize} = options;\n\n  useEffect(() => {\n    let element = ref?.current;\n    if (!element) {\n      return;\n    }\n\n    if (!hasResizeObserver()) {\n      window.addEventListener('resize', onResize, false);\n      return () => {\n        window.removeEventListener('resize', onResize, false);\n      };\n    } else {\n\n      const resizeObserverInstance = new window.ResizeObserver((entries) => {\n        if (!entries.length) {\n          return;\n        }\n\n        onResize();\n      });\n      resizeObserverInstance.observe(element, {box});\n\n      return () => {\n        if (element) {\n          resizeObserverInstance.unobserve(element);\n        }\n      };\n    }\n\n  }, [onResize, ref, box]);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n", "/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport function isScrollable(node: Element | null, checkForOverflow?: boolean): boolean {\n  if (!node) {\n    return false;\n  }\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n\n  if (isScrollable && checkForOverflow) {\n    isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  }\n\n  return isScrollable;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParent(node: Element, checkForOverflow?: boolean): Element {\n  let scrollableNode: Element | null = node;\n  if (isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  while (scrollableNode && !isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\n\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\ninterface ViewportSize {\n  width: number,\n  height: number\n}\n\nlet visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\nexport function useViewportSize(): ViewportSize {\n  let isSSR = useIsSSR();\n  let [size, setSize] = useState(() => isSSR ? {width: 0, height: 0} : getViewportSize());\n\n  useEffect(() => {\n    // Use visualViewport api to track available height even on iOS virtual keyboard opening\n    let onResize = () => {\n      setSize(size => {\n        let newSize = getViewportSize();\n        if (newSize.width === size.width && newSize.height === size.height) {\n          return size;\n        }\n        return newSize;\n      });\n    };\n\n    if (!visualViewport) {\n      window.addEventListener('resize', onResize);\n    } else {\n      visualViewport.addEventListener('resize', onResize);\n    }\n\n    return () => {\n      if (!visualViewport) {\n        window.removeEventListener('resize', onResize);\n      } else {\n        visualViewport.removeEventListener('resize', onResize);\n      }\n    };\n  }, []);\n\n  return size;\n}\n\nfunction getViewportSize(): ViewportSize {\n  return {\n    width: (visualViewport && visualViewport?.width) || window.innerWidth,\n    height: (visualViewport && visualViewport?.height) || window.innerHeight\n  };\n}\n", "/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let resetValue = useRef(initialValue);\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(resetValue.current);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n", "const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\n\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined>, defaultValue: Exclude<T, undefined> | undefined, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined> | undefined, defaultValue: Exclude<T, undefined>, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: T, defaultValue: T, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void] {\n  let [stateValue, setStateValue] = useState(value || defaultValue);\n\n  let isControlledRef = useRef(value !== undefined);\n  let isControlled = value !== undefined;\n  useEffect(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') {\n      console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    }\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = useCallback((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) {\n          onChange(value, ...onChangeArgs);\n        }\n      }\n      if (!isControlled) {\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n      }\n    };\n\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      }\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) {\n          return interceptedValue;\n        }\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) {\n        setStateValue(value);\n      }\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n\n  return [currentValue, setValue];\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */\nexport function clamp(value: number, min: number = -Infinity, max: number = Infinity): number {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\n\nexport function roundToStepPrecision(value: number, step: number): number {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min);\n  max = Number(max);\n  let remainder = ((value - (isNaN(min) ? 0 : min)) % step);\n  let snappedValue = roundToStepPrecision(Math.abs(remainder) * 2 >= step\n    ? value + Math.sign(remainder) * (step - Math.abs(remainder))\n    : value - remainder, step);\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step;\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step;\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step);\n\n  return snappedValue;\n}\n\n/* Takes a value and rounds off to the number of digits. */\nexport function toFixedNumber(value: number, digits: number, base: number = 10): number {\n  const pow = Math.pow(base, digits);\n\n  return Math.round(value * pow) / pow;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerDocument, isIOS, runAfterTransition} from '@react-aria/utils';\n\n// Safari on iOS starts selecting text on long press. The only way to avoid this, it seems,\n// is to add user-select: none to the entire page. Adding it to the pressable element prevents\n// that element from being selected, but nearby elements may still receive selection. We add\n// user-select: none on touch start, and remove it again on touch end to prevent this.\n// This must be implemented using global state to avoid race conditions between multiple elements.\n\n// There are three possible states due to the delay before removing user-select: none after\n// pointer up. The 'default' state always transitions to the 'disabled' state, which transitions\n// to 'restoring'. The 'restoring' state can either transition back to 'disabled' or 'default'.\n\n// For non-iOS devices, we apply user-select: none to the pressed element instead to avoid possible\n// performance issues that arise from applying and removing user-select: none to the entire page\n// (see https://github.com/adobe/react-spectrum/issues/1609).\ntype State = 'default' | 'disabled' | 'restoring';\n\n// Note that state only matters here for iOS. Non-iOS gets user-select: none applied to the target element\n// rather than at the document level so we just need to apply/remove user-select: none for each pressed element individually\nlet state: State = 'default';\nlet savedUserSelect = '';\nlet modifiedElementMap = new WeakMap<Element, string>();\n\nexport function disableTextSelection(target?: Element): void {\n  if (isIOS()) {\n    if (state === 'default') {\n\n      const documentObject = getOwnerDocument(target);\n      savedUserSelect = documentObject.documentElement.style.webkitUserSelect;\n      documentObject.documentElement.style.webkitUserSelect = 'none';\n    }\n\n    state = 'disabled';\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, store the target's original user-select and change to user-select: none\n    // Ignore state since it doesn't apply for non iOS\n    let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n    modifiedElementMap.set(target, target.style[property]);\n    target.style[property] = 'none';\n  }\n}\n\nexport function restoreTextSelection(target?: Element): void {\n  if (isIOS()) {\n    // If the state is already default, there's nothing to do.\n    // If it is restoring, then there's no need to queue a second restore.\n    if (state !== 'disabled') {\n      return;\n    }\n\n    state = 'restoring';\n\n    // There appears to be a delay on iOS where selection still might occur\n    // after pointer up, so wait a bit before removing user-select.\n    setTimeout(() => {\n      // Wait for any CSS transitions to complete so we don't recompute style\n      // for the whole page in the middle of the animation and cause jank.\n      runAfterTransition(() => {\n        // Avoid race conditions\n        if (state === 'restoring') {\n\n          const documentObject = getOwnerDocument(target);\n          if (documentObject.documentElement.style.webkitUserSelect === 'none') {\n            documentObject.documentElement.style.webkitUserSelect = savedUserSelect || '';\n          }\n\n          savedUserSelect = '';\n          state = 'default';\n        }\n      });\n    }, 300);\n  } else if (target instanceof HTMLElement || target instanceof SVGElement) {\n    // If not iOS, restore the target's original user-select if any\n    // Ignore state since it doesn't apply for non iOS\n    if (target && modifiedElementMap.has(target)) {\n      let targetOldUserSelect = modifiedElementMap.get(target) as string;\n      let property = 'userSelect' in target.style ? 'userSelect' : 'webkitUserSelect';\n\n      if (target.style[property] === 'none') {\n        target.style[property] = targetOldUserSelect;\n      }\n\n      if (target.getAttribute('style') === '') {\n        target.removeAttribute('style');\n      }\n      modifiedElementMap.delete(target);\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {PressProps} from './usePress';\nimport React, {MutableRefObject} from 'react';\n\ninterface IPressResponderContext extends PressProps {\n  register(): void,\n  ref?: MutableRefObject<FocusableElement>\n}\n\nexport const PressResponderContext = React.createContext<IPressResponderContext>({register: () => {}});\nPressResponderContext.displayName = 'PressResponderContext';\n", "function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n", "function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n", "import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n", "function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n", "import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n", "function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n", "import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {\n  chain,\n  focusWithoutScrolling,\n  getEventTarget,\n  getOwnerDocument,\n  getOwnerWindow,\n  isMac,\n  isVirtualClick,\n  isVirtualPointerEvent,\n  mergeProps,\n  nodeContains,\n  openLink,\n  useEffectEvent,\n  useGlobalListeners,\n  useSyncRef\n} from '@react-aria/utils';\nimport {createSyntheticEvent, preventFocus, setEventTarget} from './utils';\nimport {disableTextSelection, restoreTextSelection} from './textSelection';\nimport {DOMAttributes, FocusableElement, PressEvent as IPressEvent, PointerType, PressEvents, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {PressResponderContext} from './context';\nimport {MouseEvent as RMouseEvent, TouchEvent as RTouchEvent, useContext, useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface PressProps extends PressEvents {\n  /** Whether the target is in a controlled press state (e.g. an overlay it triggers is open). */\n  isPressed?: boolean,\n  /** Whether the press events should be disabled. */\n  isDisabled?: boolean,\n  /** Whether the target should not receive focus on press. */\n  preventFocusOnPress?: boolean,\n  /**\n   * Whether press events should be canceled when the pointer leaves the target while pressed.\n   * By default, this is `false`, which means if the pointer returns back over the target while\n   * still pressed, onPressStart will be fired again. If set to `true`, the press is canceled\n   * when the pointer leaves the target and onPressStart will not be fired if the pointer returns.\n   */\n  shouldCancelOnPointerExit?: boolean,\n  /** Whether text selection should be enabled on the pressable element. */\n  allowTextSelectionOnPress?: boolean\n}\n\nexport interface PressHookProps extends PressProps {\n  /** A ref to the target element. */\n  ref?: RefObject<Element | null>\n}\n\ninterface PressState {\n  isPressed: boolean,\n  ignoreEmulatedMouseEvents: boolean,\n  didFirePressStart: boolean,\n  isTriggeringEvent: boolean,\n  activePointerId: any,\n  target: FocusableElement | null,\n  isOverTarget: boolean,\n  pointerType: PointerType | null,\n  userSelect?: string,\n  metaKeyEvents?: Map<string, KeyboardEvent>,\n  disposables: Array<() => void>\n}\n\ninterface EventBase {\n  currentTarget: EventTarget | null,\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean,\n  clientX?: number,\n  clientY?: number,\n  targetTouches?: Array<{clientX?: number, clientY?: number}>\n}\n\nexport interface PressResult {\n  /** Whether the target is currently pressed. */\n  isPressed: boolean,\n  /** Props to spread on the target element. */\n  pressProps: DOMAttributes\n}\n\nfunction usePressResponderContext(props: PressHookProps): PressHookProps {\n  // Consume context from <PressResponder> and merge with props.\n  let context = useContext(PressResponderContext);\n  if (context) {\n    let {register, ...contextProps} = context;\n    props = mergeProps(contextProps, props) as PressHookProps;\n    register();\n  }\n  useSyncRef(context, props.ref);\n\n  return props;\n}\n\nclass PressEvent implements IPressEvent {\n  type: IPressEvent['type'];\n  pointerType: PointerType;\n  target: Element;\n  shiftKey: boolean;\n  ctrlKey: boolean;\n  metaKey: boolean;\n  altKey: boolean;\n  x: number;\n  y: number;\n  #shouldStopPropagation = true;\n\n  constructor(type: IPressEvent['type'], pointerType: PointerType, originalEvent: EventBase, state?: PressState) {\n    let currentTarget = state?.target ?? originalEvent.currentTarget;\n    const rect: DOMRect | undefined = (currentTarget as Element)?.getBoundingClientRect();\n    let x, y = 0;\n    let clientX, clientY: number | null = null;\n    if (originalEvent.clientX != null && originalEvent.clientY != null) {\n      clientX = originalEvent.clientX;\n      clientY = originalEvent.clientY;\n    }\n    if (rect) {\n      if (clientX != null && clientY != null) {\n        x = clientX - rect.left;\n        y = clientY - rect.top;\n      } else {\n        x = rect.width / 2;\n        y = rect.height / 2;\n      }\n    }\n    this.type = type;\n    this.pointerType = pointerType;\n    this.target = originalEvent.currentTarget as Element;\n    this.shiftKey = originalEvent.shiftKey;\n    this.metaKey = originalEvent.metaKey;\n    this.ctrlKey = originalEvent.ctrlKey;\n    this.altKey = originalEvent.altKey;\n    this.x = x;\n    this.y = y;\n  }\n\n  continuePropagation() {\n    this.#shouldStopPropagation = false;\n  }\n\n  get shouldStopPropagation() {\n    return this.#shouldStopPropagation;\n  }\n}\n\nconst LINK_CLICKED = Symbol('linkClicked');\nconst STYLE_ID = 'react-aria-pressable-style';\nconst PRESSABLE_ATTRIBUTE = 'data-react-aria-pressable';\n\n/**\n * Handles press interactions across mouse, touch, keyboard, and screen readers.\n * It normalizes behavior across browsers and platforms, and handles many nuances\n * of dealing with pointer and keyboard events.\n */\nexport function usePress(props: PressHookProps): PressResult {\n  let {\n    onPress,\n    onPressChange,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onClick,\n    isDisabled,\n    isPressed: isPressedProp,\n    preventFocusOnPress,\n    shouldCancelOnPointerExit,\n    allowTextSelectionOnPress,\n    ref: domRef,\n    ...domProps\n  } = usePressResponderContext(props);\n\n  let [isPressed, setPressed] = useState(false);\n  let ref = useRef<PressState>({\n    isPressed: false,\n    ignoreEmulatedMouseEvents: false,\n    didFirePressStart: false,\n    isTriggeringEvent: false,\n    activePointerId: null,\n    target: null,\n    isOverTarget: false,\n    pointerType: null,\n    disposables: []\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let triggerPressStart = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled || state.didFirePressStart) {\n      return false;\n    }\n\n    let shouldStopPropagation = true;\n    state.isTriggeringEvent = true;\n    if (onPressStart) {\n      let event = new PressEvent('pressstart', pointerType, originalEvent);\n      onPressStart(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(true);\n    }\n\n    state.isTriggeringEvent = false;\n    state.didFirePressStart = true;\n    setPressed(true);\n    return shouldStopPropagation;\n  });\n\n  let triggerPressEnd = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, wasPressed = true) => {\n    let state = ref.current;\n    if (!state.didFirePressStart) {\n      return false;\n    }\n\n    state.didFirePressStart = false;\n    state.isTriggeringEvent = true;\n\n    let shouldStopPropagation = true;\n    if (onPressEnd) {\n      let event = new PressEvent('pressend', pointerType, originalEvent);\n      onPressEnd(event);\n      shouldStopPropagation = event.shouldStopPropagation;\n    }\n\n    if (onPressChange) {\n      onPressChange(false);\n    }\n\n    setPressed(false);\n\n    if (onPress && wasPressed && !isDisabled) {\n      let event = new PressEvent('press', pointerType, originalEvent);\n      onPress(event);\n      shouldStopPropagation &&= event.shouldStopPropagation;\n    }\n\n    state.isTriggeringEvent = false;\n    return shouldStopPropagation;\n  });\n\n  let triggerPressUp = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    let state = ref.current;\n    if (isDisabled) {\n      return false;\n    }\n\n    if (onPressUp) {\n      state.isTriggeringEvent = true;\n      let event = new PressEvent('pressup', pointerType, originalEvent);\n      onPressUp(event);\n      state.isTriggeringEvent = false;\n      return event.shouldStopPropagation;\n    }\n\n    return true;\n  });\n\n  let cancel = useEffectEvent((e: EventBase) => {\n    let state = ref.current;\n    if (state.isPressed && state.target) {\n      if (state.didFirePressStart && state.pointerType != null) {\n        triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n      }\n      state.isPressed = false;\n      state.isOverTarget = false;\n      state.activePointerId = null;\n      state.pointerType = null;\n      removeAllGlobalListeners();\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    }\n  });\n\n  let cancelOnPointerExit = useEffectEvent((e: EventBase) => {\n    if (shouldCancelOnPointerExit) {\n      cancel(e);\n    }\n  });\n\n  let triggerClick = useEffectEvent((e: RMouseEvent<FocusableElement>) => {\n    onClick?.(e);\n  });\n\n  let triggerSyntheticClick = useEffectEvent((e: KeyboardEvent | TouchEvent, target: FocusableElement) => {\n    // Some third-party libraries pass in onClick instead of onPress.\n    // Create a fake mouse event and trigger onClick as well.\n    // This matches the browser's native activation behavior for certain elements (e.g. button).\n    // https://html.spec.whatwg.org/#activation\n    // https://html.spec.whatwg.org/#fire-a-synthetic-pointer-event\n    if (onClick) {\n      let event = new MouseEvent('click', e);\n      setEventTarget(event, target);\n      onClick(createSyntheticEvent(event));\n    }\n  });\n\n  let pressProps = useMemo(() => {\n    let state = ref.current;\n    let pressProps: DOMAttributes = {\n      onKeyDown(e) {\n        if (isValidKeyboardEvent(e.nativeEvent, e.currentTarget) && nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          if (shouldPreventDefaultKeyboard(getEventTarget(e.nativeEvent), e.key)) {\n            e.preventDefault();\n          }\n\n          // If the event is repeating, it may have started on a different element\n          // after which focus moved to the current element. Ignore these events and\n          // only handle the first key down event.\n          let shouldStopPropagation = true;\n          if (!state.isPressed && !e.repeat) {\n            state.target = e.currentTarget;\n            state.isPressed = true;\n            state.pointerType = 'keyboard';\n            shouldStopPropagation = triggerPressStart(e, 'keyboard');\n\n            // Focus may move before the key up event, so register the event on the document\n            // instead of the same element where the key down event occurred. Make it capturing so that it will trigger\n            // before stopPropagation from useKeyboard on a child element may happen and thus we can still call triggerPress for the parent element.\n            let originalTarget = e.currentTarget;\n            let pressUp = (e) => {\n              if (isValidKeyboardEvent(e, originalTarget) && !e.repeat && nodeContains(originalTarget, getEventTarget(e)) && state.target) {\n                triggerPressUp(createEvent(state.target, e), 'keyboard');\n              }\n            };\n\n            addGlobalListener(getOwnerDocument(e.currentTarget), 'keyup', chain(pressUp, onKeyUp), true);\n          }\n\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n\n          // Keep track of the keydown events that occur while the Meta (e.g. Command) key is held.\n          // macOS has a bug where keyup events are not fired while the Meta key is down.\n          // When the Meta key itself is released we will get an event for that, and we'll act as if\n          // all of these other keys were released as well.\n          // https://bugs.chromium.org/p/chromium/issues/detail?id=1393524\n          // https://bugs.webkit.org/show_bug.cgi?id=55291\n          // https://bugzilla.mozilla.org/show_bug.cgi?id=1299553\n          if (e.metaKey && isMac()) {\n            state.metaKeyEvents?.set(e.key, e.nativeEvent);\n          }\n        } else if (e.key === 'Meta') {\n          state.metaKeyEvents = new Map();\n        }\n      },\n      onClick(e) {\n        if (e && !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e && e.button === 0 && !state.isTriggeringEvent && !(openLink as any).isOpening) {\n          let shouldStopPropagation = true;\n          if (isDisabled) {\n            e.preventDefault();\n          }\n          \n          // If triggered from a screen reader or by using element.click(),\n          // trigger as if it were a keyboard click.\n          if (!state.ignoreEmulatedMouseEvents && !state.isPressed && (state.pointerType === 'virtual' || isVirtualClick(e.nativeEvent))) {\n            let stopPressStart = triggerPressStart(e, 'virtual');\n            let stopPressUp = triggerPressUp(e, 'virtual');\n            let stopPressEnd = triggerPressEnd(e, 'virtual');\n            triggerClick(e);\n            shouldStopPropagation = stopPressStart && stopPressUp && stopPressEnd;\n          } else if (state.isPressed && state.pointerType !== 'keyboard') {\n            let pointerType = state.pointerType || (e.nativeEvent as PointerEvent).pointerType as PointerType || 'virtual';\n            let stopPressUp = triggerPressUp(createEvent(e.currentTarget, e), pointerType);\n            let stopPressEnd =  triggerPressEnd(createEvent(e.currentTarget, e), pointerType, true);\n            shouldStopPropagation = stopPressUp && stopPressEnd;\n            state.isOverTarget = false;\n            triggerClick(e);\n            cancel(e);\n          }\n\n          state.ignoreEmulatedMouseEvents = false;\n          if (shouldStopPropagation) {\n            e.stopPropagation();\n          }\n        }\n      }\n    };\n\n    let onKeyUp = (e: KeyboardEvent) => {\n      if (state.isPressed && state.target && isValidKeyboardEvent(e, state.target)) {\n        if (shouldPreventDefaultKeyboard(getEventTarget(e), e.key)) {\n          e.preventDefault();\n        }\n\n        let target = getEventTarget(e);\n        let wasPressed = nodeContains(state.target, getEventTarget(e));\n        triggerPressEnd(createEvent(state.target, e), 'keyboard', wasPressed);\n        if (wasPressed) {\n          triggerSyntheticClick(e, state.target);\n        }\n        removeAllGlobalListeners();\n\n        // If a link was triggered with a key other than Enter, open the URL ourselves.\n        // This means the link has a role override, and the default browser behavior\n        // only applies when using the Enter key.\n        if (e.key !== 'Enter' && isHTMLAnchorLink(state.target) && nodeContains(state.target, target) && !e[LINK_CLICKED]) {\n          // Store a hidden property on the event so we only trigger link click once,\n          // even if there are multiple usePress instances attached to the element.\n          e[LINK_CLICKED] = true;\n          openLink(state.target, e, false);\n        }\n\n        state.isPressed = false;\n        state.metaKeyEvents?.delete(e.key);\n      } else if (e.key === 'Meta' && state.metaKeyEvents?.size) {\n        // If we recorded keydown events that occurred while the Meta key was pressed,\n        // and those haven't received keyup events already, fire keyup events ourselves.\n        // See comment above for more info about the macOS bug causing this.\n        let events = state.metaKeyEvents;\n        state.metaKeyEvents = undefined;\n        for (let event of events.values()) {\n          state.target?.dispatchEvent(new KeyboardEvent('keyup', event));\n        }\n      }\n    };\n\n    if (typeof PointerEvent !== 'undefined') {\n      pressProps.onPointerDown = (e) => {\n        // Only handle left clicks, and ignore events that bubbled through portals.\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // iOS safari fires pointer events from VoiceOver with incorrect coordinates/target.\n        // Ignore and let the onClick handler take care of it instead.\n        // https://bugs.webkit.org/show_bug.cgi?id=222627\n        // https://bugs.webkit.org/show_bug.cgi?id=223202\n        if (isVirtualPointerEvent(e.nativeEvent)) {\n          state.pointerType = 'virtual';\n          return;\n        }\n\n        state.pointerType = e.pointerType;\n\n        let shouldStopPropagation = true;\n        if (!state.isPressed) {\n          state.isPressed = true;\n          state.isOverTarget = true;\n          state.activePointerId = e.pointerId;\n          state.target = e.currentTarget as FocusableElement;\n\n          if (!allowTextSelectionOnPress) {\n            disableTextSelection(state.target);\n          }\n\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n\n          // Release pointer capture so that touch interactions can leave the original target.\n          // This enables onPointerLeave and onPointerEnter to fire.\n          let target = getEventTarget(e.nativeEvent);\n          if ('releasePointerCapture' in target) {\n            target.releasePointerCapture(e.pointerId);\n          }\n\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointerup', onPointerUp, false);\n          addGlobalListener(getOwnerDocument(e.currentTarget), 'pointercancel', onPointerCancel, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseDown = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (e.button === 0) {\n          if (preventFocusOnPress) {\n            let dispose = preventFocus(e.target as FocusableElement);\n            if (dispose) {\n              state.disposables.push(dispose);\n            }\n          }\n\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onPointerUp = (e) => {\n        // iOS fires pointerup with zero width and height, so check the pointerType recorded during pointerdown.\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent)) || state.pointerType === 'virtual') {\n          return;\n        }\n\n        // Only handle left clicks. If isPressed is true, delay until onClick.\n        if (e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || e.pointerType);\n        }\n      };\n\n      pressProps.onPointerEnter = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && !state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = true;\n          triggerPressStart(createEvent(state.target, e), state.pointerType);\n        }\n      };\n\n      pressProps.onPointerLeave = (e) => {\n        if (e.pointerId === state.activePointerId && state.target && state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          triggerPressEnd(createEvent(state.target, e), state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.activePointerId && state.isPressed && e.button === 0 && state.target) {\n          if (nodeContains(state.target, getEventTarget(e)) && state.pointerType != null) {\n            // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n            // is mutated between onPointerUp and onClick, and is more compatible with third party libraries.\n            // https://github.com/adobe/react-spectrum/issues/1513\n            // https://issues.chromium.org/issues/40732224\n            // However, iOS and Android do not focus or fire onClick after a long press.\n            // We work around this by triggering a click ourselves after a timeout.\n            // This timeout is canceled during the click event in case the real one fires first.\n            // The timeout must be at least 32ms, because Safari on iOS delays the click event on\n            // non-form elements without certain ARIA roles (for hover emulation).\n            // https://github.com/WebKit/WebKit/blob/dccfae42bb29bd4bdef052e469f604a9387241c0/Source/WebKit/WebProcess/WebPage/ios/WebPageIOS.mm#L875-L892\n            let clicked = false;\n            let timeout = setTimeout(() => {\n              if (state.isPressed && state.target instanceof HTMLElement) {\n                if (clicked) {\n                  cancel(e);\n                } else {\n                  focusWithoutScrolling(state.target);\n                  state.target.click();\n                }\n              }\n            }, 80);\n            // Use a capturing listener to track if a click occurred.\n            // If stopPropagation is called it may never reach our handler.\n            addGlobalListener(e.currentTarget as Document, 'click', () => clicked = true, true);\n            state.disposables.push(() => clearTimeout(timeout));\n          } else {\n            cancel(e);\n          }\n\n          // Ignore subsequent onPointerLeave event before onClick on touch devices.\n          state.isOverTarget = false;\n        }\n      };\n\n      let onPointerCancel = (e: PointerEvent) => {\n        cancel(e);\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        // Safari does not call onPointerCancel when a drag starts, whereas Chrome and Firefox do.\n        cancel(e);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      // NOTE: this fallback branch is entirely used by unit tests.\n      // All browsers now support pointer events, but JSDOM still does not.\n\n      pressProps.onMouseDown = (e) => {\n        // Only handle left clicks\n        if (e.button !== 0 || !nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          e.stopPropagation();\n          return;\n        }\n\n        state.isPressed = true;\n        state.isOverTarget = true;\n        state.target = e.currentTarget;\n        state.pointerType = isVirtualClick(e.nativeEvent) ? 'virtual' : 'mouse';\n\n        // Flush sync so that focus moved during react re-renders occurs before we yield back to the browser.\n        let shouldStopPropagation = flushSync(() => triggerPressStart(e, state.pointerType!));\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        if (preventFocusOnPress) {\n          let dispose = preventFocus(e.target as FocusableElement);\n          if (dispose) {\n            state.disposables.push(dispose);\n          }\n        }\n\n        addGlobalListener(getOwnerDocument(e.currentTarget), 'mouseup', onMouseUp, false);\n      };\n\n      pressProps.onMouseEnter = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = true;\n          shouldStopPropagation = triggerPressStart(e, state.pointerType);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseLeave = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let shouldStopPropagation = true;\n        if (state.isPressed && !state.ignoreEmulatedMouseEvents && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(e, state.pointerType, false);\n          cancelOnPointerExit(e);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onMouseUp = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.ignoreEmulatedMouseEvents && e.button === 0 && !state.isPressed) {\n          triggerPressUp(e, state.pointerType || 'mouse');\n        }\n      };\n\n      let onMouseUp = (e: MouseEvent) => {\n        // Only handle left clicks\n        if (e.button !== 0) {\n          return;\n        }\n\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n          return;\n        }\n\n        if (state.target && state.target.contains(e.target as Element) && state.pointerType != null) {\n          // Wait for onClick to fire onPress. This avoids browser issues when the DOM\n          // is mutated between onMouseUp and onClick, and is more compatible with third party libraries.\n        } else {\n          cancel(e);\n        }\n\n        state.isOverTarget = false;\n      };\n\n      pressProps.onTouchStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        let touch = getTouchFromEvent(e.nativeEvent);\n        if (!touch) {\n          return;\n        }\n        state.activePointerId = touch.identifier;\n        state.ignoreEmulatedMouseEvents = true;\n        state.isOverTarget = true;\n        state.isPressed = true;\n        state.target = e.currentTarget;\n        state.pointerType = 'touch';\n\n        if (!allowTextSelectionOnPress) {\n          disableTextSelection(state.target);\n        }\n\n        let shouldStopPropagation = triggerPressStart(createTouchEvent(state.target, e), state.pointerType);\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        addGlobalListener(getOwnerWindow(e.currentTarget), 'scroll', onScroll, true);\n      };\n\n      pressProps.onTouchMove = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget)) {\n          if (!state.isOverTarget && state.pointerType != null) {\n            state.isOverTarget = true;\n            shouldStopPropagation = triggerPressStart(createTouchEvent(state.target!, e), state.pointerType);\n          }\n        } else if (state.isOverTarget && state.pointerType != null) {\n          state.isOverTarget = false;\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n          cancelOnPointerExit(createTouchEvent(state.target!, e));\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n      };\n\n      pressProps.onTouchEnd = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        if (!state.isPressed) {\n          e.stopPropagation();\n          return;\n        }\n\n        let touch = getTouchById(e.nativeEvent, state.activePointerId);\n        let shouldStopPropagation = true;\n        if (touch && isOverTarget(touch, e.currentTarget) && state.pointerType != null) {\n          triggerPressUp(createTouchEvent(state.target!, e), state.pointerType);\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType);\n          triggerSyntheticClick(e.nativeEvent, state.target!);\n        } else if (state.isOverTarget && state.pointerType != null) {\n          shouldStopPropagation = triggerPressEnd(createTouchEvent(state.target!, e), state.pointerType, false);\n        }\n\n        if (shouldStopPropagation) {\n          e.stopPropagation();\n        }\n\n        state.isPressed = false;\n        state.activePointerId = null;\n        state.isOverTarget = false;\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.target && !allowTextSelectionOnPress) {\n          restoreTextSelection(state.target);\n        }\n        removeAllGlobalListeners();\n      };\n\n      pressProps.onTouchCancel = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        e.stopPropagation();\n        if (state.isPressed) {\n          cancel(createTouchEvent(state.target!, e));\n        }\n      };\n\n      let onScroll = (e: Event) => {\n        if (state.isPressed && nodeContains(getEventTarget(e), state.target)) {\n          cancel({\n            currentTarget: state.target,\n            shiftKey: false,\n            ctrlKey: false,\n            metaKey: false,\n            altKey: false\n          });\n        }\n      };\n\n      pressProps.onDragStart = (e) => {\n        if (!nodeContains(e.currentTarget, getEventTarget(e.nativeEvent))) {\n          return;\n        }\n\n        cancel(e);\n      };\n    }\n\n    return pressProps;\n  }, [\n    addGlobalListener,\n    isDisabled,\n    preventFocusOnPress,\n    removeAllGlobalListeners,\n    allowTextSelectionOnPress,\n    cancel,\n    cancelOnPointerExit,\n    triggerPressEnd,\n    triggerPressStart,\n    triggerPressUp,\n    triggerClick,\n    triggerSyntheticClick\n  ]);\n\n  // Avoid onClick delay for double tap to zoom by default.\n  useEffect(() => {\n    if (!domRef || process.env.NODE_ENV === 'test') {\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(domRef.current);\n    if (!ownerDocument || !ownerDocument.head || ownerDocument.getElementById(STYLE_ID)) {\n      return;\n    }\n\n    const style = ownerDocument.createElement('style');\n    style.id = STYLE_ID;\n    // touchAction: 'manipulation' is supposed to be equivalent, but in\n    // Safari it causes onPointerCancel not to fire on scroll.\n    // https://bugs.webkit.org/show_bug.cgi?id=240917\n    style.textContent = `\n@layer {\n  [${PRESSABLE_ATTRIBUTE}] {\n    touch-action: pan-x pan-y pinch-zoom;\n  }\n}\n    `.trim();\n    ownerDocument.head.prepend(style);\n  }, [domRef]);\n\n  // Remove user-select: none in case component unmounts immediately after pressStart\n  useEffect(() => {\n    let state = ref.current;\n    return () => {\n      if (!allowTextSelectionOnPress) {\n        restoreTextSelection(state.target ?? undefined);\n      }\n      for (let dispose of state.disposables) {\n        dispose();\n      }\n      state.disposables = [];\n    };\n  }, [allowTextSelectionOnPress]);\n\n  return {\n    isPressed: isPressedProp || isPressed,\n    pressProps: mergeProps(domProps, pressProps, {[PRESSABLE_ATTRIBUTE]: true})\n  };\n}\n\nfunction isHTMLAnchorLink(target: Element): target is HTMLAnchorElement {\n  return target.tagName === 'A' && target.hasAttribute('href');\n}\n\nfunction isValidKeyboardEvent(event: KeyboardEvent, currentTarget: Element): boolean {\n  const {key, code} = event;\n  const element = currentTarget as HTMLElement;\n  const role = element.getAttribute('role');\n  // Accessibility for keyboards. Space and Enter only.\n  // \"Spacebar\" is for IE 11\n  return (\n    (key === 'Enter' || key === ' ' || key === 'Spacebar' || code === 'Space') &&\n    !((element instanceof getOwnerWindow(element).HTMLInputElement && !isValidInputKey(element, key)) ||\n      element instanceof getOwnerWindow(element).HTMLTextAreaElement ||\n      element.isContentEditable) &&\n    // Links should only trigger with Enter key\n    !((role === 'link' || (!role && isHTMLAnchorLink(element))) && key !== 'Enter')\n  );\n}\n\nfunction getTouchFromEvent(event: TouchEvent): Touch | null {\n  const {targetTouches} = event;\n  if (targetTouches.length > 0) {\n    return targetTouches[0];\n  }\n  return null;\n}\n\nfunction getTouchById(\n  event: TouchEvent,\n  pointerId: null | number\n): null | Touch {\n  const changedTouches = event.changedTouches;\n  for (let i = 0; i < changedTouches.length; i++) {\n    const touch = changedTouches[i];\n    if (touch.identifier === pointerId) {\n      return touch;\n    }\n  }\n  return null;\n}\n\nfunction createTouchEvent(target: FocusableElement, e: RTouchEvent<FocusableElement>): EventBase {\n  let clientX = 0;\n  let clientY = 0;\n  if (e.targetTouches && e.targetTouches.length === 1) {\n    clientX = e.targetTouches[0].clientX;\n    clientY = e.targetTouches[0].clientY;\n  }\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\nfunction createEvent(target: FocusableElement, e: EventBase): EventBase {\n  let clientX = e.clientX;\n  let clientY = e.clientY;\n  return {\n    currentTarget: target,\n    shiftKey: e.shiftKey,\n    ctrlKey: e.ctrlKey,\n    metaKey: e.metaKey,\n    altKey: e.altKey,\n    clientX,\n    clientY\n  };\n}\n\ninterface Rect {\n  top: number,\n  right: number,\n  bottom: number,\n  left: number\n}\n\ninterface EventPoint {\n  clientX: number,\n  clientY: number,\n  width?: number,\n  height?: number,\n  radiusX?: number,\n  radiusY?: number\n}\n\nfunction getPointClientRect(point: EventPoint): Rect {\n  let offsetX = 0;\n  let offsetY = 0;\n  if (point.width !== undefined) {\n    offsetX = (point.width / 2);\n  } else if (point.radiusX !== undefined) {\n    offsetX = point.radiusX;\n  }\n  if (point.height !== undefined) {\n    offsetY = (point.height / 2);\n  } else if (point.radiusY !== undefined) {\n    offsetY = point.radiusY;\n  }\n\n  return {\n    top: point.clientY - offsetY,\n    right: point.clientX + offsetX,\n    bottom: point.clientY + offsetY,\n    left: point.clientX - offsetX\n  };\n}\n\nfunction areRectanglesOverlapping(a: Rect, b: Rect) {\n  // check if they cannot overlap on x axis\n  if (a.left > b.right || b.left > a.right) {\n    return false;\n  }\n  // check if they cannot overlap on y axis\n  if (a.top > b.bottom || b.top > a.bottom) {\n    return false;\n  }\n  return true;\n}\n\nfunction isOverTarget(point: EventPoint, target: Element) {\n  let rect = target.getBoundingClientRect();\n  let pointRect = getPointClientRect(point);\n  return areRectanglesOverlapping(rect, pointRect);\n}\n\nfunction shouldPreventDefaultUp(target: Element) {\n  if (target instanceof HTMLInputElement) {\n    return false;\n  }\n\n  if (target instanceof HTMLButtonElement) {\n    return target.type !== 'submit' && target.type !== 'reset';\n  }\n\n  if (isHTMLAnchorLink(target)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction shouldPreventDefaultKeyboard(target: Element, key: string) {\n  if (target instanceof HTMLInputElement) {\n    return !isValidInputKey(target, key);\n  }\n\n  return shouldPreventDefaultUp(target);\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\nfunction isValidInputKey(target: HTMLInputElement, key: string) {\n  // Only space should toggle checkboxes and radios, not enter.\n  return target.type === 'checkbox' || target.type === 'radio'\n    ? key === ' '\n    : nonTextInputTypes.has(target.type);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {\n  focusWithoutScrolling,\n  getActiveElement,\n  getOwnerDocument,\n  runAfterTransition\n} from '@react-aria/utils';\nimport {getInteractionModality} from './useFocusVisible';\n\n/**\n * A utility function that focuses an element while avoiding undesired side effects such\n * as page scrolling and screen reader issues with CSS transitions.\n */\nexport function focusSafely(element: FocusableElement): void {\n  // If the user is interacting with a virtual cursor, e.g. screen reader, then\n  // wait until after any animated transitions that are currently occurring on\n  // the page before shifting focus. This avoids issues with VoiceOver on iOS\n  // causing the page to scroll when moving focus if the element is transitioning\n  // from off the screen.\n  const ownerDocument = getOwnerDocument(element);\n  const activeElement = getActiveElement(ownerDocument);\n  if (getInteractionModality() === 'virtual') {\n    let lastFocusedElement = activeElement;\n    runAfterTransition(() => {\n      // If focus did not move and the element is still in the document, focus it.\n      if (getActiveElement(ownerDocument) === lastFocusedElement && element.isConnected) {\n        focusWithoutScrolling(element);\n      }\n    });\n  } else {\n    focusWithoutScrolling(element);\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BaseEvent} from '@react-types/shared';\nimport {SyntheticEvent} from 'react';\n\n/**\n * This function wraps a React event handler to make stopPropagation the default, and support continuePropagation instead.\n */\nexport function createEventHandler<T extends SyntheticEvent>(handler?: (e: BaseEvent<T>) => void): ((e: T) => void) | undefined {\n  if (!handler) {\n    return undefined;\n  }\n\n  let shouldStopPropagation = true;\n  return (e: T) => {\n    let event: BaseEvent<T> = {\n      ...e,\n      preventDefault() {\n        e.preventDefault();\n      },\n      isDefaultPrevented() {\n        return e.isDefaultPrevented();\n      },\n      stopPropagation() {\n        if (shouldStopPropagation && process.env.NODE_ENV !== 'production') {\n          console.error('stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.');\n        } else {\n          shouldStopPropagation = true;\n        }\n      },\n      continuePropagation() {\n        shouldStopPropagation = false;\n      },\n      isPropagationStopped() {\n        return shouldStopPropagation;\n      }\n    };\n\n    handler(event);\n\n    if (shouldStopPropagation) {\n      e.stopPropagation();\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {createEventHandler} from './createEventHandler';\nimport {DOMAttributes, KeyboardEvents} from '@react-types/shared';\n\nexport interface KeyboardProps extends KeyboardEvents {\n  /** Whether the keyboard events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface KeyboardResult {\n  /** Props to spread onto the target element. */\n  keyboardProps: DOMAttributes\n}\n\n/**\n * Handles keyboard interactions for a focusable element.\n */\nexport function useKeyboard(props: KeyboardProps): KeyboardResult {\n  return {\n    keyboardProps: props.isDisabled ? {} : {\n      onKeyDown: createEventHand<PERSON>(props.onKeyDown),\n      onKeyUp: createEvent<PERSON>and<PERSON>(props.onKeyUp)\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableDOMProps, FocusableElement, FocusableProps, RefObject} from '@react-types/shared';\nimport {focusSafely} from './';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport React, {ForwardedRef, forwardRef, MutableRefObject, ReactElement, ReactNode, useContext, useEffect, useRef} from 'react';\nimport {useFocus} from './useFocus';\nimport {useKeyboard} from './useKeyboard';\n\nexport interface FocusableOptions<T = FocusableElement> extends FocusableProps<T>, FocusableDOMProps {\n  /** Whether focus should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusableProviderProps extends DOMAttributes {\n  /** The child element to provide DOM props to. */\n  children?: ReactNode\n}\n\ninterface FocusableContextValue extends FocusableProviderProps {\n  ref?: MutableRefObject<FocusableElement | null>\n}\n\n// Exported for @react-aria/collections, which forwards this context.\n/** @private */\nexport let FocusableContext = React.createContext<FocusableContextValue | null>(null);\n\nfunction useFocusableContext(ref: RefObject<FocusableElement | null>): FocusableContextValue {\n  let context = useContext(FocusableContext) || {};\n  useSyncRef(context, ref);\n\n  // eslint-disable-next-line\n  let {ref: _, ...otherProps} = context;\n  return otherProps;\n}\n\n/**\n * Provides DOM props to the nearest focusable child.\n */\nexport const FocusableProvider = React.forwardRef(function FocusableProvider(props: FocusableProviderProps, ref: ForwardedRef<FocusableElement>) {\n  let {children, ...otherProps} = props;\n  let objRef = useObjectRef(ref);\n  let context = {\n    ...otherProps,\n    ref: objRef\n  };\n\n  return (\n    <FocusableContext.Provider value={context}>\n      {children}\n    </FocusableContext.Provider>\n  );\n});\n\nexport interface FocusableAria {\n  /** Props for the focusable element. */\n  focusableProps: DOMAttributes\n}\n\n/**\n * Used to make an element focusable and capable of auto focus.\n */\nexport function useFocusable<T extends FocusableElement = FocusableElement>(props: FocusableOptions<T>, domRef: RefObject<FocusableElement | null>): FocusableAria {\n  let {focusProps} = useFocus(props);\n  let {keyboardProps} = useKeyboard(props);\n  let interactions = mergeProps(focusProps, keyboardProps);\n  let domProps = useFocusableContext(domRef);\n  let interactionProps = props.isDisabled ? {} : domProps;\n  let autoFocusRef = useRef(props.autoFocus);\n\n  useEffect(() => {\n    if (autoFocusRef.current && domRef.current) {\n      focusSafely(domRef.current);\n    }\n    autoFocusRef.current = false;\n  }, [domRef]);\n\n  // Always set a tabIndex so that Safari allows focusing native buttons and inputs.\n  let tabIndex: number | undefined = props.excludeFromTabOrder ? -1 : 0;\n  if (props.isDisabled) {\n    tabIndex = undefined;\n  }\n\n  return {\n    focusableProps: mergeProps(\n      {\n        ...interactions,\n        tabIndex\n      },\n      interactionProps\n    )\n  };\n}\n\nexport interface FocusableComponentProps extends FocusableOptions {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Focusable = forwardRef(({children, ...props}: FocusableComponentProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Focusable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Focusable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary' &&\n      el.localName !== 'img' &&\n      el.localName !== 'svg'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Focusable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'tabpanel' &&\n        role !== 'textbox' &&\n        role !== 'treeitem' &&\n        // aria-describedby is also announced on these roles\n        role !== 'img' &&\n        role !== 'meter' &&\n        role !== 'progressbar'\n      ) {\n        console.warn(`<Focusable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement} from '@react-types/shared';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef} from '@react-aria/utils';\nimport {PressProps, usePress} from './usePress';\nimport React, {ForwardedRef, ReactElement, useEffect} from 'react';\nimport {useFocusable} from './useFocusable';\n\ninterface PressableProps extends PressProps {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Pressable = React.forwardRef(({children, ...props}: PressableProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {pressProps} = usePress({...props, ref});\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Pressable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Pressable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Pressable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'textbox' &&\n        role !== 'treeitem'\n      ) {\n        console.warn(`<Pressable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(pressProps, focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {mergeProps, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport {PressProps} from './usePress';\nimport {PressResponderContext} from './context';\nimport React, {ForwardedRef, JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\ninterface PressResponderProps extends PressProps {\n  children: ReactNode\n}\n\nexport const PressResponder = React.forwardRef(({children, ...props}: PressResponderProps, ref: ForwardedRef<FocusableElement>) => {\n  let isRegistered = useRef(false);\n  let prevContext = useContext(PressResponderContext);\n  ref = useObjectRef(ref || prevContext?.ref);\n  let context = mergeProps(prevContext || {}, {\n    ...props,\n    ref,\n    register() {\n      isRegistered.current = true;\n      if (prevContext) {\n        prevContext.register();\n      }\n    }\n  });\n\n  useSyncRef(prevContext, ref);\n\n  useEffect(() => {\n    if (!isRegistered.current) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'A PressResponder was rendered without a pressable child. ' +\n          'Either call the usePress hook, or wrap your DOM node with <Pressable> component.'\n        );\n      }\n      isRegistered.current = true; // only warn once in strict mode.\n    }\n  }, []);\n\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n});\n\nexport function ClearPressResponder({children}: {children: ReactNode}): JSX.Element {\n  let context = useMemo(() => ({register: () => {}}), []);\n  return (\n    <PressResponderContext.Provider value={context}>\n      {children}\n    </PressResponderContext.Provider>\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, useEffectEvent} from '@react-aria/utils';\nimport {RefObject} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\n\nexport interface InteractOutsideProps {\n  ref: RefObject<Element | null>,\n  onInteractOutside?: (e: PointerEvent) => void,\n  onInteractOutsideStart?: (e: PointerEvent) => void,\n  /** Whether the interact outside events should be disabled. */\n  isDisabled?: boolean\n}\n\n/**\n * Example, used in components like Dialogs and Popovers so they can close\n * when a user clicks outside them.\n */\nexport function useInteractOutside(props: InteractOutsideProps): void {\n  let {ref, onInteractOutside, isDisabled, onInteractOutsideStart} = props;\n  let stateRef = useRef({\n    isPointerDown: false,\n    ignoreEmulatedMouseEvents: false\n  });\n\n  let onPointerDown = useEffectEvent((e) => {\n    if (onInteractOutside && isValidEvent(e, ref)) {\n      if (onInteractOutsideStart) {\n        onInteractOutsideStart(e);\n      }\n      stateRef.current.isPointerDown = true;\n    }\n  });\n\n  let triggerInteractOutside = useEffectEvent((e: PointerEvent) => {\n    if (onInteractOutside) {\n      onInteractOutside(e);\n    }\n  });\n\n  useEffect(() => {\n    let state = stateRef.current;\n    if (isDisabled) {\n      return;\n    }\n\n    const element = ref.current;\n    const documentObject = getOwnerDocument(element);\n\n    // Use pointer events if available. Otherwise, fall back to mouse and touch events.\n    if (typeof PointerEvent !== 'undefined') {\n      let onClick = (e) => {\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      // changing these to capture phase fixed combobox\n      // Use click instead of pointerup to avoid Android Chrome issue\n      // https://issues.chromium.org/issues/40732224\n      documentObject.addEventListener('pointerdown', onPointerDown, true);\n      documentObject.addEventListener('click', onClick, true);\n\n      return () => {\n        documentObject.removeEventListener('pointerdown', onPointerDown, true);\n        documentObject.removeEventListener('click', onClick, true);\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      let onMouseUp = (e) => {\n        if (state.ignoreEmulatedMouseEvents) {\n          state.ignoreEmulatedMouseEvents = false;\n        } else if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      let onTouchEnd = (e) => {\n        state.ignoreEmulatedMouseEvents = true;\n        if (state.isPointerDown && isValidEvent(e, ref)) {\n          triggerInteractOutside(e);\n        }\n        state.isPointerDown = false;\n      };\n\n      documentObject.addEventListener('mousedown', onPointerDown, true);\n      documentObject.addEventListener('mouseup', onMouseUp, true);\n      documentObject.addEventListener('touchstart', onPointerDown, true);\n      documentObject.addEventListener('touchend', onTouchEnd, true);\n\n      return () => {\n        documentObject.removeEventListener('mousedown', onPointerDown, true);\n        documentObject.removeEventListener('mouseup', onMouseUp, true);\n        documentObject.removeEventListener('touchstart', onPointerDown, true);\n        documentObject.removeEventListener('touchend', onTouchEnd, true);\n      };\n    }\n  }, [ref, isDisabled, onPointerDown, triggerInteractOutside]);\n}\n\nfunction isValidEvent(event, ref) {\n  if (event.button > 0) {\n    return false;\n  }\n  if (event.target) {\n    // if the event target is no longer in the document, ignore\n    const ownerDocument = event.target.ownerDocument;\n    if (!ownerDocument || !ownerDocument.documentElement.contains(event.target)) {\n      return false;\n    }\n    // If the target is within a top layer element (e.g. toasts), ignore.\n    if (event.target.closest('[data-react-aria-top-layer]')) {\n      return false;\n    }\n  }\n\n  if (!ref.current) {\n    return false;\n  }\n\n  // When the event source is inside a Shadow DOM, event.target is just the shadow root.\n  // Using event.composedPath instead means we can get the actual element inside the shadow root.\n  // This only works if the shadow root is open, there is no way to detect if it is closed.\n  // If the event composed path contains the ref, interaction is inside.\n  return !event.composedPath().includes(ref.current);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {mergeProps} from '@react-aria/utils';\nimport React, {CSSProperties, JSX, JSXElementConstructor, ReactNode, useMemo, useState} from 'react';\nimport {useFocusWithin} from '@react-aria/interactions';\n\nexport interface VisuallyHiddenProps extends DOMAttributes {\n  /** The content to visually hide. */\n  children?: ReactNode,\n\n  /**\n   * The element type for the container.\n   * @default 'div'\n   */\n  elementType?: string | JSXElementConstructor<any>,\n\n  /** Whether the element should become visible on focus, for example skip links. */\n  isFocusable?: boolean\n}\n\nconst styles: CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  clipPath: 'inset(50%)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  width: '1px',\n  whiteSpace: 'nowrap'\n};\n\nexport interface VisuallyHiddenAria {\n  visuallyHiddenProps: DOMAttributes\n}\n\n/**\n * Provides props for an element that hides its children visually\n * but keeps content visible to assistive technology.\n */\nexport function useVisuallyHidden(props: VisuallyHiddenProps = {}): VisuallyHiddenAria {\n  let {\n    style,\n    isFocusable\n  } = props;\n\n  let [isFocused, setFocused] = useState(false);\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !isFocusable,\n    onFocusWithinChange: (val) => setFocused(val)\n  });\n\n  // If focused, don't hide the element.\n  let combinedStyles = useMemo(() => {\n    if (isFocused) {\n      return style;\n    } else if (style) {\n      return {...styles, ...style};\n    } else {\n      return styles;\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isFocused]);\n\n  return {\n    visuallyHiddenProps: {\n      ...focusWithinProps,\n      style: combinedStyles\n    }\n  };\n}\n\n/**\n * VisuallyHidden hides its children visually, while keeping content visible\n * to screen readers.\n */\nexport function VisuallyHidden(props: VisuallyHiddenProps): JSX.Element {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  let {children, elementType: Element = 'div', isFocusable, style, ...otherProps} = props;\n  let {visuallyHiddenProps} = useVisuallyHidden(props);\n\n  return (\n    <Element {...mergeProps(otherProps, visuallyHiddenProps)}>\n      {children}\n    </Element>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAiBO,IAAM,4CAAkB,OAAO,aAAa,eAC/C,GAAA,aAAAA,SAAM,kBACN,MAAA;AAAO;;;;ACJJ,SAAS,0CAAmC,IAAM;AACvD,QAAM,OAAM,GAAA,cAAAC,QAA6B,IAAA;AACzC,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,UAAU;EAChB,GAAG;IAAC;GAAG;AAEP,UAAO,GAAA,cAAAC,aAAe,IAAI,SAAA;AACxB,UAAM,IAAI,IAAI;AACd,WAAO,MAAA,QAAA,MAAA,SAAA,SAAA,EAAA,GAAO,IAAA;EAChB,GAAG,CAAA,CAAE;AACP;;;;ACJO,SAAS,0CAAkB,cAA2B;AAC3D,MAAI,CAAC,OAAO,QAAA,KAAY,GAAA,cAAAC,UAAS,YAAA;AACjC,MAAI,UAAgD,GAAA,cAAAC,QAA4B,IAAA;AAIhF,MAAI,WAAU,GAAA,2CAAe,MAAA;AAC3B,QAAI,CAAC,OAAO;AACV;AAGF,QAAI,WAAW,OAAO,QAAQ,KAAI;AAGlC,QAAI,SAAS,MAAM;AACjB,aAAO,UAAU;AACjB;IACF;AAKA,QAAI,UAAU,SAAS;AACrB,cAAA;;AAEA,eAAS,SAAS,KAAK;EAE3B,CAAA;AAEA,GAAA,GAAA,2CAAgB,MAAA;AAEd,QAAI,OAAO;AACT,cAAA;EAEJ,CAAA;AAEA,MAAI,SAAQ,GAAA,2CAAe,CAAA,OAAA;AACzB,WAAO,UAAU,GAAG,KAAA;AACpB,YAAA;EACF,CAAA;AAEA,SAAO;IAAC;IAAO;;AACjB;;;;;;;AC9BA,IAAM,uCAAkC;EACtC,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,IAAA,CAAA;EAC1C,SAAS;AACX;AAEA,IAAM,oCAAa,GAAA,cAAAC,SAAM,cAA+B,oCAAA;AACxD,IAAM,sCAAe,GAAA,cAAAA,SAAM,cAAc,KAAA;AAwDzC,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS,aAAa;AAG/B,IAAI,qCAAe,oBAAI,QAAA;AAEvB,SAAS,iCAAW,aAAa,OAAK;AACpC,MAAI,OAAM,GAAA,cAAAC,YAAW,gCAAA;AACrB,MAAI,OAAM,GAAA,cAAAC,QAAsB,IAAA;AAEhC,MAAI,IAAI,YAAY,QAAQ,CAAC,YAAY;QAWpB,6EAAA;AAAnB,QAAI,gBAAe,6DAAA,GAAA,cAAAC,SAAM,wDAAkD,QAAxD,8DAAA,SAAA,UAAA,8EAAA,0DAA0D,uBAAiB,QAA3E,gFAAA,SAAA,SAAA,4EAA6E;AAChG,QAAI,cAAc;AAChB,UAAI,qBAAqB,mCAAa,IAAI,YAAA;AAC1C,UAAI,sBAAsB;AAExB,2CAAa,IAAI,cAAc;UAC7B,IAAI,IAAI;UACR,OAAO,aAAa;QACtB,CAAA;eACS,aAAa,kBAAkB,mBAAmB,OAAO;AAIlE,YAAI,UAAU,mBAAmB;AACjC,2CAAa,OAAO,YAAA;MACtB;IACF;AAGA,QAAI,UAAU,EAAE,IAAI;EACtB;AAGA,SAAO,IAAI;AACb;AAEA,SAAS,yCAAmB,WAAkB;AAC5C,MAAI,OAAM,GAAA,cAAAF,YAAW,gCAAA;AAIrB,MAAI,QAAQ,wCAAkB,CAAC,mCAAa;AAC1C,YAAQ,KAAK,iJAAA;AAGf,MAAI,UAAU,iCAAW,CAAC,CAAC,SAAA;AAC3B,MAAI,SAAS,QAAQ,wCAAkB,QAAkC,eAAe,aAAa,IAAI,MAAM;AAC/G,SAAO,aAAa,GAAG,MAAA,IAAU,OAAA;AACnC;AAEA,SAAS,yCAAmB,WAAkB;AAC5C,MAAI,MAAK,GAAA,cAAAE,SAAM,MAAK;AACpB,MAAI,CAAC,MAAA,KAAU,GAAA,cAAAC,UAAS,0CAAA,CAAA;AACxB,MAAI,SAAS,UAAU,QAAkC,eAAe,aAAa,qCAAe,MAAM;AAC1G,SAAO,aAAa,GAAG,MAAA,IAAU,EAAA;AACnC;AAIO,IAAM,4CAAe,QAAO,GAAA,cAAAD,SAAM,OAAA,MAAa,aAAa,2CAAqB;AAExF,SAAS,oCAAA;AACP,SAAO;AACT;AAEA,SAAS,0CAAA;AACP,SAAO;AACT;AAGA,SAAS,gCAAU,eAAyB;AAE1C,SAAO,MAAA;EAAO;AAChB;AAOO,SAAS,4CAAA;AAEd,MAAI,QAAO,GAAA,cAAAA,SAAM,sBAAA,MAA4B;AAC3C,YAAO,GAAA,cAAAA,SAAM,sBAAA,EAAwB,iCAAW,mCAAa,uCAAA;AAI/D,UAAO,GAAA,cAAAF,YAAW,kCAAA;AACpB;;;AClLA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS,aAAa;AAGxB,IAAI,4CAA2D,oBAAI,IAAA;AAI1E,IAAI;AACJ,IAAI,OAAO,yBAAyB;AAClC,mCAAW,IAAI,qBAA6B,CAAC,cAAA;AAC3C,8CAAc,OAAO,SAAA;EACvB,CAAA;AAOK,SAAS,0CAAM,WAAkB;AACtC,MAAI,CAAC,OAAO,QAAA,KAAY,GAAA,cAAAI,UAAS,SAAA;AACjC,MAAI,UAAS,GAAA,cAAAC,QAAO,IAAA;AAEpB,MAAI,OAAM,GAAA,2CAAa,KAAA;AACvB,MAAI,cAAa,GAAA,cAAAA,QAAO,IAAA;AAExB,MAAI;AACF,mCAAS,SAAS,YAAY,GAAA;AAGhC,MAAI,iCAAW;AACb,UAAM,aAAa,0CAAc,IAAI,GAAA;AACrC,QAAI,cAAc,CAAC,WAAW,SAAS,MAAA;AACrC,iBAAW,KAAK,MAAA;;AAEhB,gDAAc,IAAI,KAAK;QAAC;OAAO;EAEnC;AAEA,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,IAAI;AACR,WAAO,MAAA;AAGL,UAAI;AACF,uCAAS,WAAW,UAAA;AAEtB,gDAAc,OAAO,CAAA;IACvB;EACF,GAAG;IAAC;GAAI;AAIR,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,QAAI,QAAQ,OAAO;AACnB,QAAI;AAAS,eAAS,KAAA;AAEtB,WAAO,MAAA;AACL,UAAI;AAAS,eAAO,UAAU;IAChC;EACF,CAAA;AAEA,SAAO;AACT;AAMO,SAAS,0CAAS,KAAa,KAAW;AAC/C,MAAI,QAAQ;AACV,WAAO;AAGT,MAAI,UAAU,0CAAc,IAAI,GAAA;AAChC,MAAI,SAAS;AACX,YAAQ,QAAQ,CAAA,QAAQ,IAAI,UAAU,GAAA;AACtC,WAAO;EACT;AAEA,MAAI,UAAU,0CAAc,IAAI,GAAA;AAChC,MAAI,SAAS;AACX,YAAQ,QAAQ,CAAC,QAAS,IAAI,UAAU,GAAA;AACxC,WAAO;EACT;AAEA,SAAO;AACT;AAOO,SAAS,0CAAU,WAA+B,CAAA,GAAE;AACzD,MAAI,KAAK,0CAAA;AACT,MAAI,CAAC,YAAY,aAAA,KAAiB,GAAA,2CAAe,EAAA;AACjD,MAAI,YAAW,GAAA,cAAAC,aAAY,MAAA;AACzB,kBAAc,aAAA;AACZ,YAAM;AAEN,YAAM,SAAS,eAAe,EAAA,IAAM,KAAK;IAC3C,CAAA;EACF,GAAG;IAAC;IAAI;GAAc;AAEtB,GAAA,GAAA,2CAAgB,UAAU;IAAC;IAAI;OAAa;GAAS;AAErD,SAAO;AACT;;;ACjHO,SAAS,6CAAS,WAAgB;AACvC,SAAO,IAAI,SAAA;AACT,aAAS,YAAY;AACnB,UAAI,OAAO,aAAa;AACtB,iBAAA,GAAY,IAAA;EAGlB;AACF;;;ACvBO,IAAM,4CAAmB,CAAC,OAAA;MACxB;AAAP,UAAO,oBAAA,OAAA,QAAA,OAAA,SAAA,SAAA,GAAI,mBAAa,QAAjB,sBAAA,SAAA,oBAAqB;AAC9B;AAEO,IAAM,4CAAiB,CAC5B,OAAA;AAEA,MAAI,MAAM,YAAY,MAAM,GAAG,WAAW;AACxC,WAAO;AAGT,QAAM,MAAM,0CAAiB,EAAA;AAC7B,SAAO,IAAI,eAAe;AAC5B;AAKA,SAAS,6BAAO,OAAc;AAC5B,SAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,aAAa;AACxC;AAKO,SAAS,0CAAa,MAAiB;AAC5C,SAAO,6BAAO,IAAA,KACZ,KAAK,aAAa,KAAK,0BACvB,UAAU;AACd;;;ACnBA,IAAI,mCAAa;AAcV,SAAS,4CAAA;AACd,SAAO;AACT;;;ACrBO,SAAS,0CACd,MACA,WAAkC;AAElC,MAAI,EAAC,GAAA,2CAAQ;AACX,WAAO,aAAa,OAAO,KAAK,SAAS,SAAA,IAAa;AAGxD,MAAI,CAAC,QAAQ,CAAC;AACZ,WAAO;AAGT,MAAI,cAAqD;AAEzD,SAAO,gBAAgB,MAAM;AAC3B,QAAI,gBAAgB;AAClB,aAAO;AAGT,QAAK,YAAgC,YAAY,UAC9C,YAAgC;AAEjC,oBAAe,YAAgC,aAAc;cACpD,GAAA,2CAAa,WAAA;AAEtB,oBAAc,YAAY;;AAE1B,oBAAc,YAAY;EAE9B;AAEA,SAAO;AACT;AAKO,IAAM,4CAAmB,CAAC,MAAgB,aAAQ;MAOvD;AANA,MAAI,EAAC,GAAA,2CAAQ;AACX,WAAO,IAAI;AAEb,MAAI,gBAAgC,IAAI;AAExC,SAAO,iBAAiB,gBAAgB,mBACxC,4BAAA,cAAc,gBAAU,QAAxB,8BAAA,SAAA,SAAA,0BAA0B;AACxB,oBAAgB,cAAc,WAAW;AAG3C,SAAO;AACT;AAKO,SAAS,0CAAgC,OAAQ;AACtD,OAAI,GAAA,2CAAQ,KAAQ,MAAM,OAAuB,YAAY;AAC3D,QAAI,MAAM;AACR,aAAO,MAAM,aAAY,EAAG,CAAA;EAEhC;AACA,SAAO,MAAM;AACf;;;AChEO,IAAM,4CAAN,MAAM;EAmEX,IAAW,cAAoB;AAC7B,WAAO,KAAK;EACd;EAEA,IAAW,YAAY,MAAY;AACjC,QAAI,EAAC,GAAA,2CAAa,KAAK,MAAM,IAAA;AAC3B,YAAM,IAAI,MACR,0EAAA;AAIJ,UAAM,UAAwB,CAAA;AAC9B,QAAI,UAAmC;AACvC,QAAI,2BAA2B;AAE/B,SAAK,eAAe;AAEpB,WAAO,WAAW,YAAY,KAAK;AACjC,UAAI,QAAQ,aAAa,KAAK,wBAAwB;AACpD,cAAM,aAAa;AAEnB,cAAMC,UAAS,KAAK,KAAK,iBACvB,YACA,KAAK,YACL;UAAC,YAAY,KAAK;QAAW,CAAA;AAG/B,gBAAQ,KAAKA,OAAA;AAEb,QAAAA,QAAO,cAAc;AAErB,aAAK,eAAe,IAAIA,OAAA;AAExB,kBAAU,2BAA2B,WAAW;MAClD;AACE,kBAAU,QAAQ;AAItB,UAAM,SAAS,KAAK,KAAK,iBACvB,KAAK,MACL,KAAK,YACL;MAAC,YAAY,KAAK;IAAW,CAAA;AAG/B,YAAQ,KAAK,MAAA;AAEb,WAAO,cAAc;AAErB,SAAK,eAAe,IAAI,MAAA;AAExB,SAAK,eAAe;EACtB;EAEA,IAAW,MAAgB;AACzB,WAAO,KAAK;EACd;EAEO,aAA0B;AAC/B,QAAI,cAAc,KAAK;AACvB,QAAI,UAAU,KAAK,SAAQ;AAC3B,QAAI,EAAC,GAAA,2CAAa,aAAa,OAAA,GAAU;AACvC,WAAK,cAAc;AACnB,aAAO;IACT;AACA,QAAI;AACF,WAAK,cAAc;AAErB,WAAO;EACT;EAEO,YAAyB;AAC9B,QAAI,SAAS,KAAK,aAAa,CAAA;AAC/B,QAAI,UAAU,OAAO,UAAS;AAC9B,QAAI;AACF,WAAK,cAAc;AAErB,WAAO;EACT;EAEO,WAAwB;AAC7B,UAAM,WAAW,KAAK,aAAa,CAAA,EAAG,SAAQ;AAE9C,QAAI,UAAU;AACZ,YAAM,aAAc,SAAqB;AAEzC,UAAI,YAAY;YAKH;AAJX,YAAI;AAEJ,YAAI,OAAO,KAAK,WAAW;AACzB,uBAAa,KAAK,OAAO,QAAA;kBAChB,eAAA,KAAK,YAAM,QAAX,iBAAA,SAAA,SAAA,aAAa;AACtB,uBAAa,KAAK,OAAO,WAAW,QAAA;AAGtC,YAAI,eAAe,WAAW,eAAe;AAC3C,eAAK,cAAc;AACnB,iBAAO;QACT;AAIA,YAAI,UAAU,KAAK,SAAQ;AAC3B,YAAI;AACF,eAAK,cAAc;AAErB,eAAO;MACT;AAEA,UAAI;AACF,aAAK,cAAc;AAErB,aAAO;IACT,OAAO;AACL,UAAI,KAAK,aAAa,SAAS,GAAG;AAChC,aAAK,aAAa,MAAK;AAEvB,YAAI,UAAU,KAAK,SAAQ;AAC3B,YAAI;AACF,eAAK,cAAc;AAErB,eAAO;MACT;AACE,eAAO;IAEX;EACF;EAEO,eAA4B;AACjC,UAAM,gBAAgB,KAAK,aAAa,CAAA;AAExC,QAAI,cAAc,gBAAgB,cAAc,MAAM;AACpD,UAAI,KAAK,eAAe,IAAI,aAAA,GAAgB;AAC1C,aAAK,eAAe,OAAO,aAAA;AAE3B,YAAI,KAAK,aAAa,SAAS,GAAG;AAChC,eAAK,aAAa,MAAK;AACvB,cAAI,UAAU,KAAK,aAAY;AAC/B,cAAI;AACF,iBAAK,cAAc;AAErB,iBAAO;QACT;AACE,iBAAO;MAEX;AAEA,aAAO;IACT;AAEA,UAAM,eAAe,cAAc,aAAY;AAE/C,QAAI,cAAc;AAChB,YAAM,aAAc,aAAyB;AAE7C,UAAI,YAAY;YAKH;AAJX,YAAI;AAEJ,YAAI,OAAO,KAAK,WAAW;AACzB,uBAAa,KAAK,OAAO,YAAA;kBAChB,eAAA,KAAK,YAAM,QAAX,iBAAA,SAAA,SAAA,aAAa;AACtB,uBAAa,KAAK,OAAO,WAAW,YAAA;AAGtC,YAAI,eAAe,WAAW,eAAe;AAC3C,cAAI;AACF,iBAAK,cAAc;AAErB,iBAAO;QACT;AAIA,YAAI,UAAU,KAAK,UAAS;AAC5B,YAAI;AACF,eAAK,cAAc;AAErB,eAAO;MACT;AAEA,UAAI;AACF,aAAK,cAAc;AAErB,aAAO;IACT,OAAO;AACL,UAAI,KAAK,aAAa,SAAS,GAAG;AAChC,aAAK,aAAa,MAAK;AAEvB,YAAI,UAAU,KAAK,aAAY;AAC/B,YAAI;AACF,eAAK,cAAc;AAErB,eAAO;MACT;AACE,eAAO;IAEX;EACF;;;;EAKO,cAA2B;AAKhC,WAAO;EACT;;;;EAKO,kBAA+B;AAKpC,WAAO;EACT;;;;EAKO,aAA0B;AAK/B,WAAO;EACT;EA/RA,YACI,KACA,MACA,YACA,QACA;SATI,eAAkC,CAAA;SAElC,iBAAkC,oBAAI,IAAA;SA+BtC,cAAc,CAAC,SAAA;AACrB,UAAI,KAAK,aAAa,KAAK,cAAc;AACvC,cAAMC,cAAc,KAAiB;AAErC,YAAIA,aAAY;AACd,gBAAM,SAAS,KAAK,KAAK,iBACvBA,aACA,KAAK,YACL;YAAC,YAAY,KAAK;UAAW,CAAA;AAG/B,eAAK,aAAa,QAAQ,MAAA;AAE1B,iBAAO,WAAW;QACpB,OAAO;cAGM;AAFX,cAAI,OAAO,KAAK,WAAW;AACzB,mBAAO,KAAK,OAAO,IAAA;oBACV,eAAA,KAAK,YAAM,QAAX,iBAAA,SAAA,SAAA,aAAa;AACtB,mBAAO,KAAK,OAAO,WAAW,IAAA;mBACrB,KAAK,WAAW;AACzB,mBAAO,WAAW;QAEtB;MACF;AAEA,aAAO,WAAW;IACpB;AAjDE,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS,WAAA,QAAA,WAAA,SAAA,SAAU;AACxB,SAAK,aAAa,eAAA,QAAA,eAAA,SAAA,aAAc,WAAW;AAC3C,SAAK,eAAe;AAEpB,SAAK,aAAa,QAChB,IAAI,iBAAiB,MAAM,YAAY,KAAK,WAAW,CAAA;AAGzD,UAAM,aAAc,KAAiB;AAErC,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,KAAK,iBACvB,YACA,KAAK,YACL;QAAC,YAAY,KAAK;MAAW,CAAA;AAG/B,WAAK,aAAa,QAAQ,MAAA;IAC5B;EACF;AAqQF;AAKO,SAAS,0CACZ,KACA,MACA,YACA,QAA0B;AAE5B,OAAI,GAAA,2CAAQ;AACV,WAAO,IAAI,0CAAiB,KAAK,MAAM,YAAY,MAAA;AAErD,SAAO,IAAI,iBAAiB,MAAM,YAAY,MAAA;AAChD;;;AC3RO,SAAS,6CAAoC,MAAO;AAGzD,MAAI,SAAgB;IAAC,GAAG,KAAK,CAAA;EAAE;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,QAAQ,KAAK,CAAA;AACjB,aAAS,OAAO,OAAO;AACrB,UAAI,IAAI,OAAO,GAAA;AACf,UAAI,IAAI,MAAM,GAAA;AAGd,UACE,OAAO,MAAM,cACb,OAAO,MAAM;MAEb,IAAI,CAAA,MAAO,OACX,IAAI,CAAA,MAAO,OACX,IAAI,WAAW,CAAA;MAAgB,MAC/B,IAAI,WAAW,CAAA;MAAgB;AAE/B,eAAO,GAAA,KAAO,GAAA,2CAAM,GAAG,CAAA;gBAItB,QAAQ,eAAe,QAAQ,uBAChC,OAAO,MAAM,YACb,OAAO,MAAM;AAEb,eAAO,GAAA,KAAO,GAAA,cAAK,GAAG,CAAA;eACb,QAAQ,QAAQ,KAAK;AAC9B,eAAO,MAAK,GAAA,2CAAS,GAAG,CAAA;;AAGxB,eAAO,GAAA,IAAO,MAAM,SAAY,IAAI;IAExC;EACF;AAEA,SAAO;AACT;;;ACzDO,SAAS,6CAAgB,MAA4D;AAC1F,MAAI,KAAK,WAAW,KAAK,KAAK,CAAA;AAC5B,WAAO,KAAK,CAAA;AAGd,SAAO,CAAC,UAAA;AACN,QAAI,aAAa;AAEjB,UAAM,WAAW,KAAK,IAAI,CAAA,QAAA;AACxB,YAAM,UAAU,6BAAO,KAAK,KAAA;AAC5B,qBAAA,aAAe,OAAO,WAAW;AACjC,aAAO;IACT,CAAA;AAEA,QAAI;AACF,aAAO,MAAA;AACL,iBAAS,QAAQ,CAAC,SAAS,MAAA;AACzB,cAAI,OAAO,YAAY;AACrB,oBAAA;;AAEA,yCAAO,KAAK,CAAA,GAAI,IAAA;QAEpB,CAAA;MACF;EAEJ;AACF;AAEA,SAAS,6BAAU,KAAsD,OAAQ;AAC/E,MAAI,OAAO,QAAQ;AACjB,WAAO,IAAI,KAAA;WACF,OAAO;AAChB,QAAI,UAAU;AAElB;;;ACrCA,IAAM,qCAAe,oBAAI,IAAI;EAC3B;CACD;AAED,IAAM,2CAAqB,oBAAI,IAAI;EACjC;EACA;EACA;EACA;CACD;AAGD,IAAM,sCAAgB,oBAAI,IAAI;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAeD,IAAM,+BAAS;AAOR,SAAS,0CAAe,OAAoD,OAAgB,CAAC,GAAC;AACnG,MAAI,EAAA,WAAU,QAAQ,UAAW,IAAI;AACrC,MAAI,gBAAgB,CAAC;AAErB,aAAW,QAAQ;AACjB,QACE,OAAO,UAAU,eAAe,KAAK,OAAO,IAAA,MAC1C,mCAAa,IAAI,IAAA,KAChB,aAAa,yCAAmB,IAAI,IAAA,KACpC,UAAU,oCAAc,IAAI,IAAA,MAC7B,cAAA,QAAA,cAAA,SAAA,SAAA,UAAW,IAAI,IAAA,MACf,6BAAO,KAAK,IAAA;AAGd,oBAAc,IAAA,IAAQ,MAAM,IAAA;AAIhC,SAAO;AACT;;;AC7CO,SAAS,0CAAsB,SAAyB;AAC7D,MAAI,4CAAA;AACF,YAAQ,MAAM;MAAC,eAAe;IAAI,CAAA;OAC7B;AACL,QAAI,qBAAqB,4CAAsB,OAAA;AAC/C,YAAQ,MAAK;AACb,gDAAsB,kBAAA;EACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS,8CAAA;AACP,MAAI,qDAA+B,MAAM;AACvC,wDAA8B;AAC9B,QAAI;AACF,UAAI,YAAY,SAAS,cAAc,KAAA;AACvC,gBAAU,MAAM;QACd,IAAI,gBAAgB;AAClB,8DAA8B;AAC9B,iBAAO;QACT;MACF,CAAA;IACF,QAAQ;IAER;EACF;AAEA,SAAO;AACT;AAEA,SAAS,4CAAsB,SAAyB;AACtD,MAAI,SAAS,QAAQ;AACrB,MAAI,qBAA0C,CAAA;AAC9C,MAAI,uBAAuB,SAAS,oBAAoB,SAAS;AAEjE,SAAO,kBAAkB,eAAe,WAAW,sBAAsB;AACvE,QACE,OAAO,eAAe,OAAO,gBAC7B,OAAO,cAAc,OAAO;AAE5B,yBAAmB,KAAK;QACtB,SAAS;QACT,WAAW,OAAO;QAClB,YAAY,OAAO;MACrB,CAAA;AAEF,aAAS,OAAO;EAClB;AAEA,MAAI,gCAAgC;AAClC,uBAAmB,KAAK;MACtB,SAAS;MACT,WAAW,qBAAqB;MAChC,YAAY,qBAAqB;IACnC,CAAA;AAGF,SAAO;AACT;AAEA,SAAS,4CAAsB,oBAAuC;AACpE,WAAS,EAAA,SAAQ,WAAW,WAAY,KAAK,oBAAoB;AAC/D,YAAQ,YAAY;AACpB,YAAQ,aAAa;EACvB;AACF;;;ACnFA,SAAS,oCAAc,IAAU;MAK7B;AAJF,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AACvD,WAAO;AAET,WACE,kCAAA,OAAO,UAAU,eAAA,OAAgB,QAAjC,oCAAA,SAAA,SAAA,gCAAmC,OAAO,KAAK,CAAC,UAA4C,GAAG,KAAK,MAAM,KAAK,CAAA,MAEjH,GAAG,KAAK,OAAO,UAAU,SAAS;AACpC;AAEA,SAAS,mCAAa,IAAU;MAElB;AADZ,SAAO,OAAO,WAAW,eAAe,OAAO,aAAa,OACxD,GAAG,OAAK,kCAAA,OAAO,UAAU,eAAA,OAAgB,QAAjC,oCAAA,SAAA,SAAA,gCAAmC,aAAY,OAAO,UAAU,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,IAAiB;AAC/B,MAAI;AACF,WAAO;AAGT,MAAI,MAAsB;AAC1B,SAAO,MAAA;AACL,QAAI,OAAO;AACT,YAAM,GAAA;AAER,WAAO;EACT;AACF;AAEO,IAAM,4CAAQ,6BAAO,WAAA;AAC1B,SAAO,mCAAa,OAAA;AACtB,CAAA;AAEO,IAAM,2CAAW,6BAAO,WAAA;AAC7B,SAAO,mCAAa,UAAA;AACtB,CAAA;AAEO,IAAM,4CAAS,6BAAO,WAAA;AAC3B,SAAO,mCAAa,QAAA;EAEjB,0CAAA,KAAW,UAAU,iBAAiB;AAC3C,CAAA;AAEO,IAAM,4CAAQ,6BAAO,WAAA;AAC1B,SAAO,yCAAA,KAAc,0CAAA;AACvB,CAAA;AAEO,IAAM,4CAAgB,6BAAO,WAAA;AAClC,SAAO,0CAAA,KAAW,0CAAA;AACpB,CAAA;AAEO,IAAM,4CAAW,6BAAO,WAAA;AAC7B,SAAO,oCAAc,cAAA,KAAmB,CAAC,0CAAA;AAC3C,CAAA;AAEO,IAAM,4CAAW,6BAAO,WAAA;AAC7B,SAAO,oCAAc,SAAA;AACvB,CAAA;AAEO,IAAM,4CAAY,6BAAO,WAAA;AAC9B,SAAO,oCAAc,UAAA;AACvB,CAAA;AAEO,IAAM,4CAAY,6BAAO,WAAA;AAC9B,SAAO,oCAAc,UAAA;AACvB,CAAA;;;;ACvDA,IAAM,uCAAgB,GAAA,cAAAC,eAAsB;EAC1C,UAAU;EACV,MAAM;EACN,SAAS,CAAC,SAAS;AACrB,CAAA;AAYO,SAAS,0CAAe,OAA0B;AACvD,MAAI,EAAA,UAAS,UAAU,QAAS,IAAI;AAEpC,MAAI,OAAM,GAAA,cAAAC,SAAQ,OAAO;IACvB,UAAU;IACV,MAAM,CAAC,QAAiB,WAAsB,MAAY,kBAAA;AACxD,6CAAiB,QAAQ,CAAA,SAAA;AACvB,YAAI,0CAAqB,MAAM,SAAA;AAC7B,mBAAS,MAAM,aAAA;;AAEf,oDAAS,MAAM,SAAA;MAEnB,CAAA;IACF;IACA,SAAS,YAAY,CAAC,SAAS;EACjC,IAAI;IAAC;IAAU;GAAQ;AAEvB,UACE,GAAA,cAAAC,SAAA,cAAC,oCAAc,UAAQ;IAAC,OAAO;KAC5B,QAAA;AAGP;AAEO,SAAS,4CAAA;AACd,UAAO,GAAA,cAAAC,YAAW,mCAAA;AACpB;AASO,SAAS,0CAAqB,MAAyB,WAAoB;AAEhF,MAAI,SAAS,KAAK,aAAa,QAAA;AAC/B,UACG,CAAC,UAAU,WAAW,YACvB,KAAK,WAAW,SAAS,UACzB,CAAC,KAAK,aAAa,UAAA,KACnB,CAAC,UAAU;EACX,CAAC,UAAU;EACX,CAAC,UAAU;EACX,CAAC,UAAU;AAEf;AAEO,SAAS,0CAAS,QAA2B,WAAsB,aAAa,MAAI;MAOtE,oBAAA;AANnB,MAAI,EAAA,SAAQ,SAAS,QAAQ,SAAU,IAAI;AAM3C,OAAI,GAAA,2CAAQ,OAAO,gBAAA,OAAO,WAAK,QAAZ,kBAAA,SAAA,UAAA,qBAAA,cAAc,UAAI,QAAlB,uBAAA,SAAA,SAAA,mBAAoB,WAAW,KAAA,MAAU,OAAO,WAAW,UAAA;AAC5E,SAAI,GAAA,2CAAI;AACN,gBAAU;;AAEV,gBAAU;;AAMd,MAAI,SAAQ,GAAA,2CAAO,MAAO,GAAA,2CAAI,KAAO,EAAC,GAAA,2CAAK,KAAO,OAE9C,IAAI,cAAc,WAAW;IAAC,eAAe;;;;;EAA2C,CAAA,IACxF,IAAI,WAAW,SAAS;;;;;IAAqC,SAAS;IAAM,YAAY;EAAI,CAAA;AAC/F,4CAAiB,YAAY;AAC9B,GAAA,GAAA,2CAAsB,MAAA;AACtB,SAAO,cAAc,KAAA;AACpB,4CAAiB,YAAY;AAChC;AAEC,0CAAiB,YAAY;AAE9B,SAAS,uCAAiB,QAAiB,MAAuC;AAChF,MAAI,kBAAkB;AACpB,SAAK,MAAA;WACI,OAAO,aAAa,WAAA,GAAc;AAC3C,QAAI,OAAO,SAAS,cAAc,GAAA;AAClC,SAAK,OAAO,OAAO,aAAa,WAAA;AAChC,QAAI,OAAO,aAAa,aAAA;AACtB,WAAK,SAAS,OAAO,aAAa,aAAA;AAEpC,QAAI,OAAO,aAAa,UAAA;AACtB,WAAK,MAAM,OAAO,aAAa,UAAA;AAEjC,QAAI,OAAO,aAAa,eAAA;AACtB,WAAK,WAAW,OAAO,aAAa,eAAA;AAEtC,QAAI,OAAO,aAAa,WAAA;AACtB,WAAK,OAAO,OAAO,aAAa,WAAA;AAElC,QAAI,OAAO,aAAa,sBAAA;AACtB,WAAK,iBAAiB,OAAO,aAAa,sBAAA;AAE5C,WAAO,YAAY,IAAA;AACnB,SAAK,IAAA;AACL,WAAO,YAAY,IAAA;EACrB;AACF;AAEA,SAAS,wCAAkB,QAAiB,WAAoB;AAC9D,yCAAiB,QAAQ,CAAA,SAAQ,0CAAS,MAAM,SAAA,CAAA;AAClD;AA2BO,SAAS,0CAAa,OAAoB;AAC/C,MAAI,SAAS,0CAAA;MACe;AAA5B,QAAM,OAAO,OAAO,SAAQ,cAAA,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,UAAI,QAAX,gBAAA,SAAA,cAAe,EAAA;AAC3C,SAAO;IACL,OAAM,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAO,OAAO;IAC3B,QAAQ,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO;IACf,KAAK,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO;IACZ,UAAU,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO;IACjB,MAAM,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO;IACb,gBAAgB,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO;EACzB;AACF;;;ACtKA,IAAI,6CAAuB,oBAAI,IAAA;AAG/B,IAAI,4CAAsB,oBAAI,IAAA;AAE9B,SAAS,0CAAA;AACP,MAAI,OAAO,WAAW;AACpB;AAGF,WAAS,kBAAkB,OAAY;AACrC,WAAO,kBAAkB;EAC3B;AAEA,MAAI,oBAAoB,CAAC,MAAA;AACvB,QAAI,CAAC,kBAAkB,CAAA,KAAM,CAAC,EAAE;AAC9B;AAGF,QAAI,cAAc,2CAAqB,IAAI,EAAE,MAAM;AACnD,QAAI,CAAC,aAAa;AAChB,oBAAc,oBAAI,IAAA;AAClB,iDAAqB,IAAI,EAAE,QAAQ,WAAA;AAKnC,QAAE,OAAO,iBAAiB,oBAAoB,iBAAiB;QAC7D,MAAM;MACR,CAAA;IACF;AAEA,gBAAY,IAAI,EAAE,YAAY;EAChC;AAEA,MAAI,kBAAkB,CAAC,MAAA;AACrB,QAAI,CAAC,kBAAkB,CAAA,KAAM,CAAC,EAAE;AAC9B;AAGF,QAAI,aAAa,2CAAqB,IAAI,EAAE,MAAM;AAClD,QAAI,CAAC;AACH;AAGF,eAAW,OAAO,EAAE,YAAY;AAGhC,QAAI,WAAW,SAAS,GAAG;AACzB,QAAE,OAAO,oBAAoB,oBAAoB,eAAA;AACjD,iDAAqB,OAAO,EAAE,MAAM;IACtC;AAGA,QAAI,2CAAqB,SAAS,GAAG;AACnC,eAAS,MAAM;AACb,WAAA;AAGF,gDAAoB,MAAK;IAC3B;EACF;AAEA,WAAS,KAAK,iBAAiB,iBAAiB,iBAAA;AAChD,WAAS,KAAK,iBAAiB,iBAAiB,eAAA;AAClD;AAEA,IAAI,OAAO,aAAa,aAAA;AACtB,MAAI,SAAS,eAAe;AAC1B,4CAAA;;AAEA,aAAS,iBAAiB,oBAAoB,uCAAA;;AASlD,SAAS,gDAAA;AACP,aAAW,CAAC,WAAA,KAAgB;AAG1B,QAAI,iBAAiB,eAAe,CAAC,YAAY;AAC/C,iDAAqB,OAAO,WAAA;AAGlC;AAEO,SAAS,0CAAmB,IAAc;AAE/C,wBAAsB,MAAA;AACpB,kDAAA;AAGA,QAAI,2CAAqB,SAAS;AAChC,SAAA;;AAEA,gDAAoB,IAAI,EAAA;EAE5B,CAAA;AACF;;;;;;;ACjGO,SAAS,4CAAA;AACd,MAAI,mBAAkB,GAAA,cAAAC,QAAO,oBAAI,IAAA,CAAA;AACjC,MAAI,qBAAoB,GAAA,cAAAC,aAAY,CAAC,aAAa,MAAM,UAAU,YAAA;AAEhE,QAAI,MAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,QAAO,IAAI,SAAA;AAC3B,sBAAgB,QAAQ,OAAO,QAAA;AAC/B,eAAA,GAAY,IAAA;IACd,IAAI;AACJ,oBAAgB,QAAQ,IAAI,UAAU;;;;;IAA+B,CAAA;AACrE,gBAAY,iBAAiB,MAAM,IAAI,OAAA;EACzC,GAAG,CAAA,CAAE;AACL,MAAI,wBAAuB,GAAA,cAAAA,aAAY,CAAC,aAAa,MAAM,UAAU,YAAA;QAC1D;AAAT,QAAI,OAAK,+BAAA,gBAAgB,QAAQ,IAAI,QAAA,OAAA,QAA5B,iCAAA,SAAA,SAAA,6BAAuC,OAAM;AACtD,gBAAY,oBAAoB,MAAM,IAAI,OAAA;AAC1C,oBAAgB,QAAQ,OAAO,QAAA;EACjC,GAAG,CAAA,CAAE;AACL,MAAI,4BAA2B,GAAA,cAAAA,aAAY,MAAA;AACzC,oBAAgB,QAAQ,QAAQ,CAAC,OAAO,QAAA;AACtC,2BAAqB,MAAM,aAAa,MAAM,MAAM,KAAK,MAAM,OAAO;IACxE,CAAA;EACF,GAAG;IAAC;GAAqB;AAGzB,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,WAAO;EACT,GAAG;IAAC;GAAyB;AAE7B,SAAO;;;;EAAkE;AAC3E;;;AC/BO,SAAS,0CAAU,OAAqC,cAAqB;AAClF,MAAI,EAAA,IAEF,cAAc,OACd,mBAAmB,WAAU,IAC3B;AAIJ,QAAK,GAAA,2CAAM,EAAA;AACX,MAAI,cAAc,OAAO;AACvB,QAAI,MAAM,oBAAI,IAAI;MAAC;SAAO,WAAW,KAAI,EAAG,MAAM,KAAA;KAAO;AACzD,iBAAa;SAAI;MAAK,KAAK,GAAA;EAC7B,WAAW;AACT,iBAAa,WAAW,KAAI,EAAG,MAAM,KAAA,EAAO,KAAK,GAAA;AAInD,MAAI,CAAC,SAAS,CAAC,cAAc;AAC3B,YAAQ;AAGV,SAAO;;IAEL,cAAc;IACd,mBAAmB;EACrB;AACF;;;;ACxBO,SAAS,0CAAgB,KAAuF;AACrH,QAAM,UAAqC,GAAA,cAAAC,QAAU,IAAA;AACrD,QAAM,cAAoD,GAAA,cAAAA,QAAO,MAAA;AAEjE,QAAM,aAAY,GAAA,cAAAC,aAChB,CAAC,aAAA;AACC,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,cAAc;AACpB,YAAM,aAAa,YAAY,QAAA;AAC/B,aAAO,MAAA;AACL,YAAI,OAAO,eAAe;AACxB,qBAAA;;AAEA,sBAAY,IAAA;MAEhB;IACF,WAAW,KAAK;AACd,UAAI,UAAU;AACd,aAAO,MAAA;AACL,YAAI,UAAU;MAChB;IACF;EACF,GACA;IAAC;GAAI;AAGP,UAAO,GAAA,cAAAC,SACL,OAAO;IACL,IAAI,UAAU;AACZ,aAAO,OAAO;IAChB;IACA,IAAI,QAAQ,OAAO;AACjB,aAAO,UAAU;AACjB,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAO;AAClB,mBAAW,UAAU;MACvB;AAEA,UAAI,SAAS;AACX,mBAAW,UAAU,UAAU,KAAA;IAEnC;EACF,IACA;IAAC;GAAU;AAEf;;;;;;;;;;AChEA,SAAS,0CAAA;AACP,SAAO,OAAO,OAAO,mBAAmB;AAC1C;AAQO,SAAS,0CAAqC,SAAwC;AAC3F,QAAM,EAAA,KAAI,KAAK,SAAU,IAAI;AAE7B,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,UAAU,QAAA,QAAA,QAAA,SAAA,SAAA,IAAK;AACnB,QAAI,CAAC;AACH;AAGF,QAAI,CAAC,wCAAA,GAAqB;AACxB,aAAO,iBAAiB,UAAU,UAAU,KAAA;AAC5C,aAAO,MAAA;AACL,eAAO,oBAAoB,UAAU,UAAU,KAAA;MACjD;IACF,OAAO;AAEL,YAAM,yBAAyB,IAAI,OAAO,eAAe,CAAC,YAAA;AACxD,YAAI,CAAC,QAAQ;AACX;AAGF,iBAAA;MACF,CAAA;AACA,6BAAuB,QAAQ,SAAS;;MAAI,CAAA;AAE5C,aAAO,MAAA;AACL,YAAI;AACF,iCAAuB,UAAU,OAAA;MAErC;IACF;EAEF,GAAG;IAAC;IAAU;IAAK;GAAI;AACzB;;;AC1BO,SAAS,0CAAc,SAAkC,KAAyB;AACvF,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,WAAW,QAAQ,OAAO,KAAK;AACjC,cAAQ,IAAI,UAAU,IAAI;AAC1B,aAAO,MAAA;AACL,YAAI,QAAQ;AACV,kBAAQ,IAAI,UAAU;MAE1B;IACF;EACF,CAAA;AACF;;;ACpBO,SAAS,0CAAa,MAAsB,kBAA0B;AAC3E,MAAI,CAAC;AACH,WAAO;AAET,MAAI,QAAQ,OAAO,iBAAiB,IAAA;AACpC,MAAI,eAAe,gBAAgB,KAAK,MAAM,WAAW,MAAM,YAAY,MAAM,SAAS;AAE1F,MAAI,gBAAgB;AAClB,mBAAe,KAAK,iBAAiB,KAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAGtF,SAAO;AACT;;;ACVO,SAAS,0CAAgB,MAAe,kBAA0B;AACvE,MAAI,iBAAiC;AACrC,OAAI,GAAA,2CAAa,gBAAgB,gBAAA;AAC/B,qBAAiB,eAAe;AAGlC,SAAO,kBAAkB,EAAC,GAAA,2CAAa,gBAAgB,gBAAA;AACrD,qBAAiB,eAAe;AAGlC,SAAO,kBAAkB,SAAS,oBAAoB,SAAS;AACjE;;;;ACLA,IAAI,uCAAiB,OAAO,aAAa,eAAe,OAAO;;;;;;;;;ACKxD,SAAS,0CAAe,OAAgC;AAE7D,MAAK,MAAc,mBAAmB,KAAK,MAAM;AAC/C,WAAO;AAMT,OAAI,GAAA,2CAAQ,KAAQ,MAAuB;AACzC,WAAO,MAAM,SAAS,WAAW,MAAM,YAAY;AAGrD,SAAO,MAAM,WAAW,KAAK,CAAE,MAAuB;AACxD;AAEO,SAAS,0CAAsB,OAAmB;AAOvD,SACG,EAAC,GAAA,2CAAQ,KAAO,MAAM,UAAU,KAAK,MAAM,WAAW,KACtD,MAAM,UAAU,KACf,MAAM,WAAW,KACjB,MAAM,aAAa,KACnB,MAAM,WAAW,KACjB,MAAM,gBAAgB;AAG5B;;;;;;;ACzCO,SAAS,0CACd,KACA,cACA,SAA2B;AAE3B,MAAI,cAAa,GAAA,eAAAC,QAAO,YAAA;AACxB,MAAI,eAAc,GAAA,2CAAe,MAAA;AAC/B,QAAI;AACF,cAAQ,WAAW,OAAO;EAE9B,CAAA;AAEA,GAAA,GAAA,eAAAC,WAAU,MAAA;QACG;AAAX,QAAI,OAAO,QAAA,QAAA,QAAA,SAAA,UAAA,eAAA,IAAK,aAAO,QAAZ,iBAAA,SAAA,SAAA,aAAc;AACzB,aAAA,QAAA,SAAA,SAAA,SAAA,KAAM,iBAAiB,SAAS,WAAA;AAChC,WAAO,MAAA;AACL,eAAA,QAAA,SAAA,SAAA,SAAA,KAAM,oBAAoB,SAAS,WAAA;IACrC;EACF,GAAG;IAAC;IAAK;GAAY;AACvB;;;;;;;;;;;;;;;;ACnCA,IAAM,0CAAoB;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF,IAAM,mDAA6B,wCAAkB,KAAK,iBAAA,IAAqB;AAE/E,wCAAkB,KAAK,iDAAA;AACvB,IAAM,kDAA4B,wCAAkB,KAAK,sCAAA;AAElD,SAAS,0CAAY,SAAgB;AAC1C,SAAO,QAAQ,QAAQ,gDAAA;AACzB;AAEO,SAAS,0CAAW,SAAgB;AACzC,SAAO,QAAQ,QAAQ,+CAAA;AACzB;;;;ACXO,SAAS,0CAA6B,OAAU,cAAiB,UAAyC;AAC/G,MAAI,CAAC,YAAY,aAAA,KAAiB,GAAA,eAAAC,UAAS,SAAS,YAAA;AAEpD,MAAI,mBAAkB,GAAA,eAAAC,QAAO,UAAU,MAAA;AACvC,MAAI,eAAe,UAAU;AAC7B,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,gBAAgB,gBAAgB;AACpC,QAAI,kBAAkB,gBAAgB;AACpC,cAAQ,KAAK,kCAAkC,gBAAgB,eAAe,cAAA,OAAqB,eAAe,eAAe,cAAA,GAAiB;AAEpJ,oBAAgB,UAAU;EAC5B,GAAG;IAAC;GAAa;AAEjB,MAAI,eAAe,eAAe,QAAQ;AAC1C,MAAI,YAAW,GAAA,eAAAC,aAAY,CAACC,WAAU,SAAA;AACpC,QAAI,iBAAiB,CAACA,WAAU,iBAAA;AAC9B,UAAI,UACF;AAAA,YAAI,CAAC,OAAO,GAAG,cAAcA,MAAA;AAC3B,mBAASA,QAAA,GAAU,YAAA;MACrB;AAEF,UAAI,CAAC;AAMH,uBAAeA;IAEnB;AAEA,QAAI,OAAOA,WAAU,YAAY;AAC/B,UAAI;AACF,gBAAQ,KAAK,2HAAA;AAOf,UAAI,iBAAiB,CAAC,aAAa,iBAAA;AACjC,YAAI,mBAAmBA,OAAM,eAAe,eAAe,UAAA,GAAa,YAAA;AACxE,uBAAe,kBAAA,GAAqB,IAAA;AACpC,YAAI,CAAC;AACH,iBAAO;AAET,eAAO;MACT;AACA,oBAAc,cAAA;IAChB,OAAO;AACL,UAAI,CAAC;AACH,sBAAcA,MAAA;AAEhB,qBAAeA,QAAA,GAAU,IAAA;IAC3B;EACF,GAAG;IAAC;IAAc;IAAc;GAAS;AAEzC,SAAO;IAAC;IAAc;;AACxB;;;AC3DO,SAAS,0CAAM,OAAe,MAAc,WAAW,MAAc,UAAQ;AAClF,MAAI,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAA,GAAM,GAAA;AAC9C,SAAO;AACT;;;;;;;ACDO,SAAS,yCAA+C,aAAkB;AAC/E,MAAI,QAAQ;AACZ,QAAM,cAAc;AACpB,QAAM,qBAAqB,MAAM,MAAM;AAEvC,QAAM,uBAAuB,MAAO,MAAc;AAClD,QAAM,UAAU,MAAA;EAAO;AACvB,SAAO;AACT;AAEO,SAAS,0CAAe,OAAc,QAAe;AAC1D,SAAO,eAAe,OAAO,UAAU;IAAC,OAAO;EAAM,CAAA;AACrD,SAAO,eAAe,OAAO,iBAAiB;IAAC,OAAO;EAAM,CAAA;AAC9D;AAEO,SAAS,0CAAwD,QAA4C;AAClH,MAAI,YAAW,GAAA,eAAAC,QAAO;IACpB,WAAW;IACX,UAAU;EACZ,CAAA;AAIA,GAAA,GAAA,2CAAgB,MAAA;AACd,UAAM,QAAQ,SAAS;AACvB,WAAO,MAAA;AACL,UAAI,MAAM,UAAU;AAClB,cAAM,SAAS,WAAU;AACzB,cAAM,WAAW;MACnB;IACF;EACF,GAAG,CAAA,CAAE;AAEL,MAAI,gBAAe,GAAA,2CAAe,CAAC,MAAA;AACjC,eAAA,QAAA,WAAA,SAAA,SAAA,OAAS,CAAA;EACX,CAAA;AAGA,UAAO,GAAA,eAAAC,aAAY,CAAC,MAAA;AAKlB,QACE,EAAE,kBAAkB,qBACpB,EAAE,kBAAkB,oBACpB,EAAE,kBAAkB,uBACpB,EAAE,kBAAkB,mBACpB;AACA,eAAS,QAAQ,YAAY;AAE7B,UAAI,SAAS,EAAE;AACf,UAAI,gBAA2D,CAACC,OAAA;AAC9D,iBAAS,QAAQ,YAAY;AAE7B,YAAI,OAAO,UAAU;AAEnB,cAAI,QAAQ,yCAA8CA,EAAA;AAC1D,uBAAa,KAAA;QACf;AAGA,YAAI,SAAS,QAAQ,UAAU;AAC7B,mBAAS,QAAQ,SAAS,WAAU;AACpC,mBAAS,QAAQ,WAAW;QAC9B;MACF;AAEA,aAAO,iBAAiB,YAAY,eAAe;QAAC,MAAM;MAAI,CAAA;AAE9D,eAAS,QAAQ,WAAW,IAAI,iBAAiB,MAAA;AAC/C,YAAI,SAAS,QAAQ,aAAa,OAAO,UAAU;cACjD;WAAA,6BAAA,SAAS,QAAQ,cAAQ,QAAzB,+BAAA,SAAA,SAAA,2BAA2B,WAAU;AACrC,cAAI,kBAAkB,WAAW,SAAS,gBAAgB,OAAO,SAAS;AAC1E,iBAAO,cAAc,IAAI,WAAW,QAAQ;YAAC,eAAe;UAAe,CAAA,CAAA;AAC3E,iBAAO,cAAc,IAAI,WAAW,YAAY;YAAC,SAAS;YAAM,eAAe;UAAe,CAAA,CAAA;QAChG;MACF,CAAA;AAEA,eAAS,QAAQ,SAAS,QAAQ,QAAQ;QAAC,YAAY;QAAM,iBAAiB;UAAC;;MAAW,CAAA;IAC5F;EACF,GAAG;IAAC;GAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,QAA+B;AAE1D,SAAO,UAAU,EAAC,GAAA,2CAAY,MAAA;AAC5B,aAAS,OAAO;AAGlB,MAAIC,WAAS,GAAA,2CAAe,MAAA;AAC5B,MAAI,gBAAgBA,QAAO,SAAS;AACpC,MAAI,CAAC,iBAAiB,kBAAkB;AACtC;AAGF,8CAAmB;AACnB,MAAI,eAAe;AACnB,MAAI,SAAS,CAAC,MAAA;AACZ,QAAI,EAAE,WAAW,iBAAiB;AAChC,QAAE,yBAAwB;EAE9B;AAEA,MAAI,aAAa,CAAC,MAAA;AAChB,QAAI,EAAE,WAAW,iBAAiB,cAAc;AAC9C,QAAE,yBAAwB;AAI1B,UAAI,CAAC,UAAU,CAAC,cAAc;AAC5B,uBAAe;AACf,SAAA,GAAA,2CAAsB,aAAA;AACtB,gBAAA;MACF;IACF;EACF;AAEA,MAAI,UAAU,CAAC,MAAA;AACb,QAAI,EAAE,WAAW,UAAU;AACzB,QAAE,yBAAwB;EAE9B;AAEA,MAAI,YAAY,CAAC,MAAA;AACf,QAAI,EAAE,WAAW,UAAU,cAAc;AACvC,QAAE,yBAAwB;AAE1B,UAAI,CAAC,cAAc;AACjB,uBAAe;AACf,SAAA,GAAA,2CAAsB,aAAA;AACtB,gBAAA;MACF;IACF;EACF;AAEA,EAAAA,QAAO,iBAAiB,QAAQ,QAAQ,IAAA;AACxC,EAAAA,QAAO,iBAAiB,YAAY,YAAY,IAAA;AAChD,EAAAA,QAAO,iBAAiB,WAAW,WAAW,IAAA;AAC9C,EAAAA,QAAO,iBAAiB,SAAS,SAAS,IAAA;AAE1C,MAAI,UAAU,MAAA;AACZ,yBAAqB,GAAA;AACrB,IAAAA,QAAO,oBAAoB,QAAQ,QAAQ,IAAA;AAC3C,IAAAA,QAAO,oBAAoB,YAAY,YAAY,IAAA;AACnD,IAAAA,QAAO,oBAAoB,WAAW,WAAW,IAAA;AACjD,IAAAA,QAAO,oBAAoB,SAAS,SAAS,IAAA;AAC7C,gDAAmB;AACnB,mBAAe;EACjB;AAEA,MAAI,MAAM,sBAAsB,OAAA;AAChC,SAAO;AACT;;;AClJA,IAAI,8BAAe;AACnB,IAAI,wCAAkB;AACtB,IAAI,2CAAqB,oBAAI,QAAA;AAEtB,SAAS,0CAAqB,QAAgB;AACnD,OAAI,GAAA,2CAAI,GAAK;AACX,QAAI,gCAAU,WAAW;AAEvB,YAAM,kBAAiB,GAAA,2CAAiB,MAAA;AACxC,8CAAkB,eAAe,gBAAgB,MAAM;AACvD,qBAAe,gBAAgB,MAAM,mBAAmB;IAC1D;AAEA,kCAAQ;EACV,WAAW,kBAAkB,eAAe,kBAAkB,YAAY;AAGxE,QAAI,WAAW,gBAAgB,OAAO,QAAQ,eAAe;AAC7D,6CAAmB,IAAI,QAAQ,OAAO,MAAM,QAAA,CAAS;AACrD,WAAO,MAAM,QAAA,IAAY;EAC3B;AACF;AAEO,SAAS,0CAAqB,QAAgB;AACnD,OAAI,GAAA,2CAAI,GAAK;AAGX,QAAI,gCAAU;AACZ;AAGF,kCAAQ;AAIR,eAAW,MAAA;AAGT,OAAA,GAAA,2CAAmB,MAAA;AAEjB,YAAI,gCAAU,aAAa;AAEzB,gBAAM,kBAAiB,GAAA,2CAAiB,MAAA;AACxC,cAAI,eAAe,gBAAgB,MAAM,qBAAqB;AAC5D,2BAAe,gBAAgB,MAAM,mBAAmB,yCAAmB;AAG7E,kDAAkB;AAClB,wCAAQ;QACV;MACF,CAAA;IACF,GAAG,GAAA;EACL,WAAW,kBAAkB,eAAe,kBAAkB,YAG5D;AAAA,QAAI,UAAU,yCAAmB,IAAI,MAAA,GAAS;AAC5C,UAAI,sBAAsB,yCAAmB,IAAI,MAAA;AACjD,UAAI,WAAW,gBAAgB,OAAO,QAAQ,eAAe;AAE7D,UAAI,OAAO,MAAM,QAAA,MAAc;AAC7B,eAAO,MAAM,QAAA,IAAY;AAG3B,UAAI,OAAO,aAAa,OAAA,MAAa;AACnC,eAAO,gBAAgB,OAAA;AAEzB,+CAAmB,OAAO,MAAA;IAC5B;EAAA;AAEJ;;;;AC/EO,IAAM,6CAAwB,GAAA,eAAAC,SAAM,cAAsC;EAAC,UAAU,MAAA;EAAO;AAAC,CAAA;AACpG,0CAAsB,cAAc;;;ACtBpC,SAAS,4BAA4B,UAAU,YAAY;AACvD,MAAI,WAAW;AAAK,WAAO,WAAW,IAAI,KAAK,QAAQ;AAEvD,SAAO,WAAW;AACtB;;;ACJA,SAAS,gCAAgC,UAAU,YAAY,QAAQ;AACnE,MAAI,CAAC,WAAW,IAAI,QAAQ;AAAG,UAAM,IAAI,UAAU,kBAAkB,SAAS,gCAAgC;AAE9G,SAAO,WAAW,IAAI,QAAQ;AAClC;;;ACDA,SAAS,yBAAyB,UAAU,YAAY;AACpD,MAAI,aAAa,gCAAgC,UAAU,YAAY,KAAK;AAC5E,SAAO,4BAA4B,UAAU,UAAU;AAC3D;;;ACNA,SAAS,6BAA6B,KAAK,mBAAmB;AAC1D,MAAI,kBAAkB,IAAI,GAAG,GAAG;AAC5B,UAAM,IAAI,UAAU,gEAAgE;AAAA,EACxF;AACJ;;;ACFA,SAAS,0BAA0B,KAAK,YAAY,OAAO;AACvD,+BAA6B,KAAK,UAAU;AAC5C,aAAW,IAAI,KAAK,KAAK;AAC7B;;;ACLA,SAAS,4BAA4B,UAAU,YAAY,OAAO;AAC9D,MAAI,WAAW;AAAK,eAAW,IAAI,KAAK,UAAU,KAAK;AAAA,OAClD;AACD,QAAI,CAAC,WAAW,UAAU;AAItB,YAAM,IAAI,UAAU,0CAA0C;AAAA,IAClE;AACA,eAAW,QAAQ;AAAA,EACvB;AACJ;;;ACRA,SAAS,yBAAyB,UAAU,YAAY,OAAO;AAC3D,MAAI,aAAa,gCAAgC,UAAU,YAAY,KAAK;AAC5E,8BAA4B,UAAU,YAAY,KAAK;AACvD,SAAO;AACX;;;;;ACwFA,SAAS,+CAAyB,OAAqB;AAErD,MAAI,WAAU,GAAA,eAAAC,aAAW,GAAA,0CAAoB;AAC7C,MAAI,SAAS;AACX,QAAI,EAAA,UAAW,GAAG,aAAA,IAAgB;AAClC,aAAQ,GAAA,2CAAW,cAAc,KAAA;AACjC,aAAA;EACF;AACA,GAAA,GAAA,2CAAW,SAAS,MAAM,GAAG;AAE7B,SAAO;AACT;IAYE,+CAAA,oBAAA,QAAA;AAVF,IAAM,mCAAN,MAAM;EAyCJ,sBAAsB;wCACf,8CAAyB,KAAA;EAChC;EAEA,IAAI,wBAAwB;AAC1B,YAAA,GAAA,0BAAO,MAAK,4CAAA;EACd;EAnCA,YAAY,MAA2B,aAA0B,eAA0B,OAAoB;AAF/G,KAAA,GAAA,2BAAA,MAAA,8CAAA;;aAAA;;wCAAA,8CAAyB,IAAA;QAGH;AAApB,QAAI,iBAAgB,gBAAA,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,YAAM,QAAb,kBAAA,SAAA,gBAAiB,cAAc;AACnD,UAAM,OAA6B,kBAAA,QAAA,kBAAA,SAAA,SAAA,cAA2B,sBAAqB;AACnF,QAAI,GAAG,IAAI;AACX,QAAI,SAAS,UAAyB;AACtC,QAAI,cAAc,WAAW,QAAQ,cAAc,WAAW,MAAM;AAClE,gBAAU,cAAc;AACxB,gBAAU,cAAc;IAC1B;AACA,QAAI,MAAA;AACF,UAAI,WAAW,QAAQ,WAAW,MAAM;AACtC,YAAI,UAAU,KAAK;AACnB,YAAI,UAAU,KAAK;MACrB,OAAO;AACL,YAAI,KAAK,QAAQ;AACjB,YAAI,KAAK,SAAS;MACpB;;AAEF,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,SAAS,cAAc;AAC5B,SAAK,WAAW,cAAc;AAC9B,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,cAAc;AAC7B,SAAK,SAAS,cAAc;AAC5B,SAAK,IAAI;AACT,SAAK,IAAI;EACX;AASF;AAEA,IAAM,qCAAe,OAAO,aAAA;AAC5B,IAAM,iCAAW;AACjB,IAAM,4CAAsB;AAOrB,SAAS,0CAAS,OAAqB;AAC5C,MAAI,EAAA,SACK,eACM,cACD,YACF,WACD,SACF,YAEP,WAAW,eAAa,qBACL,2BACM,2BAEzB,KAAK,QACL,GAAG,SAAA,IACD,+CAAyB,KAAA;AAE7B,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,OAAM,GAAA,eAAAC,QAAmB;IAC3B,WAAW;IACX,2BAA2B;IAC3B,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IACjB,QAAQ;IACR,cAAc;IACd,aAAa;IACb,aAAa,CAAA;EACf,CAAA;AAEA,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,qBAAoB,GAAA,2CAAe,CAAC,eAA0B,gBAAA;AAChE,QAAI,QAAQ,IAAI;AAChB,QAAI,cAAc,MAAM;AACtB,aAAO;AAGT,QAAI,wBAAwB;AAC5B,UAAM,oBAAoB;AAC1B,QAAI,cAAc;AAChB,UAAI,QAAQ,IAAI,iCAAW,cAAc,aAAa,aAAA;AACtD,mBAAa,KAAA;AACb,8BAAwB,MAAM;IAChC;AAEA,QAAI;AACF,oBAAc,IAAA;AAGhB,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB;AAC1B,eAAW,IAAA;AACX,WAAO;EACT,CAAA;AAEA,MAAI,mBAAkB,GAAA,2CAAe,CAAC,eAA0B,aAA0B,aAAa,SAAI;AACzG,QAAI,QAAQ,IAAI;AAChB,QAAI,CAAC,MAAM;AACT,aAAO;AAGT,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB;AAE1B,QAAI,wBAAwB;AAC5B,QAAI,YAAY;AACd,UAAI,QAAQ,IAAI,iCAAW,YAAY,aAAa,aAAA;AACpD,iBAAW,KAAA;AACX,8BAAwB,MAAM;IAChC;AAEA,QAAI;AACF,oBAAc,KAAA;AAGhB,eAAW,KAAA;AAEX,QAAI,WAAW,cAAc,CAAC,YAAY;AACxC,UAAI,QAAQ,IAAI,iCAAW,SAAS,aAAa,aAAA;AACjD,cAAQ,KAAA;AACR,gCAAA,wBAA0B,MAAM;IAClC;AAEA,UAAM,oBAAoB;AAC1B,WAAO;EACT,CAAA;AAEA,MAAI,kBAAiB,GAAA,2CAAe,CAAC,eAA0B,gBAAA;AAC7D,QAAI,QAAQ,IAAI;AAChB,QAAI;AACF,aAAO;AAGT,QAAI,WAAW;AACb,YAAM,oBAAoB;AAC1B,UAAI,QAAQ,IAAI,iCAAW,WAAW,aAAa,aAAA;AACnD,gBAAU,KAAA;AACV,YAAM,oBAAoB;AAC1B,aAAO,MAAM;IACf;AAEA,WAAO;EACT,CAAA;AAEA,MAAI,UAAS,GAAA,2CAAe,CAAC,MAAA;AAC3B,QAAI,QAAQ,IAAI;AAChB,QAAI,MAAM,aAAa,MAAM,QAAQ;AACnC,UAAI,MAAM,qBAAqB,MAAM,eAAe;AAClD,wBAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,aAAa,KAAA;AAEnE,YAAM,YAAY;AAClB,YAAM,eAAe;AACrB,YAAM,kBAAkB;AACxB,YAAM,cAAc;AACpB,+BAAA;AACA,UAAI,CAAC;AACH,SAAA,GAAA,2CAAqB,MAAM,MAAM;AAEnC,eAAS,WAAW,MAAM;AACxB,gBAAA;AAEF,YAAM,cAAc,CAAA;IACtB;EACF,CAAA;AAEA,MAAI,uBAAsB,GAAA,2CAAe,CAAC,MAAA;AACxC,QAAI;AACF,aAAO,CAAA;EAEX,CAAA;AAEA,MAAI,gBAAe,GAAA,2CAAe,CAAC,MAAA;AACjC,gBAAA,QAAA,YAAA,SAAA,SAAA,QAAU,CAAA;EACZ,CAAA;AAEA,MAAI,yBAAwB,GAAA,2CAAe,CAAC,GAA+B,WAAA;AAMzE,QAAI,SAAS;AACX,UAAI,QAAQ,IAAI,WAAW,SAAS,CAAA;AACpC,OAAA,GAAA,2CAAe,OAAO,MAAA;AACtB,eAAQ,GAAA,0CAAqB,KAAA,CAAA;IAC/B;EACF,CAAA;AAEA,MAAI,cAAa,GAAA,eAAAC,SAAQ,MAAA;AACvB,QAAI,QAAQ,IAAI;AAChB,QAAIC,cAA4B;MAC9B,UAAU,GAAC;AACT,YAAI,2CAAqB,EAAE,aAAa,EAAE,aAAa,MAAK,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,GAAI;cAwCtH;AAvCF,cAAI,oDAA6B,GAAA,2CAAe,EAAE,WAAW,GAAG,EAAE,GAAG;AACnE,cAAE,eAAc;AAMlB,cAAI,wBAAwB;AAC5B,cAAI,CAAC,MAAM,aAAa,CAAC,EAAE,QAAQ;AACjC,kBAAM,SAAS,EAAE;AACjB,kBAAM,YAAY;AAClB,kBAAM,cAAc;AACpB,oCAAwB,kBAAkB,GAAG,UAAA;AAK7C,gBAAI,iBAAiB,EAAE;AACvB,gBAAI,UAAU,CAACC,OAAA;AACb,kBAAI,2CAAqBA,IAAG,cAAA,KAAmB,CAACA,GAAE,WAAU,GAAA,2CAAa,iBAAgB,GAAA,2CAAeA,EAAA,CAAA,KAAO,MAAM;AACnH,+BAAe,kCAAY,MAAM,QAAQA,EAAA,GAAI,UAAA;YAEjD;AAEA,+BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,UAAS,GAAA,2CAAM,SAAS,OAAA,GAAU,IAAA;UACzF;AAEA,cAAI;AACF,cAAE,gBAAe;AAUnB,cAAI,EAAE,YAAW,GAAA,2CAAI;AAAA,aACnB,uBAAA,MAAM,mBAAa,QAAnB,yBAAA,SAAA,SAAA,qBAAqB,IAAI,EAAE,KAAK,EAAE,WAAW;QAEjD,WAAW,EAAE,QAAQ;AACnB,gBAAM,gBAAgB,oBAAI,IAAA;MAE9B;MACA,QAAQ,GAAC;AACP,YAAI,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAClE;AAGF,YAAI,KAAK,EAAE,WAAW,KAAK,CAAC,MAAM,qBAAqB,EAAE,GAAA,2CAAiB,WAAW;AACnF,cAAI,wBAAwB;AAC5B,cAAI;AACF,cAAE,eAAc;AAKlB,cAAI,CAAC,MAAM,6BAA6B,CAAC,MAAM,cAAc,MAAM,gBAAgB,cAAa,GAAA,2CAAe,EAAE,WAAW,IAAI;AAC9H,gBAAI,iBAAiB,kBAAkB,GAAG,SAAA;AAC1C,gBAAI,cAAc,eAAe,GAAG,SAAA;AACpC,gBAAI,eAAe,gBAAgB,GAAG,SAAA;AACtC,yBAAa,CAAA;AACb,oCAAwB,kBAAkB,eAAe;UAC3D,WAAW,MAAM,aAAa,MAAM,gBAAgB,YAAY;AAC9D,gBAAI,cAAc,MAAM,eAAgB,EAAE,YAA6B,eAA8B;AACrG,gBAAI,cAAc,eAAe,kCAAY,EAAE,eAAe,CAAA,GAAI,WAAA;AAClE,gBAAI,eAAgB,gBAAgB,kCAAY,EAAE,eAAe,CAAA,GAAI,aAAa,IAAA;AAClF,oCAAwB,eAAe;AACvC,kBAAM,eAAe;AACrB,yBAAa,CAAA;AACb,mBAAO,CAAA;UACT;AAEA,gBAAM,4BAA4B;AAClC,cAAI;AACF,cAAE,gBAAe;QAErB;MACF;IACF;AAEA,QAAI,UAAU,CAAC,MAAA;UA0BkB;AAzB/B,UAAI,MAAM,aAAa,MAAM,UAAU,2CAAqB,GAAG,MAAM,MAAM,GAAG;YAwB5E;AAvBA,YAAI,oDAA6B,GAAA,2CAAe,CAAA,GAAI,EAAE,GAAG;AACvD,YAAE,eAAc;AAGlB,YAAI,UAAS,GAAA,2CAAe,CAAA;AAC5B,YAAI,cAAa,GAAA,2CAAa,MAAM,SAAQ,GAAA,2CAAe,CAAA,CAAA;AAC3D,wBAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,YAAY,UAAA;AAC1D,YAAI;AACF,gCAAsB,GAAG,MAAM,MAAM;AAEvC,iCAAA;AAKA,YAAI,EAAE,QAAQ,WAAW,uCAAiB,MAAM,MAAM,MAAK,GAAA,2CAAa,MAAM,QAAQ,MAAA,KAAW,CAAC,EAAE,kCAAA,GAAe;AAGjH,YAAE,kCAAA,IAAgB;AAClB,WAAA,GAAA,2CAAS,MAAM,QAAQ,GAAG,KAAA;QAC5B;AAEA,cAAM,YAAY;SAClB,wBAAA,MAAM,mBAAa,QAAnB,0BAAA,SAAA,SAAA,sBAAqB,OAAO,EAAE,GAAG;MACnC,WAAW,EAAE,QAAQ,YAAU,uBAAA,MAAM,mBAAa,QAAnB,yBAAA,SAAA,SAAA,qBAAqB,OAAM;YAOtD;AAHF,YAAI,SAAS,MAAM;AACnB,cAAM,gBAAgB;AACtB,iBAAS,SAAS,OAAO,OAAM;AAAA,WAC7B,gBAAA,MAAM,YAAM,QAAZ,kBAAA,SAAA,SAAA,cAAc,cAAc,IAAI,cAAc,SAAS,KAAA,CAAA;MAE3D;IACF;AAEA,QAAI,OAAO,iBAAiB,aAAa;AACvC,MAAAD,YAAW,gBAAgB,CAAC,MAAA;AAE1B,YAAI,EAAE,WAAW,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC/E;AAOF,aAAI,GAAA,2CAAsB,EAAE,WAAW,GAAG;AACxC,gBAAM,cAAc;AACpB;QACF;AAEA,cAAM,cAAc,EAAE;AAEtB,YAAI,wBAAwB;AAC5B,YAAI,CAAC,MAAM,WAAW;AACpB,gBAAM,YAAY;AAClB,gBAAM,eAAe;AACrB,gBAAM,kBAAkB,EAAE;AAC1B,gBAAM,SAAS,EAAE;AAEjB,cAAI,CAAC;AACH,aAAA,GAAA,2CAAqB,MAAM,MAAM;AAGnC,kCAAwB,kBAAkB,GAAG,MAAM,WAAW;AAI9D,cAAI,UAAS,GAAA,2CAAe,EAAE,WAAW;AACzC,cAAI,2BAA2B;AAC7B,mBAAO,sBAAsB,EAAE,SAAS;AAG1C,6BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,aAAa,aAAa,KAAA;AAC/E,6BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,iBAAiB,iBAAiB,KAAA;QACzF;AAEA,YAAI;AACF,YAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,EAAE,WAAW,GAAG;AAClB,cAAI,qBAAqB;AACvB,gBAAI,WAAU,GAAA,2CAAa,EAAE,MAAM;AACnC,gBAAI;AACF,oBAAM,YAAY,KAAK,OAAA;UAE3B;AAEA,YAAE,gBAAe;QACnB;MACF;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AAExB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA,KAAM,MAAM,gBAAgB;AACzF;AAIF,YAAI,EAAE,WAAW,KAAK,CAAC,MAAM;AAC3B,yBAAe,GAAG,MAAM,eAAe,EAAE,WAAW;MAExD;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,UAAU,CAAC,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC7G,gBAAM,eAAe;AACrB,4BAAkB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,WAAW;QACnE;MACF;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,UAAU,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC5G,gBAAM,eAAe;AACrB,0BAAgB,kCAAY,MAAM,QAAQ,CAAA,GAAI,MAAM,aAAa,KAAA;AACjE,8BAAoB,CAAA;QACtB;MACF;AAEA,UAAI,cAAc,CAAC,MAAA;AACjB,YAAI,EAAE,cAAc,MAAM,mBAAmB,MAAM,aAAa,EAAE,WAAW,KAAK,MAAM,QAAQ;AAC9F,eAAI,GAAA,2CAAa,MAAM,SAAQ,GAAA,2CAAe,CAAA,CAAA,KAAO,MAAM,eAAe,MAAM;AAW9E,gBAAI,UAAU;AACd,gBAAI,UAAU,WAAW,MAAA;AACvB,kBAAI,MAAM,aAAa,MAAM,kBAAkB,aAAA;AAC7C,oBAAI;AACF,yBAAO,CAAA;qBACF;AACL,mBAAA,GAAA,2CAAsB,MAAM,MAAM;AAClC,wBAAM,OAAO,MAAK;gBACpB;;YAEJ,GAAG,EAAA;AAGH,8BAAkB,EAAE,eAA2B,SAAS,MAAM,UAAU,MAAM,IAAA;AAC9E,kBAAM,YAAY,KAAK,MAAM,aAAa,OAAA,CAAA;UAC5C;AACE,mBAAO,CAAA;AAIT,gBAAM,eAAe;QACvB;MACF;AAEA,UAAI,kBAAkB,CAAC,MAAA;AACrB,eAAO,CAAA;MACT;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAIF,eAAO,CAAA;MACT;IACF,WAAW,OAAiC;AAI1C,MAAAA,YAAW,cAAc,CAAC,MAAA;AAExB,YAAI,EAAE,WAAW,KAAK,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC/E;AAGF,YAAI,MAAM,2BAA2B;AACnC,YAAE,gBAAe;AACjB;QACF;AAEA,cAAM,YAAY;AAClB,cAAM,eAAe;AACrB,cAAM,SAAS,EAAE;AACjB,cAAM,eAAc,GAAA,2CAAe,EAAE,WAAW,IAAI,YAAY;AAGhE,YAAI,yBAAwB,GAAA,kBAAAE,WAAU,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAA;AAClF,YAAI;AACF,YAAE,gBAAe;AAGnB,YAAI,qBAAqB;AACvB,cAAI,WAAU,GAAA,2CAAa,EAAE,MAAM;AACnC,cAAI;AACF,kBAAM,YAAY,KAAK,OAAA;QAE3B;AAEA,2BAAkB,GAAA,2CAAiB,EAAE,aAAa,GAAG,WAAW,WAAW,KAAA;MAC7E;AAEA,MAAAF,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,wBAAwB;AAC5B,YAAI,MAAM,aAAa,CAAC,MAAM,6BAA6B,MAAM,eAAe,MAAM;AACpF,gBAAM,eAAe;AACrB,kCAAwB,kBAAkB,GAAG,MAAM,WAAW;QAChE;AAEA,YAAI;AACF,YAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,wBAAwB;AAC5B,YAAI,MAAM,aAAa,CAAC,MAAM,6BAA6B,MAAM,eAAe,MAAM;AACpF,gBAAM,eAAe;AACrB,kCAAwB,gBAAgB,GAAG,MAAM,aAAa,KAAA;AAC9D,8BAAoB,CAAA;QACtB;AAEA,YAAI;AACF,YAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,YAAY,CAAC,MAAA;AACtB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,CAAC,MAAM,6BAA6B,EAAE,WAAW,KAAK,CAAC,MAAM;AAC/D,yBAAe,GAAG,MAAM,eAAe,OAAA;MAE3C;AAEA,UAAI,YAAY,CAAC,MAAA;AAEf,YAAI,EAAE,WAAW;AACf;AAGF,YAAI,MAAM,2BAA2B;AACnC,gBAAM,4BAA4B;AAClC;QACF;AAEA,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,EAAE,MAAM,KAAgB,MAAM,eAAe;AAAA;;AAIrF,iBAAO,CAAA;AAGT,cAAM,eAAe;MACvB;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,QAAQ,wCAAkB,EAAE,WAAW;AAC3C,YAAI,CAAC;AACH;AAEF,cAAM,kBAAkB,MAAM;AAC9B,cAAM,4BAA4B;AAClC,cAAM,eAAe;AACrB,cAAM,YAAY;AAClB,cAAM,SAAS,EAAE;AACjB,cAAM,cAAc;AAEpB,YAAI,CAAC;AACH,WAAA,GAAA,2CAAqB,MAAM,MAAM;AAGnC,YAAI,wBAAwB,kBAAkB,uCAAiB,MAAM,QAAQ,CAAA,GAAI,MAAM,WAAW;AAClG,YAAI;AACF,YAAE,gBAAe;AAGnB,2BAAkB,GAAA,2CAAe,EAAE,aAAa,GAAG,UAAU,UAAU,IAAA;MACzE;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,CAAC,MAAM,WAAW;AACpB,YAAE,gBAAe;AACjB;QACF;AAEA,YAAI,QAAQ,mCAAa,EAAE,aAAa,MAAM,eAAe;AAC7D,YAAI,wBAAwB;AAC5B,YAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,GAC9C;AAAA,cAAI,CAAC,MAAM,gBAAgB,MAAM,eAAe,MAAM;AACpD,kBAAM,eAAe;AACrB,oCAAwB,kBAAkB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;UACjG;QAAA,WACS,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAC1D,gBAAM,eAAe;AACrB,kCAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,aAAa,KAAA;AAC/F,8BAAoB,uCAAiB,MAAM,QAAS,CAAA,CAAA;QACtD;AAEA,YAAI;AACF,YAAE,gBAAe;MAErB;AAEA,MAAAA,YAAW,aAAa,CAAC,MAAA;AACvB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,YAAI,CAAC,MAAM,WAAW;AACpB,YAAE,gBAAe;AACjB;QACF;AAEA,YAAI,QAAQ,mCAAa,EAAE,aAAa,MAAM,eAAe;AAC7D,YAAI,wBAAwB;AAC5B,YAAI,SAAS,mCAAa,OAAO,EAAE,aAAa,KAAK,MAAM,eAAe,MAAM;AAC9E,yBAAe,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;AACpE,kCAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,WAAW;AAC7F,gCAAsB,EAAE,aAAa,MAAM,MAAM;QACnD,WAAW,MAAM,gBAAgB,MAAM,eAAe;AACpD,kCAAwB,gBAAgB,uCAAiB,MAAM,QAAS,CAAA,GAAI,MAAM,aAAa,KAAA;AAGjG,YAAI;AACF,YAAE,gBAAe;AAGnB,cAAM,YAAY;AAClB,cAAM,kBAAkB;AACxB,cAAM,eAAe;AACrB,cAAM,4BAA4B;AAClC,YAAI,MAAM,UAAU,CAAC;AACnB,WAAA,GAAA,2CAAqB,MAAM,MAAM;AAEnC,iCAAA;MACF;AAEA,MAAAA,YAAW,gBAAgB,CAAC,MAAA;AAC1B,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,UAAE,gBAAe;AACjB,YAAI,MAAM;AACR,iBAAO,uCAAiB,MAAM,QAAS,CAAA,CAAA;MAE3C;AAEA,UAAI,WAAW,CAAC,MAAA;AACd,YAAI,MAAM,cAAa,GAAA,4CAAa,GAAA,2CAAe,CAAA,GAAI,MAAM,MAAM;AACjE,iBAAO;YACL,eAAe,MAAM;YACrB,UAAU;YACV,SAAS;YACT,SAAS;YACT,QAAQ;UACV,CAAA;MAEJ;AAEA,MAAAA,YAAW,cAAc,CAAC,MAAA;AACxB,YAAI,EAAC,GAAA,2CAAa,EAAE,gBAAe,GAAA,2CAAe,EAAE,WAAW,CAAA;AAC7D;AAGF,eAAO,CAAA;MACT;IACF;AAEA,WAAOA;EACT,GAAG;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GACD;AAGD,GAAA,GAAA,eAAAG,WAAU,MAAA;AACR,QAAI,CAAC,UAAU;AACb;AAGF,UAAM,iBAAgB,GAAA,2CAAiB,OAAO,OAAO;AACrD,QAAI,CAAC,iBAAiB,CAAC,cAAc,QAAQ,cAAc,eAAe,8BAAA;AACxE;AAGF,UAAM,QAAQ,cAAc,cAAc,OAAA;AAC1C,UAAM,KAAK;AAIX,UAAM,cAAc;;KAEnB,yCAAA;;;;MAIC,KAAI;AACN,kBAAc,KAAK,QAAQ,KAAA;EAC7B,GAAG;IAAC;GAAO;AAGX,GAAA,GAAA,eAAAA,WAAU,MAAA;AACR,QAAI,QAAQ,IAAI;AAChB,WAAO,MAAA;UAEkB;AADvB,UAAI,CAAC;AACH,SAAA,GAAA,4CAAqB,gBAAA,MAAM,YAAM,QAAZ,kBAAA,SAAA,gBAAgB,MAAA;AAEvC,eAAS,WAAW,MAAM;AACxB,gBAAA;AAEF,YAAM,cAAc,CAAA;IACtB;EACF,GAAG;IAAC;GAA0B;AAE9B,SAAO;IACL,WAAW,iBAAiB;IAC5B,aAAY,GAAA,2CAAW,UAAU,YAAY;MAAC,CAAC,yCAAA,GAAsB;IAAI,CAAA;EAC3E;AACF;AAEA,SAAS,uCAAiB,QAAe;AACvC,SAAO,OAAO,YAAY,OAAO,OAAO,aAAa,MAAA;AACvD;AAEA,SAAS,2CAAqB,OAAsB,eAAsB;AACxE,QAAM,EAAA,KAAI,KAAM,IAAI;AACpB,QAAM,UAAU;AAChB,QAAM,OAAO,QAAQ,aAAa,MAAA;AAGlC,UACG,QAAQ,WAAW,QAAQ,OAAO,QAAQ,cAAc,SAAS,YAClE,EAAG,oBAAmB,GAAA,2CAAe,OAAA,EAAS,oBAAoB,CAAC,sCAAgB,SAAS,GAAA,KAC1F,oBAAmB,GAAA,2CAAe,OAAA,EAAS,uBAC3C,QAAQ;EAEV,GAAG,SAAS,UAAW,CAAC,QAAQ,uCAAiB,OAAA,MAAc,QAAQ;AAE3E;AA0CA,SAAS,kCAAY,QAA0B,GAAY;AACzD,MAAI,UAAU,EAAE;AAChB,MAAI,UAAU,EAAE;AAChB,SAAO;IACL,eAAe;IACf,UAAU,EAAE;IACZ,SAAS,EAAE;IACX,SAAS,EAAE;IACX,QAAQ,EAAE;;;EAGZ;AACF;AA0DA,SAAS,6CAAuB,QAAe;AAC7C,MAAI,kBAAkB;AACpB,WAAO;AAGT,MAAI,kBAAkB;AACpB,WAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAGrD,MAAI,uCAAiB,MAAA;AACnB,WAAO;AAGT,SAAO;AACT;AAEA,SAAS,mDAA6B,QAAiB,KAAW;AAChE,MAAI,kBAAkB;AACpB,WAAO,CAAC,sCAAgB,QAAQ,GAAA;AAGlC,SAAO,6CAAuB,MAAA;AAChC;AAEA,IAAM,0CAAoB,oBAAI,IAAI;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAED,SAAS,sCAAgB,QAA0B,KAAW;AAE5D,SAAO,OAAO,SAAS,cAAc,OAAO,SAAS,UACjD,QAAQ,MACR,wCAAkB,IAAI,OAAO,IAAI;AACvC;;;;ACt+BA,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,oBAAI,IAAA;AAIlB,IAAI,4CAA0B,oBAAI,IAAA;AACzC,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAG/B,IAAM,iDAA2B;EAC/B,KAAK;EACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,UAAoB,GAAe;AAChE,WAAS,WAAW;AAClB,YAAQ,UAAU,CAAA;AAEtB;AAKA,SAAS,iCAAW,GAAgB;AAElC,SAAO,EAAE,EAAE,WAAY,EAAC,GAAA,2CAAI,KAAO,EAAE,UAAW,EAAE,WAAW,EAAE,QAAQ,aAAa,EAAE,QAAQ,WAAW,EAAE,QAAQ;AACrH;AAGA,SAAS,0CAAoB,GAAgB;AAC3C,8CAAsB;AACtB,MAAI,iCAAW,CAAA,GAAI;AACjB,4CAAkB;AAClB,gDAAsB,YAAY,CAAA;EACpC;AACF;AAEA,SAAS,yCAAmB,GAA4B;AACtD,0CAAkB;AAClB,MAAI,EAAE,SAAS,eAAe,EAAE,SAAS,eAAe;AACtD,gDAAsB;AACtB,gDAAsB,WAAW,CAAA;EACnC;AACF;AAEA,SAAS,uCAAiB,GAAa;AACrC,OAAI,GAAA,2CAAe,CAAA,GAAI;AACrB,gDAAsB;AACtB,4CAAkB;EACpB;AACF;AAEA,SAAS,uCAAiB,GAAa;AAIrC,MAAI,EAAE,WAAW,UAAU,EAAE,WAAW,aAAY,GAAA,8CAAoB,CAAC,EAAE;AACzE;AAKF,MAAI,CAAC,6CAAuB,CAAC,gDAA0B;AACrD,4CAAkB;AAClB,gDAAsB,WAAW,CAAA;EACnC;AAEA,8CAAsB;AACtB,mDAA2B;AAC7B;AAEA,SAAS,yCAAA;AACP,MAAI,GAAA;AACF;AAKF,8CAAsB;AACtB,mDAA2B;AAC7B;AAKA,SAAS,6CAAuB,SAA4B;AAC1D,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,0CAAwB,KAAI,GAAA,2CAAe,OAAA,CAAA;AACjH;AAGF,QAAM,gBAAe,GAAA,2CAAe,OAAA;AACpC,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AAMxC,MAAI,QAAQ,aAAa,YAAY,UAAU;AAC/C,eAAa,YAAY,UAAU,QAAQ,WAAA;AACzC,gDAAsB;AACtB,UAAM,MAAM,MAAM,SAAA;EACpB;AAEA,iBAAe,iBAAiB,WAAW,2CAAqB,IAAA;AAChE,iBAAe,iBAAiB,SAAS,2CAAqB,IAAA;AAC9D,iBAAe,iBAAiB,SAAS,wCAAkB,IAAA;AAI3D,eAAa,iBAAiB,SAAS,wCAAkB,IAAA;AACzD,eAAa,iBAAiB,QAAQ,wCAAkB,KAAA;AAExD,MAAI,OAAO,iBAAiB,aAAa;AACvC,mBAAe,iBAAiB,eAAe,0CAAoB,IAAA;AACnE,mBAAe,iBAAiB,eAAe,0CAAoB,IAAA;AACnE,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;EACnE,WAAW,OAAiC;AAC1C,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;AACjE,mBAAe,iBAAiB,aAAa,0CAAoB,IAAA;AACjE,mBAAe,iBAAiB,WAAW,0CAAoB,IAAA;EACjE;AAGA,eAAa,iBAAiB,gBAAgB,MAAA;AAC5C,sDAA4B,OAAA;EAC9B,GAAG;IAAC,MAAM;EAAI,CAAA;AAEd,4CAAwB,IAAI,cAAc;;EAAM,CAAA;AAClD;AAEA,IAAM,oDAA8B,CAAC,SAAS,iBAAA;AAC5C,QAAM,gBAAe,GAAA,2CAAe,OAAA;AACpC,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AACxC,MAAI;AACF,mBAAe,oBAAoB,oBAAoB,YAAA;AAEzD,MAAI,CAAC,0CAAwB,IAAI,YAAA;AAC/B;AAEF,eAAa,YAAY,UAAU,QAAQ,0CAAwB,IAAI,YAAA,EAAe;AAEtF,iBAAe,oBAAoB,WAAW,2CAAqB,IAAA;AACnE,iBAAe,oBAAoB,SAAS,2CAAqB,IAAA;AACjE,iBAAe,oBAAoB,SAAS,wCAAkB,IAAA;AAE9D,eAAa,oBAAoB,SAAS,wCAAkB,IAAA;AAC5D,eAAa,oBAAoB,QAAQ,wCAAkB,KAAA;AAE3D,MAAI,OAAO,iBAAiB,aAAa;AACvC,mBAAe,oBAAoB,eAAe,0CAAoB,IAAA;AACtE,mBAAe,oBAAoB,eAAe,0CAAoB,IAAA;AACtE,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;EACtE,WAAW,OAAiC;AAC1C,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;AACpE,mBAAe,oBAAoB,aAAa,0CAAoB,IAAA;AACpE,mBAAe,oBAAoB,WAAW,0CAAoB,IAAA;EACpE;AAEA,4CAAwB,OAAO,YAAA;AACjC;AAmBO,SAAS,0CAAuB,SAA4B;AACjE,QAAM,kBAAiB,GAAA,2CAAiB,OAAA;AACxC,MAAI;AACJ,MAAI,eAAe,eAAe;AAChC,iDAAuB,OAAA;OAClB;AACL,mBAAe,MAAA;AACb,mDAAuB,OAAA;IACzB;AACA,mBAAe,iBAAiB,oBAAoB,YAAA;EACtD;AAEA,SAAO,MAAM,kDAA4B,SAAS,YAAA;AACpD;AAIA,IAAI,OAAO,aAAa;AACtB,4CAAA;AAMK,SAAS,4CAAA;AACd,SAAO,0CAAoB;AAC7B;AAEO,SAAS,4CAAA;AACd,SAAO;AACT;AAEO,SAAS,0CAAuB,UAAkB;AACvD,0CAAkB;AAClB,8CAAsB,UAAU,IAAA;AAClC;AAuBA,IAAM,0CAAoB,oBAAI,IAAI;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAMD,SAAS,2CAAqB,aAAsB,UAAoB,GAAe;AACrF,MAAI,aAAW,GAAA,2CAAiB,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM;AACzC,QAAM,oBAAoB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,mBAAmB;AAClH,QAAM,uBAAuB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,sBAAsB;AACxH,QAAM,eAAe,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,cAAc;AACxG,QAAM,iBAAiB,OAAO,WAAW,eAAc,GAAA,2CAAe,MAAA,QAAA,MAAA,SAAA,SAAA,EAAG,MAAM,EAAa,gBAAgB;AAI5G,gBAAc,eACX,UAAS,yBAAyB,qBAAqB,CAAC,wCAAkB,IAAI,UAAS,cAAc,IAAI,KAC1G,UAAS,yBAAyB,wBACjC,UAAS,yBAAyB,gBAAgB,UAAS,cAAc;AAC5E,SAAO,EAAE,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,+CAAyB,EAAE,GAAG;AACnH;AAkBO,SAAS,0CAAwB,IAAyB,MAA0B,MAA8B;AACvH,+CAAA;AAEA,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,UAAU,CAAC,UAAoB,MAAA;AAEjC,UAAI,CAAC,2CAAqB,CAAC,EAAE,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,cAAc,UAAU,CAAA;AACzD;AAEF,SAAG,0CAAA,CAAA;IACL;AACA,yCAAe,IAAI,OAAA;AACnB,WAAO,MAAA;AACL,2CAAe,OAAO,OAAA;IACxB;EAEF,GAAG,IAAA;AACL;;;AC3TO,SAAS,0CAAY,SAAyB;AAMnD,QAAM,iBAAgB,GAAA,2CAAiB,OAAA;AACvC,QAAM,iBAAgB,GAAA,2CAAiB,aAAA;AACvC,OAAI,GAAA,2CAAqB,MAAQ,WAAW;AAC1C,QAAI,qBAAqB;AACzB,KAAA,GAAA,2CAAmB,MAAA;AAEjB,WAAI,GAAA,2CAAiB,aAAA,MAAmB,sBAAsB,QAAQ;AACpE,SAAA,GAAA,2CAAsB,OAAA;IAE1B,CAAA;EACF;AACE,KAAA,GAAA,2CAAsB,OAAA;AAE1B;;;;ACRO,SAAS,0CAA6D,OAAyB;AACpG,MAAI,EAAA,YAEF,SAAS,aACT,QAAQ,YAAU,cACL,IACX;AAEJ,QAAM,UAAuC,GAAA,eAAAC,aAAY,CAAC,MAAA;AACxD,QAAI,EAAE,WAAW,EAAE,eAAe;AAChC,UAAI;AACF,mBAAW,CAAA;AAGb,UAAI;AACF,sBAAc,KAAA;AAGhB,aAAO;IACT;EACF,GAAG;IAAC;IAAY;GAAc;AAG9B,QAAM,oBAAmB,GAAA,2CAA8B,MAAA;AAEvD,QAAM,WAAyC,GAAA,eAAAA,aAAY,CAAC,MAAA;AAI1D,UAAM,iBAAgB,GAAA,2CAAiB,EAAE,MAAM;AAC/C,UAAM,gBAAgB,iBAAgB,GAAA,2CAAiB,aAAA,KAAiB,GAAA,2CAAe;AACvF,QAAI,EAAE,WAAW,EAAE,iBAAiB,mBAAkB,GAAA,2CAAe,EAAE,WAAW,GAAG;AACnF,UAAI;AACF,oBAAY,CAAA;AAGd,UAAI;AACF,sBAAc,IAAA;AAGhB,uBAAiB,CAAA;IACnB;EACF,GAAG;IAAC;IAAe;IAAa;GAAiB;AAEjD,SAAO;IACL,YAAY;MACV,SAAU,CAAC,eAAe,eAAe,iBAAiB,cAAe,UAAU;MACnF,QAAS,CAAC,eAAe,cAAc,iBAAkB,SAAS;IACpE;EACF;AACF;;;ACpEO,SAAS,0CAA6C,SAAmC;AAC9F,MAAI,CAAC;AACH,WAAO;AAGT,MAAI,wBAAwB;AAC5B,SAAO,CAAC,MAAA;AACN,QAAI,QAAsB;MACxB,GAAG;MACH,iBAAA;AACE,UAAE,eAAc;MAClB;MACA,qBAAA;AACE,eAAO,EAAE,mBAAkB;MAC7B;MACA,kBAAA;AACE,YAAI,yBAAyB;AAC3B,kBAAQ,MAAM,sIAAA;;AAEd,kCAAwB;MAE5B;MACA,sBAAA;AACE,gCAAwB;MAC1B;MACA,uBAAA;AACE,eAAO;MACT;IACF;AAEA,YAAQ,KAAA;AAER,QAAI;AACF,QAAE,gBAAe;EAErB;AACF;;;AC1BO,SAAS,0CAAY,OAAoB;AAC9C,SAAO;IACL,eAAe,MAAM,aAAa,CAAC,IAAI;MACrC,YAAW,GAAA,2CAAmB,MAAM,SAAS;MAC7C,UAAS,GAAA,2CAAmB,MAAM,OAAO;IAC3C;EACF;AACF;;;;ACAO,IAAI,6CAAmB,GAAA,eAAAC,SAAM,cAA4C,IAAA;AAEhF,SAAS,0CAAoB,KAAuC;AAClE,MAAI,WAAU,GAAA,eAAAC,YAAW,yCAAA,KAAqB,CAAC;AAC/C,GAAA,GAAA,2CAAW,SAAS,GAAA;AAGpB,MAAI,EAAC,KAAK,GAAG,GAAG,WAAA,IAAc;AAC9B,SAAO;AACT;AAKO,IAAM,4CAAoB,GAAA,eAAAD,SAAM,WAAW,SAAS,kBAAkB,OAA+B,KAAmC;AAC7I,MAAI,EAAA,UAAW,GAAG,WAAA,IAAc;AAChC,MAAI,UAAS,GAAA,2CAAa,GAAA;AAC1B,MAAI,UAAU;IACZ,GAAG;IACH,KAAK;EACP;AAEA,UACE,GAAA,eAAAA,SAAA,cAAC,0CAAiB,UAAQ;IAAC,OAAO;KAC/B,QAAA;AAGP,CAAA;AAUO,SAAS,0CAA4D,OAA4B,QAA0C;AAChJ,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS,KAAA;AAC5B,MAAI,EAAA,cAAc,KAAI,GAAA,2CAAY,KAAA;AAClC,MAAI,gBAAe,GAAA,2CAAW,YAAY,aAAA;AAC1C,MAAI,WAAW,0CAAoB,MAAA;AACnC,MAAI,mBAAmB,MAAM,aAAa,CAAC,IAAI;AAC/C,MAAI,gBAAe,GAAA,eAAAE,QAAO,MAAM,SAAS;AAEzC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,aAAa,WAAW,OAAO;AACjC,OAAA,GAAA,2CAAY,OAAO,OAAO;AAE5B,iBAAa,UAAU;EACzB,GAAG;IAAC;GAAO;AAGX,MAAI,WAA+B,MAAM,sBAAsB,KAAK;AACpE,MAAI,MAAM;AACR,eAAW;AAGb,SAAO;IACL,iBAAgB,GAAA,2CACd;MACE,GAAG;;IAEL,GACA,gBAAA;EAEJ;AACF;AAMO,IAAM,6CAAY,GAAA,eAAAC,YAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAAiC,QAAA;AAClF,SAAM,GAAA,2CAAa,GAAA;AACnB,MAAI,EAAA,eAAe,IAAI,0CAAa,OAAO,GAAA;AAC3C,MAAI,SAAQ,GAAA,eAAAJ,SAAM,SAAS,KAAK,QAAA;AAEhC,GAAA,GAAA,eAAAG,WAAU,MAAA;AACR,QAAI;AACF;AAGF,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,MAAM,EAAE,eAAc,GAAA,2CAAe,EAAA,EAAI,UAAU;AACtD,cAAQ,MAAM,0DAAA;AACd;IACF;AAEA,QAAI,CAAC,MAAM,cAAc,EAAC,GAAA,2CAAY,EAAA,GAAK;AACzC,cAAQ,KAAK,yFAAA;AACb;IACF;AAEA,QACE,GAAG,cAAc,YACjB,GAAG,cAAc,WACjB,GAAG,cAAc,YACjB,GAAG,cAAc,cACjB,GAAG,cAAc,OACjB,GAAG,cAAc,UACjB,GAAG,cAAc,aACjB,GAAG,cAAc,SACjB,GAAG,cAAc,OACjB;AACA,UAAI,OAAO,GAAG,aAAa,MAAA;AAC3B,UAAI,CAAC;AACH,gBAAQ,KAAK,uDAAA;;;QAGb,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,cACT,SAAS,aACT,SAAS;QAET,SAAS,SACT,SAAS,WACT,SAAS;;AAET,gBAAQ,KAAK,8DAA8D,IAAA,IAAQ;IAEvF;EACF,GAAG;IAAC;IAAK,MAAM;GAAW;AAG1B,MAAI,WAAW,UAAS,GAAA,eAAAH,SAAM,SAAS,EAAA,IAAM,KAAK,MAAM,MAAM,MAAM,MAAM;AAE1E,UAAO,GAAA,eAAAA,SAAM,aACX,OACA;IACE,IAAG,GAAA,2CAAW,gBAAgB,MAAM,KAAK;;IAEzC,MAAK,GAAA,2CAAU,UAAU,GAAA;EAC3B,CAAA;AAEJ,CAAA;;;;ACpKO,IAAM,6CAAY,GAAA,eAAAK,SAAM,WAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAAwB,QAAA;AAC/E,SAAM,GAAA,2CAAa,GAAA;AACnB,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;IAAC,GAAG;;EAAU,CAAA;AAC1C,MAAI,EAAA,eAAe,KAAI,GAAA,2CAAa,OAAO,GAAA;AAC3C,MAAI,SAAQ,GAAA,eAAAA,SAAM,SAAS,KAAK,QAAA;AAEhC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI;AACF;AAGF,QAAI,KAAK,IAAI;AACb,QAAI,CAAC,MAAM,EAAE,eAAc,GAAA,2CAAe,EAAA,EAAI,UAAU;AACtD,cAAQ,MAAM,0DAAA;AACd;IACF;AAEA,QAAI,CAAC,MAAM,cAAc,EAAC,GAAA,2CAAY,EAAA,GAAK;AACzC,cAAQ,KAAK,yFAAA;AACb;IACF;AAEA,QACE,GAAG,cAAc,YACjB,GAAG,cAAc,WACjB,GAAG,cAAc,YACjB,GAAG,cAAc,cACjB,GAAG,cAAc,OACjB,GAAG,cAAc,UACjB,GAAG,cAAc,WACjB;AACA,UAAI,OAAO,GAAG,aAAa,MAAA;AAC3B,UAAI,CAAC;AACH,gBAAQ,KAAK,uDAAA;;;QAGb,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,aACT,SAAS;;AAET,gBAAQ,KAAK,8DAA8D,IAAA,IAAQ;IAEvF;EACF,GAAG;IAAC;IAAK,MAAM;GAAW;AAG1B,MAAI,WAAW,UAAS,GAAA,eAAAD,SAAM,SAAS,EAAA,IAAM,KAAK,MAAM,MAAM,MAAM,MAAM;AAE1E,UAAO,GAAA,eAAAA,SAAM,aACX,OACA;IACE,IAAG,GAAA,2CAAW,YAAY,gBAAgB,MAAM,KAAK;;IAErD,MAAK,GAAA,2CAAU,UAAU,GAAA;EAC3B,CAAA;AAEJ,CAAA;;;;ACxEO,IAAM,6CAAiB,GAAA,eAAAE,SAAM,WAAW,CAAC,EAAA,UAAW,GAAG,MAAA,GAA6B,QAAA;AACzF,MAAI,gBAAe,GAAA,eAAAC,QAAO,KAAA;AAC1B,MAAI,eAAc,GAAA,eAAAC,aAAW,GAAA,0CAAoB;AACjD,SAAM,GAAA,2CAAa,QAAO,gBAAA,QAAA,gBAAA,SAAA,SAAA,YAAa,IAAG;AAC1C,MAAI,WAAU,GAAA,2CAAW,eAAe,CAAC,GAAG;IAC1C,GAAG;;IAEH,WAAA;AACE,mBAAa,UAAU;AACvB,UAAI;AACF,oBAAY,SAAQ;IAExB;EACF,CAAA;AAEA,GAAA,GAAA,2CAAW,aAAa,GAAA;AAExB,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,CAAC,aAAa,SAAS;AACzB,UAAI;AACF,gBAAQ,KACN,2IAAA;AAIJ,mBAAa,UAAU;IACzB;EACF,GAAG,CAAA,CAAE;AAEL,UACE,GAAA,eAAAH,SAAA,eAAC,GAAA,2CAAsB,UAAQ;IAAC,OAAO;KACpC,QAAA;AAGP,CAAA;AAEO,SAAS,0CAAoB,EAAA,SAAS,GAAwB;AACnE,MAAI,WAAU,GAAA,eAAAI,SAAQ,OAAO;IAAC,UAAU,MAAA;IAAO;EAAC,IAAI,CAAA,CAAE;AACtD,UACE,GAAA,eAAAJ,SAAA,eAAC,GAAA,2CAAsB,UAAQ;IAAC,OAAO;KACpC,QAAA;AAGP;;;;ACxBO,SAAS,0CAAe,OAAuB;AACpD,MAAI,EAAA,YACQ,cACE,eACC,oBACM,IACjB;AACJ,MAAI,SAAQ,GAAA,eAAAK,QAAO;IACjB,eAAe;EACjB,CAAA;AAEA,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,UAAS,GAAA,eAAAC,aAAY,CAAC,MAAA;AAExB,QAAI,CAAC,EAAE,cAAc,SAAS,EAAE,MAAM;AACpC;AAMF,QAAI,MAAM,QAAQ,iBAAiB,CAAE,EAAE,cAA0B,SAAS,EAAE,aAAa,GAAc;AACrG,YAAM,QAAQ,gBAAgB;AAC9B,+BAAA;AAEA,UAAI;AACF,qBAAa,CAAA;AAGf,UAAI;AACF,4BAAoB,KAAA;IAExB;EACF,GAAG;IAAC;IAAc;IAAqB;IAAO;GAAyB;AAEvE,MAAI,oBAAmB,GAAA,2CAAsB,MAAA;AAC7C,MAAI,WAAU,GAAA,eAAAA,aAAY,CAAC,MAAA;AAEzB,QAAI,CAAC,EAAE,cAAc,SAAS,EAAE,MAAM;AACpC;AAKF,UAAM,iBAAgB,GAAA,2CAAiB,EAAE,MAAM;AAC/C,UAAM,iBAAgB,GAAA,2CAAiB,aAAA;AACvC,QAAI,CAAC,MAAM,QAAQ,iBAAiB,mBAAkB,GAAA,2CAAe,EAAE,WAAW,GAAG;AACnF,UAAI;AACF,sBAAc,CAAA;AAGhB,UAAI;AACF,4BAAoB,IAAA;AAGtB,YAAM,QAAQ,gBAAgB;AAC9B,uBAAiB,CAAA;AAKjB,UAAI,gBAAgB,EAAE;AACtB,wBAAkB,eAAe,SAAS,CAAAC,OAAA;AACxC,YAAI,MAAM,QAAQ,iBAAiB,EAAC,GAAA,2CAAa,eAAeA,GAAE,MAAM,GAAc;AACpF,cAAI,cAAc,IAAI,cAAc,YAAa,WAAW,QAAQ;YAAC,eAAeA,GAAE;UAAM,CAAA;AAC5F,WAAA,GAAA,2CAAe,aAAa,aAAA;AAC5B,cAAI,SAAQ,GAAA,0CAAiC,WAAA;AAC7C,iBAAO,KAAA;QACT;MACF,GAAG;QAAC,SAAS;MAAI,CAAA;IACnB;EACF,GAAG;IAAC;IAAe;IAAqB;IAAkB;IAAmB;GAAO;AAEpF,MAAI;AACF,WAAO;MACL,kBAAkB;;QAEhB,SAAS;QACT,QAAQ;MACV;IACF;AAGF,SAAO;IACL,kBAAkB;;;IAGlB;EACF;AACF;;;;AChGA,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS,2DAAA;AACP,0DAAkC;AAMlC,aAAW,MAAA;AACT,4DAAkC;EACpC,GAAG,EAAA;AACL;AAEA,SAAS,+CAAyB,GAAC;AACjC,MAAI,EAAE,gBAAgB;AACpB,6DAAA;AAEJ;AAEA,SAAS,+CAAA;AACP,MAAI,OAAO,aAAa;AACtB;AAGF,MAAI,OAAO,iBAAiB;AAC1B,aAAS,iBAAiB,aAAa,8CAAA;WAC9B;AACT,aAAS,iBAAiB,YAAY,wDAAA;AAGxC;AACA,SAAO,MAAA;AACL;AACA,QAAI,mCAAa;AACf;AAGF,QAAI,OAAO,iBAAiB;AAC1B,eAAS,oBAAoB,aAAa,8CAAA;aACjC;AACT,eAAS,oBAAoB,YAAY,wDAAA;EAE7C;AACF;AAMO,SAAS,0CAAS,OAAiB;AACxC,MAAI,EAAA,cACU,eACC,YACH,WACA,IACR;AAEJ,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,SAAQ,GAAA,eAAAC,QAAO;IACjB,WAAW;IACX,2BAA2B;IAC3B,aAAa;IACb,QAAQ;EACV,CAAA,EAAG;AAEH,GAAA,GAAA,eAAAC,WAAU,8CAAwB,CAAA,CAAE;AACpC,MAAI,EAAA,mBAAkB,yBAA0B,KAAI,GAAA,2CAAiB;AAErE,MAAI,EAAA,YAAW,gBAAiB,KAAI,GAAA,eAAAC,SAAQ,MAAA;AAC1C,QAAI,oBAAoB,CAAC,OAAO,gBAAA;AAC9B,YAAM,cAAc;AACpB,UAAI,cAAc,gBAAgB,WAAW,MAAM,aAAa,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM;AACxG;AAGF,YAAM,YAAY;AAClB,UAAI,SAAS,MAAM;AACnB,YAAM,SAAS;AAMf,yBAAkB,GAAA,2CAAiB,MAAM,MAAM,GAAG,eAAe,CAAA,MAAA;AAC/D,YAAI,MAAM,aAAa,MAAM,UAAU,EAAC,GAAA,2CAAa,MAAM,QAAQ,EAAE,MAAM;AACzE,UAAAC,iBAAgB,GAAG,EAAE,WAAW;MAEpC,GAAG;QAAC,SAAS;MAAI,CAAA;AAEjB,UAAI;AACF,qBAAa;UACX,MAAM;;;QAGR,CAAA;AAGF,UAAI;AACF,sBAAc,IAAA;AAGhB,iBAAW,IAAA;IACb;AAEA,QAAIA,mBAAkB,CAAC,OAAO,gBAAA;AAC5B,UAAI,SAAS,MAAM;AACnB,YAAM,cAAc;AACpB,YAAM,SAAS;AAEf,UAAI,gBAAgB,WAAW,CAAC,MAAM,aAAa,CAAC;AAClD;AAGF,YAAM,YAAY;AAClB,+BAAA;AAEA,UAAI;AACF,mBAAW;UACT,MAAM;;;QAGR,CAAA;AAGF,UAAI;AACF,sBAAc,KAAA;AAGhB,iBAAW,KAAA;IACb;AAEA,QAAIC,cAA4B,CAAC;AAEjC,QAAI,OAAO,iBAAiB,aAAa;AACvC,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,yDAAmC,EAAE,gBAAgB;AACvD;AAGF,0BAAkB,GAAG,EAAE,WAAW;MACpC;AAEA,MAAAA,YAAW,iBAAiB,CAAC,MAAA;AAC3B,YAAI,CAAC,cAAc,EAAE,cAAc,SAAS,EAAE,MAAM;AAClD,UAAAD,iBAAgB,GAAG,EAAE,WAAW;MAEpC;IACF,WAAW,OAAiC;AAC1C,MAAAC,YAAW,eAAe,MAAA;AACxB,cAAM,4BAA4B;MACpC;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,CAAC,MAAM,6BAA6B,CAAC;AACvC,4BAAkB,GAAG,OAAA;AAGvB,cAAM,4BAA4B;MACpC;AAEA,MAAAA,YAAW,eAAe,CAAC,MAAA;AACzB,YAAI,CAAC,cAAc,EAAE,cAAc,SAAS,EAAE,MAAM;AAClD,UAAAD,iBAAgB,GAAG,OAAA;MAEvB;IACF;AACA,WAAO;kBAACC;uBAAYD;IAAe;EACrC,GAAG;IAAC;IAAc;IAAe;IAAY;IAAY;IAAO;IAAmB;GAAyB;AAE5G,GAAA,GAAA,eAAAF,WAAU,MAAA;AAGR,QAAI;AACF,sBAAgB;QAAC,eAAe,MAAM;MAAM,GAAG,MAAM,WAAW;EAGpE,GAAG;IAAC;GAAW;AAEf,SAAO;;;EAGP;AACF;;;;AC1LO,SAAS,0CAAmB,OAA2B;AAC5D,MAAI,EAAA,KAAI,mBAAmB,YAAY,uBAAwB,IAAI;AACnE,MAAI,YAAW,GAAA,eAAAI,QAAO;IACpB,eAAe;IACf,2BAA2B;EAC7B,CAAA;AAEA,MAAI,iBAAgB,GAAA,2CAAe,CAAC,MAAA;AAClC,QAAI,qBAAqB,mCAAa,GAAG,GAAA,GAAM;AAC7C,UAAI;AACF,+BAAuB,CAAA;AAEzB,eAAS,QAAQ,gBAAgB;IACnC;EACF,CAAA;AAEA,MAAI,0BAAyB,GAAA,2CAAe,CAAC,MAAA;AAC3C,QAAI;AACF,wBAAkB,CAAA;EAEtB,CAAA;AAEA,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,QAAQ,SAAS;AACrB,QAAI;AACF;AAGF,UAAM,UAAU,IAAI;AACpB,UAAM,kBAAiB,GAAA,2CAAiB,OAAA;AAGxC,QAAI,OAAO,iBAAiB,aAAa;AACvC,UAAI,UAAU,CAAC,MAAA;AACb,YAAI,MAAM,iBAAiB,mCAAa,GAAG,GAAA;AACzC,iCAAuB,CAAA;AAEzB,cAAM,gBAAgB;MACxB;AAKA,qBAAe,iBAAiB,eAAe,eAAe,IAAA;AAC9D,qBAAe,iBAAiB,SAAS,SAAS,IAAA;AAElD,aAAO,MAAA;AACL,uBAAe,oBAAoB,eAAe,eAAe,IAAA;AACjE,uBAAe,oBAAoB,SAAS,SAAS,IAAA;MACvD;IACF,WAAW,OAAiC;AAC1C,UAAI,YAAY,CAAC,MAAA;AACf,YAAI,MAAM;AACR,gBAAM,4BAA4B;iBACzB,MAAM,iBAAiB,mCAAa,GAAG,GAAA;AAChD,iCAAuB,CAAA;AAEzB,cAAM,gBAAgB;MACxB;AAEA,UAAI,aAAa,CAAC,MAAA;AAChB,cAAM,4BAA4B;AAClC,YAAI,MAAM,iBAAiB,mCAAa,GAAG,GAAA;AACzC,iCAAuB,CAAA;AAEzB,cAAM,gBAAgB;MACxB;AAEA,qBAAe,iBAAiB,aAAa,eAAe,IAAA;AAC5D,qBAAe,iBAAiB,WAAW,WAAW,IAAA;AACtD,qBAAe,iBAAiB,cAAc,eAAe,IAAA;AAC7D,qBAAe,iBAAiB,YAAY,YAAY,IAAA;AAExD,aAAO,MAAA;AACL,uBAAe,oBAAoB,aAAa,eAAe,IAAA;AAC/D,uBAAe,oBAAoB,WAAW,WAAW,IAAA;AACzD,uBAAe,oBAAoB,cAAc,eAAe,IAAA;AAChE,uBAAe,oBAAoB,YAAY,YAAY,IAAA;MAC7D;IACF;EACF,GAAG;IAAC;IAAK;IAAY;IAAe;GAAuB;AAC7D;AAEA,SAAS,mCAAa,OAAO,KAAG;AAC9B,MAAI,MAAM,SAAS;AACjB,WAAO;AAET,MAAI,MAAM,QAAQ;AAEhB,UAAM,gBAAgB,MAAM,OAAO;AACnC,QAAI,CAAC,iBAAiB,CAAC,cAAc,gBAAgB,SAAS,MAAM,MAAM;AACxE,aAAO;AAGT,QAAI,MAAM,OAAO,QAAQ,6BAAA;AACvB,aAAO;EAEX;AAEA,MAAI,CAAC,IAAI;AACP,WAAO;AAOT,SAAO,CAAC,MAAM,aAAY,EAAG,SAAS,IAAI,OAAO;AACnD;;;;;;;;;;;;AC9GA,IAAM,+BAAwB;EAC5B,QAAQ;EACR,MAAM;EACN,UAAU;EACV,QAAQ;EACR,QAAQ;EACR,UAAU;EACV,SAAS;EACT,UAAU;EACV,OAAO;EACP,YAAY;AACd;AAUO,SAAS,0CAAkB,QAA6B,CAAC,GAAC;AAC/D,MAAI,EAAA,OACG,YACM,IACT;AAEJ,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,EAAA,iBAAiB,KAAI,GAAA,2CAAe;IACtC,YAAY,CAAC;IACb,qBAAqB,CAAC,QAAQ,WAAW,GAAA;EAC3C,CAAA;AAGA,MAAI,kBAAiB,GAAA,eAAAC,SAAQ,MAAA;AAC3B,QAAI;AACF,aAAO;aACE;AACT,aAAO;QAAC,GAAG;QAAQ,GAAG;MAAK;;AAE3B,aAAO;EAGX,GAAG;IAAC;GAAU;AAEd,SAAO;IACL,qBAAqB;MACnB,GAAG;MACH,OAAO;IACT;EACF;AACF;AAMO,SAAS,0CAAe,OAA0B;AAEvD,MAAI,EAAA,UAAW,aAAa,UAAU,OAAK,aAAa,OAAS,GAAG,WAAA,IAAc;AAClF,MAAI,EAAA,oBAAoB,IAAI,0CAAkB,KAAA;AAE9C,UACE,GAAA,eAAAC,SAAA,cAAC,UAAY,GAAA,2CAAW,YAAY,mBAAA,GACjC,QAAA;AAGP;", "names": ["$HgANd$react", "$lmaYr$useRef", "$lmaYr$useCallback", "$fCAlL$useState", "$fCAlL$useRef", "$670gB$react", "$670gB$useContext", "$670gB$useRef", "$670gB$react", "$670gB$useState", "$eKkEp$useState", "$eKkEp$useRef", "$eKkEp$useEffect", "$eKkEp$useCallback", "walker", "shadowRoot", "$g3jFn$createContext", "$g3jFn$useMemo", "$g3jFn$react", "$g3jFn$useContext", "$lPAwt$useRef", "$lPAwt$useCallback", "$lPAwt$useEffect", "$gbmns$useRef", "$gbmns$useCallback", "$gbmns$useMemo", "$Vsl8o$useEffect", "$8rM3G$useRef", "$8rM3G$useEffect", "$3whtM$useState", "$3whtM$useRef", "$3whtM$useEffect", "$3whtM$useCallback", "value", "$6dfIe$useRef", "$6dfIe$useCallback", "e", "window", "$3aeG1$react", "$7mdmh$useContext", "$7mdmh$useState", "$7mdmh$useRef", "$7mdmh$useMemo", "pressProps", "e", "$7mdmh$flushSync", "$7mdmh$useEffect", "$28AnR$useEffect", "$hf0lj$useCallback", "$fcPuG$react", "$fcPuG$useContext", "$fcPuG$useRef", "$fcPuG$useEffect", "$fcPuG$forwardRef", "$hhDyF$react", "$hhDyF$useEffect", "$87RPk$react", "$87RPk$useRef", "$87RPk$useContext", "$87RPk$useEffect", "$87RPk$useMemo", "$3b9Q0$useRef", "$3b9Q0$useCallback", "e", "$AWxnT$useState", "$AWxnT$useRef", "$AWxnT$useEffect", "$AWxnT$useMemo", "triggerHoverEnd", "hoverProps", "$ispOf$useRef", "$ispOf$useEffect", "$7JYt2$useState", "$7JYt2$useMemo", "$7JYt2$react"]}
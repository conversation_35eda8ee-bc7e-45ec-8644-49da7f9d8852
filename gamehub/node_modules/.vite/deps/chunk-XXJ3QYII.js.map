{"version": 3, "sources": ["../../@heroui/react-utils/dist/chunk-3XT5V4LF.mjs", "../../@heroui/react-utils/dist/chunk-BDGLNRCW.mjs", "../../@heroui/react-utils/dist/chunk-LGMZDQT5.mjs", "../../@heroui/react-utils/dist/chunk-6UBKM7F3.mjs", "../../@heroui/react-rsc-utils/dist/chunk-WR7VNGRW.mjs", "../../@heroui/react-rsc-utils/dist/chunk-RFWDHYLZ.mjs", "../../@heroui/react-rsc-utils/dist/chunk-RJKRL3AU.mjs", "../../@heroui/react-rsc-utils/dist/chunk-6HA6QXMR.mjs", "../../@heroui/system/dist/chunk-Q3W45BN5.mjs", "../../@react-aria/i18n/dist/packages/@react-aria/i18n/src/utils.ts", "../../@react-aria/i18n/dist/packages/@react-aria/i18n/src/useDefaultLocale.ts", "../../@react-aria/i18n/dist/packages/@react-aria/i18n/src/context.tsx", "../../tslib/tslib.es6.mjs", "../../@formatjs/fast-memoize/lib/index.js", "../../@formatjs/icu-messageformat-parser/lib/error.js", "../../@formatjs/icu-messageformat-parser/lib/types.js", "../../@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../../@formatjs/icu-skeleton-parser/lib/date-time.js", "../../@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../../@formatjs/icu-skeleton-parser/lib/number.js", "../../@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../../@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../../@formatjs/icu-messageformat-parser/lib/parser.js", "../../@formatjs/icu-messageformat-parser/lib/index.js", "../../intl-messageformat/lib/src/error.js", "../../intl-messageformat/lib/src/formatters.js", "../../intl-messageformat/lib/src/core.js", "../../@internationalized/string/dist/packages/@internationalized/string/src/LocalizedStringDictionary.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/string.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/HebrewCalendar.ts", "../../@internationalized/number/dist/packages/@internationalized/number/src/NumberFormatter.ts", "../../@internationalized/number/dist/packages/@internationalized/number/src/NumberParser.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/calculatePosition.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/useCloseOnScroll.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/useOverlayPosition.ts", "../../@react-aria/focus/dist/packages/@react-aria/focus/src/isElementVisible.ts", "../../@react-aria/focus/dist/packages/@react-aria/focus/src/FocusScope.tsx", "../../@react-aria/focus/dist/packages/@react-aria/focus/src/useFocusRing.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/useOverlay.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/usePreventScroll.ts", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/PortalProvider.tsx", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/useModal.tsx", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/ar-AE.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/bg-BG.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/cs-CZ.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/da-DK.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/de-DE.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/el-GR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/en-US.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/es-ES.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/et-EE.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/fi-FI.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/fr-FR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/he-IL.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/hr-HR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/hu-HU.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/it-IT.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/ja-JP.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/ko-KR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/lt-LT.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/lv-LV.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/nb-NO.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/nl-NL.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/pl-PL.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/pt-BR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/pt-PT.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/ro-RO.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/ru-RU.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/sk-SK.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/sl-SI.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/sr-SP.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/sv-SE.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/tr-TR.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/uk-UA.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/zh-CN.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/intl/zh-TW.json", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/*.js", "../../@react-aria/overlays/dist/packages/@react-aria/overlays/src/Overlay.tsx", "../../@heroui/system/dist/chunk-OKNU54ZL.mjs", "../../@heroui/system/dist/chunk-2BFF5KFD.mjs"], "sourcesContent": ["\"use client\";\n\n// src/context.ts\nimport * as React from \"react\";\nfunction createContext2(options = {}) {\n  const {\n    strict = true,\n    errorMessage = \"useContext: `context` is undefined. Seems you forgot to wrap component within the Provider\",\n    name\n  } = options;\n  const Context = React.createContext(void 0);\n  Context.displayName = name;\n  function useContext2() {\n    var _a;\n    const context = React.useContext(Context);\n    if (!context && strict) {\n      const error = new Error(errorMessage);\n      error.name = \"ContextError\";\n      (_a = Error.captureStackTrace) == null ? void 0 : _a.call(Error, error, useContext2);\n      throw error;\n    }\n    return context;\n  }\n  return [Context.Provider, useContext2, Context];\n}\n\nexport {\n  createContext2 as createContext\n};\n", "\"use client\";\n\n// src/dom.ts\nimport { useImperativeHandle, useLayoutEffect, useRef } from \"react\";\nfunction canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n}\nvar isBrowser = canUseDOM();\nfunction getUserAgentBrowser(navigator) {\n  const { userAgent: ua, vendor } = navigator;\n  const android = /(android)/i.test(ua);\n  switch (true) {\n    case /CriOS/.test(ua):\n      return \"Chrome for iOS\";\n    case /Edg\\//.test(ua):\n      return \"Edge\";\n    case (android && /Silk\\//.test(ua)):\n      return \"Silk\";\n    case (/Chrome/.test(ua) && /Google Inc/.test(vendor)):\n      return \"Chrome\";\n    case /Firefox\\/\\d+\\.\\d+$/.test(ua):\n      return \"Firefox\";\n    case android:\n      return \"AOSP\";\n    case /MSIE|Trident/.test(ua):\n      return \"IE\";\n    case (/Safari/.test(navigator.userAgent) && /Apple Computer/.test(ua)):\n      return \"Safari\";\n    case /AppleWebKit/.test(ua):\n      return \"WebKit\";\n    default:\n      return null;\n  }\n}\nfunction getUserAgentOS(navigator) {\n  const { userAgent: ua, platform } = navigator;\n  switch (true) {\n    case /Android/.test(ua):\n      return \"Android\";\n    case /iPhone|iPad|iPod/.test(platform):\n      return \"iOS\";\n    case /Win/.test(platform):\n      return \"Windows\";\n    case /Mac/.test(platform):\n      return \"Mac\";\n    case /CrOS/.test(ua):\n      return \"Chrome OS\";\n    case /Firefox/.test(ua):\n      return \"Firefox OS\";\n    default:\n      return null;\n  }\n}\nfunction detectDeviceType(navigator) {\n  const { userAgent: ua } = navigator;\n  if (/(tablet)|(iPad)|(Nexus 9)/i.test(ua)) return \"tablet\";\n  if (/(mobi)/i.test(ua)) return \"phone\";\n  return \"desktop\";\n}\nfunction detectOS(os) {\n  if (!isBrowser) return false;\n  return getUserAgentOS(window.navigator) === os;\n}\nfunction detectBrowser(browser) {\n  if (!isBrowser) return false;\n  return getUserAgentBrowser(window.navigator) === browser;\n}\nfunction detectTouch() {\n  if (!isBrowser) return false;\n  return window.ontouchstart === null && window.ontouchmove === null && window.ontouchend === null;\n}\nfunction createDOMRef(ref) {\n  return {\n    UNSAFE_getDOMNode() {\n      return ref.current;\n    }\n  };\n}\nfunction createFocusableRef(domRef, focusableRef = domRef) {\n  return {\n    ...createDOMRef(domRef),\n    focus() {\n      if (focusableRef.current) {\n        focusableRef.current.focus();\n      }\n    }\n  };\n}\nfunction useDOMRef(ref) {\n  const domRef = useRef(null);\n  useImperativeHandle(ref, () => domRef.current);\n  return domRef;\n}\nfunction useFocusableRef(ref, focusableRef) {\n  const domRef = useRef(null);\n  useImperativeHandle(ref, () => createFocusableRef(domRef, focusableRef));\n  return domRef;\n}\nfunction useSyncRef(context, ref) {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref && ref.current) {\n      context.ref.current = ref.current;\n      return () => {\n        var _a;\n        if ((_a = context.ref) == null ? void 0 : _a.current) {\n          context.ref.current = null;\n        }\n      };\n    }\n  }, [context, ref]);\n}\nfunction areRectsIntersecting(rect1, rect2) {\n  return rect1 && rect2 && rect1.x < rect2.x + rect2.width && rect1.x + rect1.width > rect2.x && rect1.y < rect2.y + rect2.height && rect1.y + rect1.height > rect2.y;\n}\n\nexport {\n  canUseDOM,\n  isBrowser,\n  getUserAgentBrowser,\n  getUserAgentOS,\n  detectDeviceType,\n  detectOS,\n  detectBrowser,\n  detectTouch,\n  createDOMRef,\n  createFocusableRef,\n  useDOMRef,\n  useFocusableRef,\n  useSyncRef,\n  areRectsIntersecting\n};\n", "\"use client\";\n\n// src/refs.ts\nimport { isFunction } from \"@heroui/shared-utils\";\nfunction assignRef(ref, value) {\n  if (ref == null) return;\n  if (isFunction(ref)) {\n    ref(value);\n    return;\n  }\n  try {\n    ref.current = value;\n  } catch {\n    throw new Error(`Cannot assign value '${value}' to ref '${ref}'`);\n  }\n}\nfunction mergeRefs(...refs) {\n  return (node) => {\n    refs.forEach((ref) => assignRef(ref, node));\n  };\n}\n\nexport {\n  assignRef,\n  mergeRefs\n};\n", "\"use client\";\n\n// src/use-is-hydrated.ts\nimport * as React from \"react\";\nfunction useIsHydrated() {\n  const subscribe = () => () => {\n  };\n  return React.useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nexport {\n  useIsHydrated\n};\n", "// src/children.ts\nimport { Children, isValidElement } from \"react\";\nfunction getValidChildren(children) {\n  return Children.toArray(children).filter(\n    (child) => isValidElement(child)\n  );\n}\nvar pickChildren = (children, targetChild) => {\n  var _a;\n  let target = [];\n  const withoutTargetChildren = (_a = Children.map(children, (item) => {\n    if (!isValidElement(item)) return item;\n    if (item.type === targetChild) {\n      target.push(item);\n      return null;\n    }\n    return item;\n  })) == null ? void 0 : _a.filter(Boolean);\n  const targetChildren = target.length >= 0 ? target : void 0;\n  return [withoutTargetChildren, targetChildren];\n};\n\nexport {\n  getValidChildren,\n  pickChildren\n};\n", "// src/dom-props.ts\nvar DOMPropNames = /* @__PURE__ */ new Set([\n  \"id\",\n  \"type\",\n  \"style\",\n  \"title\",\n  \"role\",\n  \"tabIndex\",\n  \"htmlFor\",\n  \"width\",\n  \"height\",\n  \"abbr\",\n  \"accept\",\n  \"acceptCharset\",\n  \"accessKey\",\n  \"action\",\n  \"allowFullScreen\",\n  \"allowTransparency\",\n  \"alt\",\n  \"async\",\n  \"autoComplete\",\n  \"autoFocus\",\n  \"autoPlay\",\n  \"cellPadding\",\n  \"cellSpacing\",\n  \"challenge\",\n  \"charset\",\n  \"checked\",\n  \"cite\",\n  \"class\",\n  \"className\",\n  \"cols\",\n  \"colSpan\",\n  \"command\",\n  \"content\",\n  \"contentEditable\",\n  \"contextMenu\",\n  \"controls\",\n  \"coords\",\n  \"crossOrigin\",\n  \"data\",\n  \"dateTime\",\n  \"default\",\n  \"defer\",\n  \"dir\",\n  \"disabled\",\n  \"download\",\n  \"draggable\",\n  \"dropzone\",\n  \"encType\",\n  \"enterKeyHint\",\n  \"for\",\n  \"form\",\n  \"formAction\",\n  \"formEncType\",\n  \"formMethod\",\n  \"formNoValidate\",\n  \"formTarget\",\n  \"frameBorder\",\n  \"headers\",\n  \"hidden\",\n  \"high\",\n  \"href\",\n  \"hrefLang\",\n  \"httpEquiv\",\n  \"icon\",\n  \"inputMode\",\n  \"isMap\",\n  \"itemId\",\n  \"itemProp\",\n  \"itemRef\",\n  \"itemScope\",\n  \"itemType\",\n  \"kind\",\n  \"label\",\n  \"lang\",\n  \"list\",\n  \"loop\",\n  \"manifest\",\n  \"max\",\n  \"maxLength\",\n  \"media\",\n  \"mediaGroup\",\n  \"method\",\n  \"min\",\n  \"minLength\",\n  \"multiple\",\n  \"muted\",\n  \"name\",\n  \"noValidate\",\n  \"open\",\n  \"optimum\",\n  \"pattern\",\n  \"ping\",\n  \"placeholder\",\n  \"poster\",\n  \"preload\",\n  \"radioGroup\",\n  \"referrerPolicy\",\n  \"readOnly\",\n  \"rel\",\n  \"required\",\n  \"rows\",\n  \"rowSpan\",\n  \"sandbox\",\n  \"scope\",\n  \"scoped\",\n  \"scrolling\",\n  \"seamless\",\n  \"selected\",\n  \"shape\",\n  \"size\",\n  \"sizes\",\n  \"slot\",\n  \"sortable\",\n  \"span\",\n  \"spellCheck\",\n  \"src\",\n  \"srcDoc\",\n  \"srcSet\",\n  \"start\",\n  \"step\",\n  \"target\",\n  \"translate\",\n  \"typeMustMatch\",\n  \"useMap\",\n  \"value\",\n  \"wmode\",\n  \"wrap\"\n]);\nvar DOMEventNames = /* @__PURE__ */ new Set([\n  \"onCopy\",\n  \"onCut\",\n  \"onPaste\",\n  \"onLoad\",\n  \"onError\",\n  \"onWheel\",\n  \"onScroll\",\n  \"onCompositionEnd\",\n  \"onCompositionStart\",\n  \"onCompositionUpdate\",\n  \"onKeyDown\",\n  \"onKeyPress\",\n  \"onKeyUp\",\n  \"onFocus\",\n  \"onBlur\",\n  \"onChange\",\n  \"onInput\",\n  \"onSubmit\",\n  \"onClick\",\n  \"onContextMenu\",\n  \"onDoubleClick\",\n  \"onDrag\",\n  \"onDragEnd\",\n  \"onDragEnter\",\n  \"onDragExit\",\n  \"onDragLeave\",\n  \"onDragOver\",\n  \"onDragStart\",\n  \"onDrop\",\n  \"onMouseDown\",\n  \"onMouseEnter\",\n  \"onMouseLeave\",\n  \"onMouseMove\",\n  \"onMouseOut\",\n  \"onMouseOver\",\n  \"onMouseUp\",\n  \"onPointerDown\",\n  \"onPointerEnter\",\n  \"onPointerLeave\",\n  \"onPointerUp\",\n  \"onSelect\",\n  \"onTouchCancel\",\n  \"onTouchEnd\",\n  \"onTouchMove\",\n  \"onTouchStart\",\n  \"onAnimationStart\",\n  \"onAnimationEnd\",\n  \"onAnimationIteration\",\n  \"onTransitionEnd\"\n]);\n\nexport {\n  DOMPropNames,\n  DOMEventNames\n};\n", "import {\n  DOMEventNames,\n  DOMPropNames\n} from \"./chunk-RFWDHYLZ.mjs\";\n\n// src/filter-dom-props.ts\nvar propRe = /^(data-.*)$/;\nvar ariaRe = /^(aria-.*)$/;\nvar funcRe = /^(on[A-Z].*)$/;\nfunction filterDOMProps(props, opts = {}) {\n  let {\n    labelable = true,\n    enabled = true,\n    propNames,\n    omitPropNames,\n    omitEventNames,\n    omitDataProps,\n    omitEventProps\n  } = opts;\n  let filteredProps = {};\n  if (!enabled) {\n    return props;\n  }\n  for (const prop in props) {\n    if (omitPropNames == null ? void 0 : omitPropNames.has(prop)) {\n      continue;\n    }\n    if ((omitEventNames == null ? void 0 : omitEventNames.has(prop)) && funcRe.test(prop)) {\n      continue;\n    }\n    if (funcRe.test(prop) && !DOMEventNames.has(prop)) {\n      continue;\n    }\n    if (omitDataProps && propRe.test(prop)) {\n      continue;\n    }\n    if (omitEventProps && funcRe.test(prop)) {\n      continue;\n    }\n    if (Object.prototype.hasOwnProperty.call(props, prop) && (DOMPropNames.has(prop) || labelable && ariaRe.test(prop) || (propNames == null ? void 0 : propNames.has(prop)) || propRe.test(prop)) || funcRe.test(prop)) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n  return filteredProps;\n}\n\nexport {\n  filterDOMProps\n};\n", "// src/functions.ts\nimport * as React from \"react\";\nfunction renderFn({ Component, props, renderCustom }) {\n  if (renderCustom && typeof renderCustom === \"function\") {\n    return renderCustom(props);\n  } else {\n    return React.createElement(Component, props);\n  }\n}\n\nexport {\n  renderFn\n};\n", "\"use client\";\n\n// src/provider-context.ts\nimport { createContext } from \"@heroui/react-utils\";\nvar [ProviderContext, useProviderContext] = createContext({\n  name: \"ProviderContext\",\n  strict: false\n});\n\nexport {\n  ProviderContext,\n  useProviderContext\n};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// https://en.wikipedia.org/wiki/Right-to-left\nconst RTL_SCRIPTS = new Set(['Arab', 'Syrc', 'Samr', 'Mand', 'Thaa', 'Mend', 'Nkoo', 'Adlm', 'Rohg', 'Hebr']);\nconst RTL_LANGS = new Set(['ae', 'ar', 'arc', 'bcc', 'bqi', 'ckb', 'dv', 'fa', 'glk', 'he', 'ku', 'mzn', 'nqo', 'pnb', 'ps', 'sd', 'ug', 'ur', 'yi']);\n\n/**\n * Determines if a locale is read right to left using [Intl.Locale]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale}.\n */\nexport function isRTL(localeString: string): boolean {\n  // If the Intl.Locale API is available, use it to get the locale's text direction.\n  if (Intl.Locale) {\n    let locale = new Intl.Locale(localeString).maximize();\n\n    // Use the text info object to get the direction if possible.\n    // @ts-ignore - this was implemented as a property by some browsers before it was standardized as a function.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getTextInfo\n    let textInfo = typeof locale.getTextInfo === 'function' ? locale.getTextInfo() : locale.textInfo;\n    if (textInfo) {\n      return textInfo.direction === 'rtl';\n    }\n\n    // Fallback: guess using the script.\n    // This is more accurate than guessing by language, since languages can be written in multiple scripts.\n    if (locale.script) {\n      return RTL_SCRIPTS.has(locale.script);\n    }\n  }\n\n  // If not, just guess by the language (first part of the locale)\n  let lang = localeString.split('-')[0];\n  return RTL_LANGS.has(lang);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Direction} from '@react-types/shared';\nimport {isRTL} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport interface Locale {\n  /** The [BCP47](https://www.ietf.org/rfc/bcp/bcp47.txt) language code for the locale. */\n  locale: string,\n  /** The writing direction for the locale. */\n  direction: Direction\n}\n\n// Locale passed from server by PackageLocalizationProvider.\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\n\n/**\n * Gets the locale setting of the browser.\n */\nexport function getDefaultLocale(): Locale {\n  let locale = typeof window !== 'undefined' && window[localeSymbol]\n    // @ts-ignore\n    || (typeof navigator !== 'undefined' && (navigator.language || navigator.userLanguage))\n    || 'en-US';\n\n  try {\n    Intl.DateTimeFormat.supportedLocalesOf([locale]);\n  } catch {\n    locale = 'en-US';\n  }\n  return {\n    locale,\n    direction: isRTL(locale) ? 'rtl' : 'ltr'\n  };\n}\n\nlet currentLocale = getDefaultLocale();\nlet listeners = new Set<(locale: Locale) => void>();\n\nfunction updateLocale() {\n  currentLocale = getDefaultLocale();\n  for (let listener of listeners) {\n    listener(currentLocale);\n  }\n}\n\n/**\n * Returns the current browser/system language, and updates when it changes.\n */\nexport function useDefaultLocale(): Locale {\n  let isSSR = useIsSSR();\n  let [defaultLocale, setDefaultLocale] = useState(currentLocale);\n\n  useEffect(() => {\n    if (listeners.size === 0) {\n      window.addEventListener('languagechange', updateLocale);\n    }\n\n    listeners.add(setDefaultLocale);\n\n    return () => {\n      listeners.delete(setDefaultLocale);\n      if (listeners.size === 0) {\n        window.removeEventListener('languagechange', updateLocale);\n      }\n    };\n  }, []);\n\n  // We cannot determine the browser's language on the server, so default to\n  // en-US. This will be updated after hydration on the client to the correct value.\n  if (isSSR) {\n    return {\n      locale: 'en-US',\n      direction: 'ltr'\n    };\n  }\n\n  return defaultLocale;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isRTL} from './utils';\nimport {Locale, useDefaultLocale} from './useDefaultLocale';\nimport React, {JSX, ReactNode, useContext} from 'react';\n\nexport interface I18nProviderProps {\n  /** Contents that should have the locale applied. */\n  children: ReactNode,\n  /** The locale to apply to the children. */\n  locale?: string\n}\n\nconst I18nContext = React.createContext<Locale | null>(null);\n\n/**\n * Provides the locale for the application to all child components.\n */\nexport function I18nProvider(props: I18nProviderProps): JSX.Element {\n  let {locale, children} = props;\n  let defaultLocale = useDefaultLocale();\n\n  let value: Locale = React.useMemo(() => {\n    if (!locale) {\n      return defaultLocale;\n    }\n\n    return {\n      locale,\n      direction: isRTL(locale) ? 'rtl' : 'ltr'\n    };\n  }, [defaultLocale, locale]);\n\n  return (\n    <I18nContext.Provider value={value}>\n      {children}\n    </I18nContext.Provider>\n  );\n}\n\n/**\n * Returns the current locale and layout direction.\n */\nexport function useLocale(): Locale {\n  let defaultLocale = useDefaultLocale();\n  let context = useContext(I18nContext);\n  return context || defaultLocale;\n}\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n", "export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n", "export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n", "var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\nexport { isStructurallySame } from './manipulator';\n", "import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n", "import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTagElement, isTimeElement, } from '@formatjs/icu-messageformat-parser';\nimport { ErrorCode, FormatError, InvalidValueError, InvalidValueTypeError, MissingValueError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n", "/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedString} from './LocalizedStringFormatter';\n\nexport type LocalizedStrings<K extends string, T extends LocalizedString> = {\n  [lang: string]: Record<K, T>\n};\n\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\nconst stringsSymbol = Symbol.for('react-aria.i18n.strings');\nlet cachedGlobalStrings: {[packageName: string]: LocalizedStringDictionary<any, any>} | null | undefined = undefined;\n\n/**\n * Stores a mapping of localized strings. Can be used to find the\n * closest available string for a given locale.\n */\nexport class LocalizedStringDictionary<K extends string = string, T extends LocalizedString = string> {\n  private strings: LocalizedStrings<K, T>;\n  private defaultLocale: string;\n\n  constructor(messages: LocalizedStrings<K, T>, defaultLocale: string = 'en-US') {\n    // Clone messages so we don't modify the original object.\n    // Filter out entries with falsy values which may have been caused by applying optimize-locales-plugin.\n    this.strings = Object.fromEntries(\n      Object.entries(messages).filter(([, v]) => v)\n    );\n    this.defaultLocale = defaultLocale;\n  }\n\n  /** Returns a localized string for the given key and locale. */\n  getStringForLocale(key: K, locale: string): T {\n    let strings = this.getStringsForLocale(locale);\n    let string = strings[key];\n    if (!string) {\n      throw new Error(`Could not find intl message ${key} in ${locale} locale`);\n    }\n\n    return string;\n  }\n\n  /** Returns all localized strings for the given locale. */\n  getStringsForLocale(locale: string): Record<K, T> {\n    let strings = this.strings[locale];\n    if (!strings) {\n      strings = getStringsForLocale(locale, this.strings, this.defaultLocale);\n      this.strings[locale] = strings;\n    }\n\n    return strings;\n  }\n\n  static getGlobalDictionaryForPackage<K extends string = string, T extends LocalizedString = string>(packageName: string): LocalizedStringDictionary<K, T> | null {\n    if (typeof window === 'undefined') {\n      return null;\n    }\n\n    let locale = window[localeSymbol];\n    if (cachedGlobalStrings === undefined) {\n      let globalStrings = window[stringsSymbol];\n      if (!globalStrings) {\n        return null;\n      }\n\n      cachedGlobalStrings = {};\n      for (let pkg in globalStrings) {\n        cachedGlobalStrings[pkg] = new LocalizedStringDictionary({[locale]: globalStrings[pkg]}, locale);\n      }\n    }\n\n    let dictionary = cachedGlobalStrings?.[packageName];\n    if (!dictionary) {\n      throw new Error(`Strings for package \"${packageName}\" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);\n    }\n\n    return dictionary;\n  }\n}\n\nfunction getStringsForLocale<K extends string, T extends LocalizedString>(locale: string, strings: LocalizedStrings<K, T>, defaultLocale = 'en-US') {\n  // If there is an exact match, use it.\n  if (strings[locale]) {\n    return strings[locale];\n  }\n\n  // Attempt to find the closest match by language.\n  // For example, if the locale is fr-CA (French Canadian), but there is only\n  // an fr-FR (France) set of strings, use that.\n  // This could be replaced with Intl.LocaleMatcher once it is supported.\n  // https://github.com/tc39/proposal-intl-localematcher\n  let language = getLanguage(locale);\n  if (strings[language]) {\n    return strings[language];\n  }\n\n  for (let key in strings) {\n    if (key.startsWith(language + '-')) {\n      return strings[key];\n    }\n  }\n\n  // Nothing close, use english.\n  return strings[defaultLocale];\n}\n\nfunction getLanguage(locale: string) {\n  // @ts-ignore\n  if (Intl.Locale) {\n    // @ts-ignore\n    return new Intl.Locale(locale).language;\n  }\n\n  return locale.split('-')[0];\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyDateTime, DateTimeDuration, Disambiguation} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, possibleAbsolutes, toAbsolute, toCalendar, toCalendarDateTime, toTimeZone} from './conversion';\nimport {getLocalTimeZone} from './queries';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mu<PERSON>} from './utils';\n\nconst TIME_RE = /^(\\d{2})(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst DATE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})$/;\nconst DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst ZONED_DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:([+-]\\d{2})(?::?(\\d{2}))?)?\\[(.*?)\\]$/;\nconst ABSOLUTE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:(?:([+-]\\d{2})(?::?(\\d{2}))?)|Z)$/;\nconst DATE_TIME_DURATION_RE =\n    /^((?<negative>-)|\\+)?P((?<years>\\d*)Y)?((?<months>\\d*)M)?((?<weeks>\\d*)W)?((?<days>\\d*)D)?((?<time>T)((?<hours>\\d*[.,]?\\d{1,9})H)?((?<minutes>\\d*[.,]?\\d{1,9})M)?((?<seconds>\\d*[.,]?\\d{1,9})S)?)?$/;\nconst requiredDurationTimeGroups = ['hours', 'minutes', 'seconds'];\nconst requiredDurationGroups = ['years', 'months', 'weeks', 'days', ...requiredDurationTimeGroups];\n\n/** Parses an ISO 8601 time string. */\nexport function parseTime(value: string): Time {\n  let m = value.match(TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 time string: ' + value);\n  }\n\n  return new Time(\n    parseNumber(m[1], 0, 23),\n    m[2] ? parseNumber(m[2], 0, 59) : 0,\n    m[3] ? parseNumber(m[3], 0, 59) : 0,\n    m[4] ? parseNumber(m[4], 0, Infinity) * 1000 : 0\n  );\n}\n\n/** Parses an ISO 8601 date string, with no time components. */\nexport function parseDate(value: string): CalendarDate {\n  let m = value.match(DATE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date string: ' + value);\n  }\n\n  let date: Mutable<CalendarDate> = new CalendarDate(\n    parseNumber(m[1], 0, 9999),\n    parseNumber(m[2], 1, 12),\n    1\n  );\n\n  date.day = parseNumber(m[3], 1, date.calendar.getDaysInMonth(date));\n  return date as CalendarDate;\n}\n\n/** Parses an ISO 8601 date and time string, with no time zone. */\nexport function parseDateTime(value: string): CalendarDateTime {\n  let m = value.match(DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<CalendarDateTime> = new CalendarDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDateTime;\n}\n\n/**\n * Parses an ISO 8601 date and time string with a time zone extension and optional UTC offset\n * (e.g. \"2021-11-07T00:45[America/Los_Angeles]\" or \"2021-11-07T00:45-07:00[America/Los_Angeles]\").\n * Ambiguous times due to daylight saving time transitions are resolved according to the `disambiguation`\n * parameter.\n */\nexport function parseZonedDateTime(value: string, disambiguation?: Disambiguation): ZonedDateTime {\n  let m = value.match(ZONED_DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[10],\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  let plainDateTime = toCalendarDateTime(date as ZonedDateTime);\n\n  let ms: number;\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n    ms = epochFromDate(date as ZonedDateTime) - date.offset;\n\n    // Validate offset against parsed date.\n    let absolutes = possibleAbsolutes(plainDateTime, date.timeZone);\n    if (!absolutes.includes(ms)) {\n      throw new Error(`Offset ${offsetToString(date.offset)} is invalid for ${dateTimeToString(date)} in ${date.timeZone}`);\n    }\n  } else {\n    // Convert to absolute and back to fix invalid times due to DST.\n    ms = toAbsolute(toCalendarDateTime(plainDateTime), date.timeZone, disambiguation);\n  }\n\n  return fromAbsolute(ms, date.timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the provided time zone.\n */\nexport function parseAbsolute(value: string, timeZone: string): ZonedDateTime {\n  let m = value.match(ABSOLUTE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    timeZone,\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n  }\n\n  return toTimeZone(date as ZonedDateTime, timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the user's local time zone.\n */\nexport function parseAbsoluteToLocal(value: string): ZonedDateTime {\n  return parseAbsolute(value, getLocalTimeZone());\n}\n\nfunction parseNumber(value: string, min: number, max: number) {\n  let val = Number(value);\n  if (val < min || val > max) {\n    throw new RangeError(`Value out of range: ${min} <= ${val} <= ${max}`);\n  }\n\n  return val;\n}\n\nexport function timeToString(time: Time): string {\n  return `${String(time.hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:${String(time.second).padStart(2, '0')}${time.millisecond ? String(time.millisecond / 1000).slice(1) : ''}`;\n}\n\nexport function dateToString(date: CalendarDate): string {\n  let gregorianDate = toCalendar(date, new GregorianCalendar());\n  let year: string;\n  if (gregorianDate.era === 'BC') {\n    year = gregorianDate.year === 1\n      ? '0000'\n      : '-' + String(Math.abs(1 - gregorianDate.year)).padStart(6, '00');\n  } else {\n    year = String(gregorianDate.year).padStart(4, '0');\n  }\n  return `${year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`;\n}\n\nexport function dateTimeToString(date: AnyDateTime): string {\n  // @ts-ignore\n  return `${dateToString(date)}T${timeToString(date)}`;\n}\n\nfunction offsetToString(offset: number) {\n  let sign = Math.sign(offset) < 0 ? '-' : '+';\n  offset = Math.abs(offset);\n  let offsetHours = Math.floor(offset / (60 * 60 * 1000));\n  let offsetMinutes = (offset % (60 * 60 * 1000)) / (60 * 1000);\n  return `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;\n}\n\nexport function zonedDateTimeToString(date: ZonedDateTime): string {\n  return `${dateTimeToString(date)}${offsetToString(date.offset)}[${date.timeZone}]`;\n}\n\n/**\n * Parses an ISO 8601 duration string (e.g. \"P3Y6M6W4DT12H30M5S\").\n * @param value An ISO 8601 duration string.\n * @returns A DateTimeDuration object.\n */\nexport function parseDuration(value: string): Required<DateTimeDuration> {\n  const match = value.match(DATE_TIME_DURATION_RE);\n\n  if (!match) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const parseDurationGroup = (\n    group: string | undefined,\n    isNegative: boolean\n  ): number => {\n    if (!group) {\n      return 0;\n    }\n    try {\n      const sign = isNegative ? -1 : 1;\n      return sign * Number(group.replace(',', '.'));\n    } catch {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  };\n\n  const isNegative = !!match.groups?.negative;\n\n  const hasRequiredGroups = requiredDurationGroups.some(group => match.groups?.[group]);\n\n  if (!hasRequiredGroups) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const durationStringIncludesTime = match.groups?.time;\n\n  if (durationStringIncludesTime) {\n    const hasRequiredDurationTimeGroups = requiredDurationTimeGroups.some(group => match.groups?.[group]);\n    if (!hasRequiredDurationTimeGroups) {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  }\n\n  const duration: Mutable<DateTimeDuration> = {\n    years: parseDurationGroup(match.groups?.years, isNegative),\n    months: parseDurationGroup(match.groups?.months, isNegative),\n    weeks: parseDurationGroup(match.groups?.weeks, isNegative),\n    days: parseDurationGroup(match.groups?.days, isNegative),\n    hours: parseDurationGroup(match.groups?.hours, isNegative),\n    minutes: parseDurationGroup(match.groups?.minutes, isNegative),\n    seconds: parseDurationGroup(match.groups?.seconds, isNegative)\n  };\n\n  if (duration.hours !== undefined && ((duration.hours % 1) !== 0) && (duration.minutes || duration.seconds)) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  if (duration.minutes !== undefined && ((duration.minutes % 1) !== 0) && duration.seconds) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  return duration as Required<DateTimeDuration>;\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst HEBREW_EPOCH = 347997;\n\n// Hebrew date calculations are performed in terms of days, hours, and\n// \"parts\" (or halakim), which are 1/1080 of an hour, or 3 1/3 seconds.\nconst HOUR_PARTS = 1080;\nconst DAY_PARTS  = 24 * HOUR_PARTS;\n\n// An approximate value for the length of a lunar month.\n// It is used to calculate the approximate year and month of a given\n// absolute date.\nconst MONTH_DAYS = 29;\nconst MONTH_FRACT = 12 * HOUR_PARTS + 793;\nconst MONTH_PARTS = MONTH_DAYS * DAY_PARTS + MONTH_FRACT;\n\nfunction isLeapYear(year: number) {\n  return mod(year * 7 + 1, 19) < 7;\n}\n\n// Test for delay of start of new year and to avoid\n// Sunday, Wednesday, and Friday as start of the new year.\nfunction hebrewDelay1(year: number) {\n  let months = Math.floor((235 * year - 234) / 19);\n  let parts = 12084 + 13753 * months;\n  let day = months * 29 + Math.floor(parts / 25920);\n\n  if (mod(3 * (day + 1), 7) < 3) {\n    day += 1;\n  }\n\n  return day;\n}\n\n// Check for delay in start of new year due to length of adjacent years\nfunction hebrewDelay2(year: number) {\n  let last = hebrewDelay1(year - 1);\n  let present = hebrewDelay1(year);\n  let next = hebrewDelay1(year + 1);\n\n  if (next - present === 356) {\n    return 2;\n  }\n\n  if (present - last === 382) {\n    return 1;\n  }\n\n  return 0;\n}\n\nfunction startOfYear(year: number) {\n  return hebrewDelay1(year) + hebrewDelay2(year);\n}\n\nfunction getDaysInYear(year: number) {\n  return startOfYear(year + 1) - startOfYear(year);\n}\n\nfunction getYearType(year: number) {\n  let yearLength = getDaysInYear(year);\n\n  if (yearLength > 380) {\n    yearLength -= 30; // Subtract length of leap month.\n  }\n\n  switch (yearLength) {\n    case 353:\n      return 0; // deficient\n    case 354:\n      return 1; // normal\n    case 355:\n      return 2; // complete\n  }\n}\n\nfunction getDaysInMonth(year: number, month: number): number {\n  // Normalize month numbers from 1 - 13, even on non-leap years\n  if (month >= 6 && !isLeapYear(year)) {\n    month++;\n  }\n\n  // First of all, dispose of fixed-length 29 day months\n  if (month === 4 || month === 7 || month === 9 || month === 11 || month === 13) {\n    return 29;\n  }\n\n  let yearType = getYearType(year);\n\n  // If it's Heshvan, days depend on length of year\n  if (month === 2) {\n    return yearType === 2 ? 30 : 29;\n  }\n\n  // Similarly, Kislev varies with the length of year\n  if (month === 3) {\n    return yearType === 0 ? 29 : 30;\n  }\n\n  // Adar I only exists in leap years\n  if (month === 6) {\n    return isLeapYear(year) ? 30 : 0;\n  }\n\n  return 30;\n}\n\n/**\n * The Hebrew calendar is used in Israel and around the world by the Jewish faith.\n * Years include either 12 or 13 months depending on whether it is a leap year.\n * In leap years, an extra month is inserted at month 6.\n */\nexport class HebrewCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'hebrew';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let d = jd - HEBREW_EPOCH;\n    let m = (d * DAY_PARTS) / MONTH_PARTS;           // Months (approx)\n    let year = Math.floor((19 * m + 234) / 235) + 1; // Years (approx)\n    let ys = startOfYear(year);                      // 1st day of year\n    let dayOfYear = Math.floor(d - ys);\n\n    // Because of the postponement rules, it's possible to guess wrong.  Fix it.\n    while (dayOfYear < 1) {\n      year--;\n      ys = startOfYear(year);\n      dayOfYear = Math.floor(d - ys);\n    }\n\n    // Now figure out which month we're in, and the date within that month\n    let month = 1;\n    let monthStart = 0;\n    while (monthStart < dayOfYear) {\n      monthStart += getDaysInMonth(year, month);\n      month++;\n    }\n\n    month--;\n    monthStart -= getDaysInMonth(year, month);\n\n    let day = dayOfYear - monthStart;\n    return new CalendarDate(this, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = startOfYear(date.year);\n    for (let month = 1; month < date.month; month++) {\n      jd += getDaysInMonth(date.year, month);\n    }\n\n    return jd + date.day + HEBREW_EPOCH;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 13 : 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return getDaysInYear(date.year);\n  }\n\n  getYearsInEra(): number {\n    // 6239 gregorian\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['AM'];\n  }\n\n  balanceYearMonth(date: Mutable<AnyCalendarDate>, previousDate: AnyCalendarDate): void {\n    // Keep date in the same month when switching between leap years and non leap years\n    if (previousDate.year !== date.year) {\n      if (isLeapYear(previousDate.year) && !isLeapYear(date.year) && previousDate.month > 6) {\n        date.month--;\n      } else if (!isLeapYear(previousDate.year) && isLeapYear(date.year) && previousDate.month > 6) {\n        date.month++;\n      }\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.NumberFormat>();\n\nlet supportsSignDisplay = false;\ntry {\n  supportsSignDisplay = (new Intl.NumberFormat('de-DE', {signDisplay: 'exceptZero'})).resolvedOptions().signDisplay === 'exceptZero';\n  // eslint-disable-next-line no-empty\n} catch {}\n\nlet supportsUnit = false;\ntry {\n  supportsUnit = (new Intl.NumberFormat('de-DE', {style: 'unit', unit: 'degree'})).resolvedOptions().style === 'unit';\n  // eslint-disable-next-line no-empty\n} catch {}\n\n// Polyfill for units since Safari doesn't support them yet. See https://bugs.webkit.org/show_bug.cgi?id=215438.\n// Currently only polyfilling the unit degree in narrow format for ColorSlider in our supported locales.\n// Values were determined by switching to each locale manually in Chrome.\nconst UNITS = {\n  degree: {\n    narrow: {\n      default: '°',\n      'ja-JP': ' 度',\n      'zh-TW': '度',\n      'sl-SI': ' °'\n      // Arabic?? But Safari already doesn't use Arabic digits so might be ok...\n      // https://bugs.webkit.org/show_bug.cgi?id=218139\n    }\n  }\n};\n\nexport interface NumberFormatOptions extends Intl.NumberFormatOptions {\n  /** Overrides default numbering system for the current locale. */\n  numberingSystem?: string\n}\n\ninterface NumberRangeFormatPart extends Intl.NumberFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/**\n * A wrapper around Intl.NumberFormat providing additional options, polyfills, and caching for performance.\n */\nexport class NumberFormatter implements Intl.NumberFormat {\n  private numberFormatter: Intl.NumberFormat;\n  private options: NumberFormatOptions;\n\n  constructor(locale: string, options: NumberFormatOptions = {}) {\n    this.numberFormatter = getCachedNumberFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a number value as a string, according to the locale and options provided to the constructor. */\n  format(value: number): string {\n    let res = '';\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      res = numberFormatSignDisplayPolyfill(this.numberFormatter, this.options.signDisplay, value);\n    } else {\n      res = this.numberFormatter.format(value);\n    }\n\n    if (this.options.style === 'unit' && !supportsUnit) {\n      let {unit, unitDisplay = 'short', locale} = this.resolvedOptions();\n      if (!unit) {\n        return res;\n      }\n      let values = UNITS[unit]?.[unitDisplay];\n      res += values[locale] || values.default;\n    }\n\n    return res;\n  }\n\n  /** Formats a number to an array of parts such as separators, digits, punctuation, and more. */\n  formatToParts(value: number): Intl.NumberFormatPart[] {\n    // TODO: implement signDisplay for formatToParts\n    return this.numberFormatter.formatToParts(value);\n  }\n\n  /** Formats a number range as a string. */\n  formatRange(start: number, end: number): string {\n    if (typeof this.numberFormatter.formatRange === 'function') {\n      return this.numberFormatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.format(start)} – ${this.format(end)}`;\n  }\n\n  /** Formats a number range as an array of parts. */\n  formatRangeToParts(start: number, end: number): NumberRangeFormatPart[] {\n    if (typeof this.numberFormatter.formatRangeToParts === 'function') {\n      return this.numberFormatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.numberFormatter.formatToParts(start);\n    let endParts = this.numberFormatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as NumberRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as NumberRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedNumberFormatOptions {\n    let options = this.numberFormatter.resolvedOptions();\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      options = {...options, signDisplay: this.options.signDisplay};\n    }\n\n    if (!supportsUnit && this.options.style === 'unit') {\n      options = {...options, style: 'unit', unit: this.options.unit, unitDisplay: this.options.unitDisplay};\n    }\n\n    return options;\n  }\n}\n\nfunction getCachedNumberFormatter(locale: string, options: NumberFormatOptions = {}): Intl.NumberFormat {\n  let {numberingSystem} = options;\n  if (numberingSystem && locale.includes('-nu-')) {\n    if (!locale.includes('-u-')) {\n      locale += '-u-';\n    }\n    locale += `-nu-${numberingSystem}`;\n  }\n\n  if (options.style === 'unit' && !supportsUnit) {\n    let {unit, unitDisplay = 'short'} = options;\n    if (!unit) {\n      throw new Error('unit option must be provided with style: \"unit\"');\n    }\n    if (!UNITS[unit]?.[unitDisplay]) {\n      throw new Error(`Unsupported unit ${unit} with unitDisplay = ${unitDisplay}`);\n    }\n    options = {...options, style: 'decimal'};\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.NumberFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\n/** @private - exported for tests */\nexport function numberFormatSignDisplayPolyfill(numberFormat: Intl.NumberFormat, signDisplay: string, num: number): string {\n  if (signDisplay === 'auto') {\n    return numberFormat.format(num);\n  } else if (signDisplay === 'never') {\n    return numberFormat.format(Math.abs(num));\n  } else {\n    let needsPositiveSign = false;\n    if (signDisplay === 'always') {\n      needsPositiveSign = num > 0 || Object.is(num, 0);\n    } else if (signDisplay === 'exceptZero') {\n      if (Object.is(num, -0) || Object.is(num, 0)) {\n        num = Math.abs(num);\n      } else {\n        needsPositiveSign = num > 0;\n      }\n    }\n\n    if (needsPositiveSign) {\n      let negative = numberFormat.format(-num);\n      let noSign = numberFormat.format(num);\n      // ignore RTL/LTR marker character\n      let minus = negative.replace(noSign, '').replace(/\\u200e|\\u061C/, '');\n      if ([...minus].length !== 1) {\n        console.warn('@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case');\n      }\n      let positive = negative.replace(noSign, '!!!').replace(minus, '+').replace('!!!', noSign);\n      return positive;\n    } else {\n      return numberFormat.format(num);\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {NumberFormatter} from './NumberFormatter';\n\ninterface Symbols {\n  minusSign?: string,\n  plusSign?: string,\n  decimal?: string,\n  group?: string,\n  literals: RegExp,\n  numeral: RegExp,\n  index: (v: string) => string\n}\n\nconst CURRENCY_SIGN_REGEX = new RegExp('^.*\\\\(.*\\\\).*$');\nconst NUMBERING_SYSTEMS = ['latn', 'arab', 'hanidec', 'deva', 'beng'];\n\n/**\n * A NumberParser can be used to perform locale-aware parsing of numbers from Unicode strings,\n * as well as validation of partial user input. It automatically detects the numbering system\n * used in the input, and supports parsing decimals, percentages, currency values, and units\n * according to the locale.\n */\nexport class NumberParser {\n  private locale: string;\n  private options: Intl.NumberFormatOptions;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    this.options = options;\n  }\n\n  /**\n   * Parses the given string to a number. Returns NaN if a valid number could not be parsed.\n   */\n  parse(value: string): number {\n    return getNumberParserImpl(this.locale, this.options, value).parse(value);\n  }\n\n  /**\n   * Returns whether the given string could potentially be a valid number. This should be used to\n   * validate user input as the user types. If a `minValue` or `maxValue` is provided, the validity\n   * of the minus/plus sign characters can be checked.\n   */\n  isValidPartialNumber(value: string, minValue?: number, maxValue?: number): boolean {\n    return getNumberParserImpl(this.locale, this.options, value).isValidPartialNumber(value, minValue, maxValue);\n  }\n\n  /**\n   * Returns a numbering system for which the given string is valid in the current locale.\n   * If no numbering system could be detected, the default numbering system for the current\n   * locale is returned.\n   */\n  getNumberingSystem(value: string): string {\n    return getNumberParserImpl(this.locale, this.options, value).options.numberingSystem;\n  }\n}\n\nconst numberParserCache = new Map<string, NumberParserImpl>();\nfunction getNumberParserImpl(locale: string, options: Intl.NumberFormatOptions, value: string) {\n  // First try the default numbering system for the provided locale\n  let defaultParser = getCachedNumberParser(locale, options);\n\n  // If that doesn't match, and the locale doesn't include a hard coded numbering system,\n  // try each of the other supported numbering systems until we find one that matches.\n  if (!locale.includes('-nu-') && !defaultParser.isValidPartialNumber(value)) {\n    for (let numberingSystem of NUMBERING_SYSTEMS) {\n      if (numberingSystem !== defaultParser.options.numberingSystem) {\n        let parser = getCachedNumberParser(locale + (locale.includes('-u-') ? '-nu-' : '-u-nu-') + numberingSystem, options);\n        if (parser.isValidPartialNumber(value)) {\n          return parser;\n        }\n      }\n    }\n  }\n\n  return defaultParser;\n}\n\nfunction getCachedNumberParser(locale: string, options: Intl.NumberFormatOptions) {\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  let parser = numberParserCache.get(cacheKey);\n  if (!parser) {\n    parser = new NumberParserImpl(locale, options);\n    numberParserCache.set(cacheKey, parser);\n  }\n\n  return parser;\n}\n\n// The actual number parser implementation. Instances of this class are cached\n// based on the locale, options, and detected numbering system.\nclass NumberParserImpl {\n  formatter: Intl.NumberFormat;\n  options: Intl.ResolvedNumberFormatOptions;\n  symbols: Symbols;\n  locale: string;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    // see https://tc39.es/ecma402/#sec-setnfdigitoptions, when using roundingIncrement, the maximumFractionDigits and minimumFractionDigits must be equal\n    // by default, they are 0 and 3 respectively, so we set them to 0 if neither are set\n    if (options.roundingIncrement !== 1 && options.roundingIncrement != null) {\n      if (options.maximumFractionDigits == null && options.minimumFractionDigits == null) {\n        options.maximumFractionDigits = 0;\n        options.minimumFractionDigits = 0;\n      } else if (options.maximumFractionDigits == null) {\n        options.maximumFractionDigits = options.minimumFractionDigits;\n      } else if (options.minimumFractionDigits == null) {\n        options.minimumFractionDigits = options.maximumFractionDigits;\n      }\n      // if both are specified, let the normal Range Error be thrown\n    }\n    this.formatter = new Intl.NumberFormat(locale, options);\n    this.options = this.formatter.resolvedOptions();\n    this.symbols = getSymbols(locale, this.formatter, this.options, options);\n    if (this.options.style === 'percent' && ((this.options.minimumFractionDigits ?? 0) > 18 || (this.options.maximumFractionDigits ?? 0) > 18)) {\n      console.warn('NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.');\n    }\n  }\n\n  parse(value: string) {\n    // to parse the number, we need to remove anything that isn't actually part of the number, for example we want '-10.40' not '-10.40 USD'\n    let fullySanitizedValue = this.sanitize(value);\n\n    if (this.symbols.group) {\n      // Remove group characters, and replace decimal points and numerals with ASCII values.\n      fullySanitizedValue = replaceAll(fullySanitizedValue, this.symbols.group, '');\n    }\n    if (this.symbols.decimal) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.decimal!, '.');\n    }\n    if (this.symbols.minusSign) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.minusSign!, '-');\n    }\n    fullySanitizedValue = fullySanitizedValue.replace(this.symbols.numeral, this.symbols.index);\n\n    if (this.options.style === 'percent') {\n      // javascript is bad at dividing by 100 and maintaining the same significant figures, so perform it on the string before parsing\n      let isNegative = fullySanitizedValue.indexOf('-');\n      fullySanitizedValue = fullySanitizedValue.replace('-', '');\n      fullySanitizedValue = fullySanitizedValue.replace('+', '');\n      let index = fullySanitizedValue.indexOf('.');\n      if (index === -1) {\n        index = fullySanitizedValue.length;\n      }\n      fullySanitizedValue = fullySanitizedValue.replace('.', '');\n      if (index - 2 === 0) {\n        fullySanitizedValue = `0.${fullySanitizedValue}`;\n      } else if (index - 2 === -1) {\n        fullySanitizedValue = `0.0${fullySanitizedValue}`;\n      } else if (index - 2 === -2) {\n        fullySanitizedValue = '0.00';\n      } else {\n        fullySanitizedValue = `${fullySanitizedValue.slice(0, index - 2)}.${fullySanitizedValue.slice(index - 2)}`;\n      }\n      if (isNegative > -1) {\n        fullySanitizedValue = `-${fullySanitizedValue}`;\n      }\n    }\n\n    let newValue = fullySanitizedValue ? +fullySanitizedValue : NaN;\n    if (isNaN(newValue)) {\n      return NaN;\n    }\n\n    if (this.options.style === 'percent') {\n      // extra step for rounding percents to what our formatter would output\n      let options = {\n        ...this.options,\n        style: 'decimal' as const,\n        minimumFractionDigits: Math.min((this.options.minimumFractionDigits ?? 0) + 2, 20),\n        maximumFractionDigits: Math.min((this.options.maximumFractionDigits ?? 0) + 2, 20)\n      };\n      return (new NumberParser(this.locale, options)).parse(new NumberFormatter(this.locale, options).format(newValue));\n    }\n\n    // accounting will always be stripped to a positive number, so if it's accounting and has a () around everything, then we need to make it negative again\n    if (this.options.currencySign === 'accounting' && CURRENCY_SIGN_REGEX.test(value)) {\n      newValue = -1 * newValue;\n    }\n\n    return newValue;\n  }\n\n  sanitize(value: string) {\n    // Remove literals and whitespace, which are allowed anywhere in the string\n    value = value.replace(this.symbols.literals, '');\n\n    // Replace the ASCII minus sign with the minus sign used in the current locale\n    // so that both are allowed in case the user's keyboard doesn't have the locale's minus sign.\n    if (this.symbols.minusSign) {\n      value = value.replace('-', this.symbols.minusSign);\n    }\n\n    // In arab numeral system, their decimal character is 1643, but most keyboards don't type that\n    // instead they use the , (44) character or apparently the (1548) character.\n    if (this.options.numberingSystem === 'arab') {\n      if (this.symbols.decimal) {\n        value = value.replace(',', this.symbols.decimal);\n        value = value.replace(String.fromCharCode(1548), this.symbols.decimal);\n      }\n      if (this.symbols.group) {\n        value = replaceAll(value, '.', this.symbols.group);\n      }\n    }\n\n    // fr-FR group character is narrow non-breaking space, char code 8239 (U+202F), but that's not a key on the french keyboard,\n    // so allow space and non-breaking space as a group char as well\n    if (this.options.locale === 'fr-FR' && this.symbols.group) {\n      value = replaceAll(value, ' ', this.symbols.group);\n      value = replaceAll(value, /\\u00A0/g, this.symbols.group);\n    }\n\n    return value;\n  }\n\n  isValidPartialNumber(value: string, minValue: number = -Infinity, maxValue: number = Infinity): boolean {\n    value = this.sanitize(value);\n\n    // Remove minus or plus sign, which must be at the start of the string.\n    if (this.symbols.minusSign && value.startsWith(this.symbols.minusSign) && minValue < 0) {\n      value = value.slice(this.symbols.minusSign.length);\n    } else if (this.symbols.plusSign && value.startsWith(this.symbols.plusSign) && maxValue > 0) {\n      value = value.slice(this.symbols.plusSign.length);\n    }\n\n    // Numbers cannot start with a group separator\n    if (this.symbols.group && value.startsWith(this.symbols.group)) {\n      return false;\n    }\n\n    // Numbers that can't have any decimal values fail if a decimal character is typed\n    if (this.symbols.decimal && value.indexOf(this.symbols.decimal) > -1 && this.options.maximumFractionDigits === 0) {\n      return false;\n    }\n\n    // Remove numerals, groups, and decimals\n    if (this.symbols.group) {\n      value = replaceAll(value, this.symbols.group, '');\n    }\n    value = value.replace(this.symbols.numeral, '');\n    if (this.symbols.decimal) {\n      value = value.replace(this.symbols.decimal, '');\n    }\n\n    // The number is valid if there are no remaining characters\n    return value.length === 0;\n  }\n}\n\nconst nonLiteralParts = new Set(['decimal', 'fraction', 'integer', 'minusSign', 'plusSign', 'group']);\n\n// This list is derived from https://www.unicode.org/cldr/charts/43/supplemental/language_plural_rules.html#comparison and includes\n// all unique numbers which we need to check in order to determine all the plural forms for a given locale.\n// See: https://github.com/adobe/react-spectrum/pull/5134/files#r1337037855 for used script\nconst pluralNumbers = [\n  0, 4, 2, 1, 11, 20, 3, 7, 100, 21, 0.1, 1.1\n];\n\nfunction getSymbols(locale: string, formatter: Intl.NumberFormat, intlOptions: Intl.ResolvedNumberFormatOptions, originalOptions: Intl.NumberFormatOptions): Symbols {\n  // formatter needs access to all decimal places in order to generate the correct literal strings for the plural set\n  let symbolFormatter = new Intl.NumberFormat(locale, {...intlOptions,\n    // Resets so we get the full range of symbols\n    minimumSignificantDigits: 1,\n    maximumSignificantDigits: 21,\n    roundingIncrement: 1,\n    roundingPriority: 'auto',\n    roundingMode: 'halfExpand'\n  });\n  // Note: some locale's don't add a group symbol until there is a ten thousands place\n  let allParts = symbolFormatter.formatToParts(-10000.111);\n  let posAllParts = symbolFormatter.formatToParts(10000.111);\n  let pluralParts = pluralNumbers.map(n => symbolFormatter.formatToParts(n));\n\n  let minusSign = allParts.find(p => p.type === 'minusSign')?.value ?? '-';\n  let plusSign = posAllParts.find(p => p.type === 'plusSign')?.value;\n\n  // Safari does not support the signDisplay option, but our number parser polyfills it.\n  // If no plus sign was returned, but the original options contained signDisplay, default to the '+' character.\n  if (!plusSign && (originalOptions?.signDisplay === 'exceptZero' || originalOptions?.signDisplay === 'always')) {\n    plusSign = '+';\n  }\n\n  // If maximumSignificantDigits is 1 (the minimum) then we won't get decimal characters out of the above formatters\n  // Percent also defaults to 0 fractionDigits, so we need to make a new one that isn't percent to get an accurate decimal\n  let decimalParts = new Intl.NumberFormat(locale, {...intlOptions, minimumFractionDigits: 2, maximumFractionDigits: 2}).formatToParts(0.001);\n\n  let decimal = decimalParts.find(p => p.type === 'decimal')?.value;\n  let group = allParts.find(p => p.type === 'group')?.value;\n\n  // this set is also for a regex, it's all literals that might be in the string we want to eventually parse that\n  // don't contribute to the numerical value\n  let allPartsLiterals = allParts.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value));\n  let pluralPartsLiterals = pluralParts.flatMap(p => p.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value)));\n  let sortedLiterals = [...new Set([...allPartsLiterals, ...pluralPartsLiterals])].sort((a, b) => b.length - a.length);\n\n  let literals = sortedLiterals.length === 0 ?\n      new RegExp('[\\\\p{White_Space}]', 'gu') :\n      new RegExp(`${sortedLiterals.join('|')}|[\\\\p{White_Space}]`, 'gu');\n\n  // These are for replacing non-latn characters with the latn equivalent\n  let numerals = [...new Intl.NumberFormat(intlOptions.locale, {useGrouping: false}).format(9876543210)].reverse();\n  let indexes = new Map(numerals.map((d, i) => [d, i]));\n  let numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n  let index = d => String(indexes.get(d));\n\n  return {minusSign, plusSign, decimal, group, literals, numeral, index};\n}\n\nfunction replaceAll(str: string, find: string | RegExp, replace: string) {\n  if (str.replaceAll) {\n    return str.replaceAll(find, replace);\n  }\n\n  return str.split(find).join(replace);\n}\n\nfunction escapeRegex(string: string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Axis, Placement, PlacementAxis, SizeAxis} from '@react-types/overlays';\nimport {clamp, isWebKit} from '@react-aria/utils';\n\ninterface Position {\n  top?: number,\n  left?: number,\n  bottom?: number,\n  right?: number\n}\n\ninterface Dimensions {\n  width: number,\n  height: number,\n  totalWidth: number,\n  totalHeight: number,\n  top: number,\n  left: number,\n  scroll: Position\n}\n\ninterface ParsedPlacement {\n  placement: PlacementAxis,\n  crossPlacement: PlacementAxis,\n  axis: Axis,\n  crossAxis: Axis,\n  size: SizeAxis,\n  crossSize: SizeAxis\n}\n\ninterface Offset {\n  top: number,\n  left: number,\n  width: number,\n  height: number\n}\n\ninterface PositionOpts {\n  arrowSize: number,\n  placement: Placement,\n  targetNode: Element,\n  overlayNode: Element,\n  scrollNode: Element,\n  padding: number,\n  shouldFlip: boolean,\n  boundaryElement: Element,\n  offset: number,\n  crossOffset: number,\n  maxHeight?: number,\n  arrowBoundaryOffset?: number\n}\n\ntype HeightGrowthDirection = 'top' | 'bottom';\n\nexport interface PositionResult {\n  position: Position,\n  arrowOffsetLeft?: number,\n  arrowOffsetTop?: number,\n  maxHeight: number,\n  placement: PlacementAxis\n}\n\nconst AXIS = {\n  top: 'top',\n  bottom: 'top',\n  left: 'left',\n  right: 'left'\n};\n\nconst FLIPPED_DIRECTION = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left'\n};\n\nconst CROSS_AXIS = {\n  top: 'left',\n  left: 'top'\n};\n\nconst AXIS_SIZE = {\n  top: 'height',\n  left: 'width'\n};\n\nconst TOTAL_SIZE = {\n  width: 'totalWidth',\n  height: 'totalHeight'\n};\n\nconst PARSED_PLACEMENT_CACHE = {};\n\nlet visualViewport = typeof document !== 'undefined' ? window.visualViewport : null;\n\nfunction getContainerDimensions(containerNode: Element): Dimensions {\n  let width = 0, height = 0, totalWidth = 0, totalHeight = 0, top = 0, left = 0;\n  let scroll: Position = {};\n  let isPinchZoomedIn = (visualViewport?.scale ?? 1) > 1;\n\n  if (containerNode.tagName === 'BODY') {\n    let documentElement = document.documentElement;\n    totalWidth = documentElement.clientWidth;\n    totalHeight = documentElement.clientHeight;\n    width = visualViewport?.width ?? totalWidth;\n    height = visualViewport?.height ?? totalHeight;\n    scroll.top = documentElement.scrollTop || containerNode.scrollTop;\n    scroll.left = documentElement.scrollLeft || containerNode.scrollLeft;\n\n    // The goal of the below is to get a top/left value that represents the top/left of the visual viewport with\n    // respect to the layout viewport origin. This combined with the scrollTop/scrollLeft will allow us to calculate\n    // coordinates/values with respect to the visual viewport or with respect to the layout viewport.\n    if (visualViewport) {\n      top = visualViewport.offsetTop;\n      left = visualViewport.offsetLeft;\n    }\n  } else {\n    ({width, height, top, left} = getOffset(containerNode));\n    scroll.top = containerNode.scrollTop;\n    scroll.left = containerNode.scrollLeft;\n    totalWidth = width;\n    totalHeight = height;\n  }\n\n  if (isWebKit() && (containerNode.tagName === 'BODY' || containerNode.tagName === 'HTML') && isPinchZoomedIn) {\n    // Safari will report a non-zero scrollTop/Left for the non-scrolling body/HTML element when pinch zoomed in unlike other browsers.\n    // Set to zero for parity calculations so we get consistent positioning of overlays across all browsers.\n    // Also switch to visualViewport.pageTop/pageLeft so that we still accomodate for scroll positioning for body/HTML elements that are actually scrollable\n    // before pinch zoom happens\n    scroll.top = 0;\n    scroll.left = 0;\n    top = visualViewport?.pageTop ?? 0;\n    left = visualViewport?.pageLeft ?? 0;\n  }\n\n  return {width, height, totalWidth, totalHeight, scroll, top, left};\n}\n\nfunction getScroll(node: Element): Offset {\n  return {\n    top: node.scrollTop,\n    left: node.scrollLeft,\n    width: node.scrollWidth,\n    height: node.scrollHeight\n  };\n}\n\n// Determines the amount of space required when moving the overlay to ensure it remains in the boundary\nfunction getDelta(\n  axis: Axis,\n  offset: number,\n  size: number,\n  // The dimensions of the boundary element that the popover is\n  // positioned within (most of the time this is the <body>).\n  boundaryDimensions: Dimensions,\n  // The dimensions of the containing block element that the popover is\n  // positioned relative to (e.g. parent with position: relative).\n  // Usually this is the same as the boundary element, but if the popover\n  // is portaled somewhere other than the body and has an ancestor with\n  // position: relative/absolute, it will be different.\n  containerDimensions: Dimensions,\n  padding: number,\n  containerOffsetWithBoundary: Offset\n) {\n  let containerScroll = containerDimensions.scroll[axis] ?? 0;\n  // The height/width of the boundary. Matches the axis along which we are adjusting the overlay position\n  let boundarySize = boundaryDimensions[AXIS_SIZE[axis]];\n  // Calculate the edges of the boundary (accomodating for the boundary padding) and the edges of the overlay.\n  // Note that these values are with respect to the visual viewport (aka 0,0 is the top left of the viewport)\n  let boundaryStartEdge = boundaryDimensions.scroll[AXIS[axis]] + padding;\n  let boundaryEndEdge = boundarySize + boundaryDimensions.scroll[AXIS[axis]] - padding;\n  let startEdgeOffset = offset - containerScroll + containerOffsetWithBoundary[axis] - boundaryDimensions[AXIS[axis]];\n  let endEdgeOffset = offset - containerScroll + size + containerOffsetWithBoundary[axis] - boundaryDimensions[AXIS[axis]];\n\n  // If any of the overlay edges falls outside of the boundary, shift the overlay the required amount to align one of the overlay's\n  // edges with the closest boundary edge.\n  if (startEdgeOffset < boundaryStartEdge) {\n    return boundaryStartEdge - startEdgeOffset;\n  } else if (endEdgeOffset > boundaryEndEdge) {\n    return Math.max(boundaryEndEdge - endEdgeOffset, boundaryStartEdge - startEdgeOffset);\n  } else {\n    return 0;\n  }\n}\n\nfunction getMargins(node: Element): Position {\n  let style = window.getComputedStyle(node);\n  return {\n    top: parseInt(style.marginTop, 10) || 0,\n    bottom: parseInt(style.marginBottom, 10) || 0,\n    left: parseInt(style.marginLeft, 10) || 0,\n    right: parseInt(style.marginRight, 10) || 0\n  };\n}\n\nfunction parsePlacement(input: Placement): ParsedPlacement {\n  if (PARSED_PLACEMENT_CACHE[input]) {\n    return PARSED_PLACEMENT_CACHE[input];\n  }\n\n  let [placement, crossPlacement] = input.split(' ');\n  let axis: Axis = AXIS[placement] || 'right';\n  let crossAxis: Axis = CROSS_AXIS[axis];\n\n  if (!AXIS[crossPlacement]) {\n    crossPlacement = 'center';\n  }\n\n  let size = AXIS_SIZE[axis];\n  let crossSize = AXIS_SIZE[crossAxis];\n  PARSED_PLACEMENT_CACHE[input] = {placement, crossPlacement, axis, crossAxis, size, crossSize};\n  return PARSED_PLACEMENT_CACHE[input];\n}\n\nfunction computePosition(\n  childOffset: Offset,\n  boundaryDimensions: Dimensions,\n  overlaySize: Offset,\n  placementInfo: ParsedPlacement,\n  offset: number,\n  crossOffset: number,\n  containerOffsetWithBoundary: Offset,\n  isContainerPositioned: boolean,\n  arrowSize: number,\n  arrowBoundaryOffset: number\n) {\n  let {placement, crossPlacement, axis, crossAxis, size, crossSize} = placementInfo;\n  let position: Position = {};\n\n  // button position\n  position[crossAxis] = childOffset[crossAxis] ?? 0;\n  if (crossPlacement === 'center') {\n    //  + (button size / 2) - (overlay size / 2)\n    // at this point the overlay center should match the button center\n    position[crossAxis]! += ((childOffset[crossSize] ?? 0) - (overlaySize[crossSize] ?? 0)) / 2;\n  } else if (crossPlacement !== crossAxis) {\n    //  + (button size) - (overlay size)\n    // at this point the overlay bottom should match the button bottom\n    position[crossAxis]! += (childOffset[crossSize] ?? 0) - (overlaySize[crossSize] ?? 0);\n  }/* else {\n    the overlay top should match the button top\n  } */\n\n  position[crossAxis]! += crossOffset;\n\n  // overlay top overlapping arrow with button bottom\n  const minPosition = childOffset[crossAxis] - overlaySize[crossSize] + arrowSize + arrowBoundaryOffset;\n  // overlay bottom overlapping arrow with button top\n  const maxPosition = childOffset[crossAxis] + childOffset[crossSize] - arrowSize - arrowBoundaryOffset;\n  position[crossAxis] = clamp(position[crossAxis]!, minPosition, maxPosition);\n\n  // Floor these so the position isn't placed on a partial pixel, only whole pixels. Shouldn't matter if it was floored or ceiled, so chose one.\n  if (placement === axis) {\n    // If the container is positioned (non-static), then we use the container's actual\n    // height, as `bottom` will be relative to this height.  But if the container is static,\n    // then it can only be the `document.body`, and `bottom` will be relative to _its_\n    // container, which should be as large as boundaryDimensions.\n    const containerHeight = (isContainerPositioned ? containerOffsetWithBoundary[size] : boundaryDimensions[TOTAL_SIZE[size]]);\n    position[FLIPPED_DIRECTION[axis]] = Math.floor(containerHeight - childOffset[axis] + offset);\n  } else {\n    position[axis] = Math.floor(childOffset[axis] + childOffset[size] + offset);\n  }\n  return position;\n}\n\nfunction getMaxHeight(\n  position: Position,\n  boundaryDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  isContainerPositioned: boolean,\n  margins: Position,\n  padding: number,\n  overlayHeight: number,\n  heightGrowthDirection: HeightGrowthDirection\n) {\n  const containerHeight = (isContainerPositioned ? containerOffsetWithBoundary.height : boundaryDimensions[TOTAL_SIZE.height]);\n  // For cases where position is set via \"bottom\" instead of \"top\", we need to calculate the true overlay top with respect to the boundary. Reverse calculate this with the same method\n  // used in computePosition.\n  let overlayTop = position.top != null ? containerOffsetWithBoundary.top + position.top : containerOffsetWithBoundary.top + (containerHeight - (position.bottom ?? 0) - overlayHeight);\n  let maxHeight = heightGrowthDirection !== 'top' ?\n    // We want the distance between the top of the overlay to the bottom of the boundary\n    Math.max(0,\n      (boundaryDimensions.height + boundaryDimensions.top + (boundaryDimensions.scroll.top ?? 0)) // this is the bottom of the boundary\n      - overlayTop // this is the top of the overlay\n      - ((margins.top ?? 0) + (margins.bottom ?? 0) + padding) // save additional space for margin and padding\n    )\n    // We want the distance between the bottom of the overlay to the top of the boundary\n    : Math.max(0,\n      (overlayTop + overlayHeight) // this is the bottom of the overlay\n      - (boundaryDimensions.top + (boundaryDimensions.scroll.top ?? 0)) // this is the top of the boundary\n      - ((margins.top ?? 0) + (margins.bottom ?? 0) + padding) // save additional space for margin and padding\n    );\n  return Math.min(boundaryDimensions.height - (padding * 2), maxHeight);\n}\n\nfunction getAvailableSpace(\n  boundaryDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  childOffset: Offset,\n  margins: Position,\n  padding: number,\n  placementInfo: ParsedPlacement\n) {\n  let {placement, axis, size} = placementInfo;\n  if (placement === axis) {\n    return Math.max(0, childOffset[axis] - boundaryDimensions[axis] - (boundaryDimensions.scroll[axis] ?? 0) + containerOffsetWithBoundary[axis] - (margins[axis] ?? 0) - margins[FLIPPED_DIRECTION[axis]] - padding);\n  }\n\n  return Math.max(0, boundaryDimensions[size] + boundaryDimensions[axis] + boundaryDimensions.scroll[axis] - containerOffsetWithBoundary[axis] - childOffset[axis] - childOffset[size] - (margins[axis] ?? 0) - margins[FLIPPED_DIRECTION[axis]] - padding);\n}\n\nexport function calculatePositionInternal(\n  placementInput: Placement,\n  childOffset: Offset,\n  overlaySize: Offset,\n  scrollSize: Offset,\n  margins: Position,\n  padding: number,\n  flip: boolean,\n  boundaryDimensions: Dimensions,\n  containerDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  offset: number,\n  crossOffset: number,\n  isContainerPositioned: boolean,\n  userSetMaxHeight: number | undefined,\n  arrowSize: number,\n  arrowBoundaryOffset: number\n): PositionResult {\n  let placementInfo = parsePlacement(placementInput);\n  let {size, crossAxis, crossSize, placement, crossPlacement} = placementInfo;\n  let position = computePosition(childOffset, boundaryDimensions, overlaySize, placementInfo, offset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n  let normalizedOffset = offset;\n  let space = getAvailableSpace(\n    boundaryDimensions,\n    containerOffsetWithBoundary,\n    childOffset,\n    margins,\n    padding + offset,\n    placementInfo\n  );\n\n  // Check if the scroll size of the overlay is greater than the available space to determine if we need to flip\n  if (flip && scrollSize[size] > space) {\n    let flippedPlacementInfo = parsePlacement(`${FLIPPED_DIRECTION[placement]} ${crossPlacement}` as Placement);\n    let flippedPosition = computePosition(childOffset, boundaryDimensions, overlaySize, flippedPlacementInfo, offset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n    let flippedSpace = getAvailableSpace(\n      boundaryDimensions,\n      containerOffsetWithBoundary,\n      childOffset,\n      margins,\n      padding + offset,\n      flippedPlacementInfo\n    );\n\n    // If the available space for the flipped position is greater than the original available space, flip.\n    if (flippedSpace > space) {\n      placementInfo = flippedPlacementInfo;\n      position = flippedPosition;\n      normalizedOffset = offset;\n    }\n  }\n\n  // Determine the direction the height of the overlay can grow so that we can choose how to calculate the max height\n  let heightGrowthDirection: HeightGrowthDirection = 'bottom';\n  if (placementInfo.axis === 'top') {\n    if (placementInfo.placement === 'top') {\n      heightGrowthDirection = 'top';\n    } else if (placementInfo.placement === 'bottom') {\n      heightGrowthDirection = 'bottom';\n    }\n  } else if (placementInfo.crossAxis === 'top') {\n    if (placementInfo.crossPlacement === 'top') {\n      heightGrowthDirection = 'bottom';\n    } else if (placementInfo.crossPlacement === 'bottom') {\n      heightGrowthDirection = 'top';\n    }\n  }\n\n  let delta = getDelta(crossAxis, position[crossAxis]!, overlaySize[crossSize], boundaryDimensions, containerDimensions, padding, containerOffsetWithBoundary);\n  position[crossAxis]! += delta;\n\n  let maxHeight = getMaxHeight(\n    position,\n    boundaryDimensions,\n    containerOffsetWithBoundary,\n    isContainerPositioned,\n    margins,\n    padding,\n    overlaySize.height,\n    heightGrowthDirection\n  );\n\n  if (userSetMaxHeight && userSetMaxHeight < maxHeight) {\n    maxHeight = userSetMaxHeight;\n  }\n\n  overlaySize.height = Math.min(overlaySize.height, maxHeight);\n\n  position = computePosition(childOffset, boundaryDimensions, overlaySize, placementInfo, normalizedOffset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n  delta = getDelta(crossAxis, position[crossAxis]!, overlaySize[crossSize], boundaryDimensions, containerDimensions, padding, containerOffsetWithBoundary);\n  position[crossAxis]! += delta;\n\n  let arrowPosition: Position = {};\n\n  // All values are transformed so that 0 is at the top/left of the overlay depending on the orientation\n  // Prefer the arrow being in the center of the trigger/overlay anchor element\n  // childOffset[crossAxis] + .5 * childOffset[crossSize] = absolute position with respect to the trigger's coordinate system that would place the arrow in the center of the trigger\n  // position[crossAxis] - margins[AXIS[crossAxis]] = value use to transform the position to a value with respect to the overlay's coordinate system. A child element's (aka arrow) position absolute's \"0\"\n  // is positioned after the margin of its parent (aka overlay) so we need to subtract it to get the proper coordinate transform\n  let preferredArrowPosition = childOffset[crossAxis] + .5 * childOffset[crossSize] - position[crossAxis]! - margins[AXIS[crossAxis]];\n\n  // Min/Max position limits for the arrow with respect to the overlay\n  const arrowMinPosition = arrowSize / 2 + arrowBoundaryOffset;\n  // overlaySize[crossSize] - margins = true size of the overlay\n  const overlayMargin = AXIS[crossAxis] === 'left' ? (margins.left ?? 0) + (margins.right ?? 0) : (margins.top ?? 0) + (margins.bottom ?? 0);\n  const arrowMaxPosition = overlaySize[crossSize] - overlayMargin - (arrowSize / 2) - arrowBoundaryOffset;\n\n  // Min/Max position limits for the arrow with respect to the trigger/overlay anchor element\n  // Same margin accomodation done here as well as for the preferredArrowPosition\n  const arrowOverlappingChildMinEdge = childOffset[crossAxis] + (arrowSize / 2) - (position[crossAxis] + margins[AXIS[crossAxis]]);\n  const arrowOverlappingChildMaxEdge = childOffset[crossAxis] + childOffset[crossSize] - (arrowSize / 2) - (position[crossAxis] + margins[AXIS[crossAxis]]);\n\n  // Clamp the arrow positioning so that it always is within the bounds of the anchor and the overlay\n  const arrowPositionOverlappingChild = clamp(preferredArrowPosition, arrowOverlappingChildMinEdge, arrowOverlappingChildMaxEdge);\n  arrowPosition[crossAxis] = clamp(arrowPositionOverlappingChild, arrowMinPosition, arrowMaxPosition);\n\n  return {\n    position,\n    maxHeight: maxHeight,\n    arrowOffsetLeft: arrowPosition.left,\n    arrowOffsetTop: arrowPosition.top,\n    placement: placementInfo.placement\n  };\n}\n\n/**\n * Determines where to place the overlay with regards to the target and the position of an optional indicator.\n */\nexport function calculatePosition(opts: PositionOpts): PositionResult {\n  let {\n    placement,\n    targetNode,\n    overlayNode,\n    scrollNode,\n    padding,\n    shouldFlip,\n    boundaryElement,\n    offset,\n    crossOffset,\n    maxHeight,\n    arrowSize = 0,\n    arrowBoundaryOffset = 0\n  } = opts;\n\n  let container = overlayNode instanceof HTMLElement ? getContainingBlock(overlayNode) : document.documentElement;\n  let isViewportContainer = container === document.documentElement;\n  const containerPositionStyle = window.getComputedStyle(container).position;\n  let isContainerPositioned = !!containerPositionStyle && containerPositionStyle !== 'static';\n  let childOffset: Offset = isViewportContainer ? getOffset(targetNode) : getPosition(targetNode, container);\n\n  if (!isViewportContainer) {\n    let {marginTop, marginLeft} = window.getComputedStyle(targetNode);\n    childOffset.top += parseInt(marginTop, 10) || 0;\n    childOffset.left += parseInt(marginLeft, 10) || 0;\n  }\n\n  let overlaySize: Offset = getOffset(overlayNode);\n  let margins = getMargins(overlayNode);\n  overlaySize.width += (margins.left ?? 0) + (margins.right ?? 0);\n  overlaySize.height += (margins.top ?? 0) + (margins.bottom ?? 0);\n\n  let scrollSize = getScroll(scrollNode);\n  let boundaryDimensions = getContainerDimensions(boundaryElement);\n  let containerDimensions = getContainerDimensions(container);\n  // If the container is the HTML element wrapping the body element, the retrieved scrollTop/scrollLeft will be equal to the\n  // body element's scroll. Set the container's scroll values to 0 since the overlay's edge position value in getDelta don't then need to be further offset\n  // by the container scroll since they are essentially the same containing element and thus in the same coordinate system\n  let containerOffsetWithBoundary: Offset = boundaryElement.tagName === 'BODY' ? getOffset(container) : getPosition(container, boundaryElement);\n  if (container.tagName === 'HTML' && boundaryElement.tagName === 'BODY') {\n    containerDimensions.scroll.top = 0;\n    containerDimensions.scroll.left = 0;\n  }\n\n  return calculatePositionInternal(\n    placement,\n    childOffset,\n    overlaySize,\n    scrollSize,\n    margins,\n    padding,\n    shouldFlip,\n    boundaryDimensions,\n    containerDimensions,\n    containerOffsetWithBoundary,\n    offset,\n    crossOffset,\n    isContainerPositioned,\n    maxHeight,\n    arrowSize,\n    arrowBoundaryOffset\n  );\n}\n\nfunction getOffset(node: Element): Offset {\n  let {top, left, width, height} = node.getBoundingClientRect();\n  let {scrollTop, scrollLeft, clientTop, clientLeft} = document.documentElement;\n  return {\n    top: top + scrollTop - clientTop,\n    left: left + scrollLeft - clientLeft,\n    width,\n    height\n  };\n}\n\nfunction getPosition(node: Element, parent: Element): Offset {\n  let style = window.getComputedStyle(node);\n  let offset: Offset;\n  if (style.position === 'fixed') {\n    let {top, left, width, height} = node.getBoundingClientRect();\n    offset = {top, left, width, height};\n  } else {\n    offset = getOffset(node);\n    let parentOffset = getOffset(parent);\n    let parentStyle = window.getComputedStyle(parent);\n    parentOffset.top += (parseInt(parentStyle.borderTopWidth, 10) || 0) - parent.scrollTop;\n    parentOffset.left += (parseInt(parentStyle.borderLeftWidth, 10) || 0) - parent.scrollLeft;\n    offset.top -= parentOffset.top;\n    offset.left -= parentOffset.left;\n  }\n\n  offset.top -= parseInt(style.marginTop, 10) || 0;\n  offset.left -= parseInt(style.marginLeft, 10) || 0;\n  return offset;\n}\n\n// Returns the containing block of an element, which is the element that\n// this element will be positioned relative to.\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block\nfunction getContainingBlock(node: HTMLElement): Element {\n  // The offsetParent of an element in most cases equals the containing block.\n  // https://w3c.github.io/csswg-drafts/cssom-view/#dom-htmlelement-offsetparent\n  let offsetParent = node.offsetParent;\n\n  // The offsetParent algorithm terminates at the document body,\n  // even if the body is not a containing block. Double check that\n  // and use the documentElement if so.\n  if (\n    offsetParent &&\n    offsetParent === document.body &&\n    window.getComputedStyle(offsetParent).position === 'static' &&\n    !isContainingBlock(offsetParent)\n  ) {\n    offsetParent = document.documentElement;\n  }\n\n  // TODO(later): handle table elements?\n\n  // The offsetParent can be null if the element has position: fixed, or a few other cases.\n  // We have to walk up the tree manually in this case because fixed positioned elements\n  // are still positioned relative to their containing block, which is not always the viewport.\n  if (offsetParent == null) {\n    offsetParent = node.parentElement;\n    while (offsetParent && !isContainingBlock(offsetParent)) {\n      offsetParent = offsetParent.parentElement;\n    }\n  }\n\n  // Fall back to the viewport.\n  return offsetParent || document.documentElement;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\nfunction isContainingBlock(node: Element): boolean {\n  let style = window.getComputedStyle(node);\n  return (\n    style.transform !== 'none' ||\n    /transform|perspective/.test(style.willChange) ||\n    style.filter !== 'none' ||\n    style.contain === 'paint' ||\n    ('backdropFilter' in style && style.backdropFilter !== 'none') ||\n    ('WebkitBackdropFilter' in style && style.WebkitBackdropFilter !== 'none')\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\n// This behavior moved from useOverlayTrigger to useOverlayPosition.\n// For backward compatibility, where useOverlayTrigger handled hiding the popover on close,\n// it sets a close function here mapped from the trigger element. This way we can avoid\n// forcing users to pass an onClose function to useOverlayPosition which could be considered\n// a breaking change.\nexport const onCloseMap: WeakMap<Element, () => void> = new WeakMap();\n\ninterface CloseOnScrollOptions {\n  triggerRef: RefObject<Element | null>,\n  isOpen?: boolean,\n  onClose?: (() => void) | null\n}\n\n/** @private */\nexport function useCloseOnScroll(opts: CloseOnScrollOptions): void {\n  let {triggerRef, isOpen, onClose} = opts;\n\n  useEffect(() => {\n    if (!isOpen || onClose === null) {\n      return;\n    }\n\n    let onScroll = (e: Event) => {\n      // Ignore if scrolling an scrollable region outside the trigger's tree.\n      let target = e.target;\n      // window is not a Node and doesn't have contain, but window contains everything\n      if (!triggerRef.current || ((target instanceof Node) && !target.contains(triggerRef.current))) {\n        return;\n      }\n\n      // Ignore scroll events on any input or textarea as the cursor position can cause it to scroll\n      // such as in a combobox. Clicking the dropdown button places focus on the input, and if the\n      // text inside the input extends beyond the 'end', then it will scroll so the cursor is visible at the end.\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return;\n      }\n\n      let onCloseHandler = onClose || onCloseMap.get(triggerRef.current);\n      if (onCloseHandler) {\n        onCloseHandler();\n      }\n    };\n\n    window.addEventListener('scroll', onScroll, true);\n    return () => {\n      window.removeEventListener('scroll', onScroll, true);\n    };\n  }, [isOpen, onClose, triggerRef]);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {calculatePosition, PositionResult} from './calculatePosition';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {Placement, PlacementAxis, PositionProps} from '@react-types/overlays';\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useCloseOnScroll} from './useCloseOnScroll';\nimport {useLayoutEffect, useResizeObserver} from '@react-aria/utils';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface AriaPositionProps extends PositionProps {\n  /**\n   * Cross size of the overlay arrow in pixels.\n   * @default 0\n   */\n  arrowSize?: number,\n  /**\n   * Element that that serves as the positioning boundary.\n   * @default document.body\n   */\n  boundaryElement?: Element,\n  /**\n   * The ref for the element which the overlay positions itself with respect to.\n   */\n  targetRef: RefObject<Element | null>,\n  /**\n   * The ref for the overlay element.\n   */\n  overlayRef: RefObject<Element | null>,\n  /**\n   * A ref for the scrollable region within the overlay.\n   * @default overlayRef\n   */\n  scrollRef?: RefObject<Element | null>,\n  /**\n   * Whether the overlay should update its position automatically.\n   * @default true\n   */\n  shouldUpdatePosition?: boolean,\n  /** Handler that is called when the overlay should close. */\n  onClose?: (() => void) | null,\n  /**\n   * The maxHeight specified for the overlay element.\n   * By default, it will take all space up to the current viewport height.\n   */\n  maxHeight?: number,\n  /**\n   * The minimum distance the arrow's edge should be from the edge of the overlay element.\n   * @default 0\n   */\n  arrowBoundaryOffset?: number\n}\n\nexport interface PositionAria {\n  /** Props for the overlay container element. */\n  overlayProps: DOMAttributes,\n  /** Props for the overlay tip arrow if any. */\n  arrowProps: DOMAttributes,\n  /** Placement of the overlay with respect to the overlay trigger. */\n  placement: PlacementAxis | null,\n  /** Updates the position of the overlay. */\n  updatePosition(): void\n}\n\ninterface ScrollAnchor {\n  type: 'top' | 'bottom',\n  offset: number\n}\n\nlet visualViewport = typeof document !== 'undefined' ? window.visualViewport : null;\n\n/**\n * Handles positioning overlays like popovers and menus relative to a trigger\n * element, and updating the position when the window resizes.\n */\nexport function useOverlayPosition(props: AriaPositionProps): PositionAria {\n  let {direction} = useLocale();\n  let {\n    arrowSize = 0,\n    targetRef,\n    overlayRef,\n    scrollRef = overlayRef,\n    placement = 'bottom' as Placement,\n    containerPadding = 12,\n    shouldFlip = true,\n    boundaryElement = typeof document !== 'undefined' ? document.body : null,\n    offset = 0,\n    crossOffset = 0,\n    shouldUpdatePosition = true,\n    isOpen = true,\n    onClose,\n    maxHeight,\n    arrowBoundaryOffset = 0\n  } = props;\n  let [position, setPosition] = useState<PositionResult | null>(null);\n\n  let deps = [\n    shouldUpdatePosition,\n    placement,\n    overlayRef.current,\n    targetRef.current,\n    scrollRef.current,\n    containerPadding,\n    shouldFlip,\n    boundaryElement,\n    offset,\n    crossOffset,\n    isOpen,\n    direction,\n    maxHeight,\n    arrowBoundaryOffset,\n    arrowSize\n  ];\n\n  // Note, the position freezing breaks if body sizes itself dynamicly with the visual viewport but that might\n  // just be a non-realistic use case\n  // Upon opening a overlay, record the current visual viewport scale so we can freeze the overlay styles\n  let lastScale = useRef(visualViewport?.scale);\n  useEffect(() => {\n    if (isOpen) {\n      lastScale.current = visualViewport?.scale;\n    }\n  }, [isOpen]);\n\n  let updatePosition = useCallback(() => {\n    if (shouldUpdatePosition === false || !isOpen || !overlayRef.current || !targetRef.current || !boundaryElement) {\n      return;\n    }\n\n    if (visualViewport?.scale !== lastScale.current) {\n      return;\n    }\n\n    // Determine a scroll anchor based on the focused element.\n    // This stores the offset of the anchor element from the scroll container\n    // so it can be restored after repositioning. This way if the overlay height\n    // changes, the focused element appears to stay in the same position.\n    let anchor: ScrollAnchor | null = null;\n    if (scrollRef.current && scrollRef.current.contains(document.activeElement)) {\n      let anchorRect = document.activeElement?.getBoundingClientRect();\n      let scrollRect = scrollRef.current.getBoundingClientRect();\n      // Anchor from the top if the offset is in the top half of the scrollable element,\n      // otherwise anchor from the bottom.\n      anchor = {\n        type: 'top',\n        offset: (anchorRect?.top ?? 0) - scrollRect.top\n      };\n      if (anchor.offset > scrollRect.height / 2) {\n        anchor.type = 'bottom';\n        anchor.offset = (anchorRect?.bottom ?? 0) - scrollRect.bottom;\n      }\n    }\n\n    // Always reset the overlay's previous max height if not defined by the user so that we can compensate for\n    // RAC collections populating after a second render and properly set a correct max height + positioning when it populates.\n    let overlay = (overlayRef.current as HTMLElement);\n    if (!maxHeight && overlayRef.current) {\n      overlay.style.top = '0px';\n      overlay.style.bottom = '';\n      overlay.style.maxHeight = (window.visualViewport?.height ?? window.innerHeight) + 'px';\n    }\n\n    let position = calculatePosition({\n      placement: translateRTL(placement, direction),\n      overlayNode: overlayRef.current,\n      targetNode: targetRef.current,\n      scrollNode: scrollRef.current || overlayRef.current,\n      padding: containerPadding,\n      shouldFlip,\n      boundaryElement,\n      offset,\n      crossOffset,\n      maxHeight,\n      arrowSize,\n      arrowBoundaryOffset\n    });\n\n    if (!position.position) {\n      return;\n    }\n\n    // Modify overlay styles directly so positioning happens immediately without the need of a second render\n    // This is so we don't have to delay autoFocus scrolling or delay applying preventScroll for popovers\n    overlay.style.top = '';\n    overlay.style.bottom = '';\n    overlay.style.left = '';\n    overlay.style.right = '';\n\n    Object.keys(position.position).forEach(key => overlay.style[key] = (position.position!)[key] + 'px');\n    overlay.style.maxHeight = position.maxHeight != null ?  position.maxHeight + 'px' : '';\n\n    // Restore scroll position relative to anchor element.\n    if (anchor && document.activeElement && scrollRef.current) {\n      let anchorRect = document.activeElement.getBoundingClientRect();\n      let scrollRect = scrollRef.current.getBoundingClientRect();\n      let newOffset = anchorRect[anchor.type] - scrollRect[anchor.type];\n      scrollRef.current.scrollTop += newOffset - anchor.offset;\n    }\n\n    // Trigger a set state for a second render anyway for arrow positioning\n    setPosition(position);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n\n  // Update position when anything changes\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useLayoutEffect(updatePosition, deps);\n\n  // Update position on window resize\n  useResize(updatePosition);\n\n  // Update position when the overlay changes size (might need to flip).\n  useResizeObserver({\n    ref: overlayRef,\n    onResize: updatePosition\n  });\n\n  // Update position when the target changes size (might need to flip).\n  useResizeObserver({\n    ref: targetRef,\n    onResize: updatePosition\n  });\n\n  // Reposition the overlay and do not close on scroll while the visual viewport is resizing.\n  // This will ensure that overlays adjust their positioning when the iOS virtual keyboard appears.\n  let isResizing = useRef(false);\n  useLayoutEffect(() => {\n    let timeout: ReturnType<typeof setTimeout>;\n    let onResize = () => {\n      isResizing.current = true;\n      clearTimeout(timeout);\n\n      timeout = setTimeout(() => {\n        isResizing.current = false;\n      }, 500);\n\n      updatePosition();\n    };\n\n    // Only reposition the overlay if a scroll event happens immediately as a result of resize (aka the virtual keyboard has appears)\n    // We don't want to reposition the overlay if the user has pinch zoomed in and is scrolling the viewport around.\n    let onScroll = () => {\n      if (isResizing.current) {\n        onResize();\n      }\n    };\n\n    visualViewport?.addEventListener('resize', onResize);\n    visualViewport?.addEventListener('scroll', onScroll);\n    return () => {\n      visualViewport?.removeEventListener('resize', onResize);\n      visualViewport?.removeEventListener('scroll', onScroll);\n    };\n  }, [updatePosition]);\n\n  let close = useCallback(() => {\n    if (!isResizing.current) {\n      onClose?.();\n    }\n  }, [onClose, isResizing]);\n\n  // When scrolling a parent scrollable region of the trigger (other than the body),\n  // we hide the popover. Otherwise, its position would be incorrect.\n  useCloseOnScroll({\n    triggerRef: targetRef,\n    isOpen,\n    onClose: onClose && close\n  });\n\n  return {\n    overlayProps: {\n      style: {\n        position: 'absolute',\n        zIndex: 100000, // should match the z-index in ModalTrigger\n        ...position?.position,\n        maxHeight: position?.maxHeight ?? '100vh'\n      }\n    },\n    placement: position?.placement ?? null,\n    arrowProps: {\n      'aria-hidden': 'true',\n      role: 'presentation',\n      style: {\n        left: position?.arrowOffsetLeft,\n        top: position?.arrowOffsetTop\n      }\n    },\n    updatePosition\n  };\n}\n\nfunction useResize(onResize) {\n  useLayoutEffect(() => {\n    window.addEventListener('resize', onResize, false);\n    return () => {\n      window.removeEventListener('resize', onResize, false);\n    };\n  }, [onResize]);\n}\n\nfunction translateRTL(position, direction) {\n  if (direction === 'rtl') {\n    return position.replace('start', 'right').replace('end', 'left');\n  }\n  return position.replace('start', 'left').replace('end', 'right');\n}\n", "/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from '@react-aria/utils';\n\nfunction isStyleVisible(element: Element) {\n  const windowObject = getOwnerWindow(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) {\n    return false;\n  }\n\n  let {display, visibility} = element.style;\n\n  let isVisible = (\n    display !== 'none' &&\n    visibility !== 'hidden' &&\n    visibility !== 'collapse'\n  );\n\n  if (isVisible) {\n    const {getComputedStyle} = element.ownerDocument.defaultView as unknown as Window;\n    let {display: computedDisplay, visibility: computedVisibility} = getComputedStyle(element);\n\n    isVisible = (\n      computedDisplay !== 'none' &&\n      computedVisibility !== 'hidden' &&\n      computedVisibility !== 'collapse'\n    );\n  }\n\n  return isVisible;\n}\n\nfunction isAttributeVisible(element: Element, childElement?: Element) {\n  return (\n    !element.hasAttribute('hidden') &&\n    // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') &&\n    (element.nodeName === 'DETAILS' &&\n      childElement &&\n      childElement.nodeName !== 'SUMMARY'\n      ? element.hasAttribute('open')\n      : true)\n  );\n}\n\n/**\n * Adapted from https://github.com/testing-library/jest-dom and\n * https://github.com/vuejs/vue-test-utils-next/.\n * Licensed under the MIT License.\n * @param element - Element to evaluate for display or visibility.\n */\nexport function isElementVisible(element: Element, childElement?: Element): boolean {\n  return (\n    element.nodeName !== '#comment' &&\n    isStyleVisible(element) &&\n    isAttributeVisible(element, childElement) &&\n    (!element.parentElement || isElementVisible(element.parentElement, element))\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport {isElementVisible} from './isElementVisible';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (filter(node as Element)\n          && isElementVisible(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size() {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef) {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement) {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode) {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef) {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode) {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode) {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree = new Tree();\n", "import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {isElementInChildOfActiveScope} from '@react-aria/focus';\nimport {useEffect} from 'react';\nimport {useFocusWithin, useInteractOutside} from '@react-aria/interactions';\n\nexport interface AriaOverlayProps {\n  /** Whether the overlay is currently open. */\n  isOpen?: boolean,\n\n  /** Handler that is called when the overlay should close. */\n  onClose?: () => void,\n\n  /**\n   * Whether to close the overlay when the user interacts outside it.\n   * @default false\n   */\n  isDismissable?: boolean,\n\n  /** Whether the overlay should close when focus is lost or moves outside it. */\n  shouldCloseOnBlur?: boolean,\n\n  /**\n   * Whether pressing the escape key to close the overlay should be disabled.\n   * @default false\n   */\n  isKeyboardDismissDisabled?: boolean,\n\n  /**\n   * When user interacts with the argument element outside of the overlay ref,\n   * return true if onClose should be called.  This gives you a chance to filter\n   * out interaction with elements that should not dismiss the overlay.\n   * By default, onClose will always be called on interaction outside the overlay ref.\n   */\n  shouldCloseOnInteractOutside?: (element: Element) => boolean\n}\n\nexport interface OverlayAria {\n  /** Props to apply to the overlay container element. */\n  overlayProps: DOMAttributes,\n  /** Props to apply to the underlay element, if any. */\n  underlayProps: DOMAttributes\n}\n\nconst visibleOverlays: RefObject<Element | null>[] = [];\n\n/**\n * Provides the behavior for overlays such as dialogs, popovers, and menus.\n * Hides the overlay when the user interacts outside it, when the Escape key is pressed,\n * or optionally, on blur. Only the top-most overlay will close at once.\n */\nexport function useOverlay(props: AriaOverlayProps, ref: RefObject<Element | null>): OverlayAria {\n  let {\n    onClose,\n    shouldCloseOnBlur,\n    isOpen,\n    isDismissable = false,\n    isKeyboardDismissDisabled = false,\n    shouldCloseOnInteractOutside\n  } = props;\n\n  // Add the overlay ref to the stack of visible overlays on mount, and remove on unmount.\n  useEffect(() => {\n    if (isOpen && !visibleOverlays.includes(ref)) {\n      visibleOverlays.push(ref);\n      return () => {\n        let index = visibleOverlays.indexOf(ref);\n        if (index >= 0) {\n          visibleOverlays.splice(index, 1);\n        }\n      };\n    }\n  }, [isOpen, ref]);\n\n  // Only hide the overlay when it is the topmost visible overlay in the stack\n  let onHide = () => {\n    if (visibleOverlays[visibleOverlays.length - 1] === ref && onClose) {\n      onClose();\n    }\n  };\n\n  let onInteractOutsideStart = (e: PointerEvent) => {\n    if (!shouldCloseOnInteractOutside || shouldCloseOnInteractOutside(e.target as Element)) {\n      if (visibleOverlays[visibleOverlays.length - 1] === ref) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n  };\n\n  let onInteractOutside = (e: PointerEvent) => {\n    if (!shouldCloseOnInteractOutside || shouldCloseOnInteractOutside(e.target as Element)) {\n      if (visibleOverlays[visibleOverlays.length - 1] === ref) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n      onHide();\n    }\n  };\n\n  // Handle the escape key\n  let onKeyDown = (e) => {\n    if (e.key === 'Escape' && !isKeyboardDismissDisabled && !e.nativeEvent.isComposing) {\n      e.stopPropagation();\n      e.preventDefault();\n      onHide();\n    }\n  };\n\n  // Handle clicking outside the overlay to close it\n  useInteractOutside({ref, onInteractOutside: isDismissable && isOpen ? onInteractOutside : undefined, onInteractOutsideStart});\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !shouldCloseOnBlur,\n    onBlurWithin: (e) => {\n      // Do not close if relatedTarget is null, which means focus is lost to the body.\n      // That can happen when switching tabs, or due to a VoiceOver/Chrome bug with Control+Option+Arrow navigation.\n      // Clicking on the body to close the overlay should already be handled by useInteractOutside.\n      // https://github.com/adobe/react-spectrum/issues/4130\n      // https://github.com/adobe/react-spectrum/issues/4922\n      //\n      // If focus is moving into a child focus scope (e.g. menu inside a dialog),\n      // do not close the outer overlay. At this point, the active scope should\n      // still be the outer overlay, since blur events run before focus.\n      if (!e.relatedTarget || isElementInChildOfActiveScope(e.relatedTarget)) {\n        return;\n      }\n\n      if (!shouldCloseOnInteractOutside || shouldCloseOnInteractOutside(e.relatedTarget as Element)) {\n        onClose?.();\n      }\n    }\n  });\n\n  let onPointerDownUnderlay = e => {\n    // fixes a firefox issue that starts text selection https://bugzilla.mozilla.org/show_bug.cgi?id=1675846\n    if (e.target === e.currentTarget) {\n      e.preventDefault();\n    }\n  };\n\n  return {\n    overlayProps: {\n      onKeyDown,\n      ...focusWithinProps\n    },\n    underlayProps: {\n      onPointerDown: onPointerDownUnderlay\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain, getScrollParent, isIOS, useLayoutEffect} from '@react-aria/utils';\n\ninterface PreventScrollOptions {\n  /** Whether the scroll lock is disabled. */\n  isDisabled?: boolean\n}\n\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */\nexport function usePreventScroll(options: PreventScrollOptions = {}): void {\n  let {isDisabled} = options;\n\n  useLayoutEffect(() => {\n    if (isDisabled) {\n      return;\n    }\n\n    preventScrollCount++;\n    if (preventScrollCount === 1) {\n      if (isIOS()) {\n        restore = preventScrollMobileSafari();\n      } else {\n        restore = preventScrollStandard();\n      }\n    }\n\n    return () => {\n      preventScrollCount--;\n      if (preventScrollCount === 0) {\n        restore();\n      }\n    };\n  }, [isDisabled]);\n}\n\n// For most browsers, all we need to do is set `overflow: hidden` on the root element, and\n// add some padding to prevent the page from shifting when the scrollbar is hidden.\nfunction preventScrollStandard() {\n  let scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  return chain(\n    scrollbarWidth > 0 &&\n      // Use scrollbar-gutter when supported because it also works for fixed positioned elements.\n      ('scrollbarGutter' in document.documentElement.style\n        ? setStyle(document.documentElement, 'scrollbarGutter', 'stable')\n        : setStyle(document.documentElement, 'paddingRight', `${scrollbarWidth}px`)),\n    setStyle(document.documentElement, 'overflow', 'hidden')\n  );\n}\n\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Set `overscroll-behavior: contain` on nested scrollable regions so they do not scroll the page when at\n//    the top or bottom. Work around a bug where this does not work when the element does not actually overflow\n//    by preventing default in a `touchmove` event.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n  let scrollable: Element;\n  let restoreScrollableStyles;\n  let onTouchStart = (e: TouchEvent) => {\n    // Store the nearest scrollable parent element from the element that the user touched.\n    scrollable = getScrollParent(e.target as Element, true);\n    if (scrollable === document.documentElement && scrollable === document.body) {\n      return;\n    }\n\n    // Prevent scrolling up when at the top and scrolling down when at the bottom\n    // of a nested scrollable area, otherwise mobile Safari will start scrolling\n    // the window instead.\n    if (scrollable instanceof HTMLElement && window.getComputedStyle(scrollable).overscrollBehavior === 'auto') {\n      restoreScrollableStyles = setStyle(scrollable, 'overscrollBehavior', 'contain');\n    }\n  };\n\n  let onTouchMove = (e: TouchEvent) => {\n    // Prevent scrolling the window.\n    if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n      e.preventDefault();\n      return;\n    }\n\n    // overscroll-behavior should prevent scroll chaining, but currently does not\n    // if the element doesn't actually overflow. https://bugs.webkit.org/show_bug.cgi?id=243452\n    // This checks that both the width and height do not overflow, otherwise we might\n    // block horizontal scrolling too. In that case, adding `touch-action: pan-x` to\n    // the element will prevent vertical page scrolling. We can't add that automatically\n    // because it must be set before the touchstart event.\n    if (scrollable.scrollHeight === scrollable.clientHeight && scrollable.scrollWidth === scrollable.clientWidth) {\n      e.preventDefault();\n    }\n  };\n\n  let onTouchEnd = () => {\n    if (restoreScrollableStyles) {\n      restoreScrollableStyles();\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    let target = e.target as HTMLElement;\n    if (willOpenKeyboard(target)) {\n      setupStyles();\n\n      // Apply a transform to trick Safari into thinking the input is at the top of the page\n      // so it doesn't try to scroll it into view.\n      target.style.transform = 'translateY(-2000px)';\n      requestAnimationFrame(() => {\n        target.style.transform = '';\n\n        // This will have prevented the browser from scrolling the focused element into view,\n        // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n        if (visualViewport) {\n          if (visualViewport.height < window.innerHeight) {\n            // If the keyboard is already visible, do this after one additional frame\n            // to wait for the transform to be removed.\n            requestAnimationFrame(() => {\n              scrollIntoView(target);\n            });\n          } else {\n            // Otherwise, wait for the visual viewport to resize before scrolling so we can\n            // measure the correct position to scroll to.\n            visualViewport.addEventListener('resize', () => scrollIntoView(target), {once: true});\n          }\n        }\n      });\n    }\n  };\n\n  let restoreStyles: null | (() => void) = null;\n  let setupStyles = () => {\n    if (restoreStyles) {\n      return;\n    }\n\n    let onWindowScroll = () => {\n      // Last resort. If the window scrolled, scroll it back to the top.\n      // It should always be at the top because the body will have a negative margin (see below).\n      window.scrollTo(0, 0);\n    };\n\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n\n    restoreStyles = chain(\n      addEvent(window, 'scroll', onWindowScroll),\n      setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`),\n      setStyle(document.documentElement, 'overflow', 'hidden'),\n      setStyle(document.body, 'marginTop', `-${scrollY}px`),\n      () => {\n        window.scrollTo(scrollX, scrollY);\n      }\n    );\n\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n  };\n\n  let removeEvents = chain(\n    addEvent(document, 'touchstart', onTouchStart, {passive: false, capture: true}),\n    addEvent(document, 'touchmove', onTouchMove, {passive: false, capture: true}),\n    addEvent(document, 'touchend', onTouchEnd, {passive: false, capture: true}),\n    addEvent(document, 'focus', onFocus, true)\n  );\n\n  return () => {\n    // Restore styles and scroll the page back to where it was.\n    restoreScrollableStyles?.();\n    restoreStyles?.();\n    removeEvents();\n  };\n}\n\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element: HTMLElement, style: string, value: string) {\n  let cur = element.style[style];\n  element.style[style] = value;\n\n  return () => {\n    element.style[style] = cur;\n  };\n}\n\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent<K extends keyof GlobalEventHandlersEventMap>(\n  target: Document | Window,\n  event: K,\n  handler: (this: Document | Window, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n) {\n  // internal function, so it's ok to ignore the difficult to fix type error\n  // @ts-ignore\n  target.addEventListener(event, handler, options);\n  return () => {\n    // @ts-ignore\n    target.removeEventListener(event, handler, options);\n  };\n}\n\nfunction scrollIntoView(target: Element) {\n  let root = document.scrollingElement || document.documentElement;\n  let nextTarget: Element | null = target;\n  while (nextTarget && nextTarget !== root) {\n    // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n    let scrollable = getScrollParent(nextTarget);\n    if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== nextTarget) {\n      let scrollableTop = scrollable.getBoundingClientRect().top;\n      let targetTop = nextTarget.getBoundingClientRect().top;\n      if (targetTop > scrollableTop + nextTarget.clientHeight) {\n        scrollable.scrollTop += targetTop - scrollableTop;\n      }\n    }\n\n    nextTarget = scrollable.parentElement;\n  }\n}\n\nfunction willOpenKeyboard(target: Element) {\n  return (\n    (target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type)) ||\n    target instanceof HTMLTextAreaElement ||\n    (target instanceof HTMLElement && target.isContentEditable)\n  );\n}\n", "/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React, {createContext, JSX, ReactNode, useContext} from 'react';\n\nexport interface PortalProviderProps {\n  /** Should return the element where we should portal to. Can clear the context by passing null. */\n  getContainer?: (() => HTMLElement | null) | null,\n  /** The content of the PortalProvider. Should contain all children that want to portal their overlays to the element returned by the provided `getContainer()`. */\n  children: ReactNode\n}\n\nexport interface PortalProviderContextValue extends Omit<PortalProviderProps, 'children'>{};\n\nexport const PortalContext = createContext<PortalProviderContextValue>({});\n\n/**\n * Sets the portal container for all overlay elements rendered by its children.\n */\nexport function UNSAFE_PortalProvider(props: PortalProviderProps): JSX.Element {\n  let {getContainer} = props;\n  let {getContainer: ctxGetContainer} = useUNSAFE_PortalContext();\n  return (\n    <PortalContext.Provider value={{getContainer: getContainer === null ? undefined : getContainer ?? ctxGetContainer}}>\n      {props.children}\n    </PortalContext.Provider>\n  );\n}\n\nexport function useUNSAFE_PortalContext(): PortalProviderContextValue {\n  return useContext(PortalContext) ?? {};\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport React, {AriaAttributes, JSX, ReactNode, useContext, useEffect, useMemo, useState} from 'react';\nimport ReactDOM from 'react-dom';\nimport {useIsSSR} from '@react-aria/ssr';\nimport {useUNSAFE_PortalContext} from './PortalProvider';\n\nexport interface ModalProviderProps extends DOMAttributes {\n  children: ReactNode\n}\n\ninterface ModalContext {\n  parent: ModalContext | null,\n  modalCount: number,\n  addModal: () => void,\n  removeModal: () => void\n}\n\nconst Context = React.createContext<ModalContext | null>(null);\n\n/**\n * Each ModalProvider tracks how many modals are open in its subtree. On mount, the modals\n * trigger `addModal` to increment the count, and trigger `removeModal` on unmount to decrement it.\n * This is done recursively so that all parent providers are incremented and decremented.\n * If the modal count is greater than zero, we add `aria-hidden` to this provider to hide its\n * subtree from screen readers. This is done using React context in order to account for things\n * like portals, which can cause the React tree and the DOM tree to differ significantly in structure.\n */\nexport function ModalProvider(props: ModalProviderProps): JSX.Element {\n  let {children} = props;\n  let parent = useContext(Context);\n  let [modalCount, setModalCount] = useState(0);\n  let context = useMemo(() => ({\n    parent,\n    modalCount,\n    addModal() {\n      setModalCount(count => count + 1);\n      if (parent) {\n        parent.addModal();\n      }\n    },\n    removeModal() {\n      setModalCount(count => count - 1);\n      if (parent) {\n        parent.removeModal();\n      }\n    }\n  }), [parent, modalCount]);\n\n  return (\n    <Context.Provider value={context}>\n      {children}\n    </Context.Provider>\n  );\n}\n\nexport interface ModalProviderAria {\n  /**\n   * Props to be spread on the container element.\n   */\n  modalProviderProps: AriaAttributes\n}\n\n/**\n * Used to determine if the tree should be aria-hidden based on how many\n * modals are open.\n */\nexport function useModalProvider(): ModalProviderAria {\n  let context = useContext(Context);\n  return {\n    modalProviderProps: {\n      'aria-hidden': context && context.modalCount > 0 ? true : undefined\n    }\n  };\n}\n\n/**\n * Creates a root node that will be aria-hidden if there are other modals open.\n */\nfunction OverlayContainerDOM(props: ModalProviderProps) {\n  let {modalProviderProps} = useModalProvider();\n  return <div data-overlay-container {...props} {...modalProviderProps} />;\n}\n\n/**\n * An OverlayProvider acts as a container for the top-level application.\n * Any application that uses modal dialogs or other overlays should\n * be wrapped in a `<OverlayProvider>`. This is used to ensure that\n * the main content of the application is hidden from screen readers\n * if a modal or other overlay is opened. Only the top-most modal or\n * overlay should be accessible at once.\n */\nexport function OverlayProvider(props: ModalProviderProps): JSX.Element {\n  return (\n    <ModalProvider>\n      <OverlayContainerDOM {...props} />\n    </ModalProvider>\n  );\n}\n\nexport interface OverlayContainerProps extends ModalProviderProps {\n  /**\n   * The container element in which the overlay portal will be placed.\n   * @default document.body\n   * @deprecated - Use a parent UNSAFE_PortalProvider to set your portal container instead.\n   */\n  portalContainer?: Element\n}\n\n/**\n * A container for overlays like modals and popovers. Renders the overlay\n * into a Portal which is placed at the end of the document body.\n * Also ensures that the overlay is hidden from screen readers if a\n * nested modal is opened. Only the top-most modal or overlay should\n * be accessible at once.\n */\nexport function OverlayContainer(props: OverlayContainerProps): React.ReactPortal | null {\n  let isSSR = useIsSSR();\n  let {portalContainer = isSSR ? null : document.body, ...rest} = props;\n  let {getContainer} = useUNSAFE_PortalContext();\n  if (!props.portalContainer && getContainer) {\n    portalContainer = getContainer();\n  }\n\n  React.useEffect(() => {\n    if (portalContainer?.closest('[data-overlay-container]')) {\n      throw new Error('An OverlayContainer must not be inside another container. Please change the portalContainer prop.');\n    }\n  }, [portalContainer]);\n\n  if (!portalContainer) {\n    return null;\n  }\n\n  let contents = <OverlayProvider {...rest} />;\n  return ReactDOM.createPortal(contents, portalContainer);\n}\n\ninterface ModalAriaProps extends DOMAttributes {\n  /** Data attribute marks the dom node as a modal for the aria-modal-polyfill. */\n  'data-ismodal': boolean\n}\n\nexport interface AriaModalOptions {\n  isDisabled?: boolean\n}\n\nexport interface ModalAria {\n  /** Props for the modal content element. */\n  modalProps: ModalAriaProps\n}\n\n/**\n * Hides content outside the current `<OverlayContainer>` from screen readers\n * on mount and restores it on unmount. Typically used by modal dialogs and\n * other types of overlays to ensure that only the top-most modal is\n * accessible at once.\n */\nexport function useModal(options?: AriaModalOptions): ModalAria {\n  // Add aria-hidden to all parent providers on mount, and restore on unmount.\n  let context = useContext(Context);\n  if (!context) {\n    throw new Error('Modal is not contained within a provider');\n  }\n\n  useEffect(() => {\n    if (options?.isDisabled || !context || !context.parent) {\n      return;\n    }\n\n    // The immediate context is from the provider containing this modal, so we only\n    // want to trigger aria-hidden on its parents not on the modal provider itself.\n    context.parent.addModal();\n    return () => {\n      if (context && context.parent) {\n        context.parent.removeModal();\n      }\n    };\n  }, [context, context.parent, options?.isDisabled]);\n\n  return {\n    modalProps: {\n      'data-ismodal': !options?.isDisabled\n    }\n  };\n}\n", "{\n  \"dismiss\": \"تجاهل\"\n}\n", "{\n  \"dismiss\": \"Отхвърляне\"\n}\n", "{\n  \"dismiss\": \"Odstranit\"\n}\n", "{\n  \"dismiss\": \"Luk\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>ße<PERSON>\"\n}\n", "{\n  \"dismiss\": \"Απόρριψη\"\n}\n", "{\n  \"dismiss\": \"Dism<PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"התעלם\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"閉じる\"\n}\n", "{\n  \"dismiss\": \"무시\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"Lukk\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"Dispensar\"\n}\n", "{\n  \"dismiss\": \"Revocare\"\n}\n", "{\n  \"dismiss\": \"Пропустить\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"Avvisa\"\n}\n", "{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n", "{\n  \"dismiss\": \"Скасувати\"\n}\n", "{\n  \"dismiss\": \"取消\"\n}\n", "{\n  \"dismiss\": \"關閉\"\n}\n", "const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}", "/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ClearPressResponder} from '@react-aria/interactions';\nimport {FocusScope} from '@react-aria/focus';\nimport React, {ReactNode, useContext, useMemo, useState} from 'react';\nimport ReactDOM from 'react-dom';\nimport {useIsSSR} from '@react-aria/ssr';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useUNSAFE_PortalContext} from './PortalProvider';\n\nexport interface OverlayProps {\n  /**\n   * The container element in which the overlay portal will be placed.\n   * @default document.body\n   */\n  portalContainer?: Element,\n  /** The overlay to render in the portal. */\n  children: ReactNode,\n  /**\n   * Disables default focus management for the overlay, including containment and restoration.\n   * This option should be used very carefully. When focus management is disabled, you must\n   * implement focus containment and restoration to ensure the overlay is keyboard accessible.\n   */\n  disableFocusManagement?: boolean,\n  /**\n   * Whether to contain focus within the overlay.\n   */\n  shouldContainFocus?: boolean,\n  /**\n   * Whether the overlay is currently performing an exit animation. When true,\n   * focus is allowed to move outside.\n   */\n  isExiting?: boolean\n}\n\nexport const OverlayContext = React.createContext<{contain: boolean, setContain: React.Dispatch<React.SetStateAction<boolean>>} | null>(null);\n\n/**\n * A container which renders an overlay such as a popover or modal in a portal,\n * and provides a focus scope for the child elements.\n */\nexport function Overlay(props: OverlayProps): React.ReactPortal | null {\n  let isSSR = useIsSSR();\n  let {portalContainer = isSSR ? null : document.body, isExiting} = props;\n  let [contain, setContain] = useState(false);\n  let contextValue = useMemo(() => ({contain, setContain}), [contain, setContain]);\n\n  let {getContainer} = useUNSAFE_PortalContext();\n  if (!props.portalContainer && getContainer) {\n    portalContainer = getContainer();\n  }\n\n  if (!portalContainer) {\n    return null;\n  }\n\n  let contents = props.children;\n  if (!props.disableFocusManagement) {\n    contents = (\n      <FocusScope restoreFocus contain={(props.shouldContainFocus || contain) && !isExiting}>\n        {contents}\n      </FocusScope>\n    );\n  }\n\n  contents = (\n    <OverlayContext.Provider value={contextValue}>\n      <ClearPressResponder>\n        {contents}\n      </ClearPressResponder>\n    </OverlayContext.Provider>\n  );\n\n  return ReactDOM.createPortal(contents, portalContainer);\n}\n\n/** @private */\nexport function useOverlayFocusContain(): void {\n  let ctx = useContext(OverlayContext);\n  let setContain = ctx?.setContain;\n  useLayoutEffect(() => {\n    setContain?.(true);\n  }, [setContain]);\n}\n", "\"use client\";\nimport {\n  ProviderContext\n} from \"./chunk-Q3W45BN5.mjs\";\n\n// src/provider.tsx\nimport { I18nProvider } from \"@react-aria/i18n\";\nimport { RouterProvider } from \"@react-aria/utils\";\nimport { OverlayProvider } from \"@react-aria/overlays\";\nimport { useMemo } from \"react\";\nimport { MotionConfig, MotionGlobalConfig } from \"framer-motion\";\nimport { jsx } from \"react/jsx-runtime\";\nvar HeroUIProvider = ({\n  children,\n  navigate,\n  disableAnimation,\n  useHref,\n  disableRipple = false,\n  skipFramerMotionAnimations = disableAnimation,\n  reducedMotion = \"never\",\n  validationBehavior,\n  locale = \"en-US\",\n  labelPlacement,\n  // if minDate / maxDate are not specified in `defaultDates`\n  // then they will be set in `use-date-input.ts` or `use-calendar-base.ts`\n  defaultDates,\n  createCalendar,\n  spinnerVariant,\n  ...otherProps\n}) => {\n  let contents = children;\n  if (navigate) {\n    contents = /* @__PURE__ */ jsx(RouterProvider, { navigate, useHref, children: contents });\n  }\n  const context = useMemo(() => {\n    if (disableAnimation && skipFramerMotionAnimations) {\n      MotionGlobalConfig.skipAnimations = true;\n    }\n    return {\n      createCalendar,\n      defaultDates,\n      disableAnimation,\n      disableRipple,\n      validationBehavior,\n      labelPlacement,\n      spinnerVariant\n    };\n  }, [\n    createCalendar,\n    defaultDates == null ? void 0 : defaultDates.maxDate,\n    defaultDates == null ? void 0 : defaultDates.minDate,\n    disableAnimation,\n    disableRipple,\n    validationBehavior,\n    labelPlacement,\n    spinnerVariant\n  ]);\n  return /* @__PURE__ */ jsx(ProviderContext, { value: context, children: /* @__PURE__ */ jsx(I18nProvider, { locale, children: /* @__PURE__ */ jsx(MotionConfig, { reducedMotion, children: /* @__PURE__ */ jsx(OverlayProvider, { ...otherProps, children: contents }) }) }) });\n};\n\nexport {\n  HeroUIProvider\n};\n", "\"use client\";\nimport {\n  useProviderContext\n} from \"./chunk-Q3W45BN5.mjs\";\n\n// src/hooks/use-label-placement.ts\nimport { useMemo } from \"react\";\nfunction useLabelPlacement(props) {\n  const globalContext = useProviderContext();\n  const globalLabelPlacement = globalContext == null ? void 0 : globalContext.labelPlacement;\n  return useMemo(() => {\n    var _a, _b;\n    const labelPlacement = (_b = (_a = props.labelPlacement) != null ? _a : globalLabelPlacement) != null ? _b : \"inside\";\n    if (labelPlacement === \"inside\" && !props.label) {\n      return \"outside\";\n    }\n    return labelPlacement;\n  }, [props.labelPlacement, globalLabelPlacement, props.label]);\n}\n\nexport {\n  useLabelPlacement\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,YAAuB;AACvB,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,eAAe;AAAA,IACf;AAAA,EACF,IAAI;AACJ,QAAM,UAAgB,oBAAc,MAAM;AAC1C,UAAQ,cAAc;AACtB,WAAS,cAAc;AACrB,QAAIA;AACJ,UAAM,UAAgB,iBAAW,OAAO;AACxC,QAAI,CAAC,WAAW,QAAQ;AACtB,YAAM,QAAQ,IAAI,MAAM,YAAY;AACpC,YAAM,OAAO;AACb,OAACA,MAAK,MAAM,sBAAsB,OAAO,SAASA,IAAG,KAAK,OAAO,OAAO,WAAW;AACnF,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACA,SAAO,CAAC,QAAQ,UAAU,aAAa,OAAO;AAChD;;;ACrBA,mBAA6D;AAC7D,SAAS,YAAY;AACnB,SAAO,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;AAChF;AACA,IAAI,YAAY,UAAU;AAgE1B,SAAS,aAAa,KAAK;AACzB,SAAO;AAAA,IACL,oBAAoB;AAClB,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACF;AAWA,SAAS,UAAU,KAAK;AACtB,QAAM,aAAS,qBAAO,IAAI;AAC1B,wCAAoB,KAAK,MAAM,OAAO,OAAO;AAC7C,SAAO;AACT;;;ACxFA,SAAS,UAAU,KAAK,OAAO;AAC7B,MAAI,OAAO;AAAM;AACjB,MAAI,WAAW,GAAG,GAAG;AACnB,QAAI,KAAK;AACT;AAAA,EACF;AACA,MAAI;AACF,QAAI,UAAU;AAAA,EAChB,QAAQ;AACN,UAAM,IAAI,MAAM,wBAAwB,KAAK,aAAa,GAAG,GAAG;AAAA,EAClE;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,CAAC,SAAS;AACf,SAAK,QAAQ,CAAC,QAAQ,UAAU,KAAK,IAAI,CAAC;AAAA,EAC5C;AACF;;;ACjBA,IAAAC,SAAuB;;;ACFvB,IAAAC,gBAAyC;AAMzC,IAAI,eAAe,CAAC,UAAU,gBAAgB;AAC5C,MAAIC;AACJ,MAAI,SAAS,CAAC;AACd,QAAM,yBAAyBA,MAAK,uBAAS,IAAI,UAAU,CAAC,SAAS;AACnE,QAAI,KAAC,8BAAe,IAAI;AAAG,aAAO;AAClC,QAAI,KAAK,SAAS,aAAa;AAC7B,aAAO,KAAK,IAAI;AAChB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC,MAAM,OAAO,SAASA,IAAG,OAAO,OAAO;AACxC,QAAM,iBAAiB,OAAO,UAAU,IAAI,SAAS;AACrD,SAAO,CAAC,uBAAuB,cAAc;AAC/C;;;ACnBA,IAAI,eAA+B,oBAAI,IAAI;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAI,gBAAgC,oBAAI,IAAI;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC9KD,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,SAAS;AACb,SAAS,eAAe,OAAO,OAAO,CAAC,GAAG;AACxC,MAAI;AAAA,IACF,YAAY;AAAA,IACZ,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,gBAAgB,CAAC;AACrB,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,iBAAiB,OAAO,SAAS,cAAc,IAAI,IAAI,GAAG;AAC5D;AAAA,IACF;AACA,SAAK,kBAAkB,OAAO,SAAS,eAAe,IAAI,IAAI,MAAM,OAAO,KAAK,IAAI,GAAG;AACrF;AAAA,IACF;AACA,QAAI,OAAO,KAAK,IAAI,KAAK,CAAC,cAAc,IAAI,IAAI,GAAG;AACjD;AAAA,IACF;AACA,QAAI,iBAAiB,OAAO,KAAK,IAAI,GAAG;AACtC;AAAA,IACF;AACA,QAAI,kBAAkB,OAAO,KAAK,IAAI,GAAG;AACvC;AAAA,IACF;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI,MAAM,aAAa,IAAI,IAAI,KAAK,aAAa,OAAO,KAAK,IAAI,MAAM,aAAa,OAAO,SAAS,UAAU,IAAI,IAAI,MAAM,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,IAAI,GAAG;AACnN,oBAAc,IAAI,IAAI,MAAM,IAAI;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AACT;;;AC3CA,IAAAC,SAAuB;;;ACGvB,IAAI,CAAC,iBAAiB,kBAAkB,IAAI,eAAc;AAAA,EACxD,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;;;ACMD,IAAM,oCAAc,oBAAI,IAAI;EAAC;EAAQ;EAAQ;EAAQ;EAAQ;EAAQ;EAAQ;EAAQ;EAAQ;EAAQ;CAAO;AAC5G,IAAM,kCAAY,oBAAI,IAAI;EAAC;EAAM;EAAM;EAAO;EAAO;EAAO;EAAO;EAAM;EAAM;EAAO;EAAM;EAAM;EAAO;EAAO;EAAO;EAAM;EAAM;EAAM;EAAM;CAAK;AAK7I,SAAS,0CAAM,cAAoB;AAExC,MAAI,KAAK,QAAQ;AACf,QAAI,SAAS,IAAI,KAAK,OAAO,YAAA,EAAc,SAAQ;AAKnD,QAAI,WAAW,OAAO,OAAO,gBAAgB,aAAa,OAAO,YAAW,IAAK,OAAO;AACxF,QAAI;AACF,aAAO,SAAS,cAAc;AAKhC,QAAI,OAAO;AACT,aAAO,kCAAY,IAAI,OAAO,MAAM;EAExC;AAGA,MAAI,OAAO,aAAa,MAAM,GAAA,EAAK,CAAA;AACnC,SAAO,gCAAU,IAAI,IAAA;AACvB;;;;ACjBA,IAAM,qCAAe,OAAO,IAAI,wBAAA;AAKzB,SAAS,4CAAA;AACd,MAAI,SAAS,OAAO,WAAW,eAAe,OAAO,kCAAA,KAE/C,OAAO,cAAc,gBAAgB,UAAU,YAAY,UAAU,iBACtE;AAEL,MAAI;AACF,SAAK,eAAe,mBAAmB;MAAC;KAAO;EACjD,QAAQ;AACN,aAAS;EACX;AACA,SAAO;;IAEL,YAAW,GAAA,2CAAM,MAAA,IAAU,QAAQ;EACrC;AACF;AAEA,IAAI,sCAAgB,0CAAA;AACpB,IAAI,kCAAY,oBAAI,IAAA;AAEpB,SAAS,qCAAA;AACP,wCAAgB,0CAAA;AAChB,WAAS,YAAY;AACnB,aAAS,mCAAA;AAEb;AAKO,SAAS,4CAAA;AACd,MAAI,SAAQ,GAAA,2CAAO;AACnB,MAAI,CAAC,eAAe,gBAAA,KAAoB,GAAA,cAAAC,UAAS,mCAAA;AAEjD,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,QAAI,gCAAU,SAAS;AACrB,aAAO,iBAAiB,kBAAkB,kCAAA;AAG5C,oCAAU,IAAI,gBAAA;AAEd,WAAO,MAAA;AACL,sCAAU,OAAO,gBAAA;AACjB,UAAI,gCAAU,SAAS;AACrB,eAAO,oBAAoB,kBAAkB,kCAAA;IAEjD;EACF,GAAG,CAAA,CAAE;AAIL,MAAI;AACF,WAAO;MACL,QAAQ;MACR,WAAW;IACb;AAGF,SAAO;AACT;;;;AClEA,IAAM,qCAAc,GAAA,cAAAC,SAAM,cAA6B,IAAA;AAKhD,SAAS,0CAAa,OAAwB;AACnD,MAAI,EAAA,QAAO,SAAU,IAAI;AACzB,MAAI,iBAAgB,GAAA,2CAAe;AAEnC,MAAI,SAAgB,GAAA,cAAAA,SAAM,QAAQ,MAAA;AAChC,QAAI,CAAC;AACH,aAAO;AAGT,WAAO;;MAEL,YAAW,GAAA,2CAAM,MAAA,IAAU,QAAQ;IACrC;EACF,GAAG;IAAC;IAAe;GAAO;AAE1B,UACE,GAAA,cAAAA,SAAA,cAAC,kCAAY,UAAQ;IAAC;KACnB,QAAA;AAGP;AAKO,SAAS,4CAAA;AACd,MAAI,iBAAgB,GAAA,2CAAe;AACnC,MAAI,WAAU,GAAA,cAAAC,YAAW,iCAAA;AACzB,SAAO,WAAW;AACpB;;;ACzCA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA;AAAG,UAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,QAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC3B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;AAEO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAG,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,QAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW;AAAG,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,UAAI,MAAM,EAAE,KAAK,OAAO;AACpB,YAAI,CAAC;AAAI,eAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,WAAG,CAAC,IAAI,KAAK,CAAC;AAAA,MAClB;AAAA,IACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC1NO,SAAS,QAAQ,IAAI,SAAS;AACjC,MAAI,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AACvD,MAAI,aAAa,WAAW,QAAQ,aAAa,QAAQ,aAAa;AACtE,MAAI,WAAW,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAChE,SAAO,SAAS,IAAI;AAAA,IAChB;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,YAAY,OAAO;AACxB,SAAQ,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU;AAC3E;AACA,SAAS,QAAQ,IAAI,OAAO,YAAY,KAAK;AACzC,MAAI,WAAW,YAAY,GAAG,IAAI,MAAM,WAAW,GAAG;AACtD,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACtC,oBAAgB,GAAG,KAAK,MAAM,GAAG;AACjC,UAAM,IAAI,UAAU,aAAa;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,SAAS,IAAI,OAAO,YAAY;AACrC,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,MAAI,WAAW,WAAW,IAAI;AAC9B,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACtC,oBAAgB,GAAG,MAAM,MAAM,IAAI;AACnC,UAAM,IAAI,UAAU,aAAa;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,SAAS,IAAI,SAAS,UAAU,OAAO,WAAW;AACvD,SAAO,SAAS,KAAK,SAAS,IAAI,OAAO,SAAS;AACtD;AACA,SAAS,gBAAgB,IAAI,SAAS;AAClC,MAAI,WAAW,GAAG,WAAW,IAAI,UAAU;AAC3C,SAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAClF;AACA,SAAS,iBAAiB,IAAI,SAAS;AACnC,SAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAClF;AACA,SAAS,gBAAgB,IAAI,SAAS;AAClC,SAAO,SAAS,IAAI,MAAM,SAAS,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AACjF;AAIA,IAAI,oBAAoB,WAAY;AAChC,SAAO,KAAK,UAAU,SAAS;AACnC;AAIA,IAAI;AAAA;AAAA,EAA6C,WAAY;AACzD,aAASC,+BAA8B;AACnC,WAAK,QAAQ,uBAAO,OAAO,IAAI;AAAA,IACnC;AACA,IAAAA,6BAA4B,UAAU,MAAM,SAAU,KAAK;AACvD,aAAO,KAAK,MAAM,GAAG;AAAA,IACzB;AACA,IAAAA,6BAA4B,UAAU,MAAM,SAAU,KAAK,OAAO;AAC9D,WAAK,MAAM,GAAG,IAAI;AAAA,IACtB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI,eAAe;AAAA,EACf,QAAQ,SAAS,SAAS;AACtB,WAAO,IAAI,4BAA4B;AAAA,EAC3C;AACJ;AACO,IAAI,aAAa;AAAA,EACpB,UAAU;AAAA,EACV,SAAS;AACb;;;AC/EO,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAUA,WAAU,+BAA+B,IAAI,CAAC,IAAI;AAE5D,EAAAA,WAAUA,WAAU,gBAAgB,IAAI,CAAC,IAAI;AAE7C,EAAAA,WAAUA,WAAU,oBAAoB,IAAI,CAAC,IAAI;AAEjD,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,CAAC,IAAI;AAEnD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,yBAAyB,IAAI,CAAC,IAAI;AAEtD,EAAAA,WAAUA,WAAU,4BAA4B,IAAI,CAAC,IAAI;AAEzD,EAAAA,WAAUA,WAAU,wBAAwB,IAAI,CAAC,IAAI;AAErD,EAAAA,WAAUA,WAAU,2BAA2B,IAAI,EAAE,IAAI;AAEzD,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAEhE,EAAAA,WAAUA,WAAU,gCAAgC,IAAI,EAAE,IAAI;AAE9D,EAAAA,WAAUA,WAAU,qCAAqC,IAAI,EAAE,IAAI;AAEnE,EAAAA,WAAUA,WAAU,sCAAsC,IAAI,EAAE,IAAI;AAEpE,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAKxE,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAExE,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAKhE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAIlE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAElE,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,EAAE,IAAI;AAEpD,EAAAA,WAAUA,WAAU,aAAa,IAAI,EAAE,IAAI;AAE3C,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,EAAE,IAAI;AAEhD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,EAAE,IAAI;AAErD,EAAAA,WAAUA,WAAU,cAAc,IAAI,EAAE,IAAI;AAChD,GAAG,cAAc,YAAY,CAAC,EAAE;;;AC9DzB,IAAI;AAAA,CACV,SAAUC,OAAM;AAIb,EAAAA,MAAKA,MAAK,SAAS,IAAI,CAAC,IAAI;AAI5B,EAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAI7B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAK3B,EAAAA,MAAKA,MAAK,OAAO,IAAI,CAAC,IAAI;AAI1B,EAAAA,MAAKA,MAAK,KAAK,IAAI,CAAC,IAAI;AAC5B,GAAG,SAAS,OAAO,CAAC,EAAE;AACf,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AACnD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,SAAS,iBAAiB,IAAI;AACjC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,kBAAkB,IAAI;AAClC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,eAAe,IAAI;AAC/B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,aAAa,IAAI;AAC7B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,iBAAiB,IAAI;AACjC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;AACO,SAAS,mBAAmB,IAAI;AACnC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;;;AC/EO,IAAI,wBAAwB;;;ACInC,IAAI,kBAAkB;AAOf,SAAS,sBAAsB,UAAU;AAC5C,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,iBAAiB,SAAU,OAAO;AAC/C,QAAI,MAAM,MAAM;AAChB,YAAQ,MAAM,CAAC,GAAG;AAAA,MAEd,KAAK;AACD,eAAO,MAAM,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AACzD;AAAA,MAEJ,KAAK;AACD,eAAO,OAAO,QAAQ,IAAI,YAAY;AACtC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,8DAA8D;AAAA,MAEvF,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4CAA4C;AAAA,MAErE,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ,CAAC,WAAW,WAAW,SAAS,QAAQ,QAAQ,EAAE,MAAM,CAAC;AACxE;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,yCAAyC;AAAA,MAClE,KAAK;AACD,eAAO,MAAM,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC3C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,2DAA2D;AAAA,MAEpF,KAAK;AACD,eAAO,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS;AAChB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA,MAErF,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,kEAAkE;AAAA,MAE3F,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA,MAErF,KAAK;AACD,eAAO,eAAe,MAAM,IAAI,UAAU;AAC1C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,sEAAsE;AAAA,IACnG;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;;;ACvHO,IAAI,oBAAoB;;;ACCxB,SAAS,8BAA8B,UAAU;AACpD,MAAI,SAAS,WAAW,GAAG;AACvB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AAEA,MAAI,eAAe,SACd,MAAM,iBAAiB,EACvB,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,SAAS;AAAA,EAAG,CAAC;AACjD,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,QAAQ,MAAM;AAC9E,QAAI,cAAc,eAAe,EAAE;AACnC,QAAI,iBAAiB,YAAY,MAAM,GAAG;AAC1C,QAAI,eAAe,WAAW,GAAG;AAC7B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AACA,QAAI,OAAO,eAAe,CAAC,GAAG,UAAU,eAAe,MAAM,CAAC;AAC9D,aAASC,MAAK,GAAG,YAAY,SAASA,MAAK,UAAU,QAAQA,OAAM;AAC/D,UAAI,SAAS,UAAUA,GAAE;AACzB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO,KAAK,EAAE,MAAY,QAAiB,CAAC;AAAA,EAChD;AACA,SAAO;AACX;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,KAAK,QAAQ,WAAW,EAAE;AACrC;AACA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,SAAS,0BAA0B,KAAK;AACpC,MAAI,SAAS,CAAC;AACd,MAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7B,WAAO,mBAAmB;AAAA,EAC9B,WACS,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAClC,WAAO,mBAAmB;AAAA,EAC9B;AACA,MAAI,QAAQ,6BAA6B,SAAU,GAAG,IAAI,IAAI;AAE1D,QAAI,OAAO,OAAO,UAAU;AACxB,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,OAAO,KAAK;AACjB,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,GAAG,CAAC,MAAM,KAAK;AACpB,aAAO,2BAA2B,GAAG;AAAA,IACzC,OAEK;AACD,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BACH,GAAG,UAAU,OAAO,OAAO,WAAW,GAAG,SAAS;AAAA,IAC1D;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,KAAK;AACpB,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,EACR;AACJ;AACA,SAAS,yCAAyC,MAAM;AAEpD,MAAI;AACJ,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK;AACpC,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB,WACS,KAAK,CAAC,MAAM,KAAK;AACtB,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB;AACA,MAAI,QAAQ;AACR,QAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,QAAI,gBAAgB,MAAM;AACtB,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,gBAAgB,MAAM;AAC3B,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,CAAC,4BAA4B,KAAK,IAAI,GAAG;AACzC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AACA,WAAO,uBAAuB,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,KAAK;AAC/B,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,UAAU,GAAG;AAC5B,MAAI,UAAU;AACV,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIO,SAAS,oBAAoB,QAAQ;AACxC,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,QAAQ,MAAM;AAC5D,QAAI,QAAQ,SAAS,EAAE;AACvB,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,WAAW,MAAM,QAAQ,CAAC;AACjC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,wBAAwB;AAC/B;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,OAAO,cAAc,MAAM,QAAQ,CAAC,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,aAAa,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKC,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AAChM;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,cAAc,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKA,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AACjM;AAAA,MACJ,KAAK;AACD,eAAO,WAAW;AAClB;AAAA,MAEJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MAEJ,KAAK;AACD,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,gBAAM,IAAI,WAAW,0DAA0D;AAAA,QACnF;AACA,cAAM,QAAQ,CAAC,EAAE,QAAQ,qBAAqB,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3E,cAAI,IAAI;AACJ,mBAAO,uBAAuB,GAAG;AAAA,UACrC,WACS,MAAM,IAAI;AACf,kBAAM,IAAI,MAAM,oDAAoD;AAAA,UACxE,WACS,IAAI;AACT,kBAAM,IAAI,MAAM,kDAAkD;AAAA,UACtE;AACA,iBAAO;AAAA,QACX,CAAC;AACD;AAAA,IACR;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,aAAO,uBAAuB,MAAM,KAAK;AACzC;AAAA,IACJ;AACA,QAAI,yBAAyB,KAAK,MAAM,IAAI,GAAG;AAI3C,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,cAAM,IAAI,WAAW,+DAA+D;AAAA,MACxF;AACA,YAAM,KAAK,QAAQ,0BAA0B,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAE1E,YAAI,OAAO,KAAK;AACZ,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,GAAG,CAAC,MAAM,KAAK;AAC1B,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,IAAI;AACf,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG,SAAS,GAAG;AAAA,QAClD,OACK;AACD,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG;AAAA,QACtC;AACA,eAAO;AAAA,MACX,CAAC;AACD,UAAI,MAAM,MAAM,QAAQ,CAAC;AAEzB,UAAI,QAAQ,KAAK;AACb,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,qBAAqB,iBAAiB,CAAC;AAAA,MACrF,WACS,KAAK;AACV,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,GAAG,CAAC;AAAA,MAC1E;AACA;AAAA,IACJ;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,MAAM,IAAI,CAAC;AAC7E;AAAA,IACJ;AACA,QAAI,WAAW,UAAU,MAAM,IAAI;AACnC,QAAI,UAAU;AACV,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,QAAQ;AAAA,IACpD;AACA,QAAI,sCAAsC,yCAAyC,MAAM,IAAI;AAC7F,QAAI,qCAAqC;AACrC,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,mCAAmC;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO;AACX;;;ACzTO,IAAI,WAAW;AAAA,EAClB,OAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;ACh4CO,SAAS,eAAe,UAAU,QAAQ;AAC7C,MAAI,eAAe;AACnB,WAAS,aAAa,GAAG,aAAa,SAAS,QAAQ,cAAc;AACjE,QAAI,cAAc,SAAS,OAAO,UAAU;AAC5C,QAAI,gBAAgB,KAAK;AACrB,UAAI,cAAc;AAClB,aAAO,aAAa,IAAI,SAAS,UAC7B,SAAS,OAAO,aAAa,CAAC,MAAM,aAAa;AACjD;AACA;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,cAAc;AACjC,UAAI,eAAe,cAAc,IAAI,IAAI,KAAK,eAAe;AAC7D,UAAI,gBAAgB;AACpB,UAAI,WAAW,+BAA+B,MAAM;AACpD,UAAI,YAAY,OAAO,YAAY,KAAK;AACpC,uBAAe;AAAA,MACnB;AACA,aAAO,iBAAiB,GAAG;AACvB,wBAAgB;AAAA,MACpB;AACA,aAAO,YAAY,GAAG;AAClB,uBAAe,WAAW;AAAA,MAC9B;AAAA,IACJ,WACS,gBAAgB,KAAK;AAC1B,sBAAgB;AAAA,IACpB,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,+BAA+B,QAAQ;AAC5C,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc;AAAA,EAEd,OAAO;AAAA,EAEP,OAAO,WAAW,QAAQ;AAE1B,gBAAY,OAAO,WAAW,CAAC;AAAA,EACnC;AACA,MAAI,WAAW;AACX,YAAQ,WAAW;AAAA,MACf,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,cAAM,IAAI,MAAM,mBAAmB;AAAA,IAC3C;AAAA,EACJ;AAEA,MAAI,cAAc,OAAO;AACzB,MAAI;AACJ,MAAI,gBAAgB,QAAQ;AACxB,gBAAY,OAAO,SAAS,EAAE;AAAA,EAClC;AACA,MAAI,aAAa,SAAS,aAAa,EAAE,KACrC,SAAS,eAAe,EAAE,KAC1B,SAAS,GAAG,OAAO,aAAa,MAAM,CAAC,KACvC,SAAS,KAAK;AAClB,SAAO,WAAW,CAAC;AACvB;;;AClFA,IAAI;AAOJ,IAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,sBAAsB,QAAQ,GAAG,CAAC;AAC1F,IAAI,4BAA4B,IAAI,OAAO,GAAG,OAAO,sBAAsB,QAAQ,IAAI,CAAC;AACxF,SAAS,eAAe,OAAO,KAAK;AAChC,SAAO,EAAE,OAAc,IAAS;AACpC;AAGA,IAAI,sBAAsB,CAAC,CAAC,OAAO,UAAU,cAAc,KAAK,WAAW,KAAK,CAAC;AACjF,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,uBAAuB,CAAC,CAAC,OAAO;AACpC,IAAI,uBAAuB,CAAC,CAAC,OAAO,UAAU;AAC9C,IAAI,eAAe,CAAC,CAAC,OAAO,UAAU;AACtC,IAAI,aAAa,CAAC,CAAC,OAAO,UAAU;AACpC,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,gBAAgB,yBACd,OAAO,gBACP,SAAU,GAAG;AACX,SAAQ,OAAO,MAAM,YACjB,SAAS,CAAC,KACV,KAAK,MAAM,CAAC,MAAM,KAClB,KAAK,IAAI,CAAC,KAAK;AACvB;AAEJ,IAAI,yBAAyB;AAC7B,IAAI;AACI,OAAK,GAAG,6CAA6C,IAAI;AAO7D,6BAA2B,KAAK,GAAG,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO;AAClG,SACO,GAAG;AACN,2BAAyB;AAC7B;AAXQ;AAYR,IAAI,aAAa;AAAA;AAAA,EAET,SAASC,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,WAAW,QAAQ,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA,EAEA,SAASA,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,MAAM,UAAU,WAAW,OAAO,MAAM,MAAM;AAAA,EAC3D;AAAA;AACR,IAAI,gBAAgB,yBACd,OAAO;AAAA;AAAA,EAEL,SAASC,iBAAgB;AACrB,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,EAAE,IAAI,UAAU,EAAE;AAAA,IACjC;AACA,QAAI,WAAW;AACf,QAAI,SAAS,WAAW;AACxB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,SAAS,GAAG;AACf,aAAO,WAAW,GAAG;AACrB,UAAI,OAAO;AACP,cAAM,WAAW,OAAO,4BAA4B;AACxD,kBACI,OAAO,QACD,OAAO,aAAa,IAAI,IACxB,OAAO,eAAe,QAAQ,UAAY,MAAM,OAAS,OAAO,OAAS,KAAM;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AACR,IAAI;AAAA;AAAA,EAEJ,uBACM,OAAO;AAAA;AAAA,IAEL,SAASC,aAAY,SAAS;AAC1B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,YAAIC,MAAK,UAAU,EAAE,GAAG,IAAIA,IAAG,CAAC,GAAG,IAAIA,IAAG,CAAC;AAC3C,YAAI,CAAC,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AACR,IAAI,cAAc;AAAA;AAAA,EAEV,SAASC,aAAY,GAAG,OAAO;AAC3B,WAAO,EAAE,YAAY,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA,EAEA,SAASA,aAAY,GAAG,OAAO;AAC3B,QAAI,OAAO,EAAE;AACb,QAAI,QAAQ,KAAK,SAAS,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,EAAE,WAAW,KAAK;AAC9B,QAAI;AACJ,WAAO,QAAQ,SACX,QAAQ,SACR,QAAQ,MAAM,SACb,SAAS,EAAE,WAAW,QAAQ,CAAC,KAAK,SACrC,SAAS,QACP,SACE,QAAQ,SAAW,OAAO,SAAS,SAAU;AAAA,EACzD;AAAA;AACR,IAAI,YAAY;AAAA;AAAA,EAER,SAASC,WAAU,GAAG;AAClB,WAAO,EAAE,UAAU;AAAA,EACvB;AAAA;AAAA;AAAA,EAEA,SAASA,WAAU,GAAG;AAClB,WAAO,EAAE,QAAQ,6BAA6B,EAAE;AAAA,EACpD;AAAA;AACR,IAAI,UAAU;AAAA;AAAA,EAEN,SAASC,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA,EAEA,SAASA,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ,2BAA2B,EAAE;AAAA,EAClD;AAAA;AAER,SAAS,GAAG,GAAG,MAAM;AACjB,SAAO,IAAI,OAAO,GAAG,IAAI;AAC7B;AAEA,IAAI;AACJ,IAAI,wBAAwB;AAEpB,2BAAyB,GAAG,6CAA6C,IAAI;AACjF,2BAAyB,SAASC,wBAAuB,GAAG,OAAO;AAC/D,QAAIJ;AACJ,2BAAuB,YAAY;AACnC,QAAI,QAAQ,uBAAuB,KAAK,CAAC;AACzC,YAAQA,MAAK,MAAM,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAAA,EAC5D;AACJ,OACK;AAED,2BAAyB,SAASI,wBAAuB,GAAG,OAAO;AAC/D,QAAI,QAAQ,CAAC;AACb,WAAO,MAAM;AACT,UAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,UAAI,MAAM,UAAa,cAAc,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC5D;AAAA,MACJ;AACA,YAAM,KAAK,CAAC;AACZ,eAAS,KAAK,QAAU,IAAI;AAAA,IAChC;AACA,WAAO,cAAc,MAAM,QAAQ,KAAK;AAAA,EAC5C;AACJ;AAtBQ;AAuBR,IAAI;AAAA;AAAA,EAAwB,WAAY;AACpC,aAASC,QAAO,SAAS,SAAS;AAC9B,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,WAAK,UAAU;AACf,WAAK,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE;AAChD,WAAK,YAAY,CAAC,CAAC,QAAQ;AAC3B,WAAK,SAAS,QAAQ;AACtB,WAAK,sBAAsB,CAAC,CAAC,QAAQ;AACrC,WAAK,uBAAuB,CAAC,CAAC,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,UAAI,KAAK,OAAO,MAAM,GAAG;AACrB,cAAM,MAAM,8BAA8B;AAAA,MAC9C;AACA,aAAO,KAAK,aAAa,GAAG,IAAI,KAAK;AAAA,IACzC;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe,mBAAmB;AACtF,UAAI,WAAW,CAAC;AAChB,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,SAAS,KAAe;AACxB,cAAI,SAAS,KAAK,cAAc,cAAc,iBAAiB;AAC/D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,WACS,SAAS,OAAiB,eAAe,GAAG;AACjD;AAAA,QACJ,WACS,SAAS,OACb,kBAAkB,YAAY,kBAAkB,kBAAkB;AACnE,cAAI,WAAW,KAAK,cAAc;AAClC,eAAK,KAAK;AACV,mBAAS,KAAK;AAAA,YACV,MAAM,KAAK;AAAA,YACX,UAAU,eAAe,UAAU,KAAK,cAAc,CAAC;AAAA,UAC3D,CAAC;AAAA,QACL,WACS,SAAS,MACd,CAAC,KAAK,aACN,KAAK,KAAK,MAAM,IAClB;AACE,cAAI,mBAAmB;AACnB;AAAA,UACJ,OACK;AACD,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UACjH;AAAA,QACJ,WACS,SAAS,MACd,CAAC,KAAK,aACN,SAAS,KAAK,KAAK,KAAK,CAAC,GAAG;AAC5B,cAAI,SAAS,KAAK,SAAS,cAAc,aAAa;AACtD,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,OACK;AACD,cAAI,SAAS,KAAK,aAAa,cAAc,aAAa;AAC1D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,EAAE,KAAK,UAAU,KAAK,KAAK;AAAA,IACtC;AAmBA,IAAAA,QAAO,UAAU,WAAW,SAAU,cAAc,eAAe;AAC/D,UAAI,gBAAgB,KAAK,cAAc;AACvC,WAAK,KAAK;AACV,UAAI,UAAU,KAAK,aAAa;AAChC,WAAK,UAAU;AACf,UAAI,KAAK,OAAO,IAAI,GAAG;AAEnB,eAAO;AAAA,UACH,KAAK;AAAA,YACD,MAAM,KAAK;AAAA,YACX,OAAO,IAAI,OAAO,SAAS,IAAI;AAAA,YAC/B,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,UAChE;AAAA,UACA,KAAK;AAAA,QACT;AAAA,MACJ,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,IAAI;AAC5E,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,eAAe;AAE9B,YAAI,sBAAsB,KAAK,cAAc;AAC7C,YAAI,KAAK,OAAO,IAAI,GAAG;AACnB,cAAI,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG;AACxC,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,cAAI,8BAA8B,KAAK,cAAc;AACrD,cAAI,iBAAiB,KAAK,aAAa;AACvC,cAAI,YAAY,gBAAgB;AAC5B,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,6BAA6B,KAAK,cAAc,CAAC,CAAC;AAAA,UACxH;AACA,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA,cACX,OAAO;AAAA,cACP;AAAA,cACA,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,YAChE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ,OACK;AACD,iBAAO,KAAK,MAAM,UAAU,cAAc,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,QACjG;AAAA,MACJ,OACK;AACD,eAAO,KAAK,MAAM,UAAU,aAAa,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,MAChG;AAAA,IACJ;AAIA,IAAAA,QAAO,UAAU,eAAe,WAAY;AACxC,UAAI,cAAc,KAAK,OAAO;AAC9B,WAAK,KAAK;AACV,aAAO,CAAC,KAAK,MAAM,KAAK,4BAA4B,KAAK,KAAK,CAAC,GAAG;AAC9D,aAAK,KAAK;AAAA,MACd;AACA,aAAO,KAAK,QAAQ,MAAM,aAAa,KAAK,OAAO,CAAC;AAAA,IACxD;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe;AACnE,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,QAAQ;AACZ,aAAO,MAAM;AACT,YAAI,mBAAmB,KAAK,cAAc,aAAa;AACvD,YAAI,kBAAkB;AAClB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,sBAAsB,KAAK,iBAAiB,cAAc,aAAa;AAC3E,YAAI,qBAAqB;AACrB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,uBAAuB,KAAK,yBAAyB;AACzD,YAAI,sBAAsB;AACtB,mBAAS;AACT;AAAA,QACJ;AACA;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,OAAO,KAAK,cAAc,CAAC;AACzD,aAAO;AAAA,QACH,KAAK,EAAE,MAAM,KAAK,SAAS,OAAc,SAAmB;AAAA,QAC5D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,2BAA2B,WAAY;AACpD,UAAI,CAAC,KAAK,MAAM,KACZ,KAAK,KAAK,MAAM,OACf,KAAK;AAAA,MAEF,CAAC,gBAAgB,KAAK,KAAK,KAAK,CAAC,IAAI;AACzC,aAAK,KAAK;AACV,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAMA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,eAAe;AACtD,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAc;AAC9C,eAAO;AAAA,MACX;AAGA,cAAQ,KAAK,KAAK,GAAG;AAAA,QACjB,KAAK;AAED,eAAK,KAAK;AACV,eAAK,KAAK;AACV,iBAAO;AAAA,QAEX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ,KAAK;AACD,cAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;AACjE;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACf;AACA,WAAK,KAAK;AACV,UAAI,aAAa,CAAC,KAAK,KAAK,CAAC;AAC7B,WAAK,KAAK;AAEV,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,OAAO,IAAc;AACrB,cAAI,KAAK,KAAK,MAAM,IAAc;AAC9B,uBAAW,KAAK,EAAE;AAElB,iBAAK,KAAK;AAAA,UACd,OACK;AAED,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,QACJ,OACK;AACD,qBAAW,KAAK,EAAE;AAAA,QACtB;AACA,aAAK,KAAK;AAAA,MACd;AACA,aAAO,cAAc,MAAM,QAAQ,UAAU;AAAA,IACjD;AACA,IAAAA,QAAO,UAAU,mBAAmB,SAAU,cAAc,eAAe;AACvE,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,OAAO,MACP,OAAO,OACN,OAAO,OACH,kBAAkB,YAAY,kBAAkB,oBACpD,OAAO,OAAiB,eAAe,GAAI;AAC5C,eAAO;AAAA,MACX,OACK;AACD,aAAK,KAAK;AACV,eAAO,cAAc,EAAE;AAAA,MAC3B;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,cAAc,mBAAmB;AACxE,UAAI,uBAAuB,KAAK,cAAc;AAC9C,WAAK,KAAK;AACV,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,UAAI,KAAK,KAAK,MAAM,KAAe;AAC/B,aAAK,KAAK;AACV,eAAO,KAAK,MAAM,UAAU,gBAAgB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC1G;AAEA,UAAI,QAAQ,KAAK,0BAA0B,EAAE;AAC7C,UAAI,CAAC,OAAO;AACR,eAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC9G;AACA,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,cAAQ,KAAK,KAAK,GAAG;AAAA,QAEjB,KAAK,KAAe;AAChB,eAAK,KAAK;AACV,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA;AAAA,cAEX;AAAA,cACA,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,YACvE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA,QAEA,KAAK,IAAc;AACf,eAAK,KAAK;AACV,eAAK,UAAU;AACf,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACzH;AACA,iBAAO,KAAK,qBAAqB,cAAc,mBAAmB,OAAO,oBAAoB;AAAA,QACjG;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAClH;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,4BAA4B,WAAY;AACrD,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,cAAc,KAAK,OAAO;AAC9B,UAAI,QAAQ,uBAAuB,KAAK,SAAS,WAAW;AAC5D,UAAI,YAAY,cAAc,MAAM;AACpC,WAAK,OAAO,SAAS;AACrB,UAAI,cAAc,KAAK,cAAc;AACrC,UAAI,WAAW,eAAe,kBAAkB,WAAW;AAC3D,aAAO,EAAE,OAAc,SAAmB;AAAA,IAC9C;AACA,IAAAA,QAAO,UAAU,uBAAuB,SAAU,cAAc,mBAAmB,OAAO,sBAAsB;AAC5G,UAAIL;AAIJ,UAAI,oBAAoB,KAAK,cAAc;AAC3C,UAAI,UAAU,KAAK,0BAA0B,EAAE;AAC/C,UAAI,kBAAkB,KAAK,cAAc;AACzC,cAAQ,SAAS;AAAA,QACb,KAAK;AAED,iBAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,mBAAmB,eAAe,CAAC;AAAA,QACxG,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ;AAIT,eAAK,UAAU;AACf,cAAI,mBAAmB;AACvB,cAAI,KAAK,OAAO,GAAG,GAAG;AAClB,iBAAK,UAAU;AACf,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,SAAS,KAAK,8BAA8B;AAChD,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,gBAAI,QAAQ,QAAQ,OAAO,GAAG;AAC9B,gBAAI,MAAM,WAAW,GAAG;AACpB,qBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YACjH;AACA,gBAAI,gBAAgB,eAAe,oBAAoB,KAAK,cAAc,CAAC;AAC3E,+BAAmB,EAAE,OAAc,cAA6B;AAAA,UACpE;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAE1E,cAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,MAAM,CAAC,GAAG;AAErI,gBAAI,WAAW,UAAU,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACxD,gBAAI,YAAY,UAAU;AACtB,kBAAI,SAAS,KAAK,8BAA8B,UAAU,iBAAiB,aAAa;AACxF,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAM,KAAK,QAAQ,OAAc,UAAU,YAAY,OAAO,OAAO,IAAI;AAAA,gBAChF,KAAK;AAAA,cACT;AAAA,YACJ,OACK;AACD,kBAAI,SAAS,WAAW,GAAG;AACvB,uBAAO,KAAK,MAAM,UAAU,2BAA2B,UAAU;AAAA,cACrE;AACA,kBAAI,kBAAkB;AAItB,kBAAI,KAAK,QAAQ;AACb,kCAAkB,eAAe,UAAU,KAAK,MAAM;AAAA,cAC1D;AACA,kBAAI,QAAQ;AAAA,gBACR,MAAM,cAAc;AAAA,gBACpB,SAAS;AAAA,gBACT,UAAU,iBAAiB;AAAA,gBAC3B,eAAe,KAAK,uBACd,sBAAsB,eAAe,IACrC,CAAC;AAAA,cACX;AACA,kBAAI,OAAO,YAAY,SAAS,KAAK,OAAO,KAAK;AACjD,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAY,OAAc,UAAU,YAAY,MAAa;AAAA,gBACpE,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,YAAY,WACZ,KAAK,SACL,YAAY,SACR,KAAK,OACL,KAAK;AAAA,cACf;AAAA,cACA,UAAU;AAAA,cACV,QAAQA,MAAK,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,QAAQA,QAAO,SAASA,MAAK;AAAA,YAC9I;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,UAAU;AAIX,cAAI,oBAAoB,KAAK,cAAc;AAC3C,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,gCAAgC,eAAe,mBAAmB,SAAS,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAAA,UAClI;AACA,eAAK,UAAU;AASf,cAAI,wBAAwB,KAAK,0BAA0B;AAC3D,cAAI,eAAe;AACnB,cAAI,YAAY,YAAY,sBAAsB,UAAU,UAAU;AAClE,gBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,qBAAO,KAAK,MAAM,UAAU,qCAAqC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YAC/H;AACA,iBAAK,UAAU;AACf,gBAAI,SAAS,KAAK,uBAAuB,UAAU,qCAAqC,UAAU,oCAAoC;AACtI,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AAEA,iBAAK,UAAU;AACf,oCAAwB,KAAK,0BAA0B;AACvD,2BAAe,OAAO;AAAA,UAC1B;AACA,cAAI,gBAAgB,KAAK,8BAA8B,cAAc,SAAS,mBAAmB,qBAAqB;AACtH,cAAI,cAAc,KAAK;AACnB,mBAAO;AAAA,UACX;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAC1E,cAAI,YAAY,UAAU;AACtB,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ,OACK;AACD,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,QAAQ;AAAA,gBACR,YAAY,YAAY,WAAW,aAAa;AAAA,gBAChD,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,mBAAmB,eAAe,CAAC;AAAA,MAC7G;AAAA,IACJ;AACA,IAAAK,QAAO,UAAU,wBAAwB,SAAU,sBAAsB;AAGrE,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAe;AAC/C,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,WAAK,KAAK;AACV,aAAO,EAAE,KAAK,MAAM,KAAK,KAAK;AAAA,IAClC;AAIA,IAAAA,QAAO,UAAU,gCAAgC,WAAY;AACzD,UAAI,eAAe;AACnB,UAAI,gBAAgB,KAAK,cAAc;AACvC,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,gBAAQ,IAAI;AAAA,UACR,KAAK,IAAc;AAGf,iBAAK,KAAK;AACV,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,CAAC,KAAK,UAAU,GAAG,GAAG;AACtB,qBAAO,KAAK,MAAM,UAAU,kCAAkC,eAAe,oBAAoB,KAAK,cAAc,CAAC,CAAC;AAAA,YAC1H;AACA,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,4BAAgB;AAChB,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,gBAAI,eAAe,GAAG;AAClB,8BAAgB;AAAA,YACpB,OACK;AACD,qBAAO;AAAA,gBACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,gBAC3D,KAAK;AAAA,cACT;AAAA,YACJ;AACA;AAAA,UACJ;AAAA,UACA;AACI,iBAAK,KAAK;AACV;AAAA,QACR;AAAA,MACJ;AACA,aAAO;AAAA,QACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,QAC3D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,UAAU,UAAU;AAC3E,UAAI,SAAS,CAAC;AACd,UAAI;AACA,iBAAS,8BAA8B,QAAQ;AAAA,MACnD,SACO,GAAG;AACN,eAAO,KAAK,MAAM,UAAU,yBAAyB,QAAQ;AAAA,MACjE;AACA,aAAO;AAAA,QACH,KAAK;AAAA,UACD,MAAM,cAAc;AAAA,UACpB;AAAA,UACA;AAAA,UACA,eAAe,KAAK,uBACd,oBAAoB,MAAM,IAC1B,CAAC;AAAA,QACX;AAAA,QACA,KAAK;AAAA,MACT;AAAA,IACJ;AAWA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,cAAc,eAAe,gBAAgB,uBAAuB;AAC3H,UAAIL;AACJ,UAAI,iBAAiB;AACrB,UAAI,UAAU,CAAC;AACf,UAAI,kBAAkB,oBAAI,IAAI;AAC9B,UAAI,WAAW,sBAAsB,OAAO,mBAAmB,sBAAsB;AAIrF,aAAO,MAAM;AACT,YAAI,SAAS,WAAW,GAAG;AACvB,cAAI,gBAAgB,KAAK,cAAc;AACvC,cAAI,kBAAkB,YAAY,KAAK,OAAO,GAAG,GAAG;AAEhD,gBAAI,SAAS,KAAK,uBAAuB,UAAU,iCAAiC,UAAU,gCAAgC;AAC9H,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,+BAAmB,eAAe,eAAe,KAAK,cAAc,CAAC;AACrE,uBAAW,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,UACrE,OACK;AACD;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,qCACV,UAAU,oCAAoC,gBAAgB;AAAA,QACxE;AACA,YAAI,aAAa,SAAS;AACtB,2BAAiB;AAAA,QACrB;AAIA,aAAK,UAAU;AACf,YAAI,uBAAuB,KAAK,cAAc;AAC9C,YAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,2CACV,UAAU,0CAA0C,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,QACxH;AACA,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,cAAc;AACtF,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,gBAAQ,KAAK;AAAA,UACT;AAAA,UACA;AAAA,YACI,OAAO,eAAe;AAAA,YACtB,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,UACvE;AAAA,QACJ,CAAC;AAED,wBAAgB,IAAI,QAAQ;AAE5B,aAAK,UAAU;AACf,QAACA,MAAK,KAAK,0BAA0B,GAAG,WAAWA,IAAG,OAAO,mBAAmBA,IAAG;AAAA,MACvF;AACA,UAAI,QAAQ,WAAW,GAAG;AACtB,eAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,kCACV,UAAU,iCAAiC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAC/G;AACA,UAAI,KAAK,uBAAuB,CAAC,gBAAgB;AAC7C,eAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAChH;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAK,QAAO,UAAU,yBAAyB,SAAU,mBAAmB,oBAAoB;AACvF,UAAI,OAAO;AACX,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,KAAK,OAAO,GAAG,GAAG;AAAA,MACtB,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,eAAO;AAAA,MACX;AACA,UAAI,YAAY;AAChB,UAAI,UAAU;AACd,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,MAAM,MAAgB,MAAM,IAAc;AAC1C,sBAAY;AACZ,oBAAU,UAAU,MAAM,KAAK;AAC/B,eAAK,KAAK;AAAA,QACd,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,kBAAkB,KAAK,cAAc,CAAC;AACpE,UAAI,CAAC,WAAW;AACZ,eAAO,KAAK,MAAM,mBAAmB,QAAQ;AAAA,MACjD;AACA,iBAAW;AACX,UAAI,CAAC,cAAc,OAAO,GAAG;AACzB,eAAO,KAAK,MAAM,oBAAoB,QAAQ;AAAA,MAClD;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,aAAO,KAAK,SAAS;AAAA,IACzB;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,aAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,gBAAgB,WAAY;AAEzC,aAAO;AAAA,QACH,QAAQ,KAAK,SAAS;AAAA,QACtB,MAAM,KAAK,SAAS;AAAA,QACpB,QAAQ,KAAK,SAAS;AAAA,MAC1B;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,SAAS,KAAK,SAAS;AAC3B,UAAI,UAAU,KAAK,QAAQ,QAAQ;AAC/B,cAAM,MAAM,cAAc;AAAA,MAC9B;AACA,UAAI,OAAO,YAAY,KAAK,SAAS,MAAM;AAC3C,UAAI,SAAS,QAAW;AACpB,cAAM,MAAM,UAAU,OAAO,QAAQ,0CAA0C,CAAC;AAAA,MACpF;AACA,aAAO;AAAA,IACX;AACA,IAAAA,QAAO,UAAU,QAAQ,SAAU,MAAM,UAAU;AAC/C,aAAO;AAAA,QACH,KAAK;AAAA,QACL,KAAK;AAAA,UACD;AAAA,UACA,SAAS,KAAK;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd;AAAA,MACJ;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,IAAe;AACxB,aAAK,SAAS,QAAQ;AACtB,aAAK,SAAS,SAAS;AACvB,aAAK,SAAS,UAAU;AAAA,MAC5B,OACK;AACD,aAAK,SAAS,UAAU;AAExB,aAAK,SAAS,UAAU,OAAO,QAAU,IAAI;AAAA,MACjD;AAAA,IACJ;AAOA,IAAAA,QAAO,UAAU,SAAS,SAAU,QAAQ;AACxC,UAAI,WAAW,KAAK,SAAS,QAAQ,KAAK,OAAO,CAAC,GAAG;AACjD,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,eAAK,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAKA,IAAAA,QAAO,UAAU,YAAY,SAAU,SAAS;AAC5C,UAAI,gBAAgB,KAAK,OAAO;AAChC,UAAI,QAAQ,KAAK,QAAQ,QAAQ,SAAS,aAAa;AACvD,UAAI,SAAS,GAAG;AACZ,aAAK,OAAO,KAAK;AACjB,eAAO;AAAA,MACX,OACK;AACD,aAAK,OAAO,KAAK,QAAQ,MAAM;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,SAAS,SAAU,cAAc;AAC9C,UAAI,KAAK,OAAO,IAAI,cAAc;AAC9B,cAAM,MAAM,gBAAgB,OAAO,cAAc,uDAAuD,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MACnI;AACA,qBAAe,KAAK,IAAI,cAAc,KAAK,QAAQ,MAAM;AACzD,aAAO,MAAM;AACT,YAAI,SAAS,KAAK,OAAO;AACzB,YAAI,WAAW,cAAc;AACzB;AAAA,QACJ;AACA,YAAI,SAAS,cAAc;AACvB,gBAAM,MAAM,gBAAgB,OAAO,cAAc,0CAA0C,CAAC;AAAA,QAChG;AACA,aAAK,KAAK;AACV,YAAI,KAAK,MAAM,GAAG;AACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,YAAY,WAAY;AACrC,aAAO,CAAC,KAAK,MAAM,KAAK,cAAc,KAAK,KAAK,CAAC,GAAG;AAChD,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,WAAW,KAAK,QAAQ,WAAW,UAAU,QAAQ,QAAU,IAAI,EAAE;AACzE,aAAO,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IACjE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAOF,SAAS,SAAS,WAAW;AACzB,SAAS,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AACzC;AACA,SAAS,gBAAgB,WAAW;AAChC,SAAO,SAAS,SAAS,KAAK,cAAc;AAChD;AAEA,SAAS,4BAA4B,GAAG;AACpC,SAAQ,MAAM,MACV,MAAM,MACL,KAAK,MAAM,KAAK,MACjB,MAAM,MACL,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,OACJ,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAS,KAAK,QACnB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAW,KAAK;AAC9B;AAKA,SAAS,cAAc,GAAG;AACtB,SAAS,KAAK,KAAU,KAAK,MACzB,MAAM,MACN,MAAM,OACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM;AACd;AAKA,SAAS,iBAAiB,GAAG;AACzB,SAAS,KAAK,MAAU,KAAK,MACzB,MAAM,MACL,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACL,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,OAAU,KAAK,OACrB,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,OACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK;AAC7B;;;ACvvCA,SAAS,cAAc,KAAK;AACxB,MAAI,QAAQ,SAAU,IAAI;AACtB,WAAO,GAAG;AACV,QAAI,gBAAgB,EAAE,KAAK,gBAAgB,EAAE,GAAG;AAC5C,eAAS,KAAK,GAAG,SAAS;AACtB,eAAO,GAAG,QAAQ,CAAC,EAAE;AACrB,sBAAc,GAAG,QAAQ,CAAC,EAAE,KAAK;AAAA,MACrC;AAAA,IACJ,WACS,gBAAgB,EAAE,KAAK,iBAAiB,GAAG,KAAK,GAAG;AACxD,aAAO,GAAG,MAAM;AAAA,IACpB,YACU,cAAc,EAAE,KAAK,cAAc,EAAE,MAC3C,mBAAmB,GAAG,KAAK,GAAG;AAC9B,aAAO,GAAG,MAAM;AAAA,IACpB,WACS,aAAa,EAAE,GAAG;AACvB,oBAAc,GAAG,QAAQ;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AACO,SAAS,MAAM,SAAS,MAAM;AACjC,MAAI,SAAS,QAAQ;AAAE,WAAO,CAAC;AAAA,EAAG;AAClC,SAAO,SAAS,EAAE,sBAAsB,MAAM,qBAAqB,KAAK,GAAG,IAAI;AAC/E,MAAI,SAAS,IAAI,OAAO,SAAS,IAAI,EAAE,MAAM;AAC7C,MAAI,OAAO,KAAK;AACZ,QAAI,QAAQ,YAAY,UAAU,OAAO,IAAI,IAAI,CAAC;AAElD,UAAM,WAAW,OAAO,IAAI;AAE5B,UAAM,kBAAkB,OAAO,IAAI;AACnC,UAAM;AAAA,EACV;AACA,MAAI,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,kBAAkB;AACrE,kBAAc,OAAO,GAAG;AAAA,EAC5B;AACA,SAAO,OAAO;AAClB;;;ACxCO,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,kBAAkB,IAAI;AACpC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,aAAY,KAAK,MAAM,iBAAiB;AAC7C,UAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,KAAK;AACtC,YAAM,OAAO;AACb,YAAM,kBAAkB;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,aAAO,oBAAoB,OAAO,KAAK,MAAM,IAAI,EAAE,OAAO,KAAK,OAAO;AAAA,IAC1E;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,OAAO,SAAS,iBAAiB;AACpE,aAAO,OAAO,KAAK,MAAM,uBAAwB,OAAO,YAAY,MAAQ,EAAE,OAAO,OAAO,kBAAoB,EAAE,OAAO,OAAO,KAAK,OAAO,EAAE,KAAK,MAAM,GAAG,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IACpN;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,uBAAsB,OAAO,MAAM,iBAAiB;AACzD,aAAO,OAAO,KAAK,MAAM,cAAe,OAAO,OAAO,oBAAqB,EAAE,OAAO,IAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC5I;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,iBAAiB;AACpD,aAAO,OAAO,KAAK,MAAM,qCAAsC,OAAO,YAAY,oCAAsC,EAAE,OAAO,iBAAiB,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC1M;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;;;AC5CN,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,SAAS,aAAa,OAAO;AACzB,MAAI,MAAM,SAAS,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACrC,QAAI,WAAW,IAAI,IAAI,SAAS,CAAC;AACjC,QAAI,CAAC,YACD,SAAS,SAAS,UAAU,WAC5B,KAAK,SAAS,UAAU,SAAS;AACjC,UAAI,KAAK,IAAI;AAAA,IACjB,OACK;AACD,eAAS,SAAS,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACO,SAAS,qBAAqB,IAAI;AACrC,SAAO,OAAO,OAAO;AACzB;AAEO,SAAS,cAAc,KAAK,SAAS,YAAY,SAAS,QAAQ,oBAEzE,iBAAiB;AAEb,MAAI,IAAI,WAAW,KAAK,iBAAiB,IAAI,CAAC,CAAC,GAAG;AAC9C,WAAO;AAAA,MACH;AAAA,QACI,MAAM,UAAU;AAAA,QAChB,OAAO,IAAI,CAAC,EAAE;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,QAAQ,MAAM;AACnD,QAAI,KAAK,MAAM,EAAE;AAEjB,QAAI,iBAAiB,EAAE,GAAG;AACtB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACJ;AAGA,QAAI,eAAe,EAAE,GAAG;AACpB,UAAI,OAAO,uBAAuB,UAAU;AACxC,eAAO,KAAK;AAAA,UACR,MAAM,UAAU;AAAA,UAChB,OAAO,WAAW,gBAAgB,OAAO,EAAE,OAAO,kBAAkB;AAAA,QACxE,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,QAAI,UAAU,GAAG;AAEjB,QAAI,EAAE,UAAU,WAAW,SAAS;AAChC,YAAM,IAAI,kBAAkB,SAAS,eAAe;AAAA,IACxD;AACA,QAAI,QAAQ,OAAO,OAAO;AAC1B,QAAI,kBAAkB,EAAE,GAAG;AACvB,UAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAClE,gBACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,KAAK,IACZ;AAAA,MACd;AACA,aAAO,KAAK;AAAA,QACR,MAAM,OAAO,UAAU,WAAW,UAAU,UAAU,UAAU;AAAA,QAChE;AAAA,MACJ,CAAC;AACD;AAAA,IACJ;AAIA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT;AACV,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT,QAAQ,KAAK;AACvB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,OAAO,GAAG,KAAK,IACvB,iBAAiB,GAAG,KAAK,IACrB,GAAG,MAAM,gBACT;AACV,UAAI,SAAS,MAAM,OAAO;AACtB,gBACI,SACK,MAAM,SAAS;AAAA,MAC5B;AACA,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,gBAAgB,SAAS,KAAK,EAC9B,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,aAAa,EAAE,GAAG;AAClB,UAAI,WAAW,GAAG,UAAU,UAAU,GAAG;AACzC,UAAI,WAAW,OAAO,OAAO;AAC7B,UAAI,CAAC,qBAAqB,QAAQ,GAAG;AACjC,cAAM,IAAI,sBAAsB,SAAS,YAAY,eAAe;AAAA,MACxE;AACA,UAAI,QAAQ,cAAc,UAAU,SAAS,YAAY,SAAS,QAAQ,kBAAkB;AAC5F,UAAI,SAAS,SAAS,MAAM,IAAI,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAO,CAAC,CAAC;AACjE,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,iBAAS,CAAC,MAAM;AAAA,MACpB;AACA,aAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,SAAU,GAAG;AAC9C,eAAO;AAAA,UACH,MAAM,OAAO,MAAM,WAAW,UAAU,UAAU,UAAU;AAAA,UAC5D,OAAO;AAAA,QACX;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ;AAC1C,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM,CAAC;AACxF;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AACtC,UAAI,CAAC,KAAK;AACN,YAAI,CAAC,KAAK,aAAa;AACnB,gBAAM,IAAI,YAAY,mHAAqH,UAAU,kBAAkB,eAAe;AAAA,QAC1L;AACA,YAAI,OAAO,WACN,eAAe,SAAS,EAAE,MAAM,GAAG,WAAW,CAAC,EAC/C,OAAO,SAAS,GAAG,UAAU,EAAE;AACpC,cAAM,GAAG,QAAQ,IAAI,KAAK,GAAG,QAAQ;AAAA,MACzC;AACA,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,GAAG,UAAU,EAAE,CAAC;AAClH;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,aAAa,MAAM;AAC9B;;;ACtKA,SAAS,YAAY,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI;AACL,WAAO;AAAA,EACX;AACA,SAAO,SAAS,SAAS,SAAS,CAAC,GAAI,MAAM,CAAC,CAAE,GAAI,MAAM,CAAC,CAAE,GAAG,OAAO,KAAK,EAAE,EAAE,OAAO,SAAU,KAAK,GAAG;AACrG,QAAI,CAAC,IAAI,SAAS,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC,KAAK,CAAC,CAAE;AACpD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,CAAC;AACV;AACA,SAAS,aAAa,eAAe,SAAS;AAC1C,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,QAAI,CAAC,IAAI,YAAY,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,WAAO;AAAA,EACX,GAAG,SAAS,CAAC,GAAG,aAAa,CAAC;AAClC;AACA,SAAS,uBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,QAAQ,WAAY;AAChB,aAAO;AAAA,QACH,KAAK,SAAU,KAAK;AAChB,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AACvB,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,wBAAwB,OAAO;AACpC,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,MAC5B,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,MACX,aAAa,CAAC;AAAA,IAClB;AAAA,EAAG;AACH,SAAO;AAAA,IACH,iBAAiB,QAAQ,WAAY;AACjC,UAAIC;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC/F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,MAAM;AAAA,MAC1C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,mBAAmB,QAAQ,WAAY;AACnC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACjG,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,QAAQ;AAAA,MAC5C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,QAAQ,WAAY;AAChC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC9F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,WAAW;AAAA,MAC/C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS,SAAS,iBAAiB,MAAM;AAChE,UAAI,YAAY,QAAQ;AAAE,kBAAUA,mBAAkB;AAAA,MAAe;AACrE,UAAI,QAAQ;AACZ,WAAK,iBAAiB;AAAA,QAClB,QAAQ,CAAC;AAAA,QACT,UAAU,CAAC;AAAA,QACX,aAAa,CAAC;AAAA,MAClB;AACA,WAAK,SAAS,SAAU,QAAQ;AAC5B,YAAI,QAAQ,MAAM,cAAc,MAAM;AAEtC,YAAI,MAAM,WAAW,GAAG;AACpB,iBAAO,MAAM,CAAC,EAAE;AAAA,QACpB;AACA,YAAI,SAAS,MAAM,OAAO,SAAU,KAAK,MAAM;AAC3C,cAAI,CAAC,IAAI,UACL,KAAK,SAAS,UAAU,WACxB,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AACzC,gBAAI,KAAK,KAAK,KAAK;AAAA,UACvB,OACK;AACD,gBAAI,IAAI,SAAS,CAAC,KAAK,KAAK;AAAA,UAChC;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,YAAI,OAAO,UAAU,GAAG;AACpB,iBAAO,OAAO,CAAC,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,MACX;AACA,WAAK,gBAAgB,SAAU,QAAQ;AACnC,eAAO,cAAc,MAAM,KAAK,MAAM,SAAS,MAAM,YAAY,MAAM,SAAS,QAAQ,QAAW,MAAM,OAAO;AAAA,MACpH;AACA,WAAK,kBAAkB,WAAY;AAC/B,YAAID;AACJ,eAAQ;AAAA,UACJ,UAAUA,MAAK,MAAM,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,MAClF,KAAK,aAAa,mBAAmB,MAAM,OAAO,EAAE,CAAC;AAAA,QAC7D;AAAA,MACJ;AACA,WAAK,SAAS,WAAY;AAAE,eAAO,MAAM;AAAA,MAAK;AAE9C,WAAK,UAAU;AACf,WAAK,iBAAiBC,mBAAkB,cAAc,OAAO;AAC7D,UAAI,OAAO,YAAY,UAAU;AAC7B,aAAK,UAAU;AACf,YAAI,CAACA,mBAAkB,SAAS;AAC5B,gBAAM,IAAI,UAAU,6EAA6E;AAAA,QACrG;AACA,YAAID,MAAK,QAAQ,CAAC,GAAG,aAAaA,IAAG,YAAY,YAAY,OAAOA,KAAI,CAAC,YAAY,CAAC;AAEtF,aAAK,MAAMC,mBAAkB,QAAQ,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,QAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,MACpH,OACK;AACD,aAAK,MAAM;AAAA,MACf;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,GAAG;AAC1B,cAAM,IAAI,UAAU,gDAAgD;AAAA,MACxE;AAGA,WAAK,UAAU,aAAaA,mBAAkB,SAAS,eAAe;AACtE,WAAK,aACA,QAAQ,KAAK,cAAe,wBAAwB,KAAK,cAAc;AAAA,IAChF;AACA,WAAO,eAAeA,oBAAmB,iBAAiB;AAAA,MACtD,KAAK,WAAY;AACb,YAAI,CAACA,mBAAkB,uBAAuB;AAC1C,UAAAA,mBAAkB,wBACd,IAAI,KAAK,aAAa,EAAE,gBAAgB,EAAE;AAAA,QAClD;AACA,eAAOA,mBAAkB;AAAA,MAC7B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,wBAAwB;AAC1C,IAAAA,mBAAkB,gBAAgB,SAAU,SAAS;AACjD,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC;AAAA,MACJ;AACA,UAAI,mBAAmB,KAAK,aAAa,mBAAmB,OAAO;AACnE,UAAI,iBAAiB,SAAS,GAAG;AAC7B,eAAO,IAAI,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,MAC9C;AACA,aAAO,IAAI,KAAK,OAAO,OAAO,YAAY,WAAW,UAAU,QAAQ,CAAC,CAAC;AAAA,IAC7E;AACA,IAAAA,mBAAkB,UAAU;AAI5B,IAAAA,mBAAkB,UAAU;AAAA,MACxB,QAAQ;AAAA,QACJ,SAAS;AAAA,UACL,uBAAuB;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA,SAAS;AAAA,UACL,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACJ,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,SAAS;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACZ;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;;;;AC1NF,IAAM,qCAAe,OAAO,IAAI,wBAAA;AAChC,IAAM,sCAAgB,OAAO,IAAI,yBAAA;;;;;;;;;ACOjC,IAAM,mDAA6B;EAAC;EAAS;EAAW;;AACxD,IAAM,+CAAyB;EAAC;EAAS;EAAU;EAAS;KAAW;;;;ACJvE,IAAM,mCAAa;AACnB,IAAM,kCAAa,KAAK;AAKxB,IAAM,mCAAa;AACnB,IAAM,oCAAc,KAAK,mCAAa;AACtC,IAAM,oCAAc,mCAAa,kCAAY;;;;;;ACjB7C,IAAI,4CAAsB;AAC1B,IAAI;AACF,8CAAuB,IAAI,KAAK,aAAa,SAAS;IAAC,aAAa;EAAY,CAAA,EAAI,gBAAe,EAAG,gBAAgB;AAExH,QAAQ;AAAC;AAET,IAAI,qCAAe;AACnB,IAAI;AACF,uCAAgB,IAAI,KAAK,aAAa,SAAS;IAAC,OAAO;IAAQ,MAAM;EAAQ,CAAA,EAAI,gBAAe,EAAG,UAAU;AAE/G,QAAQ;AAAC;;;ACAT,IAAM,4CAAsB,IAAI,OAAO,gBAAA;;;;;;;;;ACiDvC,IAAM,6BAAO;EACX,KAAK;EACL,QAAQ;EACR,MAAM;EACN,OAAO;AACT;AAEA,IAAM,0CAAoB;EACxB,KAAK;EACL,QAAQ;EACR,MAAM;EACN,OAAO;AACT;AAEA,IAAM,mCAAa;EACjB,KAAK;EACL,MAAM;AACR;AAEA,IAAM,kCAAY;EAChB,KAAK;EACL,MAAM;AACR;AAEA,IAAM,mCAAa;EACjB,OAAO;EACP,QAAQ;AACV;AAEA,IAAM,+CAAyB,CAAC;AAEhC,IAAI,uCAAiB,OAAO,aAAa,cAAc,OAAO,iBAAiB;AAE/E,SAAS,6CAAuB,eAAsB;AACpD,MAAI,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,cAAc,GAAG,MAAM,GAAG,OAAO;AAC5E,MAAI,SAAmB,CAAC;MACD;AAAvB,MAAI,oBAAmB,wBAAA,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,WAAK,QAArB,0BAAA,SAAA,wBAAyB,KAAK;AAErD,MAAI,cAAc,YAAY,QAAQ;AACpC,QAAI,kBAAkB,SAAS;AAC/B,iBAAa,gBAAgB;AAC7B,kBAAc,gBAAgB;QACtB;AAAR,aAAQ,wBAAA,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,WAAK,QAArB,0BAAA,SAAA,wBAAyB;QACxB;AAAT,cAAS,yBAAA,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,YAAM,QAAtB,2BAAA,SAAA,yBAA0B;AACnC,WAAO,MAAM,gBAAgB,aAAa,cAAc;AACxD,WAAO,OAAO,gBAAgB,cAAc,cAAc;AAK1D,QAAI,sCAAgB;AAClB,YAAM,qCAAe;AACrB,aAAO,qCAAe;IACxB;EACF,OAAO;AACJ,KAAA,EAAA,OAAM,QAAQ,KAAK,KAAM,IAAI,gCAAU,aAAA;AACxC,WAAO,MAAM,cAAc;AAC3B,WAAO,OAAO,cAAc;AAC5B,iBAAa;AACb,kBAAc;EAChB;AAEA,OAAI,GAAA,2CAAO,MAAQ,cAAc,YAAY,UAAU,cAAc,YAAY,WAAW,iBAAiB;AAK3G,WAAO,MAAM;AACb,WAAO,OAAO;QACR;AAAN,WAAM,0BAAA,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,aAAO,QAAvB,4BAAA,SAAA,0BAA2B;QAC1B;AAAP,YAAO,2BAAA,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,cAAQ,QAAxB,6BAAA,SAAA,2BAA4B;EACrC;AAEA,SAAO;;;;;;;;EAA0D;AACnE;AAEA,SAAS,gCAAU,MAAa;AAC9B,SAAO;IACL,KAAK,KAAK;IACV,MAAM,KAAK;IACX,OAAO,KAAK;IACZ,QAAQ,KAAK;EACf;AACF;AAGA,SAAS,+BACP,MACA,QACA,MAGA,oBAMA,qBACA,SACA,6BAAmC;MAEb;AAAtB,MAAI,mBAAkB,mCAAA,oBAAoB,OAAO,IAAA,OAAK,QAAhC,qCAAA,SAAA,mCAAoC;AAE1D,MAAI,eAAe,mBAAmB,gCAAU,IAAA,CAAK;AAGrD,MAAI,oBAAoB,mBAAmB,OAAO,2BAAK,IAAA,CAAK,IAAI;AAChE,MAAI,kBAAkB,eAAe,mBAAmB,OAAO,2BAAK,IAAA,CAAK,IAAI;AAC7E,MAAI,kBAAkB,SAAS,kBAAkB,4BAA4B,IAAA,IAAQ,mBAAmB,2BAAK,IAAA,CAAK;AAClH,MAAI,gBAAgB,SAAS,kBAAkB,OAAO,4BAA4B,IAAA,IAAQ,mBAAmB,2BAAK,IAAA,CAAK;AAIvH,MAAI,kBAAkB;AACpB,WAAO,oBAAoB;WAClB,gBAAgB;AACzB,WAAO,KAAK,IAAI,kBAAkB,eAAe,oBAAoB,eAAA;;AAErE,WAAO;AAEX;AAEA,SAAS,iCAAW,MAAa;AAC/B,MAAI,QAAQ,OAAO,iBAAiB,IAAA;AACpC,SAAO;IACL,KAAK,SAAS,MAAM,WAAW,EAAA,KAAO;IACtC,QAAQ,SAAS,MAAM,cAAc,EAAA,KAAO;IAC5C,MAAM,SAAS,MAAM,YAAY,EAAA,KAAO;IACxC,OAAO,SAAS,MAAM,aAAa,EAAA,KAAO;EAC5C;AACF;AAEA,SAAS,qCAAe,OAAgB;AACtC,MAAI,6CAAuB,KAAA;AACzB,WAAO,6CAAuB,KAAA;AAGhC,MAAI,CAAC,WAAW,cAAA,IAAkB,MAAM,MAAM,GAAA;AAC9C,MAAI,OAAa,2BAAK,SAAA,KAAc;AACpC,MAAI,YAAkB,iCAAW,IAAA;AAEjC,MAAI,CAAC,2BAAK,cAAA;AACR,qBAAiB;AAGnB,MAAI,OAAO,gCAAU,IAAA;AACrB,MAAI,YAAY,gCAAU,SAAA;AAC1B,+CAAuB,KAAA,IAAS;;;;;;;EAA4D;AAC5F,SAAO,6CAAuB,KAAA;AAChC;AAEA,SAAS,sCACP,aACA,oBACA,aACA,eACA,QACA,aACA,6BACA,uBACA,WACA,qBAA2B;AAE3B,MAAI,EAAA,WAAU,gBAAgB,MAAM,WAAW,MAAM,UAAW,IAAI;AACpE,MAAI,WAAqB,CAAC;MAGJ;AAAtB,WAAS,SAAA,KAAa,yBAAA,YAAY,SAAA,OAAU,QAAtB,2BAAA,SAAA,yBAA0B;MAIpB,wBAAgC,wBAIjC,yBAAgC;AAP3D,MAAI,mBAAmB;AAGrB,aAAS,SAAA,QAAiB,yBAAA,YAAY,SAAA,OAAU,QAAtB,2BAAA,SAAA,yBAA0B,OAAM,yBAAA,YAAY,SAAA,OAAU,QAAtB,2BAAA,SAAA,yBAA0B,MAAM;WACjF,mBAAmB;AAG5B,aAAS,SAAA,OAAgB,0BAAA,YAAY,SAAA,OAAU,QAAtB,4BAAA,SAAA,0BAA0B,OAAM,0BAAA,YAAY,SAAA,OAAU,QAAtB,4BAAA,SAAA,0BAA0B;AAKrF,WAAS,SAAA,KAAe;AAGxB,QAAM,cAAc,YAAY,SAAA,IAAa,YAAY,SAAA,IAAa,YAAY;AAElF,QAAM,cAAc,YAAY,SAAA,IAAa,YAAY,SAAA,IAAa,YAAY;AAClF,WAAS,SAAA,KAAa,GAAA,2CAAM,SAAS,SAAA,GAAa,aAAa,WAAA;AAG/D,MAAI,cAAc,MAAM;AAKtB,UAAM,kBAAmB,wBAAwB,4BAA4B,IAAA,IAAQ,mBAAmB,iCAAW,IAAA,CAAK;AACxH,aAAS,wCAAkB,IAAA,CAAK,IAAI,KAAK,MAAM,kBAAkB,YAAY,IAAA,IAAQ,MAAA;EACvF;AACE,aAAS,IAAA,IAAQ,KAAK,MAAM,YAAY,IAAA,IAAQ,YAAY,IAAA,IAAQ,MAAA;AAEtE,SAAO;AACT;AAEA,SAAS,mCACP,UACA,oBACA,6BACA,uBACA,SACA,SACA,eACA,uBAA4C;AAE5C,QAAM,kBAAmB,wBAAwB,4BAA4B,SAAS,mBAAmB,iCAAW,MAAM;MAGqB;AAA/I,MAAI,aAAa,SAAS,OAAO,OAAO,4BAA4B,MAAM,SAAS,MAAM,4BAA4B,OAAO,oBAAmB,mBAAA,SAAS,YAAM,QAAf,qBAAA,SAAA,mBAAmB,KAAK;MAI5G,gCAEnD,cAAqB,iBAKI,iCACzB,eAAqB;AAX7B,MAAI,YAAY,0BAA0B;;IAExC,KAAK,IAAI,GACN,mBAAmB,SAAS,mBAAmB,QAAO,iCAAA,mBAAmB,OAAO,SAAG,QAA7B,mCAAA,SAAA,iCAAiC,KACtF,gBACE,eAAA,QAAQ,SAAG,QAAX,iBAAA,SAAA,eAAe,OAAM,kBAAA,QAAQ,YAAM,QAAd,oBAAA,SAAA,kBAAkB,KAAK,QAAM;MAGtD,KAAK,IAAI,GACR,aAAa,iBACX,mBAAmB,QAAO,kCAAA,mBAAmB,OAAO,SAAG,QAA7B,oCAAA,SAAA,kCAAiC,SAC1D,gBAAA,QAAQ,SAAG,QAAX,kBAAA,SAAA,gBAAe,OAAM,mBAAA,QAAQ,YAAM,QAAd,qBAAA,SAAA,mBAAkB,KAAK,QAAM;AAE1D,SAAO,KAAK,IAAI,mBAAmB,SAAU,UAAU,GAAI,SAAA;AAC7D;AAEA,SAAS,wCACP,oBACA,6BACA,aACA,SACA,SACA,eAA8B;AAE9B,MAAI,EAAA,WAAU,MAAM,KAAM,IAAI;MAEuC,iCAA6E;AADlJ,MAAI,cAAc;AAChB,WAAO,KAAK,IAAI,GAAG,YAAY,IAAA,IAAQ,mBAAmB,IAAA,MAAS,kCAAA,mBAAmB,OAAO,IAAA,OAAK,QAA/B,oCAAA,SAAA,kCAAmC,KAAK,4BAA4B,IAAA,MAAS,gBAAA,QAAQ,IAAA,OAAK,QAAb,kBAAA,SAAA,gBAAiB,KAAK,QAAQ,wCAAkB,IAAA,CAAK,IAAI,OAAA;MAGnB;AAAxL,SAAO,KAAK,IAAI,GAAG,mBAAmB,IAAA,IAAQ,mBAAmB,IAAA,IAAQ,mBAAmB,OAAO,IAAA,IAAQ,4BAA4B,IAAA,IAAQ,YAAY,IAAA,IAAQ,YAAY,IAAA,MAAS,iBAAA,QAAQ,IAAA,OAAK,QAAb,mBAAA,SAAA,iBAAiB,KAAK,QAAQ,wCAAkB,IAAA,CAAK,IAAI,OAAA;AACnP;AAEO,SAAS,0CACd,gBACA,aACA,aACA,YACA,SACA,SACA,MACA,oBACA,qBACA,6BACA,QACA,aACA,uBACA,kBACA,WACA,qBAA2B;AAE3B,MAAI,gBAAgB,qCAAe,cAAA;AACnC,MAAI,EAAA,MAAK,WAAW,WAAW,WAAW,eAAgB,IAAI;AAC9D,MAAI,WAAW,sCAAgB,aAAa,oBAAoB,aAAa,eAAe,QAAQ,aAAa,6BAA6B,uBAAuB,WAAW,mBAAA;AAChL,MAAI,mBAAmB;AACvB,MAAI,QAAQ,wCACV,oBACA,6BACA,aACA,SACA,UAAU,QACV,aAAA;AAIF,MAAI,QAAQ,WAAW,IAAA,IAAQ,OAAO;AACpC,QAAI,uBAAuB,qCAAe,GAAG,wCAAkB,SAAA,CAAU,IAAI,cAAA,EAAgB;AAC7F,QAAI,kBAAkB,sCAAgB,aAAa,oBAAoB,aAAa,sBAAsB,QAAQ,aAAa,6BAA6B,uBAAuB,WAAW,mBAAA;AAC9L,QAAI,eAAe,wCACjB,oBACA,6BACA,aACA,SACA,UAAU,QACV,oBAAA;AAIF,QAAI,eAAe,OAAO;AACxB,sBAAgB;AAChB,iBAAW;AACX,yBAAmB;IACrB;EACF;AAGA,MAAI,wBAA+C;AACnD,MAAI,cAAc,SAAS,OAAO;AAChC,QAAI,cAAc,cAAc;AAC9B,8BAAwB;aACf,cAAc,cAAc;AACrC,8BAAwB;EAE5B,WAAW,cAAc,cAAc,OAAO;AAC5C,QAAI,cAAc,mBAAmB;AACnC,8BAAwB;aACf,cAAc,mBAAmB;AAC1C,8BAAwB;EAE5B;AAEA,MAAI,QAAQ,+BAAS,WAAW,SAAS,SAAA,GAAa,YAAY,SAAA,GAAY,oBAAoB,qBAAqB,SAAS,2BAAA;AAChI,WAAS,SAAA,KAAe;AAExB,MAAI,YAAY,mCACd,UACA,oBACA,6BACA,uBACA,SACA,SACA,YAAY,QACZ,qBAAA;AAGF,MAAI,oBAAoB,mBAAmB;AACzC,gBAAY;AAGd,cAAY,SAAS,KAAK,IAAI,YAAY,QAAQ,SAAA;AAElD,aAAW,sCAAgB,aAAa,oBAAoB,aAAa,eAAe,kBAAkB,aAAa,6BAA6B,uBAAuB,WAAW,mBAAA;AACtL,UAAQ,+BAAS,WAAW,SAAS,SAAA,GAAa,YAAY,SAAA,GAAY,oBAAoB,qBAAqB,SAAS,2BAAA;AAC5H,WAAS,SAAA,KAAe;AAExB,MAAI,gBAA0B,CAAC;AAO/B,MAAI,yBAAyB,YAAY,SAAA,IAAa,MAAK,YAAY,SAAA,IAAa,SAAS,SAAA,IAAc,QAAQ,2BAAK,SAAA,CAAU;AAGlI,QAAM,mBAAmB,YAAY,IAAI;MAEW,eAAsB,gBAAuB,cAAqB;AAAtH,QAAM,gBAAgB,2BAAK,SAAA,MAAe,WAAU,gBAAA,QAAQ,UAAI,QAAZ,kBAAA,SAAA,gBAAgB,OAAM,iBAAA,QAAQ,WAAK,QAAb,mBAAA,SAAA,iBAAiB,OAAM,eAAA,QAAQ,SAAG,QAAX,iBAAA,SAAA,eAAe,OAAM,kBAAA,QAAQ,YAAM,QAAd,oBAAA,SAAA,kBAAkB;AACxI,QAAM,mBAAmB,YAAY,SAAA,IAAa,gBAAiB,YAAY,IAAK;AAIpF,QAAM,+BAA+B,YAAY,SAAA,IAAc,YAAY,KAAM,SAAS,SAAA,IAAa,QAAQ,2BAAK,SAAA,CAAU;AAC9H,QAAM,+BAA+B,YAAY,SAAA,IAAa,YAAY,SAAA,IAAc,YAAY,KAAM,SAAS,SAAA,IAAa,QAAQ,2BAAK,SAAA,CAAU;AAGvJ,QAAM,iCAAgC,GAAA,2CAAM,wBAAwB,8BAA8B,4BAAA;AAClG,gBAAc,SAAA,KAAa,GAAA,2CAAM,+BAA+B,kBAAkB,gBAAA;AAElF,SAAO;;IAEL;IACA,iBAAiB,cAAc;IAC/B,gBAAgB,cAAc;IAC9B,WAAW,cAAc;EAC3B;AACF;AAKO,SAAS,0CAAkB,MAAkB;AAClD,MAAI,EAAA,WACO,YACC,aACC,YACD,SACH,YACG,iBACK,QACT,aACK,WACF,YACG,GAAA,sBACU,EAAA,IACpB;AAEJ,MAAI,YAAY,uBAAuB,cAAc,yCAAmB,WAAA,IAAe,SAAS;AAChG,MAAI,sBAAsB,cAAc,SAAS;AACjD,QAAM,yBAAyB,OAAO,iBAAiB,SAAA,EAAW;AAClE,MAAI,wBAAwB,CAAC,CAAC,0BAA0B,2BAA2B;AACnF,MAAI,cAAsB,sBAAsB,gCAAU,UAAA,IAAc,kCAAY,YAAY,SAAA;AAEhG,MAAI,CAAC,qBAAqB;AACxB,QAAI,EAAA,WAAU,WAAY,IAAI,OAAO,iBAAiB,UAAA;AACtD,gBAAY,OAAO,SAAS,WAAW,EAAA,KAAO;AAC9C,gBAAY,QAAQ,SAAS,YAAY,EAAA,KAAO;EAClD;AAEA,MAAI,cAAsB,gCAAU,WAAA;AACpC,MAAI,UAAU,iCAAW,WAAA;MACH,eAAsB;AAA5C,cAAY,WAAU,gBAAA,QAAQ,UAAI,QAAZ,kBAAA,SAAA,gBAAgB,OAAM,iBAAA,QAAQ,WAAK,QAAb,mBAAA,SAAA,iBAAiB;MACtC,cAAqB;AAA5C,cAAY,YAAW,eAAA,QAAQ,SAAG,QAAX,iBAAA,SAAA,eAAe,OAAM,kBAAA,QAAQ,YAAM,QAAd,oBAAA,SAAA,kBAAkB;AAE9D,MAAI,aAAa,gCAAU,UAAA;AAC3B,MAAI,qBAAqB,6CAAuB,eAAA;AAChD,MAAI,sBAAsB,6CAAuB,SAAA;AAIjD,MAAI,8BAAsC,gBAAgB,YAAY,SAAS,gCAAU,SAAA,IAAa,kCAAY,WAAW,eAAA;AAC7H,MAAI,UAAU,YAAY,UAAU,gBAAgB,YAAY,QAAQ;AACtE,wBAAoB,OAAO,MAAM;AACjC,wBAAoB,OAAO,OAAO;EACpC;AAEA,SAAO,0CACL,WACA,aACA,aACA,YACA,SACA,SACA,YACA,oBACA,qBACA,6BACA,QACA,aACA,uBACA,WACA,WACA,mBAAA;AAEJ;AAEA,SAAS,gCAAU,MAAa;AAC9B,MAAI,EAAA,KAAI,MAAM,OAAO,OAAQ,IAAI,KAAK,sBAAqB;AAC3D,MAAI,EAAA,WAAU,YAAY,WAAW,WAAY,IAAI,SAAS;AAC9D,SAAO;IACL,KAAK,MAAM,YAAY;IACvB,MAAM,OAAO,aAAa;;;EAG5B;AACF;AAEA,SAAS,kCAAY,MAAe,QAAe;AACjD,MAAI,QAAQ,OAAO,iBAAiB,IAAA;AACpC,MAAI;AACJ,MAAI,MAAM,aAAa,SAAS;AAC9B,QAAI,EAAA,KAAI,MAAM,OAAO,OAAQ,IAAI,KAAK,sBAAqB;AAC3D,aAAS;;;;;IAAyB;EACpC,OAAO;AACL,aAAS,gCAAU,IAAA;AACnB,QAAI,eAAe,gCAAU,MAAA;AAC7B,QAAI,cAAc,OAAO,iBAAiB,MAAA;AAC1C,iBAAa,QAAQ,SAAS,YAAY,gBAAgB,EAAA,KAAO,KAAK,OAAO;AAC7E,iBAAa,SAAS,SAAS,YAAY,iBAAiB,EAAA,KAAO,KAAK,OAAO;AAC/E,WAAO,OAAO,aAAa;AAC3B,WAAO,QAAQ,aAAa;EAC9B;AAEA,SAAO,OAAO,SAAS,MAAM,WAAW,EAAA,KAAO;AAC/C,SAAO,QAAQ,SAAS,MAAM,YAAY,EAAA,KAAO;AACjD,SAAO;AACT;AAKA,SAAS,yCAAmB,MAAiB;AAG3C,MAAI,eAAe,KAAK;AAKxB,MACE,gBACA,iBAAiB,SAAS,QAC1B,OAAO,iBAAiB,YAAA,EAAc,aAAa,YACnD,CAAC,wCAAkB,YAAA;AAEnB,mBAAe,SAAS;AAQ1B,MAAI,gBAAgB,MAAM;AACxB,mBAAe,KAAK;AACpB,WAAO,gBAAgB,CAAC,wCAAkB,YAAA;AACxC,qBAAe,aAAa;EAEhC;AAGA,SAAO,gBAAgB,SAAS;AAClC;AAGA,SAAS,wCAAkB,MAAa;AACtC,MAAI,QAAQ,OAAO,iBAAiB,IAAA;AACpC,SACE,MAAM,cAAc,UACpB,wBAAwB,KAAK,MAAM,UAAU,KAC7C,MAAM,WAAW,UACjB,MAAM,YAAY,WACjB,oBAAoB,SAAS,MAAM,mBAAmB,UACtD,0BAA0B,SAAS,MAAM,yBAAyB;AAEvE;;;;AC9jBO,IAAM,4CAA2C,oBAAI,QAAA;AASrD,SAAS,0CAAiB,MAA0B;AACzD,MAAI,EAAA,YAAW,QAAQ,QAAS,IAAI;AAEpC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,CAAC,UAAU,YAAY;AACzB;AAGF,QAAI,WAAW,CAAC,MAAA;AAEd,UAAI,SAAS,EAAE;AAEf,UAAI,CAAC,WAAW,WAAa,kBAAkB,QAAS,CAAC,OAAO,SAAS,WAAW,OAAO;AACzF;AAMF,UAAI,EAAE,kBAAkB,oBAAoB,EAAE,kBAAkB;AAC9D;AAGF,UAAI,iBAAiB,WAAW,0CAAW,IAAI,WAAW,OAAO;AACjE,UAAI;AACF,uBAAA;IAEJ;AAEA,WAAO,iBAAiB,UAAU,UAAU,IAAA;AAC5C,WAAO,MAAA;AACL,aAAO,oBAAoB,UAAU,UAAU,IAAA;IACjD;EACF,GAAG;IAAC;IAAQ;IAAS;GAAW;AAClC;;;;ACgBA,IAAI,uCAAiB,OAAO,aAAa,cAAc,OAAO,iBAAiB;AAMxE,SAAS,0CAAmB,OAAwB;AACzD,MAAI,EAAA,UAAU,KAAI,GAAA,2CAAQ;AAC1B,MAAI,EAAA,YACU,GAAA,WACH,YACC,YACE,YAAA,YACA,UAAA,mBACO,IAAA,aACN,MAAA,kBACK,OAAO,aAAa,cAAc,SAAS,OAAO,MAAA,SAC3D,GAAA,cACK,GAAA,uBACS,MAAA,SACd,MAAA,SACF,WACE,sBACa,EAAA,IACpB;AACJ,MAAI,CAAC,UAAU,WAAA,KAAe,GAAA,eAAAC,UAAgC,IAAA;AAE9D,MAAI,OAAO;IACT;IACA;IACA,WAAW;IACX,UAAU;IACV,UAAU;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAMF,MAAI,aAAY,GAAA,eAAAC,QAAO,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,KAAK;AAC5C,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI;AACF,gBAAU,UAAU,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB;EAExC,GAAG;IAAC;GAAO;AAEX,MAAI,kBAAiB,GAAA,eAAAC,aAAY,MAAA;AAC/B,QAAI,yBAAyB,SAAS,CAAC,UAAU,CAAC,WAAW,WAAW,CAAC,UAAU,WAAW,CAAC;AAC7F;AAGF,SAAI,yCAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,WAAU,UAAU;AACtC;AAOF,QAAI,SAA8B;AAClC,QAAI,UAAU,WAAW,UAAU,QAAQ,SAAS,SAAS,aAAa,GAAG;UAC1D;AAAjB,UAAI,cAAa,0BAAA,SAAS,mBAAa,QAAtB,4BAAA,SAAA,SAAA,wBAAwB,sBAAqB;AAC9D,UAAI,aAAa,UAAU,QAAQ,sBAAqB;UAK7C;AAFX,eAAS;QACP,MAAM;QACN,UAAS,kBAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,SAAG,QAAf,oBAAA,SAAA,kBAAmB,KAAK,WAAW;MAC9C;AACA,UAAI,OAAO,SAAS,WAAW,SAAS,GAAG;AACzC,eAAO,OAAO;YACG;AAAjB,eAAO,WAAU,qBAAA,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,YAAM,QAAlB,uBAAA,SAAA,qBAAsB,KAAK,WAAW;MACzD;IACF;AAIA,QAAI,UAAW,WAAW;AAC1B,QAAI,CAAC,aAAa,WAAW,SAAS;UAGT;AAF3B,cAAQ,MAAM,MAAM;AACpB,cAAQ,MAAM,SAAS;UACI;AAA3B,cAAQ,MAAM,cAAa,iCAAA,yBAAA,OAAO,oBAAc,QAArB,2BAAA,SAAA,SAAA,uBAAuB,YAAM,QAA7B,kCAAA,SAAA,gCAAiC,OAAO,eAAe;IACpF;AAEA,QAAIC,aAAW,GAAA,2CAAkB;MAC/B,WAAW,mCAAa,WAAW,SAAA;MACnC,aAAa,WAAW;MACxB,YAAY,UAAU;MACtB,YAAY,UAAU,WAAW,WAAW;MAC5C,SAAS;;;;;;;;IAQX,CAAA;AAEA,QAAI,CAACA,UAAS;AACZ;AAKF,YAAQ,MAAM,MAAM;AACpB,YAAQ,MAAM,SAAS;AACvB,YAAQ,MAAM,OAAO;AACrB,YAAQ,MAAM,QAAQ;AAEtB,WAAO,KAAKA,UAAS,QAAQ,EAAE,QAAQ,CAAA,QAAO,QAAQ,MAAM,GAAA,IAAQA,UAAS,SAAW,GAAA,IAAO,IAAA;AAC/F,YAAQ,MAAM,YAAYA,UAAS,aAAa,OAAQA,UAAS,YAAY,OAAO;AAGpF,QAAI,UAAU,SAAS,iBAAiB,UAAU,SAAS;AACzD,UAAI,aAAa,SAAS,cAAc,sBAAqB;AAC7D,UAAI,aAAa,UAAU,QAAQ,sBAAqB;AACxD,UAAI,YAAY,WAAW,OAAO,IAAI,IAAI,WAAW,OAAO,IAAI;AAChE,gBAAU,QAAQ,aAAa,YAAY,OAAO;IACpD;AAGA,gBAAYA,SAAA;EAEd,GAAG,IAAA;AAIH,GAAA,GAAA,2CAAgB,gBAAgB,IAAA;AAGhC,kCAAU,cAAA;AAGV,GAAA,GAAA,2CAAkB;IAChB,KAAK;IACL,UAAU;EACZ,CAAA;AAGA,GAAA,GAAA,2CAAkB;IAChB,KAAK;IACL,UAAU;EACZ,CAAA;AAIA,MAAI,cAAa,GAAA,eAAAH,QAAO,KAAA;AACxB,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI;AACJ,QAAI,WAAW,MAAA;AACb,iBAAW,UAAU;AACrB,mBAAa,OAAA;AAEb,gBAAU,WAAW,MAAA;AACnB,mBAAW,UAAU;MACvB,GAAG,GAAA;AAEH,qBAAA;IACF;AAIA,QAAI,WAAW,MAAA;AACb,UAAI,WAAW;AACb,iBAAA;IAEJ;AAEA,6CAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,iBAAiB,UAAU,QAAA;AAC3C,6CAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,iBAAiB,UAAU,QAAA;AAC3C,WAAO,MAAA;AACL,+CAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,oBAAoB,UAAU,QAAA;AAC9C,+CAAA,QAAA,yCAAA,SAAA,SAAA,qCAAgB,oBAAoB,UAAU,QAAA;IAChD;EACF,GAAG;IAAC;GAAe;AAEnB,MAAI,SAAQ,GAAA,eAAAE,aAAY,MAAA;AACtB,QAAI,CAAC,WAAW;AACd,kBAAA,QAAA,YAAA,SAAA,SAAA,QAAA;EAEJ,GAAG;IAAC;IAAS;GAAW;AAIxB,GAAA,GAAA,2CAAiB;IACf,YAAY;;IAEZ,SAAS,WAAW;EACtB,CAAA;MAQiB,qBAGJ;AATb,SAAO;IACL,cAAc;MACZ,OAAO;QACL,UAAU;QACV,QAAQ;WACL,aAAA,QAAA,aAAA,SAAA,SAAA,SAAU;QACb,YAAW,sBAAA,aAAA,QAAA,aAAA,SAAA,SAAA,SAAU,eAAS,QAAnB,wBAAA,SAAA,sBAAuB;MACpC;IACF;IACA,YAAW,sBAAA,aAAA,QAAA,aAAA,SAAA,SAAA,SAAU,eAAS,QAAnB,wBAAA,SAAA,sBAAuB;IAClC,YAAY;MACV,eAAe;MACf,MAAM;MACN,OAAO;QACL,MAAM,aAAA,QAAA,aAAA,SAAA,SAAA,SAAU;QAChB,KAAK,aAAA,QAAA,aAAA,SAAA,SAAA,SAAU;MACjB;IACF;;EAEF;AACF;AAEA,SAAS,gCAAU,UAAQ;AACzB,GAAA,GAAA,2CAAgB,MAAA;AACd,WAAO,iBAAiB,UAAU,UAAU,KAAA;AAC5C,WAAO,MAAA;AACL,aAAO,oBAAoB,UAAU,UAAU,KAAA;IACjD;EACF,GAAG;IAAC;GAAS;AACf;AAEA,SAAS,mCAAa,UAAU,WAAS;AACvC,MAAI,cAAc;AAChB,WAAO,SAAS,QAAQ,SAAS,OAAA,EAAS,QAAQ,OAAO,MAAA;AAE3D,SAAO,SAAS,QAAQ,SAAS,MAAA,EAAQ,QAAQ,OAAO,OAAA;AAC1D;;;AC7SA,SAAS,qCAAe,SAAgB;AACtC,QAAM,gBAAe,GAAA,2CAAe,OAAA;AACpC,MAAI,EAAE,mBAAmB,aAAa,gBAAgB,EAAE,mBAAmB,aAAa;AACtF,WAAO;AAGT,MAAI,EAAA,SAAQ,WAAY,IAAI,QAAQ;AAEpC,MAAI,YACF,YAAY,UACZ,eAAe,YACf,eAAe;AAGjB,MAAI,WAAW;AACb,UAAM,EAAA,iBAAiB,IAAI,QAAQ,cAAc;AACjD,QAAI,EAAC,SAAS,iBAAiB,YAAY,mBAAkB,IAAI,iBAAiB,OAAA;AAElF,gBACE,oBAAoB,UACpB,uBAAuB,YACvB,uBAAuB;EAE3B;AAEA,SAAO;AACT;AAEA,SAAS,yCAAmB,SAAkB,cAAsB;AAClE,SACE,CAAC,QAAQ,aAAa,QAAA;EAEtB,CAAC,QAAQ,aAAa,+BAAA,MACrB,QAAQ,aAAa,aACpB,gBACA,aAAa,aAAa,YACxB,QAAQ,aAAa,MAAA,IACrB;AAER;AAQO,SAAS,0CAAiB,SAAkB,cAAsB;AACvE,SACE,QAAQ,aAAa,cACrB,qCAAe,OAAA,KACf,yCAAmB,SAAS,YAAA,MAC3B,CAAC,QAAQ,iBAAiB,0CAAiB,QAAQ,eAAe,OAAA;AAEvE;;;;ACSA,IAAM,sCAAe,GAAA,eAAAE,SAAM,cAAoC,IAAA;AAC/D,IAAM,4CAAsB;AAE5B,IAAI,oCAAwB;AAYrB,SAAS,0CAAW,OAAsB;AAC/C,MAAI,EAAA,UAAS,SAAS,cAAc,UAAW,IAAI;AACnD,MAAI,YAAW,GAAA,eAAAC,QAAwB,IAAA;AACvC,MAAI,UAAS,GAAA,eAAAA,QAAwB,IAAA;AACrC,MAAI,YAAW,GAAA,eAAAA,QAAkB,CAAA,CAAE;AACnC,MAAI,EAAA,WAAW,KAAI,GAAA,eAAAC,YAAW,kCAAA,KAAiB,CAAC;AAGhD,MAAI,QAAO,GAAA,eAAAC,SAAQ,MAAM,IAAI,+BAAS;;EAAS,CAAA,GAAI;IAAC;GAAS;AAE7D,GAAA,GAAA,2CAAgB,MAAA;AAKd,QAAI,SAAS,cAAc,0CAAe;AAC1C,QAAI,0CAAe,YAAY,OAAO,QAAQ,KAAK,qCAAe,CAAC,sCAAgB,mCAAa,OAAO,QAAQ,GAAG;AAChH,UAAI,aAAa,0CAAe,YAAY,iCAAA;AAC5C,UAAI;AACF,iBAAS;IAEb;AAGA,WAAO,SAAS,IAAA;AAChB,8CAAe,QAAQ,IAAA;EACzB,GAAG;IAAC;IAAM;GAAW;AAErB,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAIC,QAAO,0CAAe,YAAY,QAAA;AACtC,QAAIA;AACF,MAAAA,MAAK,UAAU,CAAC,CAAC;EAErB,GAAG;IAAC;GAAQ;AAEZ,GAAA,GAAA,2CAAgB,MAAA;QAEH;AAAX,QAAIA,SAAO,oBAAA,SAAS,aAAO,QAAhB,sBAAA,SAAA,SAAA,kBAAkB;AAC7B,QAAI,QAAmB,CAAA;AACvB,QAAI,kBAAkB,CAAA,MAAK,EAAE,gBAAe;AAC5C,WAAOA,SAAQA,UAAS,OAAO,SAAS;AACtC,YAAM,KAAKA,KAAA;AAEX,MAAAA,MAAK,iBAAiB,2CAAqB,eAAA;AAC3C,MAAAA,QAAOA,MAAK;IACd;AAEA,aAAS,UAAU;AAEnB,WAAO,MAAA;AACL,eAASA,SAAQ;AACf,QAAAA,MAAK,oBAAoB,2CAAqB,eAAA;IAElD;EACF,GAAG;IAAC;GAAS;AAEb,8CAAsB,UAAU,cAAc,OAAA;AAC9C,4CAAoB,UAAU,OAAA;AAC9B,wCAAgB,UAAU,cAAc,OAAA;AACxC,qCAAa,UAAU,SAAA;AAIvB,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,UAAM,iBAAgB,GAAA,4CAAiB,GAAA,2CAAiB,SAAS,UAAU,SAAS,QAAQ,CAAA,IAAK,MAAA,CAAA;AACjG,QAAI,QAAyB;AAE7B,QAAI,uCAAiB,eAAe,SAAS,OAAO,GAAG;AAGrD,eAASD,SAAQ,0CAAe,SAAQ;AACtC,YAAIA,MAAK,YAAY,uCAAiB,eAAeA,MAAK,SAAS,OAAO;AACxE,kBAAQA;AAIZ,UAAI,UAAU,0CAAe,YAAY,QAAA;AACvC,4CAAc,MAAM;IAExB;EACF,GAAG;IAAC;GAAS;AAIb,GAAA,GAAA,2CAAgB,MAAA;AACd,WAAO,MAAA;UAEa,oCAAA;UAAA;AAAlB,UAAI,eAAc,+CAAA,8BAAA,0CAAe,YAAY,QAAA,OAAA,QAA3B,gCAAA,SAAA,UAAA,qCAAA,4BAAsC,YAAM,QAA5C,uCAAA,SAAA,SAAA,mCAA8C,cAAQ,QAAtD,gDAAA,SAAA,8CAA0D;AAE5E,WACG,aAAa,qCAAe,sCAAgB,UAAU,iCAAA,OACtD,CAAC,eAAe,0CAAe,YAAY,WAAA;AAE5C,4CAAc;AAEhB,gDAAe,eAAe,QAAA;IAChC;EACF,GAAG;IAAC;GAAS;AAEb,MAAI,gBAAe,GAAA,eAAAD,SAAQ,MAAM,iDAA2B,QAAA,GAAW,CAAA,CAAE;AACzE,MAAI,SAAQ,GAAA,eAAAA,SAAQ,OAAO;;IAEzB,YAAY;EACd,IAAI;IAAC;IAAM;GAAa;AAExB,UACE,GAAA,eAAAH,SAAA,cAAC,mCAAa,UAAQ;IAAC;MACrB,GAAA,eAAAA,SAAA,cAAC,QAAA;IAAK,0BAAA;IAAuB,QAAA;IAAO,KAAK;MACxC,WACD,GAAA,eAAAA,SAAA,cAAC,QAAA;IAAK,wBAAA;IAAqB,QAAA;IAAO,KAAK;;AAG7C;AAWA,SAAS,iDAA2B,UAA2C;AAC7E,SAAO;IACL,UAAU,OAA4B,CAAC,GAAC;AACtC,UAAI,QAAQ,SAAS;AACrB,UAAI,EAAA,MAAK,UAAU,MAAM,OAAQ,IAAI;UACgB;AAArD,UAAI,OAAO,SAAQ,GAAA,4CAAiB,GAAA,4CAAiB,UAAA,MAAM,CAAA,OAAE,QAAR,YAAA,SAAA,UAAY,MAAA,CAAA;AACjE,UAAI,WAAW,MAAM,CAAA,EAAG;AACxB,UAAI,YAAY,mCAAa,KAAA;AAC7B,UAAI,SAAS,0CAAuB,WAAW;;;MAAiB,GAAG,KAAA;AACnE,aAAO,cAAc,uCAAiB,MAAM,KAAA,IAAS,OAAO;AAC5D,UAAI,WAAW,OAAO,SAAQ;AAC9B,UAAI,CAAC,YAAY,MAAM;AACrB,eAAO,cAAc;AACrB,mBAAW,OAAO,SAAQ;MAC5B;AACA,UAAI;AACF,2CAAa,UAAU,IAAA;AAEzB,aAAO;IACT;IACA,cAAc,OAA4B,CAAC,GAAC;AAC1C,UAAI,QAAQ,SAAS;AACrB,UAAI,EAAA,MAAK,UAAU,MAAM,OAAQ,IAAI;UACgB;AAArD,UAAI,OAAO,SAAQ,GAAA,4CAAiB,GAAA,4CAAiB,UAAA,MAAM,CAAA,OAAE,QAAR,YAAA,SAAA,UAAY,MAAA,CAAA;AACjE,UAAI,WAAW,MAAM,MAAM,SAAS,CAAA,EAAG;AACvC,UAAI,YAAY,mCAAa,KAAA;AAC7B,UAAI,SAAS,0CAAuB,WAAW;;;MAAiB,GAAG,KAAA;AACnE,aAAO,cAAc,uCAAiB,MAAM,KAAA,IAAS,OAAQ;AAC7D,UAAI,eAAe,OAAO,aAAY;AACtC,UAAI,CAAC,gBAAgB,MAAM;AACzB,eAAO,cAAc;AACrB,uBAAe,OAAO,aAAY;MACpC;AACA,UAAI;AACF,2CAAa,cAAc,IAAA;AAE7B,aAAO;IACT;IACA,WAAW,OAAO,CAAC,GAAC;AAClB,UAAI,QAAQ,SAAS;AACrB,UAAI,EAAA,UAAS,OAAQ,IAAI;AACzB,UAAI,YAAY,mCAAa,KAAA;AAC7B,UAAI,SAAS,0CAAuB,WAAW;;;MAAiB,GAAG,KAAA;AACnE,aAAO,cAAc,MAAM,CAAA,EAAG;AAC9B,UAAI,WAAW,OAAO,SAAQ;AAC9B,UAAI;AACF,2CAAa,UAAU,IAAA;AAEzB,aAAO;IACT;IACA,UAAU,OAAO,CAAC,GAAC;AACjB,UAAI,QAAQ,SAAS;AACrB,UAAI,EAAA,UAAS,OAAQ,IAAI;AACzB,UAAI,YAAY,mCAAa,KAAA;AAC7B,UAAI,SAAS,0CAAuB,WAAW;;;MAAiB,GAAG,KAAA;AACnE,aAAO,cAAc,MAAM,MAAM,SAAS,CAAA,EAAG;AAC7C,UAAI,eAAe,OAAO,aAAY;AACtC,UAAI;AACF,2CAAa,cAAc,IAAA;AAE7B,aAAO;IACT;EACF;AACF;AAEA,SAAS,mCAAa,OAAgB;AACpC,SAAO,MAAM,CAAA,EAAG;AAClB;AAEA,SAAS,yCAAmB,UAAkB;AAC5C,MAAI,QAAQ,0CAAe,YAAY,iCAAA;AACvC,SAAO,SAAS,MAAM,aAAa,UAAU;AAC3C,QAAI,MAAM;AACR,aAAO;AAGT,YAAQ,MAAM;EAChB;AAEA,SAAO;AACT;AAEA,SAAS,0CAAoB,UAAuC,SAAiB;AACnF,MAAI,eAAc,GAAA,eAAAM,QAAyB,MAAA;AAE3C,MAAI,OAAM,GAAA,eAAAA,QAAiD,MAAA;AAC3D,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,QAAQ,SAAS;AACrB,QAAI,CAAC,SAAS;AAEZ,UAAI,IAAI,SAAS;AACf,6BAAqB,IAAI,OAAO;AAChC,YAAI,UAAU;MAChB;AACA;IACF;AAEA,UAAM,iBAAgB,GAAA,2CAAiB,QAAQ,MAAM,CAAA,IAAK,MAAA;AAG1D,QAAI,YAAY,CAAC,MAAA;AACf,UAAI,EAAE,QAAQ,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,yCAAmB,QAAA,KAAa,EAAE;AAC9F;AAGF,UAAI,kBAAiB,GAAA,2CAAiB,aAAA;AACtC,UAAIC,SAAQ,SAAS;AACrB,UAAI,CAACA,UAAS,CAAC,uCAAiB,gBAAgBA,MAAA;AAC9C;AAGF,UAAI,YAAY,mCAAaA,MAAA;AAC7B,UAAI,SAAS,0CAAuB,WAAW;QAAC,UAAU;MAAI,GAAGA,MAAA;AACjE,UAAI,CAAC;AACH;AAEF,aAAO,cAAc;AACrB,UAAI,cAAe,EAAE,WAAW,OAAO,aAAY,IAAK,OAAO,SAAQ;AACvE,UAAI,CAAC,aAAa;AAChB,eAAO,cAAc,EAAE,WAAWA,OAAMA,OAAM,SAAS,CAAA,EAAG,qBAAsBA,OAAM,CAAA,EAAG;AACzF,sBAAe,EAAE,WAAW,OAAO,aAAY,IAAK,OAAO,SAAQ;MACrE;AAEA,QAAE,eAAc;AAChB,UAAI;AACF,2CAAa,aAAa,IAAA;IAE9B;AAEA,QAAI,UAAyB,CAAC,MAAA;AAG5B,WAAK,CAAC,qCAAe,sCAAgB,mCAAa,QAAA,MAAc,wCAAiB,GAAA,2CAAe,CAAA,GAAe,SAAS,OAAO,GAAG;AAChI,4CAAc;AACd,oBAAY,WAAU,GAAA,2CAAe,CAAA;MACvC,WAAW,yCAAmB,QAAA,KAAa,CAAC,6CAAsB,GAAA,2CAAe,CAAA,GAAe,QAAA,GAAW;AAGzG,YAAI,YAAY;AACd,sBAAY,QAAQ,MAAK;iBAChB,qCAAe,kCAAY;AACpC,kDAAkB,kCAAY,OAAO;MAEzC,WAAW,yCAAmB,QAAA;AAC5B,oBAAY,WAAU,GAAA,2CAAe,CAAA;IAEzC;AAEA,QAAI,SAAwB,CAAC,MAAA;AAE3B,UAAI,IAAI;AACN,6BAAqB,IAAI,OAAO;AAElC,UAAI,UAAU,sBAAsB,MAAA;AAIlC,YAAI,YAAW,GAAA,2CAAqB;AACpC,YAAI,0BAA0B,aAAa,aAAa,aAAa,UAAS,GAAA,2CAAQ,MAAO,GAAA,2CAAO;AAGpG,YAAI,iBAAgB,GAAA,2CAAiB,aAAA;AACrC,YAAI,CAAC,0BAA0B,iBAAiB,yCAAmB,QAAA,KAAa,CAAC,4CAAsB,eAAe,QAAA,GAAW;AAC/H,8CAAc;AACd,cAAI,UAAS,GAAA,2CAAe,CAAA;AAC5B,cAAI,UAAU,OAAO,aAAa;gBAEhC;AADA,wBAAY,UAAU;aACtB,uBAAA,YAAY,aAAO,QAAnB,yBAAA,SAAA,SAAA,qBAAqB,MAAK;UAC5B,WAAW,kCAAY;AACrB,oDAAkB,kCAAY,OAAO;QAEzC;MACF,CAAA;IACF;AAEA,kBAAc,iBAAiB,WAAW,WAAW,KAAA;AACrD,kBAAc,iBAAiB,WAAW,SAAS,KAAA;AACnD,cAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,iBAAiB,WAAW,SAAS,KAAA,CAAA;AACvE,cAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,iBAAiB,YAAY,QAAQ,KAAA,CAAA;AACvE,WAAO,MAAA;AACL,oBAAc,oBAAoB,WAAW,WAAW,KAAA;AACxD,oBAAc,oBAAoB,WAAW,SAAS,KAAA;AACtD,gBAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,oBAAoB,WAAW,SAAS,KAAA,CAAA;AAC1E,gBAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,oBAAoB,YAAY,QAAQ,KAAA,CAAA;IAC5E;EACF,GAAG;IAAC;IAAU;GAAQ;AAItB,GAAA,GAAA,2CAAgB,MAAA;AACd,WAAO,MAAA;AACL,UAAI,IAAI;AACN,6BAAqB,IAAI,OAAO;IAEpC;EACF,GAAG;IAAC;GAAI;AACV;AAEA,SAAS,0CAAoB,SAAgB;AAC3C,SAAO,4CAAsB,OAAA;AAC/B;AAEA,SAAS,uCAAiB,SAA0B,OAAwB;AAC1E,MAAI,CAAC;AACH,WAAO;AAET,MAAI,CAAC;AACH,WAAO;AAET,SAAO,MAAM,KAAK,CAAA,SAAQ,KAAK,SAAS,OAAA,CAAA;AAC1C;AAEA,SAAS,4CAAsB,SAAkB,QAAkB,MAAI;AAErE,MAAI,mBAAmB,WAAW,QAAQ,QAAQ,6BAAA;AAChD,WAAO;AAKT,WAAS,EAAC,UAAU,EAAC,KAAK,0CAAe,SAAS,0CAAe,YAAY,KAAA,CAAA,GAAS;AACpF,QAAI,KAAK,uCAAiB,SAAS,EAAE,OAAO;AAC1C,aAAO;EAEX;AAEA,SAAO;AACT;AAGO,SAAS,0CAA8B,SAAgB;AAC5D,SAAO,4CAAsB,SAAS,iCAAA;AACxC;AAEA,SAAS,sCAAgB,UAAoB,OAAe;MAC7C;AAAb,MAAI,UAAS,8BAAA,0CAAe,YAAY,KAAA,OAAA,QAA3B,gCAAA,SAAA,SAAA,4BAAmC;AAChD,SAAO,QAAQ;AACb,QAAI,OAAO,aAAa;AACtB,aAAO;AAET,aAAS,OAAO;EAClB;AACA,SAAO;AACT;AAEA,SAAS,mCAAa,SAAkC,SAAS,OAAK;AACpE,MAAI,WAAW,QAAQ,CAAC;AACtB,QAAI;AACF,OAAA,GAAA,2CAAY,OAAA;IACd,QAAQ;IAER;WACS,WAAW;AACpB,QAAI;AACF,cAAQ,MAAK;IACf,QAAQ;IAER;AAEJ;AAEA,SAAS,sCAAgB,OAAkB,WAAW,MAAI;AACxD,MAAI,WAAW,MAAM,CAAA,EAAG;AACxB,MAAI,YAAY,mCAAa,KAAA;AAC7B,MAAI,SAAS,0CAAuB,WAAW;;EAAS,GAAG,KAAA;AAC3D,SAAO,cAAc;AACrB,MAAI,WAAW,OAAO,SAAQ;AAG9B,MAAI,YAAY,CAAC,UAAU;AACzB,gBAAY,mCAAa,KAAA;AACzB,aAAS,0CAAuB,WAAW;MAAC,UAAU;IAAK,GAAG,KAAA;AAC9D,WAAO,cAAc;AACrB,eAAW,OAAO,SAAQ;EAC5B;AAEA,SAAO;AACT;AAEA,SAAS,wCAAkB,OAAkB,WAAmB,MAAI;AAClE,qCAAa,sCAAgB,OAAO,QAAA,CAAA;AACtC;AAEA,SAAS,mCAAa,UAAuC,WAAmB;AAC9E,QAAM,gBAAe,GAAA,eAAAC,SAAM,OAAO,SAAA;AAClC,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,aAAa,SAAS;AACxB,0CAAc;AACd,YAAM,iBAAgB,GAAA,2CAAiB,SAAS,UAAU,SAAS,QAAQ,CAAA,IAAK,MAAA;AAChF,UAAI,CAAC,wCAAiB,GAAA,2CAAiB,aAAA,GAAgB,kCAAY,OAAO,KAAK,SAAS;AACtF,gDAAkB,SAAS,OAAO;IAEtC;AACA,iBAAa,UAAU;EACzB,GAAG;IAAC;GAAS;AACf;AAEA,SAAS,4CAAsB,UAAuC,SAAmB,SAAiB;AAGxG,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,WAAW;AACb;AAGF,QAAI,QAAQ,SAAS;AACrB,UAAM,iBAAgB,GAAA,2CAAiB,QAAQ,MAAM,CAAA,IAAK,MAAA;AAE1D,QAAI,UAAU,CAAC,MAAA;AACb,UAAI,UAAS,GAAA,2CAAe,CAAA;AAC5B,UAAI,uCAAiB,QAAQ,SAAS,OAAO;AAC3C,4CAAc;eACL,CAAC,0CAAoB,MAAA;AAC9B,4CAAc;IAElB;AAEA,kBAAc,iBAAiB,WAAW,SAAS,KAAA;AACnD,cAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,iBAAiB,WAAW,SAAS,KAAA,CAAA;AACvE,WAAO,MAAA;AACL,oBAAc,oBAAoB,WAAW,SAAS,KAAA;AACtD,gBAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,oBAAoB,WAAW,SAAS,KAAA,CAAA;IAC5E;EACF,GAAG;IAAC;IAAU;IAAS;GAAQ;AACjC;AAEA,SAAS,yCAAmB,UAAkB;AAC5C,MAAI,QAAQ,0CAAe,YAAY,iCAAA;AACvC,SAAO,SAAS,MAAM,aAAa,UAAU;AAC3C,QAAI,MAAM;AACR,aAAO;AAGT,YAAQ,MAAM;EAChB;AAEA,UAAO,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,cAAa;AAC7B;AAEA,SAAS,sCAAgB,UAAuC,cAAwB,SAAiB;AAGvG,QAAM,oBAAmB,GAAA,eAAAH,QAAO,OAAO,aAAa,eAAc,GAAA,4CAAiB,GAAA,2CAAiB,SAAS,UAAU,SAAS,QAAQ,CAAA,IAAK,MAAA,CAAA,IAAkC,IAAA;AAI/K,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,QAAQ,SAAS;AACrB,UAAM,iBAAgB,GAAA,2CAAiB,QAAQ,MAAM,CAAA,IAAK,MAAA;AAC1D,QAAI,CAAC,gBAAgB;AACnB;AAGF,QAAI,UAAU,MAAA;AAGZ,WAAK,CAAC,qCAAe,sCAAgB,mCAAa,QAAA,MAChD,wCAAiB,GAAA,2CAAiB,aAAA,GAAgB,SAAS,OAAO;AAElE,4CAAc;IAElB;AAEA,kBAAc,iBAAiB,WAAW,SAAS,KAAA;AACnD,cAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,iBAAiB,WAAW,SAAS,KAAA,CAAA;AACvE,WAAO,MAAA;AACL,oBAAc,oBAAoB,WAAW,SAAS,KAAA;AACtD,gBAAA,QAAA,UAAA,SAAA,SAAA,MAAO,QAAQ,CAAA,YAAW,QAAQ,oBAAoB,WAAW,SAAS,KAAA,CAAA;IAC5E;EAEF,GAAG;IAAC;IAAU;GAAQ;AAEtB,GAAA,GAAA,2CAAgB,MAAA;AACd,UAAM,iBAAgB,GAAA,2CAAiB,SAAS,UAAU,SAAS,QAAQ,CAAA,IAAK,MAAA;AAEhF,QAAI,CAAC;AACH;AAOF,QAAI,YAAY,CAAC,MAAA;AACf,UAAI,EAAE,QAAQ,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,yCAAmB,QAAA,KAAa,EAAE;AAC9F;AAGF,UAAI,iBAAiB,cAAc;AACnC,UAAI,CAAC,4CAAsB,gBAAgB,QAAA,KAAa,CAAC,yCAAmB,QAAA;AAC1E;AAEF,UAAI,WAAW,0CAAe,YAAY,QAAA;AAC1C,UAAI,CAAC;AACH;AAEF,UAAI,gBAAgB,SAAS;AAG7B,UAAI,SAAS,0CAAuB,cAAc,MAAM;QAAC,UAAU;MAAI,CAAA;AAGvE,aAAO,cAAc;AACrB,UAAI,cAAe,EAAE,WAAW,OAAO,aAAY,IAAK,OAAO,SAAQ;AAEvE,UAAI,CAAC,iBAAiB,CAAC,cAAc,eAAe,kBAAkB,cAAc,MAAM;AACxF,wBAAgB;AAChB,iBAAS,gBAAgB;MAC3B;AAIA,WAAK,CAAC,eAAe,CAAC,4CAAsB,aAAa,QAAA,MAAc,eAAe;AACpF,eAAO,cAAc;AAGrB;AACE,wBAAe,EAAE,WAAW,OAAO,aAAY,IAAK,OAAO,SAAQ;eAC5D,4CAAsB,aAAa,QAAA;AAE5C,UAAE,eAAc;AAChB,UAAE,gBAAe;AACjB,YAAI;AACF,6CAAa,aAAa,IAAA;iBAKtB,CAAC,0CAAoB,aAAA;AACvB,yBAAe,KAAI;;AAEnB,6CAAa,eAAe,IAAA;MAGlC;IACF;AAEA,QAAI,CAAC;AACH,oBAAc,iBAAiB,WAAW,WAA4B,IAAA;AAGxE,WAAO,MAAA;AACL,UAAI,CAAC;AACH,sBAAc,oBAAoB,WAAW,WAA4B,IAAA;IAE7E;EACF,GAAG;IAAC;IAAU;IAAc;GAAQ;AAGpC,GAAA,GAAA,2CAAgB,MAAA;AACd,UAAM,iBAAgB,GAAA,2CAAiB,SAAS,UAAU,SAAS,QAAQ,CAAA,IAAK,MAAA;AAEhF,QAAI,CAAC;AACH;AAGF,QAAI,WAAW,0CAAe,YAAY,QAAA;AAC1C,QAAI,CAAC;AACH;QAEuB;AAAzB,aAAS,iBAAgB,4BAAA,iBAAiB,aAAO,QAAxB,8BAAA,SAAA,4BAA4B;AACrD,WAAO,MAAA;AACL,UAAII,YAAW,0CAAe,YAAY,QAAA;AAC1C,UAAI,CAACA;AACH;AAEF,UAAI,gBAAgBA,UAAS;AAG7B,UAAI,iBAAgB,GAAA,2CAAiB,aAAA;AACrC,UACE,gBACG,kBAEC,iBAAiB,4CAAsB,eAAe,QAAA,KAAe,kBAAkB,cAAc,QAAQ,yCAAmB,QAAA,IAEpI;AAEA,YAAI,aAAa,0CAAe,MAAK;AACrC,8BAAsB,MAAA;AAEpB,cAAI,cAAc,kBAAkB,cAAc,MAAM;AAEtD,gBAAIA,YAAW,WAAW,YAAY,QAAA;AACtC,mBAAOA,WAAU;AACf,kBAAIA,UAAS,iBAAiBA,UAAS,cAAc,aAAa;AAChE,4DAAsBA,UAAS,aAAa;AAC5C;cACF;AACA,cAAAA,YAAWA,UAAS;YACtB;AAIA,YAAAA,YAAW,WAAW,YAAY,QAAA;AAClC,mBAAOA,WAAU;AACf,kBAAIA,UAAS,YAAYA,UAAS,SAAS,WAAW,0CAAe,YAAYA,UAAS,QAAQ,GAAG;AACnG,oBAAI,OAAO,sCAAgBA,UAAS,SAAS,SAAS,IAAA;AACtD,4DAAsB,IAAA;AACtB;cACF;AACA,cAAAA,YAAWA,UAAS;YACtB;UACF;QACF,CAAA;MACF;IACF;EACF,GAAG;IAAC;IAAU;GAAa;AAC7B;AAEA,SAAS,4CAAsB,MAAsB;AAInD,MAAI,KAAK,cAAc,IAAI,YAAY,2CAAqB;IAAC,SAAS;IAAM,YAAY;EAAI,CAAA,CAAA;AAC1F,uCAAa,IAAA;AAEjB;AAMO,SAAS,0CAAuB,MAAe,MAA4B,OAAiB;AACjG,MAAI,UAAS,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,aAAW,GAAA,8CAAa,GAAA;AAG3C,MAAI,eAAc,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,cAAa,KAAK,eAAgB,OAAmB;AAG7E,MAAI,OAAM,GAAA,2CAAiB,WAAA;AAG3B,MAAI,UAAS,GAAA,2CACX,KACA,QAAQ,KACR,WAAW,cACX;IACE,WAAW,MAAI;UAET;AAAJ,UAAI,SAAA,QAAA,SAAA,SAAA,UAAA,aAAA,KAAM,UAAI,QAAV,eAAA,SAAA,SAAA,WAAY,SAAS,IAAA;AACvB,eAAO,WAAW;AAGpB,UAAI,OAAO,IAAA,MACN,GAAA,2CAAiB,IAAA,MAChB,CAAC,SAAS,uCAAiB,MAAiB,KAAA,OAC5C,EAAC,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,WAAU,KAAK,OAAO,IAAA;AAEjC,eAAO,WAAW;AAGpB,aAAO,WAAW;IACpB;EACF,CAAA;AAGF,MAAI,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM;AACR,WAAO,cAAc,KAAK;AAG5B,SAAO;AACT;AAsGA,IAAM,6BAAN,MAAM,4BAAA;EASJ,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAgB;AAC1B,WAAO,KAAK,QAAQ,IAAI,IAAA;EAC1B;EAEA,YAAY,UAAoB,QAAkB,eAAkC;AAClF,QAAI,aAAa,KAAK,QAAQ,IAAI,WAAA,QAAA,WAAA,SAAA,SAAU,IAAA;AAC5C,QAAI,CAAC;AACH;AAEF,QAAI,OAAO,IAAI,+BAAS;;IAAS,CAAA;AACjC,eAAW,SAAS,IAAA;AACpB,SAAK,SAAS;AACd,SAAK,QAAQ,IAAI,UAAU,IAAA;AAC3B,QAAI;AACF,WAAK,gBAAgB;EAEzB;EAEA,QAAQ,MAAgB;AACtB,SAAK,QAAQ,IAAI,KAAK,UAAU,IAAA;EAClC;EAEA,eAAe,UAAoB;AAEjC,QAAI,aAAa;AACf;AAEF,QAAI,OAAO,KAAK,QAAQ,IAAI,QAAA;AAC5B,QAAI,CAAC;AACH;AAEF,QAAI,aAAa,KAAK;AAGtB,aAAS,WAAW,KAAK,SAAQ;AAC/B,UACE,YAAY,QACZ,KAAK,iBACL,QAAQ,iBACR,KAAK,YACL,KAAK,SAAS,WACd,uCAAiB,QAAQ,eAAe,KAAK,SAAS,OAAO;AAE7D,gBAAQ,gBAAgB,KAAK;AAGjC,QAAI,WAAW,KAAK;AACpB,QAAI,YAAY;AACd,iBAAW,YAAY,IAAA;AACvB,UAAI,SAAS,OAAO;AAClB,iBAAS,QAAQ,CAAA,UAAS,cAAc,WAAW,SAAS,KAAA,CAAA;IAEhE;AAEA,SAAK,QAAQ,OAAO,KAAK,QAAQ;EACnC;;EAGA,CAAC,SAAS,OAAiB,KAAK,MAA2B;AACzD,QAAI,KAAK,YAAY;AACnB,YAAM;AAER,QAAI,KAAK,SAAS,OAAO;AACvB,eAAS,SAAS,KAAK;AACrB,eAAO,KAAK,SAAS,KAAA;EAG3B;EAEA,QAAc;QAGyB;AAFrC,QAAI,UAAU,IAAI,4BAAA;QAEmB;AADrC,aAAS,QAAQ,KAAK,SAAQ;AAC5B,cAAQ,YAAY,KAAK,WAAU,yBAAA,eAAA,KAAK,YAAM,QAAX,iBAAA,SAAA,SAAA,aAAa,cAAQ,QAArB,0BAAA,SAAA,wBAAyB,MAAM,KAAK,aAAa;AAEtF,WAAO;EACT;EApFA,cAAc;SAFN,UAAU,oBAAI,IAAA;AAGpB,SAAK,OAAO,IAAI,+BAAS;MAAC,UAAU;IAAI,CAAA;AACxC,SAAK,QAAQ,IAAI,MAAM,KAAK,IAAI;EAClC;AAkFF;AAEA,IAAM,iCAAN,MAAM;EAUJ,SAAS,MAAgB;AACvB,SAAK,SAAS,IAAI,IAAA;AAClB,SAAK,SAAS;EAChB;EACA,YAAY,MAAgB;AAC1B,SAAK,SAAS,OAAO,IAAA;AACrB,SAAK,SAAS;EAChB;EAVA,YAAY,OAA6B;SAHlC,WAA0B,oBAAI,IAAA;SAC9B,UAAU;AAGf,SAAK,WAAW,MAAM;EACxB;AASF;AAEO,IAAI,4CAAiB,IAAI,2BAAA;;;;AC17BzB,SAAS,0CAAa,QAA4B,CAAC,GAAC;AACzD,MAAI,EAAA,YACU,OAAA,aACD,OACL,IACJ;AACJ,MAAI,SAAQ,GAAA,eAAAC,QAAO;IACjB,WAAW;IACX,gBAAgB,cAAa,GAAA,2CAAa;EAC5C,CAAA;AACA,MAAI,CAAC,WAAW,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACvC,MAAI,CAAC,qBAAqB,eAAA,KAAmB,GAAA,eAAAA,UAAS,MAAM,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc;AAEnH,MAAI,eAAc,GAAA,eAAAC,aAAY,MAAM,gBAAgB,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc,GAAG,CAAA,CAAE;AAEhH,MAAI,iBAAgB,GAAA,eAAAA,aAAY,CAAAC,eAAA;AAC9B,UAAM,QAAQ,YAAYA;AAC1B,eAAWA,UAAA;AACX,gBAAA;EACF,GAAG;IAAC;GAAY;AAEhB,GAAA,GAAA,2CAAwB,CAAC,mBAAA;AACvB,UAAM,QAAQ,iBAAiB;AAC/B,gBAAA;EACF,GAAG,CAAA,GAAI;;EAAY,CAAA;AAEnB,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;IAC1B,YAAY;;EAEd,CAAA;AAEA,MAAI,EAAA,iBAAiB,KAAI,GAAA,2CAAe;IACtC,YAAY,CAAC;IACb,qBAAqB;EACvB,CAAA;AAEA,SAAO;;IAEL,gBAAgB;IAChB,YAAY,SAAS,mBAAmB;EAC1C;AACF;;;;;;;;;;ACtBA,IAAM,wCAA+C,CAAA;AAO9C,SAAS,0CAAW,OAAyB,KAA8B;AAChF,MAAI,EAAA,SACK,mBACU,QACX,gBACU,OAAA,4BACY,OAAA,6BACA,IAC1B;AAGJ,GAAA,GAAA,eAAAC,WAAU,MAAA;AACR,QAAI,UAAU,CAAC,sCAAgB,SAAS,GAAA,GAAM;AAC5C,4CAAgB,KAAK,GAAA;AACrB,aAAO,MAAA;AACL,YAAI,QAAQ,sCAAgB,QAAQ,GAAA;AACpC,YAAI,SAAS;AACX,gDAAgB,OAAO,OAAO,CAAA;MAElC;IACF;EACF,GAAG;IAAC;IAAQ;GAAI;AAGhB,MAAI,SAAS,MAAA;AACX,QAAI,sCAAgB,sCAAgB,SAAS,CAAA,MAAO,OAAO;AACzD,cAAA;EAEJ;AAEA,MAAI,yBAAyB,CAAC,MAAA;AAC5B,QAAI,CAAC,gCAAgC,6BAA6B,EAAE,MAAM,GACxE;AAAA,UAAI,sCAAgB,sCAAgB,SAAS,CAAA,MAAO,KAAK;AACvD,UAAE,gBAAe;AACjB,UAAE,eAAc;MAClB;IAAA;EAEJ;AAEA,MAAI,oBAAoB,CAAC,MAAA;AACvB,QAAI,CAAC,gCAAgC,6BAA6B,EAAE,MAAM,GAAc;AACtF,UAAI,sCAAgB,sCAAgB,SAAS,CAAA,MAAO,KAAK;AACvD,UAAE,gBAAe;AACjB,UAAE,eAAc;MAClB;AACA,aAAA;IACF;EACF;AAGA,MAAI,YAAY,CAAC,MAAA;AACf,QAAI,EAAE,QAAQ,YAAY,CAAC,6BAA6B,CAAC,EAAE,YAAY,aAAa;AAClF,QAAE,gBAAe;AACjB,QAAE,eAAc;AAChB,aAAA;IACF;EACF;AAGA,GAAA,GAAA,2CAAmB;;IAAM,mBAAmB,iBAAiB,SAAS,oBAAoB;;EAAiC,CAAA;AAE3H,MAAI,EAAA,iBAAiB,KAAI,GAAA,2CAAe;IACtC,YAAY,CAAC;IACb,cAAc,CAAC,MAAA;AAUb,UAAI,CAAC,EAAE,kBAAiB,GAAA,2CAA8B,EAAE,aAAa;AACnE;AAGF,UAAI,CAAC,gCAAgC,6BAA6B,EAAE,aAAa;AAC/E,oBAAA,QAAA,YAAA,SAAA,SAAA,QAAA;IAEJ;EACF,CAAA;AAEA,MAAI,wBAAwB,CAAA,MAAA;AAE1B,QAAI,EAAE,WAAW,EAAE;AACjB,QAAE,eAAc;EAEpB;AAEA,SAAO;IACL,cAAc;;MAEZ,GAAG;IACL;IACA,eAAe;MACb,eAAe;IACjB;EACF;AACF;;;;;;AC9IA,IAAM,uCAAiB,OAAO,aAAa,eAAe,OAAO;AAGjE,IAAM,0CAAoB,oBAAI,IAAI;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGD,IAAI,2CAAqB;AACzB,IAAI;AAOG,SAAS,0CAAiB,UAAgC,CAAC,GAAC;AACjE,MAAI,EAAA,WAAW,IAAI;AAEnB,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI;AACF;AAGF;AACA,QAAI,6CAAuB,GAAA;AACzB,WAAI,GAAA,2CAAI;AACN,wCAAU,gDAAA;;AAEV,wCAAU,4CAAA;;AAId,WAAO,MAAA;AACL;AACA,UAAI,6CAAuB;AACzB,sCAAA;IAEJ;EACF,GAAG;IAAC;GAAW;AACjB;AAIA,SAAS,8CAAA;AACP,MAAI,iBAAiB,OAAO,aAAa,SAAS,gBAAgB;AAClE,UAAO,GAAA,2CACL,iBAAiB;GAEd,qBAAqB,SAAS,gBAAgB,QAC3C,+BAAS,SAAS,iBAAiB,mBAAmB,QAAA,IACtD,+BAAS,SAAS,iBAAiB,gBAAgB,GAAG,cAAA,IAAkB,IAC9E,+BAAS,SAAS,iBAAiB,YAAY,QAAA,CAAA;AAEnD;AA6BA,SAAS,kDAAA;AACP,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe,CAAC,MAAA;AAElB,kBAAa,GAAA,2CAAgB,EAAE,QAAmB,IAAA;AAClD,QAAI,eAAe,SAAS,mBAAmB,eAAe,SAAS;AACrE;AAMF,QAAI,sBAAsB,eAAe,OAAO,iBAAiB,UAAA,EAAY,uBAAuB;AAClG,gCAA0B,+BAAS,YAAY,sBAAsB,SAAA;EAEzE;AAEA,MAAI,cAAc,CAAC,MAAA;AAEjB,QAAI,CAAC,cAAc,eAAe,SAAS,mBAAmB,eAAe,SAAS,MAAM;AAC1F,QAAE,eAAc;AAChB;IACF;AAQA,QAAI,WAAW,iBAAiB,WAAW,gBAAgB,WAAW,gBAAgB,WAAW;AAC/F,QAAE,eAAc;EAEpB;AAEA,MAAI,aAAa,MAAA;AACf,QAAI;AACF,8BAAA;EAEJ;AAEA,MAAI,UAAU,CAAC,MAAA;AACb,QAAI,SAAS,EAAE;AACf,QAAI,uCAAiB,MAAA,GAAS;AAC5B,kBAAA;AAIA,aAAO,MAAM,YAAY;AACzB,4BAAsB,MAAA;AACpB,eAAO,MAAM,YAAY;AAIzB,YAAI,sCAAA;AACF,cAAI,qCAAe,SAAS,OAAO;AAGjC,kCAAsB,MAAA;AACpB,mDAAe,MAAA;YACjB,CAAA;;AAIA,iDAAe,iBAAiB,UAAU,MAAM,qCAAe,MAAA,GAAS;cAAC,MAAM;YAAI,CAAA;;MAGzF,CAAA;IACF;EACF;AAEA,MAAI,gBAAqC;AACzC,MAAI,cAAc,MAAA;AAChB,QAAI;AACF;AAGF,QAAI,iBAAiB,MAAA;AAGnB,aAAO,SAAS,GAAG,CAAA;IACrB;AAKA,QAAI,UAAU,OAAO;AACrB,QAAI,UAAU,OAAO;AAErB,qBAAgB,GAAA,2CACd,+BAAS,QAAQ,UAAU,cAAA,GAC3B,+BAAS,SAAS,iBAAiB,gBAAgB,GAAG,OAAO,aAAa,SAAS,gBAAgB,WAAW,IAAI,GAClH,+BAAS,SAAS,iBAAiB,YAAY,QAAA,GAC/C,+BAAS,SAAS,MAAM,aAAa,IAAI,OAAA,IAAW,GACpD,MAAA;AACE,aAAO,SAAS,SAAS,OAAA;IAC3B,CAAA;AAIF,WAAO,SAAS,GAAG,CAAA;EACrB;AAEA,MAAI,gBAAe,GAAA,2CACjB,+BAAS,UAAU,cAAc,cAAc;IAAC,SAAS;IAAO,SAAS;EAAI,CAAA,GAC7E,+BAAS,UAAU,aAAa,aAAa;IAAC,SAAS;IAAO,SAAS;EAAI,CAAA,GAC3E,+BAAS,UAAU,YAAY,YAAY;IAAC,SAAS;IAAO,SAAS;EAAI,CAAA,GACzE,+BAAS,UAAU,SAAS,SAAS,IAAA,CAAA;AAGvC,SAAO,MAAA;AAEL,gCAAA,QAAA,4BAAA,SAAA,SAAA,wBAAA;AACA,sBAAA,QAAA,kBAAA,SAAA,SAAA,cAAA;AACA,iBAAA;EACF;AACF;AAGA,SAAS,+BAAS,SAAsB,OAAe,OAAa;AAClE,MAAI,MAAM,QAAQ,MAAM,KAAA;AACxB,UAAQ,MAAM,KAAA,IAAS;AAEvB,SAAO,MAAA;AACL,YAAQ,MAAM,KAAA,IAAS;EACzB;AACF;AAGA,SAAS,+BACP,QACA,OACA,SACA,SAA2C;AAI3C,SAAO,iBAAiB,OAAO,SAAS,OAAA;AACxC,SAAO,MAAA;AAEL,WAAO,oBAAoB,OAAO,SAAS,OAAA;EAC7C;AACF;AAEA,SAAS,qCAAe,QAAe;AACrC,MAAI,OAAO,SAAS,oBAAoB,SAAS;AACjD,MAAI,aAA6B;AACjC,SAAO,cAAc,eAAe,MAAM;AAExC,QAAI,cAAa,GAAA,2CAAgB,UAAA;AACjC,QAAI,eAAe,SAAS,mBAAmB,eAAe,SAAS,QAAQ,eAAe,YAAY;AACxG,UAAI,gBAAgB,WAAW,sBAAqB,EAAG;AACvD,UAAI,YAAY,WAAW,sBAAqB,EAAG;AACnD,UAAI,YAAY,gBAAgB,WAAW;AACzC,mBAAW,aAAa,YAAY;IAExC;AAEA,iBAAa,WAAW;EAC1B;AACF;AAEA,SAAS,uCAAiB,QAAe;AACvC,SACG,kBAAkB,oBAAoB,CAAC,wCAAkB,IAAI,OAAO,IAAI,KACzE,kBAAkB,uBACjB,kBAAkB,eAAe,OAAO;AAE7C;;;;AChQO,IAAM,6CAAgB,GAAA,eAAAC,eAA0C,CAAC,CAAA;AAejE,SAAS,4CAAA;MACP;AAAP,UAAO,eAAA,GAAA,eAAAC,YAAW,yCAAA,OAAA,QAAX,gBAAA,SAAA,cAA6B,CAAC;AACvC;;;;;ACXA,IAAM,iCAAU,GAAA,eAAAC,SAAM,cAAmC,IAAA;AAUlD,SAAS,yCAAc,OAAyB;AACrD,MAAI,EAAA,SAAS,IAAI;AACjB,MAAI,UAAS,GAAA,eAAAC,YAAW,6BAAA;AACxB,MAAI,CAAC,YAAY,aAAA,KAAiB,GAAA,eAAAC,UAAS,CAAA;AAC3C,MAAI,WAAU,GAAA,eAAAC,SAAQ,OAAO;;;IAG3B,WAAA;AACE,oBAAc,CAAA,UAAS,QAAQ,CAAA;AAC/B,UAAI;AACF,eAAO,SAAQ;IAEnB;IACA,cAAA;AACE,oBAAc,CAAA,UAAS,QAAQ,CAAA;AAC/B,UAAI;AACF,eAAO,YAAW;IAEtB;EACF,IAAI;IAAC;IAAQ;GAAW;AAExB,UACE,GAAA,eAAAH,SAAA,cAAC,8BAAQ,UAAQ;IAAC,OAAO;KACtB,QAAA;AAGP;AAaO,SAAS,4CAAA;AACd,MAAI,WAAU,GAAA,eAAAC,YAAW,6BAAA;AACzB,SAAO;IACL,oBAAoB;MAClB,eAAe,WAAW,QAAQ,aAAa,IAAI,OAAO;IAC5D;EACF;AACF;AAKA,SAAS,0CAAoB,OAAyB;AACpD,MAAI,EAAA,mBAAmB,IAAI,0CAAA;AAC3B,UAAO,GAAA,eAAAD,SAAA,cAAC,OAAA;IAAI,0BAAA;IAAwB,GAAG;IAAQ,GAAG;;AACpD;AAUO,SAAS,0CAAgB,OAAyB;AACvD,UACE,GAAA,eAAAA,SAAA,cAAC,0CAAA,OACC,GAAA,eAAAA,SAAA,cAAC,2CAAwB,KAAA,CAAA;AAG/B;AAkBO,SAAS,0CAAiB,OAA4B;AAC3D,MAAI,SAAQ,GAAA,2CAAO;AACnB,MAAI,EAAA,kBAAmB,QAAQ,OAAO,SAAS,MAAM,GAAG,KAAA,IAAQ;AAChE,MAAI,EAAA,aAAa,KAAI,GAAA,2CAAsB;AAC3C,MAAI,CAAC,MAAM,mBAAmB;AAC5B,sBAAkB,aAAA;AAGpB,GAAA,GAAA,eAAAA,SAAM,UAAU,MAAA;AACd,QAAI,oBAAA,QAAA,oBAAA,SAAA,SAAA,gBAAiB,QAAQ,0BAAA;AAC3B,YAAM,IAAI,MAAM,mGAAA;EAEpB,GAAG;IAAC;GAAgB;AAEpB,MAAI,CAAC;AACH,WAAO;AAGT,MAAI,YAAW,GAAA,eAAAA,SAAA,cAAC,2CAAoB,IAAA;AACpC,UAAO,GAAA,iBAAAI,SAAS,aAAa,UAAU,eAAA;AACzC;;;;ACnJA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACDA,4BAAiB;EAAG,WAAW;AAC/B;;;;ACiCA,4BAAiB;EACf,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;AACX;;;;;;;;ACxBO,IAAM,6CAAiB,GAAA,eAAAC,SAAM,cAAoG,IAAA;AAMjI,SAAS,0CAAQ,OAAmB;AACzC,MAAI,SAAQ,GAAA,2CAAO;AACnB,MAAI,EAAA,kBAAmB,QAAQ,OAAO,SAAS,MAAI,UAAW,IAAI;AAClE,MAAI,CAAC,SAAS,UAAA,KAAc,GAAA,eAAAC,UAAS,KAAA;AACrC,MAAI,gBAAe,GAAA,eAAAC,SAAQ,OAAO;;;EAAoB,IAAI;IAAC;IAAS;GAAW;AAE/E,MAAI,EAAA,aAAa,KAAI,GAAA,2CAAsB;AAC3C,MAAI,CAAC,MAAM,mBAAmB;AAC5B,sBAAkB,aAAA;AAGpB,MAAI,CAAC;AACH,WAAO;AAGT,MAAI,WAAW,MAAM;AACrB,MAAI,CAAC,MAAM;AACT,gBACE,GAAA,eAAAF,SAAA,eAAC,GAAA,4CAAS;MAAE,cAAA;MAAa,UAAU,MAAM,sBAAsB,YAAY,CAAC;OACzE,QAAA;AAKP,cACE,GAAA,eAAAA,SAAA,cAAC,0CAAe,UAAQ;IAAC,OAAO;MAC9B,GAAA,eAAAA,SAAA,eAAC,GAAA,4CAAkB,MAChB,QAAA,CAAA;AAKP,UAAO,GAAA,kBAAAG,SAAS,aAAa,UAAU,eAAA;AACzC;;;;;;AC3EA,IAAAC,iBAAwB;AAExB,yBAAoB;AACpB,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,6BAA6B;AAAA,EAC7B,gBAAgB;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,EACT;AAAA;AAAA;AAAA,EAGA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAAM;AACJ,MAAI,WAAW;AACf,MAAI,UAAU;AACZ,mBAA2B,wBAAI,2CAAgB,EAAE,UAAU,SAAS,UAAU,SAAS,CAAC;AAAA,EAC1F;AACA,QAAM,cAAU,wBAAQ,MAAM;AAC5B,QAAI,oBAAoB,4BAA4B;AAClD,yBAAmB,iBAAiB;AAAA,IACtC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA,gBAAgB,OAAO,SAAS,aAAa;AAAA,IAC7C,gBAAgB,OAAO,SAAS,aAAa;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,iBAAiB,EAAE,OAAO,SAAS,cAA0B,wBAAI,2CAAc,EAAE,QAAQ,cAA0B,wBAAI,cAAc,EAAE,eAAe,cAA0B,wBAAI,2CAAiB,EAAE,GAAG,YAAY,UAAU,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChR;;;ACpDA,IAAAC,iBAAwB;AACxB,SAAS,kBAAkB,OAAO;AAChC,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,uBAAuB,iBAAiB,OAAO,SAAS,cAAc;AAC5E,aAAO,wBAAQ,MAAM;AACnB,QAAIC,KAAI;AACR,UAAM,kBAAkB,MAAMA,MAAK,MAAM,mBAAmB,OAAOA,MAAK,yBAAyB,OAAO,KAAK;AAC7G,QAAI,mBAAmB,YAAY,CAAC,MAAM,OAAO;AAC/C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG,CAAC,MAAM,gBAAgB,sBAAsB,MAAM,KAAK,CAAC;AAC9D;", "names": ["_a", "React", "import_react", "_a", "React", "$ffhGL$useState", "$ffhGL$useEffect", "$h9FiU$react", "$h9FiU$useContext", "d", "b", "__assign", "ObjectWithoutPrototypeCache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "_a", "opt", "startsWith", "fromCodePoint", "fromEntries", "_a", "codePointAt", "trimStart", "trimEnd", "matchIdentifierAtIndex", "<PERSON><PERSON><PERSON>", "ErrorCode", "FormatError", "InvalidValueError", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PART_TYPE", "_a", "IntlMessageFormat", "$dRVb8$useEffect", "$39EOa$useState", "$39EOa$useRef", "$39EOa$useEffect", "$39EOa$useCallback", "position", "$cgawC$react", "$cgawC$useRef", "$cgawC$useContext", "$cgawC$useMemo", "node", "$cgawC$useEffect", "$cgawC$useRef", "scope", "$cgawC$react", "$cgawC$useEffect", "treeNode", "$isWE5$useRef", "$isWE5$useState", "$isWE5$useCallback", "isFocused", "$jtpZv$useEffect", "$7yuSY$createContext", "$7yuSY$useContext", "$4AOtR$react", "$4AOtR$useContext", "$4AOtR$useState", "$4AOtR$useMemo", "$4AOtR$reactdom", "$1CM7W$react", "$1CM7W$useState", "$1CM7W$useMemo", "$1CM7W$reactdom", "import_react", "import_react", "_a"]}
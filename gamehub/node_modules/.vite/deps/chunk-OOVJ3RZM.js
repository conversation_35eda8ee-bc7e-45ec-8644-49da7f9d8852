import {
  useSafeLayoutEffect
} from "./chunk-7U5HACHX.js";
import {
  CloseFilledIcon
} from "./chunk-5IPK33QS.js";
import {
  $f7dceffc5ad7768b$export$4e328f61c538687f,
  filterDOMProps,
  useDOMRef,
  useLabelPlacement,
  useProviderContext
} from "./chunk-NHQQQ5IV.js";
import {
  $313b98861ee5dd6c$export$d6875122194c7b44,
  $3ef42575df84b30b$export$9d1611c77c2fe928,
  $431fbd86ca7dc216$export$f21a1ffae260145a,
  $458b0a5536c1a7cf$export$40bfa8c7b0832715,
  $507fabe10e71c6fb$export$8397ddfc504fdb9a,
  $5dc95899b306f630$export$c9058316764c140e,
  $6179b936705e76d3$export$ae780daf29e6d456,
  $65484d02dcb7eb3e$export$457c3d6518dd4c6f,
  $8ae05eaa5c114e9c$export$7f54fc3180508a52,
  $99facab73266f662$export$5add1d006293d136,
  $9ab94262bd0047c7$export$420e68273165f4ec,
  $bdb11010cef70236$export$b4cc09c592e8fdb8,
  $bdb11010cef70236$export$f680877a34711e37,
  $df56164dff5785e2$export$4338b53315abf666,
  $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c,
  $f645667febf57a63$export$4c014de7c8940b4c,
  $f6c31cce2adf654f$export$45712eceda6fad21,
  $ff5963eb1fccf552$export$e08e3b67e392101e
} from "./chunk-EWPKFISU.js";
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-5K7QB4ZL.js";
import {
  clsx,
  dataAttr,
  form,
  input,
  isEmpty,
  objectToDeps,
  safeAriaLabel
} from "./chunk-CMXIESOC.js";
import {
  require_jsx_runtime
} from "./chunk-HVLLINLV.js";
import {
  require_react
} from "./chunk-HKLPI2XQ.js";
import {
  __toESM
} from "./chunk-ZS7NZCD4.js";

// node_modules/@heroui/input/dist/chunk-F2LJ3HQE.mjs
var import_react8 = __toESM(require_react(), 1);

// node_modules/@react-aria/textfield/dist/useTextField.mjs
var import_react3 = __toESM(require_react(), 1);

// node_modules/@react-aria/label/dist/useLabel.mjs
function $d191a55c9702f145$export$8467354a121f1b9f(props) {
  let { id, label, "aria-labelledby": ariaLabelledby, "aria-label": ariaLabel, labelElementType = "label" } = props;
  id = (0, $bdb11010cef70236$export$f680877a34711e37)(id);
  let labelId = (0, $bdb11010cef70236$export$f680877a34711e37)();
  let labelProps = {};
  if (label) {
    ariaLabelledby = ariaLabelledby ? `${labelId} ${ariaLabelledby}` : labelId;
    labelProps = {
      id: labelId,
      htmlFor: labelElementType === "label" ? id : void 0
    };
  } else if (!ariaLabelledby && !ariaLabel && true)
    console.warn("If you do not provide a visible label, you must specify an aria-label or aria-labelledby attribute for accessibility");
  let fieldProps = (0, $313b98861ee5dd6c$export$d6875122194c7b44)({
    id,
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledby
  });
  return {
    labelProps,
    fieldProps
  };
}

// node_modules/@react-aria/label/dist/useField.mjs
function $2baaea4c71418dea$export$294aa081a6c6f55d(props) {
  let { description, errorMessage, isInvalid, validationState } = props;
  let { labelProps, fieldProps } = (0, $d191a55c9702f145$export$8467354a121f1b9f)(props);
  let descriptionId = (0, $bdb11010cef70236$export$b4cc09c592e8fdb8)([
    Boolean(description),
    Boolean(errorMessage),
    isInvalid,
    validationState
  ]);
  let errorMessageId = (0, $bdb11010cef70236$export$b4cc09c592e8fdb8)([
    Boolean(description),
    Boolean(errorMessage),
    isInvalid,
    validationState
  ]);
  fieldProps = (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(fieldProps, {
    "aria-describedby": [
      descriptionId,
      // Use aria-describedby for error message because aria-errormessage is unsupported using VoiceOver or NVDA. See https://github.com/adobe/react-spectrum/issues/1346#issuecomment-740136268
      errorMessageId,
      props["aria-describedby"]
    ].filter(Boolean).join(" ") || void 0
  });
  return {
    labelProps,
    fieldProps,
    descriptionProps: {
      id: descriptionId
    },
    errorMessageProps: {
      id: errorMessageId
    }
  };
}

// node_modules/@react-aria/form/dist/useFormValidation.mjs
var import_react = __toESM(require_react(), 1);
function $e93e671b31057976$export$b8473d3665f3a75a(props, state, ref) {
  let { validationBehavior, focus } = props;
  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {
    if (validationBehavior === "native" && (ref === null || ref === void 0 ? void 0 : ref.current) && !ref.current.disabled) {
      let errorMessage = state.realtimeValidation.isInvalid ? state.realtimeValidation.validationErrors.join(" ") || "Invalid value." : "";
      ref.current.setCustomValidity(errorMessage);
      if (!ref.current.hasAttribute("title"))
        ref.current.title = "";
      if (!state.realtimeValidation.isInvalid)
        state.updateValidation($e93e671b31057976$var$getNativeValidity(ref.current));
    }
  });
  let onReset = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(() => {
    state.resetValidation();
  });
  let onInvalid = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)((e) => {
    var _ref_current;
    if (!state.displayValidation.isInvalid)
      state.commitValidation();
    let form2 = ref === null || ref === void 0 ? void 0 : (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.form;
    if (!e.defaultPrevented && ref && form2 && $e93e671b31057976$var$getFirstInvalidInput(form2) === ref.current) {
      var _ref_current1;
      if (focus)
        focus();
      else
        (_ref_current1 = ref.current) === null || _ref_current1 === void 0 ? void 0 : _ref_current1.focus();
      (0, $507fabe10e71c6fb$export$8397ddfc504fdb9a)("keyboard");
    }
    e.preventDefault();
  });
  let onChange = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(() => {
    state.commitValidation();
  });
  (0, import_react.useEffect)(() => {
    let input2 = ref === null || ref === void 0 ? void 0 : ref.current;
    if (!input2)
      return;
    let form2 = input2.form;
    input2.addEventListener("invalid", onInvalid);
    input2.addEventListener("change", onChange);
    form2 === null || form2 === void 0 ? void 0 : form2.addEventListener("reset", onReset);
    return () => {
      input2.removeEventListener("invalid", onInvalid);
      input2.removeEventListener("change", onChange);
      form2 === null || form2 === void 0 ? void 0 : form2.removeEventListener("reset", onReset);
    };
  }, [
    ref,
    onInvalid,
    onChange,
    onReset,
    validationBehavior
  ]);
}
function $e93e671b31057976$var$getValidity(input2) {
  let validity = input2.validity;
  return {
    badInput: validity.badInput,
    customError: validity.customError,
    patternMismatch: validity.patternMismatch,
    rangeOverflow: validity.rangeOverflow,
    rangeUnderflow: validity.rangeUnderflow,
    stepMismatch: validity.stepMismatch,
    tooLong: validity.tooLong,
    tooShort: validity.tooShort,
    typeMismatch: validity.typeMismatch,
    valueMissing: validity.valueMissing,
    valid: validity.valid
  };
}
function $e93e671b31057976$var$getNativeValidity(input2) {
  return {
    isInvalid: !input2.validity.valid,
    validationDetails: $e93e671b31057976$var$getValidity(input2),
    validationErrors: input2.validationMessage ? [
      input2.validationMessage
    ] : []
  };
}
function $e93e671b31057976$var$getFirstInvalidInput(form2) {
  for (let i = 0; i < form2.elements.length; i++) {
    let element = form2.elements[i];
    if (!element.validity.valid)
      return element;
  }
  return null;
}

// node_modules/@react-stately/form/dist/useFormValidationState.mjs
var import_react2 = __toESM(require_react(), 1);
var $e5be200c675c3b3a$export$aca958c65c314e6c = {
  badInput: false,
  customError: false,
  patternMismatch: false,
  rangeOverflow: false,
  rangeUnderflow: false,
  stepMismatch: false,
  tooLong: false,
  tooShort: false,
  typeMismatch: false,
  valueMissing: false,
  valid: true
};
var $e5be200c675c3b3a$var$CUSTOM_VALIDITY_STATE = {
  ...$e5be200c675c3b3a$export$aca958c65c314e6c,
  customError: true,
  valid: false
};
var $e5be200c675c3b3a$export$dad6ae84456c676a = {
  isInvalid: false,
  validationDetails: $e5be200c675c3b3a$export$aca958c65c314e6c,
  validationErrors: []
};
var $e5be200c675c3b3a$export$571b5131b7e65c11 = (0, import_react2.createContext)({});
var $e5be200c675c3b3a$export$a763b9476acd3eb = "__formValidationState" + Date.now();
function $e5be200c675c3b3a$export$fc1a364ae1f3ff10(props) {
  if (props[$e5be200c675c3b3a$export$a763b9476acd3eb]) {
    let { realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation } = props[$e5be200c675c3b3a$export$a763b9476acd3eb];
    return {
      realtimeValidation,
      displayValidation,
      updateValidation,
      resetValidation,
      commitValidation
    };
  }
  return $e5be200c675c3b3a$var$useFormValidationStateImpl(props);
}
function $e5be200c675c3b3a$var$useFormValidationStateImpl(props) {
  let { isInvalid, validationState, name, value, builtinValidation, validate, validationBehavior = "aria" } = props;
  if (validationState)
    isInvalid || (isInvalid = validationState === "invalid");
  let controlledError = isInvalid !== void 0 ? {
    isInvalid,
    validationErrors: [],
    validationDetails: $e5be200c675c3b3a$var$CUSTOM_VALIDITY_STATE
  } : null;
  let clientError = (0, import_react2.useMemo)(() => {
    if (!validate || value == null)
      return null;
    let validateErrors = $e5be200c675c3b3a$var$runValidate(validate, value);
    return $e5be200c675c3b3a$var$getValidationResult(validateErrors);
  }, [
    validate,
    value
  ]);
  if (builtinValidation === null || builtinValidation === void 0 ? void 0 : builtinValidation.validationDetails.valid)
    builtinValidation = void 0;
  let serverErrors = (0, import_react2.useContext)($e5be200c675c3b3a$export$571b5131b7e65c11);
  let serverErrorMessages = (0, import_react2.useMemo)(() => {
    if (name)
      return Array.isArray(name) ? name.flatMap((name2) => $e5be200c675c3b3a$var$asArray(serverErrors[name2])) : $e5be200c675c3b3a$var$asArray(serverErrors[name]);
    return [];
  }, [
    serverErrors,
    name
  ]);
  let [lastServerErrors, setLastServerErrors] = (0, import_react2.useState)(serverErrors);
  let [isServerErrorCleared, setServerErrorCleared] = (0, import_react2.useState)(false);
  if (serverErrors !== lastServerErrors) {
    setLastServerErrors(serverErrors);
    setServerErrorCleared(false);
  }
  let serverError = (0, import_react2.useMemo)(() => $e5be200c675c3b3a$var$getValidationResult(isServerErrorCleared ? [] : serverErrorMessages), [
    isServerErrorCleared,
    serverErrorMessages
  ]);
  let nextValidation = (0, import_react2.useRef)($e5be200c675c3b3a$export$dad6ae84456c676a);
  let [currentValidity, setCurrentValidity] = (0, import_react2.useState)($e5be200c675c3b3a$export$dad6ae84456c676a);
  let lastError = (0, import_react2.useRef)($e5be200c675c3b3a$export$dad6ae84456c676a);
  let commitValidation = () => {
    if (!commitQueued)
      return;
    setCommitQueued(false);
    let error = clientError || builtinValidation || nextValidation.current;
    if (!$e5be200c675c3b3a$var$isEqualValidation(error, lastError.current)) {
      lastError.current = error;
      setCurrentValidity(error);
    }
  };
  let [commitQueued, setCommitQueued] = (0, import_react2.useState)(false);
  (0, import_react2.useEffect)(commitValidation);
  let realtimeValidation = controlledError || serverError || clientError || builtinValidation || $e5be200c675c3b3a$export$dad6ae84456c676a;
  let displayValidation = validationBehavior === "native" ? controlledError || serverError || currentValidity : controlledError || serverError || clientError || builtinValidation || currentValidity;
  return {
    realtimeValidation,
    displayValidation,
    updateValidation(value2) {
      if (validationBehavior === "aria" && !$e5be200c675c3b3a$var$isEqualValidation(currentValidity, value2))
        setCurrentValidity(value2);
      else
        nextValidation.current = value2;
    },
    resetValidation() {
      let error = $e5be200c675c3b3a$export$dad6ae84456c676a;
      if (!$e5be200c675c3b3a$var$isEqualValidation(error, lastError.current)) {
        lastError.current = error;
        setCurrentValidity(error);
      }
      if (validationBehavior === "native")
        setCommitQueued(false);
      setServerErrorCleared(true);
    },
    commitValidation() {
      if (validationBehavior === "native")
        setCommitQueued(true);
      setServerErrorCleared(true);
    }
  };
}
function $e5be200c675c3b3a$var$asArray(v) {
  if (!v)
    return [];
  return Array.isArray(v) ? v : [
    v
  ];
}
function $e5be200c675c3b3a$var$runValidate(validate, value) {
  if (typeof validate === "function") {
    let e = validate(value);
    if (e && typeof e !== "boolean")
      return $e5be200c675c3b3a$var$asArray(e);
  }
  return [];
}
function $e5be200c675c3b3a$var$getValidationResult(errors) {
  return errors.length ? {
    isInvalid: true,
    validationErrors: errors,
    validationDetails: $e5be200c675c3b3a$var$CUSTOM_VALIDITY_STATE
  } : null;
}
function $e5be200c675c3b3a$var$isEqualValidation(a, b) {
  if (a === b)
    return true;
  return !!a && !!b && a.isInvalid === b.isInvalid && a.validationErrors.length === b.validationErrors.length && a.validationErrors.every((a2, i) => a2 === b.validationErrors[i]) && Object.entries(a.validationDetails).every(([k, v]) => b.validationDetails[k] === v);
}
function $e5be200c675c3b3a$export$75ee7c75d68f5b0e(...results) {
  let errors = /* @__PURE__ */ new Set();
  let isInvalid = false;
  let validationDetails = {
    ...$e5be200c675c3b3a$export$aca958c65c314e6c
  };
  for (let v of results) {
    var _validationDetails, _key;
    for (let e of v.validationErrors)
      errors.add(e);
    isInvalid || (isInvalid = v.isInvalid);
    for (let key in validationDetails)
      (_validationDetails = validationDetails)[_key = key] || (_validationDetails[_key] = v.validationDetails[key]);
  }
  validationDetails.valid = !isInvalid;
  return {
    isInvalid,
    validationErrors: [
      ...errors
    ],
    validationDetails
  };
}

// node_modules/@react-aria/textfield/dist/useTextField.mjs
function $2d73ec29415bd339$export$712718f7aec83d5(props, ref) {
  let { inputElementType = "input", isDisabled = false, isRequired = false, isReadOnly = false, type = "text", validationBehavior = "aria" } = props;
  let [value, setValue] = (0, $458b0a5536c1a7cf$export$40bfa8c7b0832715)(props.value, props.defaultValue || "", props.onChange);
  let { focusableProps } = (0, $f645667febf57a63$export$4c014de7c8940b4c)(props, ref);
  let validationState = (0, $e5be200c675c3b3a$export$fc1a364ae1f3ff10)({
    ...props,
    value
  });
  let { isInvalid, validationErrors, validationDetails } = validationState.displayValidation;
  let { labelProps, fieldProps, descriptionProps, errorMessageProps } = (0, $2baaea4c71418dea$export$294aa081a6c6f55d)({
    ...props,
    isInvalid,
    errorMessage: props.errorMessage || validationErrors
  });
  let domProps = (0, $65484d02dcb7eb3e$export$457c3d6518dd4c6f)(props, {
    labelable: true
  });
  const inputOnlyProps = {
    type,
    pattern: props.pattern
  };
  (0, $99facab73266f662$export$5add1d006293d136)(ref, value, setValue);
  (0, $e93e671b31057976$export$b8473d3665f3a75a)(props, validationState, ref);
  (0, import_react3.useEffect)(() => {
    if (ref.current instanceof (0, $431fbd86ca7dc216$export$f21a1ffae260145a)(ref.current).HTMLTextAreaElement) {
      let input2 = ref.current;
      Object.defineProperty(input2, "defaultValue", {
        get: () => input2.value,
        set: () => {
        },
        configurable: true
      });
    }
  }, [
    ref
  ]);
  return {
    labelProps,
    inputProps: (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(domProps, inputElementType === "input" ? inputOnlyProps : void 0, {
      disabled: isDisabled,
      readOnly: isReadOnly,
      required: isRequired && validationBehavior === "native",
      "aria-required": isRequired && validationBehavior === "aria" || void 0,
      "aria-invalid": isInvalid || void 0,
      "aria-errormessage": props["aria-errormessage"],
      "aria-activedescendant": props["aria-activedescendant"],
      "aria-autocomplete": props["aria-autocomplete"],
      "aria-haspopup": props["aria-haspopup"],
      "aria-controls": props["aria-controls"],
      value,
      onChange: (e) => setValue(e.target.value),
      autoComplete: props.autoComplete,
      autoCapitalize: props.autoCapitalize,
      maxLength: props.maxLength,
      minLength: props.minLength,
      name: props.name,
      placeholder: props.placeholder,
      inputMode: props.inputMode,
      autoCorrect: props.autoCorrect,
      spellCheck: props.spellCheck,
      [parseInt((0, import_react3.default).version, 10) >= 17 ? "enterKeyHint" : "enterkeyhint"]: props.enterKeyHint,
      // Clipboard events
      onCopy: props.onCopy,
      onCut: props.onCut,
      onPaste: props.onPaste,
      // Composition events
      onCompositionEnd: props.onCompositionEnd,
      onCompositionStart: props.onCompositionStart,
      onCompositionUpdate: props.onCompositionUpdate,
      // Selection events
      onSelect: props.onSelect,
      // Input events
      onBeforeInput: props.onBeforeInput,
      onInput: props.onInput,
      ...focusableProps,
      ...fieldProps
    }),
    descriptionProps,
    errorMessageProps,
    isInvalid,
    validationErrors,
    validationDetails
  };
}

// node_modules/@react-aria/textfield/dist/useFormattedTextField.mjs
var import_react4 = __toESM(require_react(), 1);
function $d841c8010a73d545$var$supportsNativeBeforeInputEvent() {
  return typeof window !== "undefined" && window.InputEvent && typeof InputEvent.prototype.getTargetRanges === "function";
}
function $d841c8010a73d545$export$4f384c9210e583c3(props, state, inputRef) {
  let onBeforeInputFallback = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)((e) => {
    let input2 = inputRef.current;
    if (!input2)
      return;
    let nextValue = null;
    switch (e.inputType) {
      case "historyUndo":
      case "historyRedo":
        return;
      case "insertLineBreak":
        return;
      case "deleteContent":
      case "deleteByCut":
      case "deleteByDrag":
        nextValue = input2.value.slice(0, input2.selectionStart) + input2.value.slice(input2.selectionEnd);
        break;
      case "deleteContentForward":
        nextValue = input2.selectionEnd === input2.selectionStart ? input2.value.slice(0, input2.selectionStart) + input2.value.slice(input2.selectionEnd + 1) : input2.value.slice(0, input2.selectionStart) + input2.value.slice(input2.selectionEnd);
        break;
      case "deleteContentBackward":
        nextValue = input2.selectionEnd === input2.selectionStart ? input2.value.slice(0, input2.selectionStart - 1) + input2.value.slice(input2.selectionStart) : input2.value.slice(0, input2.selectionStart) + input2.value.slice(input2.selectionEnd);
        break;
      case "deleteSoftLineBackward":
      case "deleteHardLineBackward":
        nextValue = input2.value.slice(input2.selectionStart);
        break;
      default:
        if (e.data != null)
          nextValue = input2.value.slice(0, input2.selectionStart) + e.data + input2.value.slice(input2.selectionEnd);
        break;
    }
    if (nextValue == null || !state.validate(nextValue))
      e.preventDefault();
  });
  (0, import_react4.useEffect)(() => {
    if (!$d841c8010a73d545$var$supportsNativeBeforeInputEvent() || !inputRef.current)
      return;
    let input2 = inputRef.current;
    input2.addEventListener("beforeinput", onBeforeInputFallback, false);
    return () => {
      input2.removeEventListener("beforeinput", onBeforeInputFallback, false);
    };
  }, [
    inputRef,
    onBeforeInputFallback
  ]);
  let onBeforeInput = !$d841c8010a73d545$var$supportsNativeBeforeInputEvent() ? (e) => {
    let nextValue = e.target.value.slice(0, e.target.selectionStart) + e.data + e.target.value.slice(e.target.selectionEnd);
    if (!state.validate(nextValue))
      e.preventDefault();
  } : null;
  let { labelProps, inputProps: textFieldProps, descriptionProps, errorMessageProps, ...validation } = (0, $2d73ec29415bd339$export$712718f7aec83d5)(props, inputRef);
  let compositionStartState = (0, import_react4.useRef)(null);
  return {
    inputProps: (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(textFieldProps, {
      onBeforeInput,
      onCompositionStart() {
        let { value, selectionStart, selectionEnd } = inputRef.current;
        compositionStartState.current = {
          value,
          selectionStart,
          selectionEnd
        };
      },
      onCompositionEnd() {
        if (inputRef.current && !state.validate(inputRef.current.value)) {
          let { value, selectionStart, selectionEnd } = compositionStartState.current;
          inputRef.current.value = value;
          inputRef.current.setSelectionRange(selectionStart, selectionEnd);
          state.setInputValue(value);
        }
      }
    }),
    labelProps,
    descriptionProps,
    errorMessageProps,
    ...validation
  };
}

// node_modules/@heroui/form/dist/chunk-BSTJ7ZCN.mjs
var import_react5 = __toESM(require_react(), 1);
var DEFAULT_SLOT = Symbol("default");
function useSlottedContext(context, slot) {
  let ctx = (0, import_react5.useContext)(context);
  if (slot === null) {
    return null;
  }
  if (ctx && typeof ctx === "object" && "slots" in ctx && ctx.slots) {
    let availableSlots = new Intl.ListFormat().format(Object.keys(ctx.slots).map((p) => `"${p}"`));
    if (!slot && !ctx.slots[DEFAULT_SLOT]) {
      throw new Error(`A slot prop is required. Valid slot names are ${availableSlots}.`);
    }
    let slotKey = slot || DEFAULT_SLOT;
    if (!ctx.slots[slotKey]) {
      throw new Error(`Invalid slot "${slot}". Valid slot names are ${availableSlots}.`);
    }
    return ctx.slots[slotKey];
  }
  return ctx;
}
function useContextProps(props, ref, context) {
  let ctx = useSlottedContext(context, props.slot) || {};
  let { ref: contextRef, ...contextProps } = ctx;
  let mergedRef = $df56164dff5785e2$export$4338b53315abf666((0, import_react5.useMemo)(() => $5dc95899b306f630$export$c9058316764c140e(ref, contextRef), [ref, contextRef]));
  let mergedProps = $3ef42575df84b30b$export$9d1611c77c2fe928(contextProps, props);
  if ("style" in contextProps && contextProps.style && "style" in props && props.style) {
    if (typeof contextProps.style === "function" || typeof props.style === "function") {
      mergedProps.style = (renderProps) => {
        let contextStyle = typeof contextProps.style === "function" ? contextProps.style(renderProps) : contextProps.style;
        let defaultStyle = { ...renderProps.defaultStyle, ...contextStyle };
        let style = typeof props.style === "function" ? props.style({ ...renderProps, defaultStyle }) : props.style;
        return { ...defaultStyle, ...style };
      };
    } else {
      mergedProps.style = { ...contextProps.style, ...props.style };
    }
  }
  return [mergedProps, mergedRef];
}

// node_modules/@heroui/form/dist/chunk-SLABUSGS.mjs
var import_react6 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var FormContext = (0, import_react6.createContext)(null);
var Form = (0, import_react6.forwardRef)(function Form2(props, ref) {
  [props, ref] = useContextProps(props, ref, FormContext);
  let { validationErrors, validationBehavior = "native", children, className, ...domProps } = props;
  const styles = (0, import_react6.useMemo)(() => form({ className }), [className]);
  return (0, import_jsx_runtime.jsx)("form", { noValidate: validationBehavior !== "native", ...domProps, ref, className: styles, children: (0, import_jsx_runtime.jsx)(FormContext.Provider, { value: { ...props, validationBehavior }, children: (0, import_jsx_runtime.jsx)($e5be200c675c3b3a$export$571b5131b7e65c11.Provider, { value: validationErrors != null ? validationErrors : {}, children }) }) });
});

// node_modules/@heroui/form/dist/chunk-LAX3QLKM.mjs
var import_react7 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var Form22 = (0, import_react7.forwardRef)(function Form3(props, ref) {
  var _a, _b;
  const globalContext = useProviderContext();
  const validationBehavior = (_b = (_a = props.validationBehavior) != null ? _a : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _b : "native";
  return (0, import_jsx_runtime2.jsx)(Form, { ...props, ref, validationBehavior });
});

// node_modules/@heroui/input/dist/chunk-F2LJ3HQE.mjs
function useInput(originalProps) {
  var _a, _b, _c, _d, _e, _f, _g;
  const globalContext = useProviderContext();
  const { validationBehavior: formValidationBehavior } = useSlottedContext(FormContext) || {};
  const [props, variantProps] = mapPropsVariants(originalProps, input.variantKeys);
  const {
    ref,
    as,
    type,
    label,
    baseRef,
    wrapperRef,
    description,
    className,
    classNames,
    autoFocus,
    startContent,
    endContent,
    onClear,
    onChange,
    validationState,
    validationBehavior = (_a = formValidationBehavior != null ? formValidationBehavior : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _a : "native",
    innerWrapperRef: innerWrapperRefProp,
    onValueChange = () => {
    },
    ...otherProps
  } = props;
  const handleValueChange = (0, import_react8.useCallback)(
    (value) => {
      onValueChange(value != null ? value : "");
    },
    [onValueChange]
  );
  const [isFocusWithin, setFocusWithin] = (0, import_react8.useState)(false);
  const Component = as || "div";
  const disableAnimation = (_c = (_b = originalProps.disableAnimation) != null ? _b : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _c : false;
  const domRef = useDOMRef(ref);
  const baseDomRef = useDOMRef(baseRef);
  const inputWrapperRef = useDOMRef(wrapperRef);
  const innerWrapperRef = useDOMRef(innerWrapperRefProp);
  const [inputValue, setInputValue] = $458b0a5536c1a7cf$export$40bfa8c7b0832715(
    props.value,
    (_d = props.defaultValue) != null ? _d : "",
    handleValueChange
  );
  const isFileTypeInput = type === "file";
  const hasUploadedFiles = ((_g = (_f = (_e = domRef == null ? void 0 : domRef.current) == null ? void 0 : _e.files) == null ? void 0 : _f.length) != null ? _g : 0) > 0;
  const isFilledByDefault = ["date", "time", "month", "week", "range"].includes(type);
  const isFilled = !isEmpty(inputValue) || isFilledByDefault || hasUploadedFiles;
  const isFilledWithin = isFilled || isFocusWithin;
  const isHiddenType = type === "hidden";
  const isMultiline = originalProps.isMultiline;
  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className, isFilled ? "is-filled" : "");
  const handleClear = (0, import_react8.useCallback)(() => {
    var _a2;
    if (isFileTypeInput) {
      domRef.current.value = "";
    } else {
      setInputValue("");
    }
    onClear == null ? void 0 : onClear();
    (_a2 = domRef.current) == null ? void 0 : _a2.focus();
  }, [setInputValue, onClear, isFileTypeInput]);
  useSafeLayoutEffect(() => {
    if (!domRef.current)
      return;
    setInputValue(domRef.current.value);
  }, [domRef.current]);
  const {
    labelProps,
    inputProps,
    isInvalid: isAriaInvalid,
    validationErrors,
    validationDetails,
    descriptionProps,
    errorMessageProps
  } = $2d73ec29415bd339$export$712718f7aec83d5(
    {
      ...originalProps,
      validationBehavior,
      autoCapitalize: originalProps.autoCapitalize,
      value: inputValue,
      "aria-label": safeAriaLabel(
        originalProps["aria-label"],
        originalProps.label,
        originalProps.placeholder
      ),
      inputElementType: isMultiline ? "textarea" : "input",
      onChange: setInputValue
    },
    domRef
  );
  if (isFileTypeInput) {
    delete inputProps.value;
    delete inputProps.onChange;
  }
  const { isFocusVisible, isFocused, focusProps } = $f7dceffc5ad7768b$export$4e328f61c538687f({
    autoFocus,
    isTextInput: true
  });
  const { isHovered, hoverProps } = $6179b936705e76d3$export$ae780daf29e6d456({ isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled) });
  const { isHovered: isLabelHovered, hoverProps: labelHoverProps } = $6179b936705e76d3$export$ae780daf29e6d456({
    isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled)
  });
  const { focusProps: clearFocusProps, isFocusVisible: isClearButtonFocusVisible } = $f7dceffc5ad7768b$export$4e328f61c538687f();
  const { focusWithinProps } = $9ab94262bd0047c7$export$420e68273165f4ec({
    onFocusWithinChange: setFocusWithin
  });
  const { pressProps: clearPressProps } = $f6c31cce2adf654f$export$45712eceda6fad21({
    isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled) || !!(originalProps == null ? void 0 : originalProps.isReadOnly),
    onPress: handleClear
  });
  const isInvalid = validationState === "invalid" || isAriaInvalid;
  const labelPlacement = useLabelPlacement({
    labelPlacement: originalProps.labelPlacement,
    label
  });
  const errorMessage = typeof props.errorMessage === "function" ? props.errorMessage({ isInvalid, validationErrors, validationDetails }) : props.errorMessage || (validationErrors == null ? void 0 : validationErrors.join(" "));
  const isClearable = !!onClear || originalProps.isClearable;
  const hasElements = !!label || !!description || !!errorMessage;
  const hasPlaceholder = !!props.placeholder;
  const hasLabel = !!label;
  const hasHelper = !!description || !!errorMessage;
  const shouldLabelBeOutside = labelPlacement === "outside" || labelPlacement === "outside-left";
  const shouldLabelBeInside = labelPlacement === "inside";
  const isPlaceholderShown = domRef.current ? (!domRef.current.value || domRef.current.value === "" || !inputValue || inputValue === "") && hasPlaceholder : false;
  const isOutsideLeft = labelPlacement === "outside-left";
  const hasStartContent = !!startContent;
  const isLabelOutside = shouldLabelBeOutside ? labelPlacement === "outside-left" || hasPlaceholder || labelPlacement === "outside" && hasStartContent : false;
  const isLabelOutsideAsPlaceholder = labelPlacement === "outside" && !hasPlaceholder && !hasStartContent;
  const slots = (0, import_react8.useMemo)(
    () => input({
      ...variantProps,
      isInvalid,
      labelPlacement,
      isClearable,
      disableAnimation
    }),
    [
      objectToDeps(variantProps),
      isInvalid,
      labelPlacement,
      isClearable,
      hasStartContent,
      disableAnimation
    ]
  );
  const getBaseProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ref: baseDomRef,
        className: slots.base({ class: baseStyles }),
        "data-slot": "base",
        "data-filled": dataAttr(
          isFilled || hasPlaceholder || hasStartContent || isPlaceholderShown || isFileTypeInput
        ),
        "data-filled-within": dataAttr(
          isFilledWithin || hasPlaceholder || hasStartContent || isPlaceholderShown || isFileTypeInput
        ),
        "data-focus-within": dataAttr(isFocusWithin),
        "data-focus-visible": dataAttr(isFocusVisible),
        "data-readonly": dataAttr(originalProps.isReadOnly),
        "data-focus": dataAttr(isFocused),
        "data-hover": dataAttr(isHovered || isLabelHovered),
        "data-required": dataAttr(originalProps.isRequired),
        "data-invalid": dataAttr(isInvalid),
        "data-disabled": dataAttr(originalProps.isDisabled),
        "data-has-elements": dataAttr(hasElements),
        "data-has-helper": dataAttr(hasHelper),
        "data-has-label": dataAttr(hasLabel),
        "data-has-value": dataAttr(!isPlaceholderShown),
        "data-hidden": dataAttr(isHiddenType),
        ...focusWithinProps,
        ...props2
      };
    },
    [
      slots,
      baseStyles,
      isFilled,
      isFocused,
      isHovered,
      isLabelHovered,
      isInvalid,
      hasHelper,
      hasLabel,
      hasElements,
      isPlaceholderShown,
      hasStartContent,
      isFocusWithin,
      isFocusVisible,
      isFilledWithin,
      hasPlaceholder,
      focusWithinProps,
      isHiddenType,
      originalProps.isReadOnly,
      originalProps.isRequired,
      originalProps.isDisabled
    ]
  );
  const getLabelProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        "data-slot": "label",
        className: slots.label({ class: classNames == null ? void 0 : classNames.label }),
        ...$3ef42575df84b30b$export$9d1611c77c2fe928(labelProps, labelHoverProps, props2)
      };
    },
    [slots, isLabelHovered, labelProps, classNames == null ? void 0 : classNames.label]
  );
  const handleKeyDown = (0, import_react8.useCallback)(
    (e) => {
      if (e.key === "Escape" && inputValue && (isClearable || onClear) && !originalProps.isReadOnly) {
        setInputValue("");
        onClear == null ? void 0 : onClear();
      }
    },
    [inputValue, setInputValue, onClear, isClearable, originalProps.isReadOnly]
  );
  const getInputProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        "data-slot": "input",
        "data-filled": dataAttr(isFilled),
        "data-filled-within": dataAttr(isFilledWithin),
        "data-has-start-content": dataAttr(hasStartContent),
        "data-has-end-content": dataAttr(!!endContent),
        "data-type": type,
        className: slots.input({
          class: clsx(
            classNames == null ? void 0 : classNames.input,
            isFilled ? "is-filled" : "",
            isMultiline ? "pe-0" : "",
            type === "password" ? "[&::-ms-reveal]:hidden" : ""
          )
        }),
        ...$3ef42575df84b30b$export$9d1611c77c2fe928(
          focusProps,
          inputProps,
          filterDOMProps(otherProps, {
            enabled: true,
            labelable: true,
            omitEventNames: new Set(Object.keys(inputProps))
          }),
          props2
        ),
        "aria-readonly": dataAttr(originalProps.isReadOnly),
        onChange: $ff5963eb1fccf552$export$e08e3b67e392101e(inputProps.onChange, onChange),
        onKeyDown: $ff5963eb1fccf552$export$e08e3b67e392101e(inputProps.onKeyDown, props2.onKeyDown, handleKeyDown),
        ref: domRef
      };
    },
    [
      slots,
      inputValue,
      focusProps,
      inputProps,
      otherProps,
      isFilled,
      isFilledWithin,
      hasStartContent,
      endContent,
      classNames == null ? void 0 : classNames.input,
      originalProps.isReadOnly,
      originalProps.isRequired,
      onChange,
      handleKeyDown
    ]
  );
  const getInputWrapperProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ref: inputWrapperRef,
        "data-slot": "input-wrapper",
        "data-hover": dataAttr(isHovered || isLabelHovered),
        "data-focus-visible": dataAttr(isFocusVisible),
        "data-focus": dataAttr(isFocused),
        className: slots.inputWrapper({
          class: clsx(classNames == null ? void 0 : classNames.inputWrapper, isFilled ? "is-filled" : "")
        }),
        ...$3ef42575df84b30b$export$9d1611c77c2fe928(props2, hoverProps),
        onClick: (e) => {
          if (domRef.current && e.currentTarget === e.target) {
            domRef.current.focus();
          }
        },
        style: {
          cursor: "text",
          ...props2.style
        }
      };
    },
    [
      slots,
      isHovered,
      isLabelHovered,
      isFocusVisible,
      isFocused,
      inputValue,
      classNames == null ? void 0 : classNames.inputWrapper
    ]
  );
  const getInnerWrapperProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        ref: innerWrapperRef,
        "data-slot": "inner-wrapper",
        onClick: (e) => {
          if (domRef.current && e.currentTarget === e.target) {
            domRef.current.focus();
          }
        },
        className: slots.innerWrapper({
          class: clsx(classNames == null ? void 0 : classNames.innerWrapper, props2 == null ? void 0 : props2.className)
        })
      };
    },
    [slots, classNames == null ? void 0 : classNames.innerWrapper]
  );
  const getMainWrapperProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        "data-slot": "main-wrapper",
        className: slots.mainWrapper({
          class: clsx(classNames == null ? void 0 : classNames.mainWrapper, props2 == null ? void 0 : props2.className)
        })
      };
    },
    [slots, classNames == null ? void 0 : classNames.mainWrapper]
  );
  const getHelperWrapperProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        "data-slot": "helper-wrapper",
        className: slots.helperWrapper({
          class: clsx(classNames == null ? void 0 : classNames.helperWrapper, props2 == null ? void 0 : props2.className)
        })
      };
    },
    [slots, classNames == null ? void 0 : classNames.helperWrapper]
  );
  const getDescriptionProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        ...descriptionProps,
        "data-slot": "description",
        className: slots.description({ class: clsx(classNames == null ? void 0 : classNames.description, props2 == null ? void 0 : props2.className) })
      };
    },
    [slots, classNames == null ? void 0 : classNames.description]
  );
  const getErrorMessageProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        ...errorMessageProps,
        "data-slot": "error-message",
        className: slots.errorMessage({ class: clsx(classNames == null ? void 0 : classNames.errorMessage, props2 == null ? void 0 : props2.className) })
      };
    },
    [slots, errorMessageProps, classNames == null ? void 0 : classNames.errorMessage]
  );
  const getClearButtonProps = (0, import_react8.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        type: "button",
        tabIndex: -1,
        disabled: originalProps.isDisabled,
        "aria-label": "clear input",
        "data-slot": "clear-button",
        "data-focus-visible": dataAttr(isClearButtonFocusVisible),
        className: slots.clearButton({
          class: clsx(classNames == null ? void 0 : classNames.clearButton, props2 == null ? void 0 : props2.className)
        }),
        ...$3ef42575df84b30b$export$9d1611c77c2fe928(clearPressProps, clearFocusProps)
      };
    },
    [slots, isClearButtonFocusVisible, clearPressProps, clearFocusProps, classNames == null ? void 0 : classNames.clearButton]
  );
  return {
    Component,
    classNames,
    domRef,
    label,
    description,
    startContent,
    endContent,
    labelPlacement,
    isClearable,
    hasHelper,
    hasStartContent,
    isLabelOutside,
    isOutsideLeft,
    isLabelOutsideAsPlaceholder,
    shouldLabelBeOutside,
    shouldLabelBeInside,
    hasPlaceholder,
    isInvalid,
    errorMessage,
    getBaseProps,
    getLabelProps,
    getInputProps,
    getMainWrapperProps,
    getInputWrapperProps,
    getInnerWrapperProps,
    getHelperWrapperProps,
    getDescriptionProps,
    getErrorMessageProps,
    getClearButtonProps
  };
}

// node_modules/@heroui/input/dist/chunk-6MDMHKHV.mjs
var import_react9 = __toESM(require_react(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var Input = forwardRef((props, ref) => {
  const {
    Component,
    label,
    description,
    isClearable,
    startContent,
    endContent,
    labelPlacement,
    hasHelper,
    isOutsideLeft,
    shouldLabelBeOutside,
    errorMessage,
    isInvalid,
    getBaseProps,
    getLabelProps,
    getInputProps,
    getInnerWrapperProps,
    getInputWrapperProps,
    getMainWrapperProps,
    getHelperWrapperProps,
    getDescriptionProps,
    getErrorMessageProps,
    getClearButtonProps
  } = useInput({ ...props, ref });
  const labelContent = label ? (0, import_jsx_runtime3.jsx)("label", { ...getLabelProps(), children: label }) : null;
  const end = (0, import_react9.useMemo)(() => {
    if (isClearable) {
      return (0, import_jsx_runtime3.jsx)("button", { ...getClearButtonProps(), children: endContent || (0, import_jsx_runtime3.jsx)(CloseFilledIcon, {}) });
    }
    return endContent;
  }, [isClearable, getClearButtonProps]);
  const helperWrapper = (0, import_react9.useMemo)(() => {
    const shouldShowError = isInvalid && errorMessage;
    const hasContent = shouldShowError || description;
    if (!hasHelper || !hasContent)
      return null;
    return (0, import_jsx_runtime3.jsx)("div", { ...getHelperWrapperProps(), children: shouldShowError ? (0, import_jsx_runtime3.jsx)("div", { ...getErrorMessageProps(), children: errorMessage }) : (0, import_jsx_runtime3.jsx)("div", { ...getDescriptionProps(), children: description }) });
  }, [
    hasHelper,
    isInvalid,
    errorMessage,
    description,
    getHelperWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  ]);
  const innerWrapper = (0, import_react9.useMemo)(() => {
    return (0, import_jsx_runtime3.jsxs)("div", { ...getInnerWrapperProps(), children: [
      startContent,
      (0, import_jsx_runtime3.jsx)("input", { ...getInputProps() }),
      end
    ] });
  }, [startContent, end, getInputProps, getInnerWrapperProps]);
  const mainWrapper = (0, import_react9.useMemo)(() => {
    if (shouldLabelBeOutside) {
      return (0, import_jsx_runtime3.jsxs)("div", { ...getMainWrapperProps(), children: [
        (0, import_jsx_runtime3.jsxs)("div", { ...getInputWrapperProps(), children: [
          !isOutsideLeft ? labelContent : null,
          innerWrapper
        ] }),
        helperWrapper
      ] });
    }
    return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [
      (0, import_jsx_runtime3.jsxs)("div", { ...getInputWrapperProps(), children: [
        labelContent,
        innerWrapper
      ] }),
      helperWrapper
    ] });
  }, [
    labelPlacement,
    helperWrapper,
    shouldLabelBeOutside,
    labelContent,
    innerWrapper,
    errorMessage,
    description,
    getMainWrapperProps,
    getInputWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  ]);
  return (0, import_jsx_runtime3.jsxs)(Component, { ...getBaseProps(), children: [
    isOutsideLeft ? labelContent : null,
    mainWrapper
  ] });
});
Input.displayName = "HeroUI.Input";
var input_default = Input;

// node_modules/@heroui/input/dist/chunk-RIE4DNUQ.mjs
var import_react13 = __toESM(require_react(), 1);

// node_modules/@babel/runtime/helpers/esm/extends.js
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function(n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t)
        ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}

// node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js
function _objectWithoutPropertiesLoose(r, e) {
  if (null == r)
    return {};
  var t = {};
  for (var n in r)
    if ({}.hasOwnProperty.call(r, n)) {
      if (-1 !== e.indexOf(n))
        continue;
      t[n] = r[n];
    }
  return t;
}

// node_modules/react-textarea-autosize/dist/react-textarea-autosize.browser.development.esm.js
var React3 = __toESM(require_react());

// node_modules/use-latest/dist/use-latest.esm.js
var import_react11 = __toESM(require_react());

// node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.browser.esm.js
var import_react10 = __toESM(require_react());
var index = import_react10.useLayoutEffect;

// node_modules/use-latest/dist/use-latest.esm.js
var useLatest = function useLatest2(value) {
  var ref = import_react11.default.useRef(value);
  index(function() {
    ref.current = value;
  });
  return ref;
};

// node_modules/use-composed-ref/dist/use-composed-ref.esm.js
var import_react12 = __toESM(require_react());
var updateRef = function updateRef2(ref, value) {
  if (typeof ref === "function") {
    ref(value);
    return;
  }
  ref.current = value;
};
var useComposedRef = function useComposedRef2(libRef, userRef) {
  var prevUserRef = import_react12.default.useRef();
  return import_react12.default.useCallback(function(instance) {
    libRef.current = instance;
    if (prevUserRef.current) {
      updateRef(prevUserRef.current, null);
    }
    prevUserRef.current = userRef;
    if (!userRef) {
      return;
    }
    updateRef(userRef, instance);
  }, [userRef]);
};

// node_modules/react-textarea-autosize/dist/react-textarea-autosize.browser.development.esm.js
var HIDDEN_TEXTAREA_STYLE = {
  "min-height": "0",
  "max-height": "none",
  height: "0",
  visibility: "hidden",
  overflow: "hidden",
  position: "absolute",
  "z-index": "-1000",
  top: "0",
  right: "0",
  display: "block"
};
var forceHiddenStyles = function forceHiddenStyles2(node) {
  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function(key) {
    node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], "important");
  });
};
var forceHiddenStyles$1 = forceHiddenStyles;
var hiddenTextarea = null;
var getHeight = function getHeight2(node, sizingData) {
  var height = node.scrollHeight;
  if (sizingData.sizingStyle.boxSizing === "border-box") {
    return height + sizingData.borderSize;
  }
  return height - sizingData.paddingSize;
};
function calculateNodeHeight(sizingData, value, minRows, maxRows) {
  if (minRows === void 0) {
    minRows = 1;
  }
  if (maxRows === void 0) {
    maxRows = Infinity;
  }
  if (!hiddenTextarea) {
    hiddenTextarea = document.createElement("textarea");
    hiddenTextarea.setAttribute("tabindex", "-1");
    hiddenTextarea.setAttribute("aria-hidden", "true");
    forceHiddenStyles$1(hiddenTextarea);
  }
  if (hiddenTextarea.parentNode === null) {
    document.body.appendChild(hiddenTextarea);
  }
  var paddingSize = sizingData.paddingSize, borderSize = sizingData.borderSize, sizingStyle = sizingData.sizingStyle;
  var boxSizing = sizingStyle.boxSizing;
  Object.keys(sizingStyle).forEach(function(_key) {
    var key = _key;
    hiddenTextarea.style[key] = sizingStyle[key];
  });
  forceHiddenStyles$1(hiddenTextarea);
  hiddenTextarea.value = value;
  var height = getHeight(hiddenTextarea, sizingData);
  hiddenTextarea.value = value;
  height = getHeight(hiddenTextarea, sizingData);
  hiddenTextarea.value = "x";
  var rowHeight = hiddenTextarea.scrollHeight - paddingSize;
  var minHeight = rowHeight * minRows;
  if (boxSizing === "border-box") {
    minHeight = minHeight + paddingSize + borderSize;
  }
  height = Math.max(minHeight, height);
  var maxHeight = rowHeight * maxRows;
  if (boxSizing === "border-box") {
    maxHeight = maxHeight + paddingSize + borderSize;
  }
  height = Math.min(maxHeight, height);
  return [height, rowHeight];
}
var noop = function noop2() {
};
var pick = function pick2(props, obj) {
  return props.reduce(function(acc, prop) {
    acc[prop] = obj[prop];
    return acc;
  }, {});
};
var SIZING_STYLE = [
  "borderBottomWidth",
  "borderLeftWidth",
  "borderRightWidth",
  "borderTopWidth",
  "boxSizing",
  "fontFamily",
  "fontSize",
  "fontStyle",
  "fontWeight",
  "letterSpacing",
  "lineHeight",
  "paddingBottom",
  "paddingLeft",
  "paddingRight",
  "paddingTop",
  // non-standard
  "tabSize",
  "textIndent",
  // non-standard
  "textRendering",
  "textTransform",
  "width",
  "wordBreak",
  "wordSpacing",
  "scrollbarGutter"
];
var isIE = !!document.documentElement.currentStyle;
var getSizingData = function getSizingData2(node) {
  var style = window.getComputedStyle(node);
  if (style === null) {
    return null;
  }
  var sizingStyle = pick(SIZING_STYLE, style);
  var boxSizing = sizingStyle.boxSizing;
  if (boxSizing === "") {
    return null;
  }
  if (isIE && boxSizing === "border-box") {
    sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + "px";
  }
  var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);
  var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);
  return {
    sizingStyle,
    paddingSize,
    borderSize
  };
};
var getSizingData$1 = getSizingData;
function useListener(target, type, listener) {
  var latestListener = useLatest(listener);
  React3.useLayoutEffect(function() {
    var handler = function handler2(ev) {
      return latestListener.current(ev);
    };
    if (!target) {
      return;
    }
    target.addEventListener(type, handler);
    return function() {
      return target.removeEventListener(type, handler);
    };
  }, []);
}
var useFormResetListener = function useFormResetListener2(libRef, listener) {
  useListener(document.body, "reset", function(ev) {
    if (libRef.current.form === ev.target) {
      listener(ev);
    }
  });
};
var useWindowResizeListener = function useWindowResizeListener2(listener) {
  useListener(window, "resize", listener);
};
var useFontsLoadedListener = function useFontsLoadedListener2(listener) {
  useListener(document.fonts, "loadingdone", listener);
};
var _excluded = ["cacheMeasurements", "maxRows", "minRows", "onChange", "onHeightChange"];
var TextareaAutosize = function TextareaAutosize2(_ref, userRef) {
  var cacheMeasurements = _ref.cacheMeasurements, maxRows = _ref.maxRows, minRows = _ref.minRows, _ref$onChange = _ref.onChange, onChange = _ref$onChange === void 0 ? noop : _ref$onChange, _ref$onHeightChange = _ref.onHeightChange, onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange, props = _objectWithoutPropertiesLoose(_ref, _excluded);
  if (props.style) {
    if ("maxHeight" in props.style) {
      throw new Error("Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.");
    }
    if ("minHeight" in props.style) {
      throw new Error("Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.");
    }
  }
  var isControlled = props.value !== void 0;
  var libRef = React3.useRef(null);
  var ref = useComposedRef(libRef, userRef);
  var heightRef = React3.useRef(0);
  var measurementsCacheRef = React3.useRef();
  var resizeTextarea = function resizeTextarea2() {
    var node = libRef.current;
    var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);
    if (!nodeSizingData) {
      return;
    }
    measurementsCacheRef.current = nodeSizingData;
    var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || "x", minRows, maxRows), height = _calculateNodeHeight[0], rowHeight = _calculateNodeHeight[1];
    if (heightRef.current !== height) {
      heightRef.current = height;
      node.style.setProperty("height", height + "px", "important");
      onHeightChange(height, {
        rowHeight
      });
    }
  };
  var handleChange = function handleChange2(event) {
    if (!isControlled) {
      resizeTextarea();
    }
    onChange(event);
  };
  {
    React3.useLayoutEffect(resizeTextarea);
    useFormResetListener(libRef, function() {
      if (!isControlled) {
        var currentValue = libRef.current.value;
        requestAnimationFrame(function() {
          var node = libRef.current;
          if (node && currentValue !== node.value) {
            resizeTextarea();
          }
        });
      }
    });
    useWindowResizeListener(resizeTextarea);
    useFontsLoadedListener(resizeTextarea);
    return React3.createElement("textarea", _extends({}, props, {
      onChange: handleChange,
      ref
    }));
  }
};
var index2 = React3.forwardRef(TextareaAutosize);

// node_modules/@heroui/input/dist/chunk-RIE4DNUQ.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var Textarea = forwardRef(
  ({
    style,
    minRows = 3,
    maxRows = 8,
    cacheMeasurements = false,
    disableAutosize = false,
    onHeightChange,
    ...otherProps
  }, ref) => {
    const {
      Component,
      label,
      description,
      startContent,
      endContent,
      hasHelper,
      shouldLabelBeOutside,
      shouldLabelBeInside,
      isInvalid,
      errorMessage,
      getBaseProps,
      getLabelProps,
      getInputProps,
      getInnerWrapperProps,
      getInputWrapperProps,
      getHelperWrapperProps,
      getDescriptionProps,
      getErrorMessageProps,
      isClearable,
      getClearButtonProps
    } = useInput({ ...otherProps, ref, isMultiline: true });
    const [hasMultipleRows, setIsHasMultipleRows] = (0, import_react13.useState)(minRows > 1);
    const [isLimitReached, setIsLimitReached] = (0, import_react13.useState)(false);
    const labelContent = label ? (0, import_jsx_runtime4.jsx)("label", { ...getLabelProps(), children: label }) : null;
    const inputProps = getInputProps();
    const handleHeightChange = (height, meta) => {
      if (minRows === 1) {
        setIsHasMultipleRows(height >= meta.rowHeight * 2);
      }
      if (maxRows > minRows) {
        const limitReached = height >= maxRows * meta.rowHeight;
        setIsLimitReached(limitReached);
      }
      onHeightChange == null ? void 0 : onHeightChange(height, meta);
    };
    const content = disableAutosize ? (0, import_jsx_runtime4.jsx)("textarea", { ...inputProps, style: $3ef42575df84b30b$export$9d1611c77c2fe928(inputProps.style, style != null ? style : {}) }) : (0, import_jsx_runtime4.jsx)(
      index2,
      {
        ...inputProps,
        cacheMeasurements,
        "data-hide-scroll": dataAttr(!isLimitReached),
        maxRows,
        minRows,
        style: $3ef42575df84b30b$export$9d1611c77c2fe928(inputProps.style, style != null ? style : {}),
        onHeightChange: handleHeightChange
      }
    );
    const clearButtonContent = (0, import_react13.useMemo)(() => {
      return isClearable ? (0, import_jsx_runtime4.jsx)("button", { ...getClearButtonProps(), children: (0, import_jsx_runtime4.jsx)(CloseFilledIcon, {}) }) : null;
    }, [isClearable, getClearButtonProps]);
    const innerWrapper = (0, import_react13.useMemo)(() => {
      if (startContent || endContent) {
        return (0, import_jsx_runtime4.jsxs)("div", { ...getInnerWrapperProps(), children: [
          startContent,
          content,
          endContent
        ] });
      }
      return (0, import_jsx_runtime4.jsx)("div", { ...getInnerWrapperProps(), children: content });
    }, [startContent, inputProps, endContent, getInnerWrapperProps]);
    const shouldShowError = isInvalid && errorMessage;
    const hasHelperContent = shouldShowError || description;
    return (0, import_jsx_runtime4.jsxs)(Component, { ...getBaseProps(), children: [
      shouldLabelBeOutside ? labelContent : null,
      (0, import_jsx_runtime4.jsxs)("div", { ...getInputWrapperProps(), "data-has-multiple-rows": dataAttr(hasMultipleRows), children: [
        shouldLabelBeInside ? labelContent : null,
        innerWrapper,
        clearButtonContent
      ] }),
      hasHelper && hasHelperContent ? (0, import_jsx_runtime4.jsx)("div", { ...getHelperWrapperProps(), children: shouldShowError ? (0, import_jsx_runtime4.jsx)("div", { ...getErrorMessageProps(), children: errorMessage }) : (0, import_jsx_runtime4.jsx)("div", { ...getDescriptionProps(), children: description }) }) : null
    ] });
  }
);
Textarea.displayName = "HeroUI.Textarea";
var textarea_default = Textarea;

export {
  $d191a55c9702f145$export$8467354a121f1b9f,
  $2baaea4c71418dea$export$294aa081a6c6f55d,
  $e93e671b31057976$export$b8473d3665f3a75a,
  $e5be200c675c3b3a$export$aca958c65c314e6c,
  $e5be200c675c3b3a$export$dad6ae84456c676a,
  $e5be200c675c3b3a$export$a763b9476acd3eb,
  $e5be200c675c3b3a$export$fc1a364ae1f3ff10,
  $e5be200c675c3b3a$export$75ee7c75d68f5b0e,
  $2d73ec29415bd339$export$712718f7aec83d5,
  $d841c8010a73d545$export$4f384c9210e583c3,
  useSlottedContext,
  FormContext,
  Form22 as Form2,
  useInput,
  input_default,
  textarea_default
};
//# sourceMappingURL=chunk-OOVJ3RZM.js.map

{"version": 3, "sources": ["../../@heroui/use-theme/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { useCallback, useEffect, useState } from \"react\";\nvar ThemeProps = {\n  // localStorage key for storing the current theme\n  KEY: \"heroui-theme\",\n  // light theme\n  LIGHT: \"light\",\n  // dark theme\n  DARK: \"dark\",\n  // system theme\n  SYSTEM: \"system\"\n};\nfunction useTheme(defaultTheme = ThemeProps.SYSTEM) {\n  const MEDIA = \"(prefers-color-scheme: dark)\";\n  const [theme, setThemeState] = useState(() => {\n    var _a;\n    const storedTheme = localStorage.getItem(ThemeProps.KEY);\n    if (storedTheme) return storedTheme;\n    if (defaultTheme === ThemeProps.SYSTEM) {\n      return ((_a = window.matchMedia) == null ? void 0 : _a.call(window, MEDIA).matches) ? ThemeProps.DARK : ThemeProps.LIGHT;\n    }\n    return defaultTheme;\n  });\n  const setTheme = useCallback(\n    (newTheme) => {\n      var _a;\n      const targetTheme = newTheme === ThemeProps.SYSTEM ? ((_a = window.matchMedia) == null ? void 0 : _a.call(window, MEDIA).matches) ? ThemeProps.DARK : ThemeProps.LIGHT : newTheme;\n      localStorage.setItem(ThemeProps.KEY, newTheme);\n      document.documentElement.classList.remove(theme);\n      document.documentElement.classList.add(targetTheme);\n      setThemeState(newTheme);\n    },\n    [theme]\n  );\n  const handleMediaQuery = useCallback(\n    (e) => {\n      if (defaultTheme === ThemeProps.SYSTEM) {\n        setTheme(e.matches ? ThemeProps.DARK : ThemeProps.LIGHT);\n      }\n    },\n    [setTheme]\n  );\n  useEffect(() => setTheme(theme), [theme, setTheme]);\n  useEffect(() => {\n    const media = window.matchMedia(MEDIA);\n    media.addEventListener(\"change\", handleMediaQuery);\n    return () => media.removeEventListener(\"change\", handleMediaQuery);\n  }, [handleMediaQuery]);\n  return { theme, setTheme };\n}\nexport {\n  ThemeProps,\n  useTheme\n};\n"], "mappings": ";;;;;;;;AACA,mBAAiD;AACjD,IAAI,aAAa;AAAA;AAAA,EAEf,KAAK;AAAA;AAAA,EAEL,OAAO;AAAA;AAAA,EAEP,MAAM;AAAA;AAAA,EAEN,QAAQ;AACV;AACA,SAAS,SAAS,eAAe,WAAW,QAAQ;AAClD,QAAM,QAAQ;AACd,QAAM,CAAC,OAAO,aAAa,QAAI,uBAAS,MAAM;AAC5C,QAAI;AACJ,UAAM,cAAc,aAAa,QAAQ,WAAW,GAAG;AACvD,QAAI;AAAa,aAAO;AACxB,QAAI,iBAAiB,WAAW,QAAQ;AACtC,eAAS,KAAK,OAAO,eAAe,OAAO,SAAS,GAAG,KAAK,QAAQ,KAAK,EAAE,WAAW,WAAW,OAAO,WAAW;AAAA,IACrH;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,eAAW;AAAA,IACf,CAAC,aAAa;AACZ,UAAI;AACJ,YAAM,cAAc,aAAa,WAAW,WAAW,KAAK,OAAO,eAAe,OAAO,SAAS,GAAG,KAAK,QAAQ,KAAK,EAAE,WAAW,WAAW,OAAO,WAAW,QAAQ;AACzK,mBAAa,QAAQ,WAAW,KAAK,QAAQ;AAC7C,eAAS,gBAAgB,UAAU,OAAO,KAAK;AAC/C,eAAS,gBAAgB,UAAU,IAAI,WAAW;AAClD,oBAAc,QAAQ;AAAA,IACxB;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AACA,QAAM,uBAAmB;AAAA,IACvB,CAAC,MAAM;AACL,UAAI,iBAAiB,WAAW,QAAQ;AACtC,iBAAS,EAAE,UAAU,WAAW,OAAO,WAAW,KAAK;AAAA,MACzD;AAAA,IACF;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACA,8BAAU,MAAM,SAAS,KAAK,GAAG,CAAC,OAAO,QAAQ,CAAC;AAClD,8BAAU,MAAM;AACd,UAAM,QAAQ,OAAO,WAAW,KAAK;AACrC,UAAM,iBAAiB,UAAU,gBAAgB;AACjD,WAAO,MAAM,MAAM,oBAAoB,UAAU,gBAAgB;AAAA,EACnE,GAAG,CAAC,gBAAgB,CAAC;AACrB,SAAO,EAAE,OAAO,SAAS;AAC3B;", "names": []}
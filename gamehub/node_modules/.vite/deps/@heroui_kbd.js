import {
  forwardRef,
  mapPropsVariants
} from "./chunk-C4VLZYLP.js";
import {
  clsx,
  kbd,
  objectToDeps
} from "./chunk-B4Z3JF7O.js";
import "./chunk-XDJEYPHN.js";
import {
  require_jsx_runtime
} from "./chunk-6HCJQXVG.js";
import {
  require_react
} from "./chunk-XEXUAUZA.js";
import {
  __toESM
} from "./chunk-LQ2VYIYD.js";

// node_modules/@heroui/kbd/dist/chunk-33JVVCIB.mjs
var import_react = __toESM(require_react(), 1);
function useKbd(originalProps) {
  const [props, variantProps] = mapPropsVariants(originalProps, kbd.variantKeys);
  const { as, children, className, keys, title, classNames, ...otherProps } = props;
  const Component = as || "kbd";
  const slots = (0, import_react.useMemo)(
    () => kbd({
      ...variantProps
    }),
    [objectToDeps(variantProps)]
  );
  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);
  const keysToRender = typeof keys === "string" ? [keys] : Array.isArray(keys) ? keys : [];
  const getKbdProps = (props2 = {}) => ({
    ...otherProps,
    ...props2,
    className: clsx(slots.base({ class: clsx(baseStyles, props2.className) }))
  });
  return { Component, slots, classNames, title, children, keysToRender, getKbdProps };
}

// node_modules/@heroui/kbd/dist/chunk-ZWTE5ZFD.mjs
var kbdKeysMap = {
  command: "⌘",
  shift: "⇧",
  ctrl: "⌃",
  option: "⌥",
  enter: "↵",
  delete: "⌫",
  escape: "⎋",
  tab: "⇥",
  capslock: "⇪",
  up: "↑",
  right: "→",
  down: "↓",
  left: "←",
  pageup: "⇞",
  pagedown: "⇟",
  home: "↖",
  end: "↘",
  help: "?",
  space: "␣",
  fn: "Fn",
  win: "⌘",
  alt: "⌥"
};
var kbdKeysLabelMap = {
  command: "Command",
  shift: "Shift",
  ctrl: "Control",
  option: "Option",
  enter: "Enter",
  delete: "Delete",
  escape: "Escape",
  tab: "Tab",
  capslock: "Caps Lock",
  up: "Up",
  right: "Right",
  down: "Down",
  left: "Left",
  pageup: "Page Up",
  pagedown: "Page Down",
  home: "Home",
  end: "End",
  help: "Help",
  space: "Space",
  fn: "Fn",
  win: "Win",
  alt: "Alt"
};

// node_modules/@heroui/kbd/dist/chunk-EZX35FZF.mjs
var import_react2 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Kbd = forwardRef((props, ref) => {
  const { Component, children, slots, classNames, keysToRender, getKbdProps } = useKbd({
    ...props
  });
  const keysContent = (0, import_react2.useMemo)(() => {
    return keysToRender.map((key) => (0, import_jsx_runtime.jsx)(
      "abbr",
      {
        className: slots.abbr({ class: classNames == null ? void 0 : classNames.abbr }),
        title: kbdKeysLabelMap[key],
        children: kbdKeysMap[key]
      },
      key
    ));
  }, [keysToRender]);
  return (0, import_jsx_runtime.jsxs)(Component, { ref, ...getKbdProps(), children: [
    keysContent,
    children && (0, import_jsx_runtime.jsx)("span", { className: slots.content({ class: classNames == null ? void 0 : classNames.content }), children })
  ] });
});
Kbd.displayName = "HeroUI.Kbd";
var kbd_default = Kbd;
export {
  kbd_default as Kbd,
  useKbd
};
//# sourceMappingURL=@heroui_kbd.js.map

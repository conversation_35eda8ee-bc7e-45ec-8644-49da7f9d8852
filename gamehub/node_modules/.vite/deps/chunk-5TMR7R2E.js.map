{"version": 3, "sources": ["../../@react-stately/toggle/dist/packages/@react-stately/toggle/src/useToggleState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ToggleStateOptions} from '@react-types/checkbox';\nimport {useControlledState} from '@react-stately/utils';\n\nexport type {ToggleStateOptions};\n\nexport interface ToggleState {\n  /** Whether the toggle is selected. */\n  readonly isSelected: boolean,\n\n  /** Updates selection state. */\n  setSelected(isSelected: boolean): void,\n\n  /** Toggle the selection state. */\n  toggle(): void\n}\n\n/**\n * Provides state management for toggle components like checkboxes and switches.\n */\nexport function useToggleState(props: ToggleStateOptions = {}): ToggleState {\n  let {isReadOnly} = props;\n\n  // have to provide an empty function so useControlledState doesn't throw a fit\n  // can't use useControlledState's prop calling because we need the event object from the change\n  let [isSelected, setSelected] = useControlledState(props.isSelected, props.defaultSelected || false, props.onChange);\n\n  function updateSelected(value) {\n    if (!isReadOnly) {\n      setSelected(value);\n    }\n  }\n\n  function toggleState() {\n    if (!isReadOnly) {\n      setSelected(!isSelected);\n    }\n  }\n\n  return {\n    isSelected,\n    setSelected: updateSelected,\n    toggle: toggleState\n  };\n}\n"], "mappings": ";;;;;;;;;;;AA+BO,SAAS,0CAAe,QAA4B,CAAC,GAAC;AAC3D,MAAI,EAAA,WAAW,IAAI;AAInB,MAAI,CAAC,YAAY,WAAA,KAAe,GAAA,2CAAmB,MAAM,YAAY,MAAM,mBAAmB,OAAO,MAAM,QAAQ;AAEnH,WAAS,eAAe,OAAK;AAC3B,QAAI,CAAC;AACH,kBAAY,KAAA;EAEhB;AAEA,WAAS,cAAA;AACP,QAAI,CAAC;AACH,kBAAY,CAAC,UAAA;EAEjB;AAEA,SAAO;;IAEL,aAAa;IACb,QAAQ;EACV;AACF;", "names": []}
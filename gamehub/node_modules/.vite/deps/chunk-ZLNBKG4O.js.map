{"version": 3, "sources": ["../../@heroui/framer-utils/dist/chunk-736YWA4T.mjs", "../../@heroui/framer-utils/dist/chunk-54L3M2TC.mjs", "../../@heroui/use-measure/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/transition-utils.ts\nvar TRANSITION_EASINGS = {\n  ease: [0.36, 0.66, 0.4, 1],\n  easeIn: [0.4, 0, 1, 1],\n  easeOut: [0, 0, 0.2, 1],\n  easeInOut: [0.4, 0, 0.2, 1],\n  spring: [0.155, 1.105, 0.295, 1.12],\n  springOut: [0.57, -0.15, 0.62, 0.07],\n  softSpring: [0.16, 1.11, 0.3, 1.02]\n};\nvar TRANSITION_DEFAULTS = {\n  enter: {\n    duration: 0.2,\n    ease: TRANSITION_EASINGS.easeOut\n  },\n  exit: {\n    duration: 0.1,\n    ease: TRANSITION_EASINGS.easeIn\n  }\n};\nvar TRANSITION_VARIANTS = {\n  scaleSpring: {\n    enter: {\n      transform: \"scale(1)\",\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        bounce: 0,\n        duration: 0.2\n      }\n    },\n    exit: {\n      transform: \"scale(0.85)\",\n      opacity: 0,\n      transition: {\n        type: \"easeOut\",\n        duration: 0.15\n      }\n    }\n  },\n  scaleSpringOpacity: {\n    initial: {\n      opacity: 0,\n      transform: \"scale(0.8)\"\n    },\n    enter: {\n      opacity: 1,\n      transform: \"scale(1)\",\n      transition: {\n        type: \"spring\",\n        bounce: 0,\n        duration: 0.3\n      }\n    },\n    exit: {\n      opacity: 0,\n      transform: \"scale(0.96)\",\n      transition: {\n        type: \"easeOut\",\n        bounce: 0,\n        duration: 0.15\n      }\n    }\n  },\n  scale: {\n    enter: { scale: 1 },\n    exit: { scale: 0.95 }\n  },\n  scaleFadeIn: {\n    enter: {\n      transform: \"scale(1)\",\n      opacity: 1,\n      transition: {\n        duration: 0.25,\n        ease: TRANSITION_EASINGS.easeIn\n      }\n    },\n    exit: {\n      transform: \"scale(0.95)\",\n      opacity: 0,\n      transition: {\n        duration: 0.2,\n        ease: TRANSITION_EASINGS.easeOut\n      }\n    }\n  },\n  scaleInOut: {\n    enter: {\n      transform: \"scale(1)\",\n      opacity: 1,\n      transition: {\n        duration: 0.4,\n        ease: TRANSITION_EASINGS.ease\n      }\n    },\n    exit: {\n      transform: \"scale(1.03)\",\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n        ease: TRANSITION_EASINGS.ease\n      }\n    }\n  },\n  fade: {\n    enter: {\n      opacity: 1,\n      transition: {\n        duration: 0.4,\n        ease: TRANSITION_EASINGS.ease\n      }\n    },\n    exit: {\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n        ease: TRANSITION_EASINGS.ease\n      }\n    }\n  },\n  collapse: {\n    enter: {\n      opacity: 1,\n      height: \"auto\",\n      transition: {\n        height: {\n          type: \"spring\",\n          bounce: 0,\n          duration: 0.3\n        },\n        opacity: {\n          easings: \"ease\",\n          duration: 0.4\n        }\n      }\n    },\n    exit: {\n      opacity: 0,\n      height: 0,\n      transition: {\n        easings: \"ease\",\n        duration: 0.3\n      }\n    }\n  }\n};\n\nexport {\n  TRANSITION_EASINGS,\n  TRANSITION_DEFAULTS,\n  TRANSITION_VARIANTS\n};\n", "\"use client\";\n\n// src/resizable-panel.tsx\nimport { forwardRef } from \"react\";\nimport { domAnimation, LazyMotion, m } from \"framer-motion\";\nimport { useMeasure } from \"@heroui/use-measure\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ResizablePanel = forwardRef(\n  (originalProps, ref) => {\n    const { children, ...props } = originalProps;\n    let [measureRef, bounds] = useMeasure();\n    return /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(\n      m.div,\n      {\n        ref,\n        animate: {\n          width: bounds.width && (bounds == null ? void 0 : bounds.width) > 0 ? bounds.width : \"auto\",\n          height: bounds.height && bounds.height > 0 ? bounds.height : \"auto\"\n        },\n        children: /* @__PURE__ */ jsx(\"div\", { ref: measureRef, ...props, children })\n      }\n    ) });\n  }\n);\nResizablePanel.displayName = \"HeroUI - ResizablePanel\";\n\nexport {\n  ResizablePanel\n};\n", "// src/index.ts\nimport { useCallback, useRef, useState } from \"react\";\nfunction useMeasure() {\n  const [dimensions, setDimensions] = useState({\n    width: null,\n    height: null\n  });\n  const previousObserver = useRef(null);\n  const customRef = useCallback((node) => {\n    if (previousObserver.current) {\n      previousObserver.current.disconnect();\n      previousObserver.current = null;\n    }\n    if ((node == null ? void 0 : node.nodeType) === Node.ELEMENT_NODE) {\n      const observer = new ResizeObserver(([entry]) => {\n        if (entry && entry.borderBoxSize) {\n          const { inlineSize: width, blockSize: height } = entry.borderBoxSize[0];\n          setDimensions({ width, height });\n        }\n      });\n      observer.observe(node);\n      previousObserver.current = observer;\n    }\n  }, []);\n  return [customRef, dimensions];\n}\nexport {\n  useMeasure\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAGA,IAAI,qBAAqB;AAAA,EACvB,MAAM,CAAC,MAAM,MAAM,KAAK,CAAC;AAAA,EACzB,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC;AAAA,EACrB,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC;AAAA,EACtB,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EAC1B,QAAQ,CAAC,OAAO,OAAO,OAAO,IAAI;AAAA,EAClC,WAAW,CAAC,MAAM,OAAO,MAAM,IAAI;AAAA,EACnC,YAAY,CAAC,MAAM,MAAM,KAAK,IAAI;AACpC;AACA,IAAI,sBAAsB;AAAA,EACxB,OAAO;AAAA,IACL,UAAU;AAAA,IACV,MAAM,mBAAmB;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,IACJ,UAAU;AAAA,IACV,MAAM,mBAAmB;AAAA,EAC3B;AACF;AACA,IAAI,sBAAsB;AAAA,EACxB,aAAa;AAAA,IACX,OAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,IAClB,SAAS;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,OAAO,EAAE,OAAO,EAAE;AAAA,IAClB,MAAM,EAAE,OAAO,KAAK;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,MACL,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM,mBAAmB;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,QACV,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;;;AChJA,IAAAA,gBAA2B;;;ACF3B,mBAA8C;AAC9C,SAAS,aAAa;AACpB,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS;AAAA,IAC3C,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,uBAAmB,qBAAO,IAAI;AACpC,QAAM,gBAAY,0BAAY,CAAC,SAAS;AACtC,QAAI,iBAAiB,SAAS;AAC5B,uBAAiB,QAAQ,WAAW;AACpC,uBAAiB,UAAU;AAAA,IAC7B;AACA,SAAK,QAAQ,OAAO,SAAS,KAAK,cAAc,KAAK,cAAc;AACjE,YAAM,WAAW,IAAI,eAAe,CAAC,CAAC,KAAK,MAAM;AAC/C,YAAI,SAAS,MAAM,eAAe;AAChC,gBAAM,EAAE,YAAY,OAAO,WAAW,OAAO,IAAI,MAAM,cAAc,CAAC;AACtE,wBAAc,EAAE,OAAO,OAAO,CAAC;AAAA,QACjC;AAAA,MACF,CAAC;AACD,eAAS,QAAQ,IAAI;AACrB,uBAAiB,UAAU;AAAA,IAC7B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,WAAW,UAAU;AAC/B;;;ADnBA,yBAAoB;AACpB,IAAI,qBAAiB;AAAA,EACnB,CAAC,eAAe,QAAQ;AACtB,UAAM,EAAE,UAAU,GAAG,MAAM,IAAI;AAC/B,QAAI,CAAC,YAAY,MAAM,IAAI,WAAW;AACtC,eAAuB,wBAAI,YAAY,EAAE,UAAU,cAAc,cAA0B;AAAA,MACzF,EAAE;AAAA,MACF;AAAA,QACE;AAAA,QACA,SAAS;AAAA,UACP,OAAO,OAAO,UAAU,UAAU,OAAO,SAAS,OAAO,SAAS,IAAI,OAAO,QAAQ;AAAA,UACrF,QAAQ,OAAO,UAAU,OAAO,SAAS,IAAI,OAAO,SAAS;AAAA,QAC/D;AAAA,QACA,cAA0B,wBAAI,OAAO,EAAE,KAAK,YAAY,GAAG,OAAO,SAAS,CAAC;AAAA,MAC9E;AAAA,IACF,EAAE,CAAC;AAAA,EACL;AACF;AACA,eAAe,cAAc;", "names": ["import_react"]}
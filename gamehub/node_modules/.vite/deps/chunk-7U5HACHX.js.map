{"version": 3, "sources": ["../../@heroui/use-safe-layout-effect/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { useEffect, useLayoutEffect } from \"react\";\nvar useSafeLayoutEffect = Boolean(globalThis == null ? void 0 : globalThis.document) ? useLayoutEffect : useEffect;\nexport {\n  useSafeLayoutEffect\n};\n"], "mappings": ";;;;;;;;AACA,mBAA2C;AAC3C,IAAI,sBAAsB,QAAQ,cAAc,OAAO,SAAS,WAAW,QAAQ,IAAI,+BAAkB;", "names": []}
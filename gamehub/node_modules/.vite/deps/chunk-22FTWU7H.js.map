{"version": 3, "sources": ["../../@heroui/input/dist/chunk-F2LJ3HQE.mjs", "../../@react-aria/label/dist/packages/@react-aria/label/src/useLabel.ts", "../../@react-aria/label/dist/packages/@react-aria/label/src/useField.ts", "../../@react-aria/form/dist/packages/@react-aria/form/src/useFormValidation.ts", "../../@react-stately/form/dist/packages/@react-stately/form/src/useFormValidationState.ts", "../../@react-aria/textfield/dist/packages/@react-aria/textfield/src/useTextField.ts", "../../@react-aria/textfield/dist/packages/@react-aria/textfield/src/useFormattedTextField.ts", "../../@heroui/form/dist/chunk-BSTJ7ZCN.mjs", "../../@heroui/form/dist/chunk-SLABUSGS.mjs", "../../@heroui/form/dist/chunk-LAX3QLKM.mjs", "../../@heroui/input/dist/chunk-6MDMHKHV.mjs", "../../@heroui/input/dist/chunk-RIE4DNUQ.mjs", "../../@babel/runtime/helpers/esm/extends.js", "../../@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../react-textarea-autosize/dist/react-textarea-autosize.browser.development.esm.js", "../../use-latest/dist/use-latest.esm.js", "../../use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.browser.esm.js", "../../use-composed-ref/dist/use-composed-ref.esm.js"], "sourcesContent": ["\"use client\";\n\n// src/use-input.ts\nimport { mapPropsVariants, useLabelPlacement, useProviderContext } from \"@heroui/system\";\nimport { useSafeLayoutEffect } from \"@heroui/use-safe-layout-effect\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { input } from \"@heroui/theme\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { useFocusWithin, useHover, usePress } from \"@react-aria/interactions\";\nimport { clsx, dataAttr, isEmpty, objectToDeps, safeAriaLabel } from \"@heroui/shared-utils\";\nimport { useControlledState } from \"@react-stately/utils\";\nimport { useMemo, useCallback, useState } from \"react\";\nimport { chain, mergeProps } from \"@react-aria/utils\";\nimport { useTextField } from \"@react-aria/textfield\";\nimport { FormContext, useSlottedContext } from \"@heroui/form\";\nfunction useInput(originalProps) {\n  var _a, _b, _c, _d, _e, _f, _g;\n  const globalContext = useProviderContext();\n  const { validationBehavior: formValidationBehavior } = useSlottedContext(FormContext) || {};\n  const [props, variantProps] = mapPropsVariants(originalProps, input.variantKeys);\n  const {\n    ref,\n    as,\n    type,\n    label,\n    baseRef,\n    wrapperRef,\n    description,\n    className,\n    classNames,\n    autoFocus,\n    startContent,\n    endContent,\n    onClear,\n    onChange,\n    validationState,\n    validationBehavior = (_a = formValidationBehavior != null ? formValidationBehavior : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _a : \"native\",\n    innerWrapperRef: innerWrapperRefProp,\n    onValueChange = () => {\n    },\n    ...otherProps\n  } = props;\n  const handleValueChange = useCallback(\n    (value) => {\n      onValueChange(value != null ? value : \"\");\n    },\n    [onValueChange]\n  );\n  const [isFocusWithin, setFocusWithin] = useState(false);\n  const Component = as || \"div\";\n  const disableAnimation = (_c = (_b = originalProps.disableAnimation) != null ? _b : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _c : false;\n  const domRef = useDOMRef(ref);\n  const baseDomRef = useDOMRef(baseRef);\n  const inputWrapperRef = useDOMRef(wrapperRef);\n  const innerWrapperRef = useDOMRef(innerWrapperRefProp);\n  const [inputValue, setInputValue] = useControlledState(\n    props.value,\n    (_d = props.defaultValue) != null ? _d : \"\",\n    handleValueChange\n  );\n  const isFileTypeInput = type === \"file\";\n  const hasUploadedFiles = ((_g = (_f = (_e = domRef == null ? void 0 : domRef.current) == null ? void 0 : _e.files) == null ? void 0 : _f.length) != null ? _g : 0) > 0;\n  const isFilledByDefault = [\"date\", \"time\", \"month\", \"week\", \"range\"].includes(type);\n  const isFilled = !isEmpty(inputValue) || isFilledByDefault || hasUploadedFiles;\n  const isFilledWithin = isFilled || isFocusWithin;\n  const isHiddenType = type === \"hidden\";\n  const isMultiline = originalProps.isMultiline;\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className, isFilled ? \"is-filled\" : \"\");\n  const handleClear = useCallback(() => {\n    var _a2;\n    if (isFileTypeInput) {\n      domRef.current.value = \"\";\n    } else {\n      setInputValue(\"\");\n    }\n    onClear == null ? void 0 : onClear();\n    (_a2 = domRef.current) == null ? void 0 : _a2.focus();\n  }, [setInputValue, onClear, isFileTypeInput]);\n  useSafeLayoutEffect(() => {\n    if (!domRef.current) return;\n    setInputValue(domRef.current.value);\n  }, [domRef.current]);\n  const {\n    labelProps,\n    inputProps,\n    isInvalid: isAriaInvalid,\n    validationErrors,\n    validationDetails,\n    descriptionProps,\n    errorMessageProps\n  } = useTextField(\n    {\n      ...originalProps,\n      validationBehavior,\n      autoCapitalize: originalProps.autoCapitalize,\n      value: inputValue,\n      \"aria-label\": safeAriaLabel(\n        originalProps[\"aria-label\"],\n        originalProps.label,\n        originalProps.placeholder\n      ),\n      inputElementType: isMultiline ? \"textarea\" : \"input\",\n      onChange: setInputValue\n    },\n    domRef\n  );\n  if (isFileTypeInput) {\n    delete inputProps.value;\n    delete inputProps.onChange;\n  }\n  const { isFocusVisible, isFocused, focusProps } = useFocusRing({\n    autoFocus,\n    isTextInput: true\n  });\n  const { isHovered, hoverProps } = useHover({ isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled) });\n  const { isHovered: isLabelHovered, hoverProps: labelHoverProps } = useHover({\n    isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled)\n  });\n  const { focusProps: clearFocusProps, isFocusVisible: isClearButtonFocusVisible } = useFocusRing();\n  const { focusWithinProps } = useFocusWithin({\n    onFocusWithinChange: setFocusWithin\n  });\n  const { pressProps: clearPressProps } = usePress({\n    isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled) || !!(originalProps == null ? void 0 : originalProps.isReadOnly),\n    onPress: handleClear\n  });\n  const isInvalid = validationState === \"invalid\" || isAriaInvalid;\n  const labelPlacement = useLabelPlacement({\n    labelPlacement: originalProps.labelPlacement,\n    label\n  });\n  const errorMessage = typeof props.errorMessage === \"function\" ? props.errorMessage({ isInvalid, validationErrors, validationDetails }) : props.errorMessage || (validationErrors == null ? void 0 : validationErrors.join(\" \"));\n  const isClearable = !!onClear || originalProps.isClearable;\n  const hasElements = !!label || !!description || !!errorMessage;\n  const hasPlaceholder = !!props.placeholder;\n  const hasLabel = !!label;\n  const hasHelper = !!description || !!errorMessage;\n  const shouldLabelBeOutside = labelPlacement === \"outside\" || labelPlacement === \"outside-left\";\n  const shouldLabelBeInside = labelPlacement === \"inside\";\n  const isPlaceholderShown = domRef.current ? (!domRef.current.value || domRef.current.value === \"\" || !inputValue || inputValue === \"\") && hasPlaceholder : false;\n  const isOutsideLeft = labelPlacement === \"outside-left\";\n  const hasStartContent = !!startContent;\n  const isLabelOutside = shouldLabelBeOutside ? labelPlacement === \"outside-left\" || hasPlaceholder || labelPlacement === \"outside\" && hasStartContent : false;\n  const isLabelOutsideAsPlaceholder = labelPlacement === \"outside\" && !hasPlaceholder && !hasStartContent;\n  const slots = useMemo(\n    () => input({\n      ...variantProps,\n      isInvalid,\n      labelPlacement,\n      isClearable,\n      disableAnimation\n    }),\n    [\n      objectToDeps(variantProps),\n      isInvalid,\n      labelPlacement,\n      isClearable,\n      hasStartContent,\n      disableAnimation\n    ]\n  );\n  const getBaseProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ref: baseDomRef,\n        className: slots.base({ class: baseStyles }),\n        \"data-slot\": \"base\",\n        \"data-filled\": dataAttr(\n          isFilled || hasPlaceholder || hasStartContent || isPlaceholderShown || isFileTypeInput\n        ),\n        \"data-filled-within\": dataAttr(\n          isFilledWithin || hasPlaceholder || hasStartContent || isPlaceholderShown || isFileTypeInput\n        ),\n        \"data-focus-within\": dataAttr(isFocusWithin),\n        \"data-focus-visible\": dataAttr(isFocusVisible),\n        \"data-readonly\": dataAttr(originalProps.isReadOnly),\n        \"data-focus\": dataAttr(isFocused),\n        \"data-hover\": dataAttr(isHovered || isLabelHovered),\n        \"data-required\": dataAttr(originalProps.isRequired),\n        \"data-invalid\": dataAttr(isInvalid),\n        \"data-disabled\": dataAttr(originalProps.isDisabled),\n        \"data-has-elements\": dataAttr(hasElements),\n        \"data-has-helper\": dataAttr(hasHelper),\n        \"data-has-label\": dataAttr(hasLabel),\n        \"data-has-value\": dataAttr(!isPlaceholderShown),\n        \"data-hidden\": dataAttr(isHiddenType),\n        ...focusWithinProps,\n        ...props2\n      };\n    },\n    [\n      slots,\n      baseStyles,\n      isFilled,\n      isFocused,\n      isHovered,\n      isLabelHovered,\n      isInvalid,\n      hasHelper,\n      hasLabel,\n      hasElements,\n      isPlaceholderShown,\n      hasStartContent,\n      isFocusWithin,\n      isFocusVisible,\n      isFilledWithin,\n      hasPlaceholder,\n      focusWithinProps,\n      isHiddenType,\n      originalProps.isReadOnly,\n      originalProps.isRequired,\n      originalProps.isDisabled\n    ]\n  );\n  const getLabelProps = useCallback(\n    (props2 = {}) => {\n      return {\n        \"data-slot\": \"label\",\n        className: slots.label({ class: classNames == null ? void 0 : classNames.label }),\n        ...mergeProps(labelProps, labelHoverProps, props2)\n      };\n    },\n    [slots, isLabelHovered, labelProps, classNames == null ? void 0 : classNames.label]\n  );\n  const handleKeyDown = useCallback(\n    (e) => {\n      if (e.key === \"Escape\" && inputValue && (isClearable || onClear) && !originalProps.isReadOnly) {\n        setInputValue(\"\");\n        onClear == null ? void 0 : onClear();\n      }\n    },\n    [inputValue, setInputValue, onClear, isClearable, originalProps.isReadOnly]\n  );\n  const getInputProps = useCallback(\n    (props2 = {}) => {\n      return {\n        \"data-slot\": \"input\",\n        \"data-filled\": dataAttr(isFilled),\n        \"data-filled-within\": dataAttr(isFilledWithin),\n        \"data-has-start-content\": dataAttr(hasStartContent),\n        \"data-has-end-content\": dataAttr(!!endContent),\n        \"data-type\": type,\n        className: slots.input({\n          class: clsx(\n            classNames == null ? void 0 : classNames.input,\n            isFilled ? \"is-filled\" : \"\",\n            isMultiline ? \"pe-0\" : \"\",\n            type === \"password\" ? \"[&::-ms-reveal]:hidden\" : \"\"\n          )\n        }),\n        ...mergeProps(\n          focusProps,\n          inputProps,\n          filterDOMProps(otherProps, {\n            enabled: true,\n            labelable: true,\n            omitEventNames: new Set(Object.keys(inputProps))\n          }),\n          props2\n        ),\n        \"aria-readonly\": dataAttr(originalProps.isReadOnly),\n        onChange: chain(inputProps.onChange, onChange),\n        onKeyDown: chain(inputProps.onKeyDown, props2.onKeyDown, handleKeyDown),\n        ref: domRef\n      };\n    },\n    [\n      slots,\n      inputValue,\n      focusProps,\n      inputProps,\n      otherProps,\n      isFilled,\n      isFilledWithin,\n      hasStartContent,\n      endContent,\n      classNames == null ? void 0 : classNames.input,\n      originalProps.isReadOnly,\n      originalProps.isRequired,\n      onChange,\n      handleKeyDown\n    ]\n  );\n  const getInputWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ref: inputWrapperRef,\n        \"data-slot\": \"input-wrapper\",\n        \"data-hover\": dataAttr(isHovered || isLabelHovered),\n        \"data-focus-visible\": dataAttr(isFocusVisible),\n        \"data-focus\": dataAttr(isFocused),\n        className: slots.inputWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.inputWrapper, isFilled ? \"is-filled\" : \"\")\n        }),\n        ...mergeProps(props2, hoverProps),\n        onClick: (e) => {\n          if (domRef.current && e.currentTarget === e.target) {\n            domRef.current.focus();\n          }\n        },\n        style: {\n          cursor: \"text\",\n          ...props2.style\n        }\n      };\n    },\n    [\n      slots,\n      isHovered,\n      isLabelHovered,\n      isFocusVisible,\n      isFocused,\n      inputValue,\n      classNames == null ? void 0 : classNames.inputWrapper\n    ]\n  );\n  const getInnerWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ref: innerWrapperRef,\n        \"data-slot\": \"inner-wrapper\",\n        onClick: (e) => {\n          if (domRef.current && e.currentTarget === e.target) {\n            domRef.current.focus();\n          }\n        },\n        className: slots.innerWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.innerWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.innerWrapper]\n  );\n  const getMainWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"main-wrapper\",\n        className: slots.mainWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.mainWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.mainWrapper]\n  );\n  const getHelperWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"helper-wrapper\",\n        className: slots.helperWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.helperWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.helperWrapper]\n  );\n  const getDescriptionProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...descriptionProps,\n        \"data-slot\": \"description\",\n        className: slots.description({ class: clsx(classNames == null ? void 0 : classNames.description, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.description]\n  );\n  const getErrorMessageProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...errorMessageProps,\n        \"data-slot\": \"error-message\",\n        className: slots.errorMessage({ class: clsx(classNames == null ? void 0 : classNames.errorMessage, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, errorMessageProps, classNames == null ? void 0 : classNames.errorMessage]\n  );\n  const getClearButtonProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        type: \"button\",\n        tabIndex: -1,\n        disabled: originalProps.isDisabled,\n        \"aria-label\": \"clear input\",\n        \"data-slot\": \"clear-button\",\n        \"data-focus-visible\": dataAttr(isClearButtonFocusVisible),\n        className: slots.clearButton({\n          class: clsx(classNames == null ? void 0 : classNames.clearButton, props2 == null ? void 0 : props2.className)\n        }),\n        ...mergeProps(clearPressProps, clearFocusProps)\n      };\n    },\n    [slots, isClearButtonFocusVisible, clearPressProps, clearFocusProps, classNames == null ? void 0 : classNames.clearButton]\n  );\n  return {\n    Component,\n    classNames,\n    domRef,\n    label,\n    description,\n    startContent,\n    endContent,\n    labelPlacement,\n    isClearable,\n    hasHelper,\n    hasStartContent,\n    isLabelOutside,\n    isOutsideLeft,\n    isLabelOutsideAsPlaceholder,\n    shouldLabelBeOutside,\n    shouldLabelBeInside,\n    hasPlaceholder,\n    isInvalid,\n    errorMessage,\n    getBaseProps,\n    getLabelProps,\n    getInputProps,\n    getMainWrapperProps,\n    getInputWrapperProps,\n    getInnerWrapperProps,\n    getHelperWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps,\n    getClearButtonProps\n  };\n}\n\nexport {\n  useInput\n};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, LabelableProps} from '@react-types/shared';\nimport {ElementType, LabelHTMLAttributes} from 'react';\nimport {useId, useLabels} from '@react-aria/utils';\n\nexport interface LabelAriaProps extends LabelableProps, DOMProps, AriaLabelingProps {\n  /**\n   * The HTML element used to render the label, e.g. 'label', or 'span'.\n   * @default 'label'\n   */\n  labelElementType?: ElementType\n}\n\nexport interface LabelAria {\n  /** Props to apply to the label container element. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props to apply to the field container element being labeled. */\n  fieldProps: AriaLabelingProps & DOMProps\n}\n\n/**\n * Provides the accessibility implementation for labels and their associated elements.\n * Labels provide context for user inputs.\n * @param props - The props for labels and fields.\n */\nexport function useLabel(props: LabelAriaProps): LabelAria {\n  let {\n    id,\n    label,\n    'aria-labelledby': ariaLabelledby,\n    'aria-label': ariaLabel,\n    labelElementType = 'label'\n  } = props;\n\n  id = useId(id);\n  let labelId = useId();\n  let labelProps = {};\n  if (label) {\n    ariaLabelledby = ariaLabelledby ? `${labelId} ${ariaLabelledby}` : labelId;\n    labelProps = {\n      id: labelId,\n      htmlFor: labelElementType === 'label' ? id : undefined\n    };\n  } else if (!ariaLabelledby && !ariaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide a visible label, you must specify an aria-label or aria-labelledby attribute for accessibility');\n  }\n\n  let fieldProps = useLabels({\n    id,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby\n  });\n\n  return {\n    labelProps,\n    fieldProps\n  };\n}\n", "/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, HelpTextProps, Validation} from '@react-types/shared';\nimport {LabelAria, LabelAriaProps, useLabel} from './useLabel';\nimport {mergeProps, useSlotId} from '@react-aria/utils';\n\nexport interface AriaFieldProps extends LabelAriaProps, HelpTextProps, Omit<Validation<any>, 'isRequired'> {}\n\nexport interface FieldAria extends LabelAria {\n  /** Props for the description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for input fields.\n * Fields accept user input, gain context from their label, and may display a description or error message.\n * @param props - Props for the Field.\n */\nexport function useField(props: AriaFieldProps): FieldAria {\n  let {description, errorMessage, isInvalid, validationState} = props;\n  let {labelProps, fieldProps} = useLabel(props);\n\n  let descriptionId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n  let errorMessageId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n\n  fieldProps = mergeProps(fieldProps, {\n    'aria-describedby': [\n      descriptionId,\n      // Use aria-describedby for error message because aria-errormessage is unsupported using VoiceOver or NVDA. See https://github.com/adobe/react-spectrum/issues/1346#issuecomment-740136268\n      errorMessageId,\n      props['aria-describedby']\n    ].filter(Boolean).join(' ') || undefined\n  });\n\n  return {\n    labelProps,\n    fieldProps,\n    descriptionProps: {\n      id: descriptionId\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    }\n  };\n}\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FormValidationState} from '@react-stately/form';\nimport {RefObject, Validation, ValidationResult} from '@react-types/shared';\nimport {setInteractionModality} from '@react-aria/interactions';\nimport {useEffect} from 'react';\nimport {useEffectEvent, useLayoutEffect} from '@react-aria/utils';\n\ntype ValidatableElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;\n\ninterface FormValidationProps<T> extends Validation<T> {\n  focus?: () => void\n}\n\nexport function useFormValidation<T>(props: FormValidationProps<T>, state: FormValidationState, ref: RefObject<ValidatableElement | null> | undefined): void {\n  let {validationBehavior, focus} = props;\n\n  // This is a useLayoutEffect so that it runs before the useEffect in useFormValidationState, which commits the validation change.\n  useLayoutEffect(() => {\n    if (validationBehavior === 'native' && ref?.current && !ref.current.disabled) {\n      let errorMessage = state.realtimeValidation.isInvalid ? state.realtimeValidation.validationErrors.join(' ') || 'Invalid value.' : '';\n      ref.current.setCustomValidity(errorMessage);\n\n      // Prevent default tooltip for validation message.\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=605277\n      if (!ref.current.hasAttribute('title')) {\n        ref.current.title = '';\n      }\n\n      if (!state.realtimeValidation.isInvalid) {\n        state.updateValidation(getNativeValidity(ref.current));\n      }\n    }\n  });\n\n  let onReset = useEffectEvent(() => {\n    state.resetValidation();\n  });\n\n  let onInvalid = useEffectEvent((e: Event) => {\n    // Only commit validation if we are not already displaying one.\n    // This avoids clearing server errors that the user didn't actually fix.\n    if (!state.displayValidation.isInvalid) {\n      state.commitValidation();\n    }\n\n    // Auto focus the first invalid input in a form, unless the error already had its default prevented.\n    let form = ref?.current?.form;\n    if (!e.defaultPrevented && ref && form && getFirstInvalidInput(form) === ref.current) {\n      if (focus) {\n        focus();\n      } else {\n        ref.current?.focus();\n      }\n\n      // Always show focus ring.\n      setInteractionModality('keyboard');\n    }\n\n    // Prevent default browser error UI from appearing.\n    e.preventDefault();\n  });\n\n  let onChange = useEffectEvent(() => {\n    state.commitValidation();\n  });\n\n  useEffect(() => {\n    let input = ref?.current;\n    if (!input) {\n      return;\n    }\n\n    let form = input.form;\n    input.addEventListener('invalid', onInvalid);\n    input.addEventListener('change', onChange);\n    form?.addEventListener('reset', onReset);\n    return () => {\n      input!.removeEventListener('invalid', onInvalid);\n      input!.removeEventListener('change', onChange);\n      form?.removeEventListener('reset', onReset);\n    };\n  }, [ref, onInvalid, onChange, onReset, validationBehavior]);\n}\n\nfunction getValidity(input: ValidatableElement) {\n  // The native ValidityState object is live, meaning each property is a getter that returns the current state.\n  // We need to create a snapshot of the validity state at the time this function is called to avoid unpredictable React renders.\n  let validity = input.validity;\n  return {\n    badInput: validity.badInput,\n    customError: validity.customError,\n    patternMismatch: validity.patternMismatch,\n    rangeOverflow: validity.rangeOverflow,\n    rangeUnderflow: validity.rangeUnderflow,\n    stepMismatch: validity.stepMismatch,\n    tooLong: validity.tooLong,\n    tooShort: validity.tooShort,\n    typeMismatch: validity.typeMismatch,\n    valueMissing: validity.valueMissing,\n    valid: validity.valid\n  };\n}\n\nfunction getNativeValidity(input: ValidatableElement): ValidationResult {\n  return {\n    isInvalid: !input.validity.valid,\n    validationDetails: getValidity(input),\n    validationErrors: input.validationMessage ? [input.validationMessage] : []\n  };\n}\n\nfunction getFirstInvalidInput(form: HTMLFormElement): ValidatableElement | null {\n  for (let i = 0; i < form.elements.length; i++) {\n    let element = form.elements[i] as ValidatableElement;\n    if (!element.validity.valid) {\n      return element;\n    }\n  }\n\n  return null;\n}\n", "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {createContext, useContext, useEffect, useMemo, useRef, useState} from 'react';\nimport {Validation, ValidationErrors, ValidationFunction, ValidationResult} from '@react-types/shared';\n\nexport const VALID_VALIDITY_STATE: ValidityState = {\n  badInput: false,\n  customError: false,\n  patternMismatch: false,\n  rangeOverflow: false,\n  rangeUnderflow: false,\n  stepMismatch: false,\n  tooLong: false,\n  tooShort: false,\n  typeMismatch: false,\n  valueMissing: false,\n  valid: true\n};\n\nconst CUSTOM_VALIDITY_STATE: ValidityState = {\n  ...VALID_VALIDITY_STATE,\n  customError: true,\n  valid: false\n};\n\nexport const DEFAULT_VALIDATION_RESULT: ValidationResult = {\n  isInvalid: false,\n  validationDetails: VALID_VALIDITY_STATE,\n  validationErrors: []\n};\n\nexport const FormValidationContext = createContext<ValidationErrors>({});\n\nexport const privateValidationStateProp = '__formValidationState' + Date.now();\n\ninterface FormValidationProps<T> extends Validation<T> {\n  builtinValidation?: ValidationResult,\n  name?: string | string[],\n  value: T | null\n}\n\nexport interface FormValidationState {\n  /** Realtime validation results, updated as the user edits the value. */\n  realtimeValidation: ValidationResult,\n  /** Currently displayed validation results, updated when the user commits their changes. */\n  displayValidation: ValidationResult,\n  /** Updates the current validation result. Not displayed to the user until `commitValidation` is called. */\n  updateValidation(result: ValidationResult): void,\n  /** Resets the displayed validation state to valid when the user resets the form. */\n  resetValidation(): void,\n  /** Commits the realtime validation so it is displayed to the user. */\n  commitValidation(): void\n}\n\nexport function useFormValidationState<T>(props: FormValidationProps<T>): FormValidationState {\n  // Private prop for parent components to pass state to children.\n  if (props[privateValidationStateProp]) {\n    let {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation} = props[privateValidationStateProp] as FormValidationState;\n    return {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation};\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useFormValidationStateImpl(props);\n}\n\nfunction useFormValidationStateImpl<T>(props: FormValidationProps<T>): FormValidationState {\n  let {isInvalid, validationState, name, value, builtinValidation, validate, validationBehavior = 'aria'} = props;\n\n  // backward compatibility.\n  if (validationState) {\n    isInvalid ||= validationState === 'invalid';\n  }\n\n  // If the isInvalid prop is controlled, update validation result in realtime.\n  let controlledError: ValidationResult | null = isInvalid !== undefined ? {\n    isInvalid,\n    validationErrors: [],\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n\n  // Perform custom client side validation.\n  let clientError: ValidationResult | null = useMemo(() => {\n    if (!validate || value == null) {\n      return null;\n    }\n    let validateErrors = runValidate(validate, value);\n    return getValidationResult(validateErrors);\n  }, [validate, value]);\n\n  if (builtinValidation?.validationDetails.valid) {\n    builtinValidation = undefined;\n  }\n\n  // Get relevant server errors from the form.\n  let serverErrors = useContext(FormValidationContext);\n  let serverErrorMessages = useMemo(() => {\n    if (name) {\n      return Array.isArray(name) ? name.flatMap(name => asArray(serverErrors[name])) : asArray(serverErrors[name]);\n    }\n    return [];\n  }, [serverErrors, name]);\n\n  // Show server errors when the form gets a new value, and clear when the user changes the value.\n  let [lastServerErrors, setLastServerErrors] = useState(serverErrors);\n  let [isServerErrorCleared, setServerErrorCleared] = useState(false);\n  if (serverErrors !== lastServerErrors) {\n    setLastServerErrors(serverErrors);\n    setServerErrorCleared(false);\n  }\n\n  let serverError: ValidationResult | null = useMemo(() =>\n    getValidationResult(isServerErrorCleared ? [] : serverErrorMessages),\n    [isServerErrorCleared, serverErrorMessages]\n  );\n\n  // Track the next validation state in a ref until commitValidation is called.\n  let nextValidation = useRef(DEFAULT_VALIDATION_RESULT);\n  let [currentValidity, setCurrentValidity] = useState(DEFAULT_VALIDATION_RESULT);\n\n  let lastError = useRef(DEFAULT_VALIDATION_RESULT);\n  let commitValidation = () => {\n    if (!commitQueued) {\n      return;\n    }\n\n    setCommitQueued(false);\n    let error = clientError || builtinValidation || nextValidation.current;\n    if (!isEqualValidation(error, lastError.current)) {\n      lastError.current = error;\n      setCurrentValidity(error);\n    }\n  };\n\n  let [commitQueued, setCommitQueued] = useState(false);\n  useEffect(commitValidation);\n\n  // realtimeValidation is used to update the native input element's state based on custom validation logic.\n  // displayValidation is the currently displayed validation state that the user sees (e.g. on input change/form submit).\n  // With validationBehavior=\"aria\", all errors are displayed in realtime rather than on submit.\n  let realtimeValidation = controlledError || serverError || clientError || builtinValidation || DEFAULT_VALIDATION_RESULT;\n  let displayValidation = validationBehavior === 'native'\n    ? controlledError || serverError || currentValidity\n    : controlledError || serverError || clientError || builtinValidation || currentValidity;\n\n  return {\n    realtimeValidation,\n    displayValidation,\n    updateValidation(value) {\n      // If validationBehavior is 'aria', update in realtime. Otherwise, store in a ref until commit.\n      if (validationBehavior === 'aria' && !isEqualValidation(currentValidity, value)) {\n        setCurrentValidity(value);\n      } else {\n        nextValidation.current = value;\n      }\n    },\n    resetValidation() {\n      // Update the currently displayed validation state to valid on form reset,\n      // even if the native validity says it isn't. It'll show again on the next form submit.\n      let error = DEFAULT_VALIDATION_RESULT;\n      if (!isEqualValidation(error, lastError.current)) {\n        lastError.current = error;\n        setCurrentValidity(error);\n      }\n\n      // Do not commit validation after the next render. This avoids a condition where\n      // useSelect calls commitValidation inside an onReset handler.\n      if (validationBehavior === 'native') {\n        setCommitQueued(false);\n      }\n\n      setServerErrorCleared(true);\n    },\n    commitValidation() {\n      // Commit validation state so the user sees it on blur/change/submit. Also clear any server errors.\n      // Wait until after the next render to commit so that the latest value has been validated.\n      if (validationBehavior === 'native') {\n        setCommitQueued(true);\n      }\n      setServerErrorCleared(true);\n    }\n  };\n}\n\nfunction asArray<T>(v: T | T[]): T[] {\n  if (!v) {\n    return [];\n  }\n\n  return Array.isArray(v) ? v : [v];\n}\n\nfunction runValidate<T>(validate: ValidationFunction<T>, value: T): string[] {\n  if (typeof validate === 'function') {\n    let e = validate(value);\n    if (e && typeof e !== 'boolean') {\n      return asArray(e);\n    }\n  }\n\n  return [];\n}\n\nfunction getValidationResult(errors: string[]): ValidationResult | null {\n  return errors.length ? {\n    isInvalid: true,\n    validationErrors: errors,\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n}\n\nfunction isEqualValidation(a: ValidationResult | null, b: ValidationResult | null): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  return !!a && !!b\n    && a.isInvalid === b.isInvalid\n    && a.validationErrors.length === b.validationErrors.length\n    && a.validationErrors.every((a, i) => a === b.validationErrors[i])\n    && Object.entries(a.validationDetails).every(([k, v]) => b.validationDetails[k] === v);\n}\n\nexport function mergeValidation(...results: ValidationResult[]): ValidationResult {\n  let errors = new Set<string>();\n  let isInvalid = false;\n  let validationDetails = {\n    ...VALID_VALIDITY_STATE\n  };\n\n  for (let v of results) {\n    for (let e of v.validationErrors) {\n      errors.add(e);\n    }\n\n    // Only these properties apply for checkboxes.\n    isInvalid ||= v.isInvalid;\n    for (let key in validationDetails) {\n      validationDetails[key] ||= v.validationDetails[key];\n    }\n  }\n\n  validationDetails.valid = !isInvalid;\n  return {\n    isInvalid,\n    validationErrors: [...errors],\n    validationDetails\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTextFieldProps} from '@react-types/textfield';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {filterDOMProps, getOwnerWindow, mergeProps, useFormReset} from '@react-aria/utils';\nimport React, {\n  ChangeEvent,\n  HTMLAttributes,\n  type JSX,\n  LabelHTMLAttributes,\n  RefObject,\n  useEffect\n} from 'react';\nimport {useControlledState} from '@react-stately/utils';\nimport {useField} from '@react-aria/label';\nimport {useFocusable} from '@react-aria/interactions';\nimport {useFormValidation} from '@react-aria/form';\nimport {useFormValidationState} from '@react-stately/form';\n\n/**\n * A map of HTML element names and their interface types.\n * For example `'a'` -> `HTMLAnchorElement`.\n */\ntype IntrinsicHTMLElements = {\n  [K in keyof IntrinsicHTMLAttributes]: IntrinsicHTMLAttributes[K] extends HTMLAttributes<infer T> ? T : never\n};\n\n/**\n * A map of HTML element names and their attribute interface types.\n * For example `'a'` -> `AnchorHTMLAttributes<HTMLAnchorElement>`.\n */\ntype IntrinsicHTMLAttributes = JSX.IntrinsicElements;\n\ntype DefaultElementType = 'input';\n\n/**\n * The intrinsic HTML element names that `useTextField` supports; e.g. `input`,\n * `textarea`.\n */\ntype TextFieldIntrinsicElements = keyof Pick<IntrinsicHTMLElements, 'input' | 'textarea'>;\n\n/**\n * The HTML element interfaces that `useTextField` supports based on what is\n * defined for `TextFieldIntrinsicElements`; e.g. `HTMLInputElement`,\n * `HTMLTextAreaElement`.\n */\ntype TextFieldHTMLElementType = Pick<IntrinsicHTMLElements, TextFieldIntrinsicElements>;\n\n/**\n * The HTML attributes interfaces that `useTextField` supports based on what\n * is defined for `TextFieldIntrinsicElements`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldHTMLAttributesType = Pick<IntrinsicHTMLAttributes, TextFieldIntrinsicElements>;\n\n/**\n * The type of `inputProps` returned by `useTextField`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldInputProps<T extends TextFieldIntrinsicElements> = TextFieldHTMLAttributesType[T];\n\nexport interface AriaTextFieldOptions<T extends TextFieldIntrinsicElements> extends AriaTextFieldProps<TextFieldHTMLElementType[T]> {\n  /**\n   * The HTML element used to render the input, e.g. 'input', or 'textarea'.\n   * It determines whether certain HTML attributes will be included in `inputProps`.\n   * For example, [`type`](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-type).\n   * @default 'input'\n   */\n  inputElementType?: T,\n  /**\n   * Controls whether inputted text is automatically capitalized and, if so, in what manner.\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autocapitalize).\n   */\n  autoCapitalize?: 'off' | 'none' | 'on' | 'sentences' | 'words' | 'characters',\n  /**\n   * An enumerated attribute that defines what action label or icon to preset for the enter key on virtual keyboards. See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/enterkeyhint).\n   */\n  enterKeyHint?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send'\n}\n\n/**\n * The type of `ref` object that can be passed to `useTextField` based on the given\n * intrinsic HTML element name; e.g.`RefObject<HTMLInputElement>`,\n * `RefObject<HTMLTextAreaElement>`.\n */\ntype TextFieldRefObject<T extends TextFieldIntrinsicElements> = RefObject<TextFieldHTMLElementType[T] | null>;\n\nexport interface TextFieldAria<T extends TextFieldIntrinsicElements = DefaultElementType> extends ValidationResult {\n  /** Props for the input element. */\n  inputProps: TextFieldInputProps<T>,\n  /** Props for the text field's visible label element, if any. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the text field's description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the text field's error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a text field.\n * @param props - Props for the text field.\n * @param ref - Ref to the HTML input or textarea element.\n */\nexport function useTextField<T extends TextFieldIntrinsicElements = DefaultElementType>(\n  props: AriaTextFieldOptions<T>,\n  ref: TextFieldRefObject<T>\n): TextFieldAria<T> {\n  let {\n    inputElementType = 'input',\n    isDisabled = false,\n    isRequired = false,\n    isReadOnly = false,\n    type = 'text',\n    validationBehavior = 'aria'\n  } = props;\n  let [value, setValue] = useControlledState<string>(props.value, props.defaultValue || '', props.onChange);\n  let {focusableProps} = useFocusable<TextFieldHTMLElementType[T]>(props, ref);\n  let validationState = useFormValidationState({\n    ...props,\n    value\n  });\n  let {isInvalid, validationErrors, validationDetails} = validationState.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  const inputOnlyProps = {\n    type,\n    pattern: props.pattern\n  };\n\n  useFormReset(ref, value, setValue);\n  useFormValidation(props, validationState, ref);\n\n  useEffect(() => {\n    // This works around a React/Chrome bug that prevents textarea elements from validating when controlled.\n    // We prevent React from updating defaultValue (i.e. children) of textarea when `value` changes,\n    // which causes Chrome to skip validation. Only updating `value` is ok in our case since our\n    // textareas are always controlled. React is planning on removing this synchronization in a\n    // future major version.\n    // https://github.com/facebook/react/issues/19474\n    // https://github.com/facebook/react/issues/11896\n    if (ref.current instanceof getOwnerWindow(ref.current).HTMLTextAreaElement) {\n      let input = ref.current;\n      Object.defineProperty(input, 'defaultValue', {\n        get: () => input.value,\n        set: () => {},\n        configurable: true\n      });\n    }\n  }, [ref]);\n\n  return {\n    labelProps,\n    inputProps: mergeProps(\n      domProps,\n      inputElementType === 'input' ? inputOnlyProps : undefined,\n      {\n        disabled: isDisabled,\n        readOnly: isReadOnly,\n        required: isRequired && validationBehavior === 'native',\n        'aria-required': (isRequired && validationBehavior === 'aria') || undefined,\n        'aria-invalid': isInvalid || undefined,\n        'aria-errormessage': props['aria-errormessage'],\n        'aria-activedescendant': props['aria-activedescendant'],\n        'aria-autocomplete': props['aria-autocomplete'],\n        'aria-haspopup': props['aria-haspopup'],\n        'aria-controls': props['aria-controls'],\n        value,\n        onChange: (e: ChangeEvent<HTMLInputElement>) => setValue(e.target.value),\n        autoComplete: props.autoComplete,\n        autoCapitalize: props.autoCapitalize,\n        maxLength: props.maxLength,\n        minLength: props.minLength,\n        name: props.name,\n        placeholder: props.placeholder,\n        inputMode: props.inputMode,\n        autoCorrect: props.autoCorrect,\n        spellCheck: props.spellCheck,\n        [parseInt(React.version, 10) >= 17 ? 'enterKeyHint' : 'enterkeyhint']: props.enterKeyHint,\n\n        // Clipboard events\n        onCopy: props.onCopy,\n        onCut: props.onCut,\n        onPaste: props.onPaste,\n\n        // Composition events\n        onCompositionEnd: props.onCompositionEnd,\n        onCompositionStart: props.onCompositionStart,\n        onCompositionUpdate: props.onCompositionUpdate,\n\n        // Selection events\n        onSelect: props.onSelect,\n\n        // Input events\n        onBeforeInput: props.onBeforeInput,\n        onInput: props.onInput,\n        ...focusableProps,\n        ...fieldProps\n      }\n    ),\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n", "/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTextFieldProps} from '@react-types/textfield';\nimport {mergeProps, useEffectEvent} from '@react-aria/utils';\nimport {RefObject} from '@react-types/shared';\nimport {TextFieldAria, useTextField} from './useTextField';\nimport {useEffect, useRef} from 'react';\n\ninterface FormattedTextFieldState {\n  validate: (val: string) => boolean,\n  setInputValue: (val: string) => void\n}\n\n\nfunction supportsNativeBeforeInputEvent() {\n  return typeof window !== 'undefined' &&\n    window.InputEvent &&\n    typeof InputEvent.prototype.getTargetRanges === 'function';\n}\n\nexport function useFormattedTextField(props: AriaTextFieldProps, state: FormattedTextFieldState, inputRef: RefObject<HTMLInputElement | null>): TextFieldAria {\n  // All browsers implement the 'beforeinput' event natively except Firefox\n  // (currently behind a flag as of Firefox 84). React's polyfill does not\n  // run in all cases that the native event fires, e.g. when deleting text.\n  // Use the native event if available so that we can prevent invalid deletions.\n  // We do not attempt to polyfill this in Firefox since it would be very complicated,\n  // the benefit of doing so is fairly minor, and it's going to be natively supported soon.\n  let onBeforeInputFallback = useEffectEvent((e: InputEvent) => {\n    let input = inputRef.current;\n    if (!input) {\n      return;\n    }\n\n    // Compute the next value of the input if the event is allowed to proceed.\n    // See https://www.w3.org/TR/input-events-2/#interface-InputEvent-Attributes for a full list of input types.\n    let nextValue: string | null = null;\n    switch (e.inputType) {\n      case 'historyUndo':\n      case 'historyRedo':\n        // Explicitly allow undo/redo. e.data is null in this case, but there's no need to validate,\n        // because presumably the input would have already been validated previously.\n        return;\n      case 'insertLineBreak':\n        // Explicitly allow \"insertLineBreak\" event, to allow onSubmit for \"enter\" key. e.data is null in this case.\n        return;\n      case 'deleteContent':\n      case 'deleteByCut':\n      case 'deleteByDrag':\n        nextValue = input.value.slice(0, input.selectionStart!) + input.value.slice(input.selectionEnd!);\n        break;\n      case 'deleteContentForward':\n        // This is potentially incorrect, since the browser may actually delete more than a single UTF-16\n        // character. In reality, a full Unicode grapheme cluster consisting of multiple UTF-16 characters\n        // or code points may be deleted. However, in our currently supported locales, there are no such cases.\n        // If we support additional locales in the future, this may need to change.\n        nextValue = input.selectionEnd === input.selectionStart\n          ? input.value.slice(0, input.selectionStart!) + input.value.slice(input.selectionEnd! + 1)\n          : input.value.slice(0, input.selectionStart!) + input.value.slice(input.selectionEnd!);\n        break;\n      case 'deleteContentBackward':\n        nextValue = input.selectionEnd === input.selectionStart\n          ? input.value.slice(0, input.selectionStart! - 1) + input.value.slice(input.selectionStart!)\n          : input.value.slice(0, input.selectionStart!) + input.value.slice(input.selectionEnd!);\n        break;\n      case 'deleteSoftLineBackward':\n      case 'deleteHardLineBackward':\n        nextValue = input.value.slice(input.selectionStart!);\n        break;\n      default:\n        if (e.data != null) {\n          nextValue =\n            input.value.slice(0, input.selectionStart!) +\n            e.data +\n            input.value.slice(input.selectionEnd!);\n        }\n        break;\n    }\n\n    // If we did not compute a value, or the new value is invalid, prevent the event\n    // so that the browser does not update the input text, move the selection, or add to\n    // the undo/redo stack.\n    if (nextValue == null || !state.validate(nextValue)) {\n      e.preventDefault();\n    }\n  });\n\n  useEffect(() => {\n    if (!supportsNativeBeforeInputEvent() || !inputRef.current) {\n      return;\n    }\n\n    let input = inputRef.current;\n    input.addEventListener('beforeinput', onBeforeInputFallback, false);\n    return () => {\n      input.removeEventListener('beforeinput', onBeforeInputFallback, false);\n    };\n  }, [inputRef, onBeforeInputFallback]);\n\n  let onBeforeInput = !supportsNativeBeforeInputEvent()\n    ? e => {\n      let nextValue =\n        e.target.value.slice(0, e.target.selectionStart) +\n        e.data +\n        e.target.value.slice(e.target.selectionEnd);\n\n      if (!state.validate(nextValue)) {\n        e.preventDefault();\n      }\n    }\n    : null;\n\n  let {labelProps, inputProps: textFieldProps, descriptionProps, errorMessageProps, ...validation} = useTextField(props, inputRef);\n\n  let compositionStartState = useRef<{value: string, selectionStart: number | null, selectionEnd: number | null} | null>(null);\n  return {\n    inputProps: mergeProps(\n      textFieldProps,\n      {\n        onBeforeInput,\n        onCompositionStart() {\n          // Chrome does not implement Input Events Level 2, which specifies the insertFromComposition\n          // and deleteByComposition inputType values for the beforeinput event. These are meant to occur\n          // at the end of a composition (e.g. Pinyin IME, Android auto correct, etc.), and crucially, are\n          // cancelable. The insertCompositionText and deleteCompositionText input types are not cancelable,\n          // nor would we want to cancel them because the input from the user is incomplete at that point.\n          // In Safari, insertFromComposition/deleteFromComposition will fire, however, allowing us to cancel\n          // the final composition result if it is invalid. As a fallback for Chrome and Firefox, which either\n          // don't support Input Events Level 2, or beforeinput at all, we store the state of the input when\n          // the compositionstart event fires, and undo the changes in compositionend (below) if it is invalid.\n          // Unfortunately, this messes up the undo/redo stack, but until insertFromComposition/deleteByComposition\n          // are implemented, there is no other way to prevent composed input.\n          // See https://bugs.chromium.org/p/chromium/issues/detail?id=1022204\n          let {value, selectionStart, selectionEnd} = inputRef.current!;\n          compositionStartState.current = {value, selectionStart, selectionEnd};\n        },\n        onCompositionEnd() {\n          if (inputRef.current && !state.validate(inputRef.current.value)) {\n            // Restore the input value in the DOM immediately so we can synchronously update the selection position.\n            // But also update the value in React state as well so it is correct for future updates.\n            let {value, selectionStart, selectionEnd} = compositionStartState.current!;\n            inputRef.current.value = value;\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n            state.setInputValue(value);\n          }\n        }\n      }\n    ),\n    labelProps,\n    descriptionProps,\n    errorMessageProps,\n    ...validation\n  };\n}\n", "\"use client\";\n\n// src/utils.ts\nimport { useContext, useMemo } from \"react\";\nimport { mergeProps, mergeRefs, useObjectRef } from \"@react-aria/utils\";\nvar DEFAULT_SLOT = Symbol(\"default\");\nfunction useSlottedContext(context, slot) {\n  let ctx = useContext(context);\n  if (slot === null) {\n    return null;\n  }\n  if (ctx && typeof ctx === \"object\" && \"slots\" in ctx && ctx.slots) {\n    let availableSlots = new Intl.ListFormat().format(Object.keys(ctx.slots).map((p) => `\"${p}\"`));\n    if (!slot && !ctx.slots[DEFAULT_SLOT]) {\n      throw new Error(`A slot prop is required. Valid slot names are ${availableSlots}.`);\n    }\n    let slotKey = slot || DEFAULT_SLOT;\n    if (!ctx.slots[slotKey]) {\n      throw new Error(`Invalid slot \"${slot}\". Valid slot names are ${availableSlots}.`);\n    }\n    return ctx.slots[slotKey];\n  }\n  return ctx;\n}\nfunction useContextProps(props, ref, context) {\n  let ctx = useSlottedContext(context, props.slot) || {};\n  let { ref: contextRef, ...contextProps } = ctx;\n  let mergedRef = useObjectRef(useMemo(() => mergeRefs(ref, contextRef), [ref, contextRef]));\n  let mergedProps = mergeProps(contextProps, props);\n  if (\"style\" in contextProps && contextProps.style && \"style\" in props && props.style) {\n    if (typeof contextProps.style === \"function\" || typeof props.style === \"function\") {\n      mergedProps.style = (renderProps) => {\n        let contextStyle = typeof contextProps.style === \"function\" ? contextProps.style(renderProps) : contextProps.style;\n        let defaultStyle = { ...renderProps.defaultStyle, ...contextStyle };\n        let style = typeof props.style === \"function\" ? props.style({ ...renderProps, defaultStyle }) : props.style;\n        return { ...defaultStyle, ...style };\n      };\n    } else {\n      mergedProps.style = { ...contextProps.style, ...props.style };\n    }\n  }\n  return [mergedProps, mergedRef];\n}\n\nexport {\n  DEFAULT_SLOT,\n  useSlottedContext,\n  useContextProps\n};\n", "\"use client\";\nimport {\n  useContextProps\n} from \"./chunk-BSTJ7ZCN.mjs\";\n\n// src/base-form.tsx\nimport { FormValidationContext } from \"@react-stately/form\";\nimport { createContext, forwardRef, useMemo } from \"react\";\nimport { form } from \"@heroui/theme\";\nimport { jsx } from \"react/jsx-runtime\";\nvar FormContext = createContext(null);\nvar Form = forwardRef(function Form2(props, ref) {\n  [props, ref] = useContextProps(props, ref, FormContext);\n  let { validationErrors, validationBehavior = \"native\", children, className, ...domProps } = props;\n  const styles = useMemo(() => form({ className }), [className]);\n  return /* @__PURE__ */ jsx(\"form\", { noValidate: validationBehavior !== \"native\", ...domProps, ref, className: styles, children: /* @__PURE__ */ jsx(FormContext.Provider, { value: { ...props, validationBehavior }, children: /* @__PURE__ */ jsx(FormValidationContext.Provider, { value: validationErrors != null ? validationErrors : {}, children }) }) });\n});\n\nexport {\n  FormContext,\n  Form\n};\n", "\"use client\";\nimport {\n  Form\n} from \"./chunk-SLABUSGS.mjs\";\n\n// src/form.tsx\nimport { useProviderContext } from \"@heroui/system\";\nimport { forwardRef } from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Form2 = forwardRef(function Form3(props, ref) {\n  var _a, _b;\n  const globalContext = useProviderContext();\n  const validationBehavior = (_b = (_a = props.validationBehavior) != null ? _a : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _b : \"native\";\n  return /* @__PURE__ */ jsx(Form, { ...props, ref, validationBehavior });\n});\n\nexport {\n  Form2 as Form\n};\n", "\"use client\";\nimport {\n  useInput\n} from \"./chunk-F2LJ3HQE.mjs\";\n\n// src/input.tsx\nimport { CloseFilledIcon } from \"@heroui/shared-icons\";\nimport { useMemo } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar Input = forwardRef((props, ref) => {\n  const {\n    Component,\n    label,\n    description,\n    isClearable,\n    startContent,\n    endContent,\n    labelPlacement,\n    hasHelper,\n    isOutsideLeft,\n    shouldLabelBeOutside,\n    errorMessage,\n    isInvalid,\n    getBaseProps,\n    getLabelProps,\n    getInputProps,\n    getInnerWrapperProps,\n    getInputWrapperProps,\n    getMainWrapperProps,\n    getHelperWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps,\n    getClearButtonProps\n  } = useInput({ ...props, ref });\n  const labelContent = label ? /* @__PURE__ */ jsx(\"label\", { ...getLabelProps(), children: label }) : null;\n  const end = useMemo(() => {\n    if (isClearable) {\n      return /* @__PURE__ */ jsx(\"button\", { ...getClearButtonProps(), children: endContent || /* @__PURE__ */ jsx(CloseFilledIcon, {}) });\n    }\n    return endContent;\n  }, [isClearable, getClearButtonProps]);\n  const helperWrapper = useMemo(() => {\n    const shouldShowError = isInvalid && errorMessage;\n    const hasContent = shouldShowError || description;\n    if (!hasHelper || !hasContent) return null;\n    return /* @__PURE__ */ jsx(\"div\", { ...getHelperWrapperProps(), children: shouldShowError ? /* @__PURE__ */ jsx(\"div\", { ...getErrorMessageProps(), children: errorMessage }) : /* @__PURE__ */ jsx(\"div\", { ...getDescriptionProps(), children: description }) });\n  }, [\n    hasHelper,\n    isInvalid,\n    errorMessage,\n    description,\n    getHelperWrapperProps,\n    getErrorMessageProps,\n    getDescriptionProps\n  ]);\n  const innerWrapper = useMemo(() => {\n    return /* @__PURE__ */ jsxs(\"div\", { ...getInnerWrapperProps(), children: [\n      startContent,\n      /* @__PURE__ */ jsx(\"input\", { ...getInputProps() }),\n      end\n    ] });\n  }, [startContent, end, getInputProps, getInnerWrapperProps]);\n  const mainWrapper = useMemo(() => {\n    if (shouldLabelBeOutside) {\n      return /* @__PURE__ */ jsxs(\"div\", { ...getMainWrapperProps(), children: [\n        /* @__PURE__ */ jsxs(\"div\", { ...getInputWrapperProps(), children: [\n          !isOutsideLeft ? labelContent : null,\n          innerWrapper\n        ] }),\n        helperWrapper\n      ] });\n    }\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsxs(\"div\", { ...getInputWrapperProps(), children: [\n        labelContent,\n        innerWrapper\n      ] }),\n      helperWrapper\n    ] });\n  }, [\n    labelPlacement,\n    helperWrapper,\n    shouldLabelBeOutside,\n    labelContent,\n    innerWrapper,\n    errorMessage,\n    description,\n    getMainWrapperProps,\n    getInputWrapperProps,\n    getErrorMessageProps,\n    getDescriptionProps\n  ]);\n  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [\n    isOutsideLeft ? labelContent : null,\n    mainWrapper\n  ] });\n});\nInput.displayName = \"HeroUI.Input\";\nvar input_default = Input;\n\nexport {\n  input_default\n};\n", "\"use client\";\nimport {\n  useInput\n} from \"./chunk-F2LJ3HQE.mjs\";\n\n// src/textarea.tsx\nimport { dataAttr } from \"@heroui/shared-utils\";\nimport { forwardRef } from \"@heroui/system\";\nimport { mergeProps } from \"@react-aria/utils\";\nimport { useMemo, useState } from \"react\";\nimport TextareaAutosize from \"react-textarea-autosize\";\nimport { CloseFilledIcon } from \"@heroui/shared-icons\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Textarea = forwardRef(\n  ({\n    style,\n    minRows = 3,\n    maxRows = 8,\n    cacheMeasurements = false,\n    disableAutosize = false,\n    onHeightChange,\n    ...otherProps\n  }, ref) => {\n    const {\n      Component,\n      label,\n      description,\n      startContent,\n      endContent,\n      hasHelper,\n      shouldLabelBeOutside,\n      shouldLabelBeInside,\n      isInvalid,\n      errorMessage,\n      getBaseProps,\n      getLabelProps,\n      getInputProps,\n      getInnerWrapperProps,\n      getInputWrapperProps,\n      getHelperWrapperProps,\n      getDescriptionProps,\n      getErrorMessageProps,\n      isClearable,\n      getClearButtonProps\n    } = useInput({ ...otherProps, ref, isMultiline: true });\n    const [hasMultipleRows, setIsHasMultipleRows] = useState(minRows > 1);\n    const [isLimitReached, setIsLimitReached] = useState(false);\n    const labelContent = label ? /* @__PURE__ */ jsx(\"label\", { ...getLabelProps(), children: label }) : null;\n    const inputProps = getInputProps();\n    const handleHeightChange = (height, meta) => {\n      if (minRows === 1) {\n        setIsHasMultipleRows(height >= meta.rowHeight * 2);\n      }\n      if (maxRows > minRows) {\n        const limitReached = height >= maxRows * meta.rowHeight;\n        setIsLimitReached(limitReached);\n      }\n      onHeightChange == null ? void 0 : onHeightChange(height, meta);\n    };\n    const content = disableAutosize ? /* @__PURE__ */ jsx(\"textarea\", { ...inputProps, style: mergeProps(inputProps.style, style != null ? style : {}) }) : /* @__PURE__ */ jsx(\n      TextareaAutosize,\n      {\n        ...inputProps,\n        cacheMeasurements,\n        \"data-hide-scroll\": dataAttr(!isLimitReached),\n        maxRows,\n        minRows,\n        style: mergeProps(inputProps.style, style != null ? style : {}),\n        onHeightChange: handleHeightChange\n      }\n    );\n    const clearButtonContent = useMemo(() => {\n      return isClearable ? /* @__PURE__ */ jsx(\"button\", { ...getClearButtonProps(), children: /* @__PURE__ */ jsx(CloseFilledIcon, {}) }) : null;\n    }, [isClearable, getClearButtonProps]);\n    const innerWrapper = useMemo(() => {\n      if (startContent || endContent) {\n        return /* @__PURE__ */ jsxs(\"div\", { ...getInnerWrapperProps(), children: [\n          startContent,\n          content,\n          endContent\n        ] });\n      }\n      return /* @__PURE__ */ jsx(\"div\", { ...getInnerWrapperProps(), children: content });\n    }, [startContent, inputProps, endContent, getInnerWrapperProps]);\n    const shouldShowError = isInvalid && errorMessage;\n    const hasHelperContent = shouldShowError || description;\n    return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [\n      shouldLabelBeOutside ? labelContent : null,\n      /* @__PURE__ */ jsxs(\"div\", { ...getInputWrapperProps(), \"data-has-multiple-rows\": dataAttr(hasMultipleRows), children: [\n        shouldLabelBeInside ? labelContent : null,\n        innerWrapper,\n        clearButtonContent\n      ] }),\n      hasHelper && hasHelperContent ? /* @__PURE__ */ jsx(\"div\", { ...getHelperWrapperProps(), children: shouldShowError ? /* @__PURE__ */ jsx(\"div\", { ...getErrorMessageProps(), children: errorMessage }) : /* @__PURE__ */ jsx(\"div\", { ...getDescriptionProps(), children: description }) }) : null\n    ] });\n  }\n);\nTextarea.displayName = \"HeroUI.Textarea\";\nvar textarea_default = Textarea;\n\nexport {\n  textarea_default\n};\n", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport * as React from 'react';\nimport useLatest from 'use-latest';\nimport useComposedRef from 'use-composed-ref';\n\nvar HIDDEN_TEXTAREA_STYLE = {\n  'min-height': '0',\n  'max-height': 'none',\n  height: '0',\n  visibility: 'hidden',\n  overflow: 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n  display: 'block'\n};\nvar forceHiddenStyles = function forceHiddenStyles(node) {\n  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function (key) {\n    node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], 'important');\n  });\n};\nvar forceHiddenStyles$1 = forceHiddenStyles;\n\nvar hiddenTextarea = null;\nvar getHeight = function getHeight(node, sizingData) {\n  var height = node.scrollHeight;\n  if (sizingData.sizingStyle.boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    return height + sizingData.borderSize;\n  }\n\n  // remove padding, since height = content\n  return height - sizingData.paddingSize;\n};\nfunction calculateNodeHeight(sizingData, value, minRows, maxRows) {\n  if (minRows === void 0) {\n    minRows = 1;\n  }\n  if (maxRows === void 0) {\n    maxRows = Infinity;\n  }\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tabindex', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    forceHiddenStyles$1(hiddenTextarea);\n  }\n  if (hiddenTextarea.parentNode === null) {\n    document.body.appendChild(hiddenTextarea);\n  }\n  var paddingSize = sizingData.paddingSize,\n    borderSize = sizingData.borderSize,\n    sizingStyle = sizingData.sizingStyle;\n  var boxSizing = sizingStyle.boxSizing;\n  Object.keys(sizingStyle).forEach(function (_key) {\n    var key = _key;\n    hiddenTextarea.style[key] = sizingStyle[key];\n  });\n  forceHiddenStyles$1(hiddenTextarea);\n  hiddenTextarea.value = value;\n  var height = getHeight(hiddenTextarea, sizingData);\n  // Double set and calc due to Firefox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1795904\n  hiddenTextarea.value = value;\n  height = getHeight(hiddenTextarea, sizingData);\n\n  // measure height of a textarea with a single row\n  hiddenTextarea.value = 'x';\n  var rowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  var minHeight = rowHeight * minRows;\n  if (boxSizing === 'border-box') {\n    minHeight = minHeight + paddingSize + borderSize;\n  }\n  height = Math.max(minHeight, height);\n  var maxHeight = rowHeight * maxRows;\n  if (boxSizing === 'border-box') {\n    maxHeight = maxHeight + paddingSize + borderSize;\n  }\n  height = Math.min(maxHeight, height);\n  return [height, rowHeight];\n}\n\nvar noop = function noop() {};\nvar pick = function pick(props, obj) {\n  return props.reduce(function (acc, prop) {\n    acc[prop] = obj[prop];\n    return acc;\n  }, {});\n};\n\nvar SIZING_STYLE = ['borderBottomWidth', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'boxSizing', 'fontFamily', 'fontSize', 'fontStyle', 'fontWeight', 'letterSpacing', 'lineHeight', 'paddingBottom', 'paddingLeft', 'paddingRight', 'paddingTop',\n// non-standard\n'tabSize', 'textIndent',\n// non-standard\n'textRendering', 'textTransform', 'width', 'wordBreak', 'wordSpacing', 'scrollbarGutter'];\nvar isIE = !!document.documentElement.currentStyle ;\nvar getSizingData = function getSizingData(node) {\n  var style = window.getComputedStyle(node);\n  if (style === null) {\n    return null;\n  }\n  var sizingStyle = pick(SIZING_STYLE, style);\n  var boxSizing = sizingStyle.boxSizing;\n\n  // probably node is detached from DOM, can't read computed dimensions\n  if (boxSizing === '') {\n    return null;\n  }\n\n  // IE (Edge has already correct behaviour) returns content width as computed width\n  // so we need to add manually padding and border widths\n  if (isIE && boxSizing === 'border-box') {\n    sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + 'px';\n  }\n  var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);\n  var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);\n  return {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize\n  };\n};\nvar getSizingData$1 = getSizingData;\n\nfunction useListener(target, type, listener) {\n  var latestListener = useLatest(listener);\n  React.useLayoutEffect(function () {\n    var handler = function handler(ev) {\n      return latestListener.current(ev);\n    };\n    // might happen if document.fonts is not defined, for instance\n    if (!target) {\n      return;\n    }\n    target.addEventListener(type, handler);\n    return function () {\n      return target.removeEventListener(type, handler);\n    };\n  }, []);\n}\nvar useFormResetListener = function useFormResetListener(libRef, listener) {\n  useListener(document.body, 'reset', function (ev) {\n    if (libRef.current.form === ev.target) {\n      listener(ev);\n    }\n  });\n};\nvar useWindowResizeListener = function useWindowResizeListener(listener) {\n  useListener(window, 'resize', listener);\n};\nvar useFontsLoadedListener = function useFontsLoadedListener(listener) {\n  useListener(document.fonts, 'loadingdone', listener);\n};\n\nvar _excluded = [\"cacheMeasurements\", \"maxRows\", \"minRows\", \"onChange\", \"onHeightChange\"];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n  var cacheMeasurements = _ref.cacheMeasurements,\n    maxRows = _ref.maxRows,\n    minRows = _ref.minRows,\n    _ref$onChange = _ref.onChange,\n    onChange = _ref$onChange === void 0 ? noop : _ref$onChange,\n    _ref$onHeightChange = _ref.onHeightChange,\n    onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (props.style) {\n    if ('maxHeight' in props.style) {\n      throw new Error('Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.');\n    }\n    if ('minHeight' in props.style) {\n      throw new Error('Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.');\n    }\n  }\n  var isControlled = props.value !== undefined;\n  var libRef = React.useRef(null);\n  var ref = useComposedRef(libRef, userRef);\n  var heightRef = React.useRef(0);\n  var measurementsCacheRef = React.useRef();\n  var resizeTextarea = function resizeTextarea() {\n    var node = libRef.current;\n    var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);\n    if (!nodeSizingData) {\n      return;\n    }\n    measurementsCacheRef.current = nodeSizingData;\n    var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || 'x', minRows, maxRows),\n      height = _calculateNodeHeight[0],\n      rowHeight = _calculateNodeHeight[1];\n    if (heightRef.current !== height) {\n      heightRef.current = height;\n      node.style.setProperty('height', height + \"px\", 'important');\n      onHeightChange(height, {\n        rowHeight: rowHeight\n      });\n    }\n  };\n  var handleChange = function handleChange(event) {\n    if (!isControlled) {\n      resizeTextarea();\n    }\n    onChange(event);\n  };\n  {\n    React.useLayoutEffect(resizeTextarea);\n    useFormResetListener(libRef, function () {\n      if (!isControlled) {\n        var currentValue = libRef.current.value;\n        requestAnimationFrame(function () {\n          var node = libRef.current;\n          if (node && currentValue !== node.value) {\n            resizeTextarea();\n          }\n        });\n      }\n    });\n    useWindowResizeListener(resizeTextarea);\n    useFontsLoadedListener(resizeTextarea);\n    return /*#__PURE__*/React.createElement(\"textarea\", _extends({}, props, {\n      onChange: handleChange,\n      ref: ref\n    }));\n  }\n};\nvar index = /* #__PURE__ */React.forwardRef(TextareaAutosize);\n\nexport { index as default };\n", "import React from 'react';\nimport useIsomorphicLayoutEffect from 'use-isomorphic-layout-effect';\n\nvar useLatest = function useLatest(value) {\n  var ref = React.useRef(value);\n  useIsomorphicLayoutEffect(function () {\n    ref.current = value;\n  });\n  return ref;\n};\n\nexport { useLatest as default };\n", "import { useLayoutEffect } from 'react';\n\nvar index = useLayoutEffect ;\n\nexport { index as default };\n", "import React from 'react';\n\n// basically Exclude<React.ClassAttributes<T>[\"ref\"], string>\n\nvar updateRef = function updateRef(ref, value) {\n  if (typeof ref === 'function') {\n    ref(value);\n    return;\n  }\n  ref.current = value;\n};\nvar useComposedRef = function useComposedRef(libRef, userRef) {\n  var prevUserRef = React.useRef();\n  return React.useCallback(function (instance) {\n    libRef.current = instance;\n    if (prevUserRef.current) {\n      updateRef(prevUserRef.current, null);\n    }\n    prevUserRef.current = userRef;\n    if (!userRef) {\n      return;\n    }\n    updateRef(userRef, instance);\n  }, [userRef]);\n};\n\nexport { useComposedRef as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAAA,gBAA+C;;;;;;ACyBxC,SAAS,0CAAS,OAAqB;AAC5C,MAAI,EAAA,IACA,OAEF,mBAAmB,gBACnB,cAAc,WAAS,mBACJ,QAAA,IACjB;AAEJ,QAAK,GAAA,2CAAM,EAAA;AACX,MAAI,WAAU,GAAA,2CAAI;AAClB,MAAI,aAAa,CAAC;AAClB,MAAI,OAAO;AACT,qBAAiB,iBAAiB,GAAG,OAAA,IAAW,cAAA,KAAmB;AACnE,iBAAa;MACX,IAAI;MACJ,SAAS,qBAAqB,UAAU,KAAK;IAC/C;EACF,WAAW,CAAC,kBAAkB,CAAC,aAAa;AAC1C,YAAQ,KAAK,sHAAA;AAGf,MAAI,cAAa,GAAA,2CAAU;;IAEzB,cAAc;IACd,mBAAmB;EACrB,CAAA;AAEA,SAAO;;;EAGP;AACF;;;ACtCO,SAAS,0CAAS,OAAqB;AAC5C,MAAI,EAAA,aAAY,cAAc,WAAW,gBAAiB,IAAI;AAC9D,MAAI,EAAA,YAAW,WAAY,KAAI,GAAA,2CAAS,KAAA;AAExC,MAAI,iBAAgB,GAAA,2CAAU;IAAC,QAAQ,WAAA;IAAc,QAAQ,YAAA;IAAe;IAAW;GAAgB;AACvG,MAAI,kBAAiB,GAAA,2CAAU;IAAC,QAAQ,WAAA;IAAc,QAAQ,YAAA;IAAe;IAAW;GAAgB;AAExG,gBAAa,GAAA,2CAAW,YAAY;IAClC,oBAAoB;MAClB;;MAEA;MACA,MAAM,kBAAA;MACN,OAAO,OAAA,EAAS,KAAK,GAAA,KAAQ;EACjC,CAAA;AAEA,SAAO;;;IAGL,kBAAkB;MAChB,IAAI;IACN;IACA,mBAAmB;MACjB,IAAI;IACN;EACF;AACF;;;;AChCO,SAAS,0CAAqB,OAA+B,OAA4B,KAAqD;AACnJ,MAAI,EAAA,oBAAmB,MAAO,IAAI;AAGlC,GAAA,GAAA,2CAAgB,MAAA;AACd,QAAI,uBAAuB,aAAY,QAAA,QAAA,QAAA,SAAA,SAAA,IAAK,YAAW,CAAC,IAAI,QAAQ,UAAU;AAC5E,UAAI,eAAe,MAAM,mBAAmB,YAAY,MAAM,mBAAmB,iBAAiB,KAAK,GAAA,KAAQ,mBAAmB;AAClI,UAAI,QAAQ,kBAAkB,YAAA;AAI9B,UAAI,CAAC,IAAI,QAAQ,aAAa,OAAA;AAC5B,YAAI,QAAQ,QAAQ;AAGtB,UAAI,CAAC,MAAM,mBAAmB;AAC5B,cAAM,iBAAiB,wCAAkB,IAAI,OAAO,CAAA;IAExD;EACF,CAAA;AAEA,MAAI,WAAU,GAAA,2CAAe,MAAA;AAC3B,UAAM,gBAAe;EACvB,CAAA;AAEA,MAAI,aAAY,GAAA,2CAAe,CAAC,MAAA;QAQnB;AALX,QAAI,CAAC,MAAM,kBAAkB;AAC3B,YAAM,iBAAgB;AAIxB,QAAIC,QAAO,QAAA,QAAA,QAAA,SAAA,UAAA,eAAA,IAAK,aAAO,QAAZ,iBAAA,SAAA,SAAA,aAAc;AACzB,QAAI,CAAC,EAAE,oBAAoB,OAAOA,SAAQ,2CAAqBA,KAAA,MAAU,IAAI,SAAS;UAIlF;AAHF,UAAI;AACF,cAAA;;SAEA,gBAAA,IAAI,aAAO,QAAX,kBAAA,SAAA,SAAA,cAAa,MAAK;AAIpB,OAAA,GAAA,2CAAuB,UAAA;IACzB;AAGA,MAAE,eAAc;EAClB,CAAA;AAEA,MAAI,YAAW,GAAA,2CAAe,MAAA;AAC5B,UAAM,iBAAgB;EACxB,CAAA;AAEA,GAAA,GAAA,aAAAC,WAAU,MAAA;AACR,QAAIC,SAAQ,QAAA,QAAA,QAAA,SAAA,SAAA,IAAK;AACjB,QAAI,CAACA;AACH;AAGF,QAAIF,QAAOE,OAAM;AACjB,IAAAA,OAAM,iBAAiB,WAAW,SAAA;AAClC,IAAAA,OAAM,iBAAiB,UAAU,QAAA;AACjC,IAAAF,UAAA,QAAAA,UAAA,SAAA,SAAAA,MAAM,iBAAiB,SAAS,OAAA;AAChC,WAAO,MAAA;AACL,MAAAE,OAAO,oBAAoB,WAAW,SAAA;AACtC,MAAAA,OAAO,oBAAoB,UAAU,QAAA;AACrC,MAAAF,UAAA,QAAAA,UAAA,SAAA,SAAAA,MAAM,oBAAoB,SAAS,OAAA;IACrC;EACF,GAAG;IAAC;IAAK;IAAW;IAAU;IAAS;GAAmB;AAC5D;AAEA,SAAS,kCAAYE,QAAyB;AAG5C,MAAI,WAAWA,OAAM;AACrB,SAAO;IACL,UAAU,SAAS;IACnB,aAAa,SAAS;IACtB,iBAAiB,SAAS;IAC1B,eAAe,SAAS;IACxB,gBAAgB,SAAS;IACzB,cAAc,SAAS;IACvB,SAAS,SAAS;IAClB,UAAU,SAAS;IACnB,cAAc,SAAS;IACvB,cAAc,SAAS;IACvB,OAAO,SAAS;EAClB;AACF;AAEA,SAAS,wCAAkBA,QAAyB;AAClD,SAAO;IACL,WAAW,CAACA,OAAM,SAAS;IAC3B,mBAAmB,kCAAYA,MAAA;IAC/B,kBAAkBA,OAAM,oBAAoB;MAACA,OAAM;QAAqB,CAAA;EAC1E;AACF;AAEA,SAAS,2CAAqBF,OAAqB;AACjD,WAAS,IAAI,GAAG,IAAIA,MAAK,SAAS,QAAQ,KAAK;AAC7C,QAAI,UAAUA,MAAK,SAAS,CAAA;AAC5B,QAAI,CAAC,QAAQ,SAAS;AACpB,aAAO;EAEX;AAEA,SAAO;AACT;;;;ACpHO,IAAM,4CAAsC;EACjD,UAAU;EACV,aAAa;EACb,iBAAiB;EACjB,eAAe;EACf,gBAAgB;EAChB,cAAc;EACd,SAAS;EACT,UAAU;EACV,cAAc;EACd,cAAc;EACd,OAAO;AACT;AAEA,IAAM,8CAAuC;EAC3C,GAAG;EACH,aAAa;EACb,OAAO;AACT;AAEO,IAAM,4CAA8C;EACzD,WAAW;EACX,mBAAmB;EACnB,kBAAkB,CAAA;AACpB;AAEO,IAAM,6CAAwB,GAAA,cAAAG,eAAgC,CAAC,CAAA;AAE/D,IAAM,2CAA6B,0BAA0B,KAAK,IAAG;AAqBrE,SAAS,0CAA0B,OAA6B;AAErE,MAAI,MAAM,wCAAA,GAA6B;AACrC,QAAI,EAAA,oBAAmB,mBAAmB,kBAAkB,iBAAiB,iBAAkB,IAAI,MAAM,wCAAA;AACzG,WAAO;;;;;;IAA2F;EACpG;AAGA,SAAO,iDAA2B,KAAA;AACpC;AAEA,SAAS,iDAA8B,OAA6B;AAClE,MAAI,EAAA,WAAU,iBAAiB,MAAM,OAAO,mBAAmB,UAAU,qBAAuB,OAAA,IAAU;AAG1G,MAAI;AACF,kBAAA,YAAc,oBAAoB;AAIpC,MAAI,kBAA2C,cAAc,SAAY;;IAEvE,kBAAkB,CAAA;IAClB,mBAAmB;EACrB,IAAI;AAGJ,MAAI,eAAuC,GAAA,cAAAC,SAAQ,MAAA;AACjD,QAAI,CAAC,YAAY,SAAS;AACxB,aAAO;AAET,QAAI,iBAAiB,kCAAY,UAAU,KAAA;AAC3C,WAAO,0CAAoB,cAAA;EAC7B,GAAG;IAAC;IAAU;GAAM;AAEpB,MAAI,sBAAA,QAAA,sBAAA,SAAA,SAAA,kBAAmB,kBAAkB;AACvC,wBAAoB;AAItB,MAAI,gBAAe,GAAA,cAAAC,YAAW,yCAAA;AAC9B,MAAI,uBAAsB,GAAA,cAAAD,SAAQ,MAAA;AAChC,QAAI;AACF,aAAO,MAAM,QAAQ,IAAA,IAAQ,KAAK,QAAQ,CAAAE,UAAQ,8BAAQ,aAAaA,KAAA,CAAK,CAAA,IAAK,8BAAQ,aAAa,IAAA,CAAK;AAE7G,WAAO,CAAA;EACT,GAAG;IAAC;IAAc;GAAK;AAGvB,MAAI,CAAC,kBAAkB,mBAAA,KAAuB,GAAA,cAAAC,UAAS,YAAA;AACvD,MAAI,CAAC,sBAAsB,qBAAA,KAAyB,GAAA,cAAAA,UAAS,KAAA;AAC7D,MAAI,iBAAiB,kBAAkB;AACrC,wBAAoB,YAAA;AACpB,0BAAsB,KAAA;EACxB;AAEA,MAAI,eAAuC,GAAA,cAAAH,SAAQ,MACjD,0CAAoB,uBAAuB,CAAA,IAAK,mBAAA,GAChD;IAAC;IAAsB;GAAoB;AAI7C,MAAI,kBAAiB,GAAA,cAAAI,QAAO,yCAAA;AAC5B,MAAI,CAAC,iBAAiB,kBAAA,KAAsB,GAAA,cAAAD,UAAS,yCAAA;AAErD,MAAI,aAAY,GAAA,cAAAC,QAAO,yCAAA;AACvB,MAAI,mBAAmB,MAAA;AACrB,QAAI,CAAC;AACH;AAGF,oBAAgB,KAAA;AAChB,QAAI,QAAQ,eAAe,qBAAqB,eAAe;AAC/D,QAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;AAChD,gBAAU,UAAU;AACpB,yBAAmB,KAAA;IACrB;EACF;AAEA,MAAI,CAAC,cAAc,eAAA,KAAmB,GAAA,cAAAD,UAAS,KAAA;AAC/C,GAAA,GAAA,cAAAE,WAAU,gBAAA;AAKV,MAAI,qBAAqB,mBAAmB,eAAe,eAAe,qBAAqB;AAC/F,MAAI,oBAAoB,uBAAuB,WAC3C,mBAAmB,eAAe,kBAClC,mBAAmB,eAAe,eAAe,qBAAqB;AAE1E,SAAO;;;IAGL,iBAAiBC,QAAK;AAEpB,UAAI,uBAAuB,UAAU,CAAC,wCAAkB,iBAAiBA,MAAA;AACvE,2BAAmBA,MAAA;;AAEnB,uBAAe,UAAUA;IAE7B;IACA,kBAAA;AAGE,UAAI,QAAQ;AACZ,UAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;AAChD,kBAAU,UAAU;AACpB,2BAAmB,KAAA;MACrB;AAIA,UAAI,uBAAuB;AACzB,wBAAgB,KAAA;AAGlB,4BAAsB,IAAA;IACxB;IACA,mBAAA;AAGE,UAAI,uBAAuB;AACzB,wBAAgB,IAAA;AAElB,4BAAsB,IAAA;IACxB;EACF;AACF;AAEA,SAAS,8BAAW,GAAU;AAC5B,MAAI,CAAC;AACH,WAAO,CAAA;AAGT,SAAO,MAAM,QAAQ,CAAA,IAAK,IAAI;IAAC;;AACjC;AAEA,SAAS,kCAAe,UAAiC,OAAQ;AAC/D,MAAI,OAAO,aAAa,YAAY;AAClC,QAAI,IAAI,SAAS,KAAA;AACjB,QAAI,KAAK,OAAO,MAAM;AACpB,aAAO,8BAAQ,CAAA;EAEnB;AAEA,SAAO,CAAA;AACT;AAEA,SAAS,0CAAoB,QAAgB;AAC3C,SAAO,OAAO,SAAS;IACrB,WAAW;IACX,kBAAkB;IAClB,mBAAmB;EACrB,IAAI;AACN;AAEA,SAAS,wCAAkB,GAA4B,GAA0B;AAC/E,MAAI,MAAM;AACR,WAAO;AAGT,SAAO,CAAC,CAAC,KAAK,CAAC,CAAC,KACX,EAAE,cAAc,EAAE,aAClB,EAAE,iBAAiB,WAAW,EAAE,iBAAiB,UACjD,EAAE,iBAAiB,MAAM,CAACC,IAAG,MAAMA,OAAM,EAAE,iBAAiB,CAAA,CAAE,KAC9D,OAAO,QAAQ,EAAE,iBAAiB,EAAE,MAAM,CAAC,CAAC,GAAG,CAAA,MAAO,EAAE,kBAAkB,CAAA,MAAO,CAAA;AACxF;AAEO,SAAS,6CAAmB,SAA2B;AAC5D,MAAI,SAAS,oBAAI,IAAA;AACjB,MAAI,YAAY;AAChB,MAAI,oBAAoB;IACtB,GAAG;EACL;AAEA,WAAS,KAAK,SAAS;QAQnB,oBAAkB;AAPpB,aAAS,KAAK,EAAE;AACd,aAAO,IAAI,CAAA;AAIb,kBAAA,YAAc,EAAE;AAChB,aAAS,OAAO;AACd,OAAA,qBAAA,mBAAkB,OAAA,GAAA,MAAlB,mBAAkB,IAAA,IAAS,EAAE,kBAAkB,GAAA;EAEnD;AAEA,oBAAkB,QAAQ,CAAC;AAC3B,SAAO;;IAEL,kBAAkB;SAAI;;;EAExB;AACF;;;AChJO,SAAS,yCACd,OACA,KAA0B;AAE1B,MAAI,EAAA,mBACiB,SAAA,aACN,OAAA,aACA,OAAA,aACA,OAAA,OACN,QAAA,qBACc,OAAA,IACnB;AACJ,MAAI,CAAC,OAAO,QAAA,KAAY,GAAA,2CAA2B,MAAM,OAAO,MAAM,gBAAgB,IAAI,MAAM,QAAQ;AACxG,MAAI,EAAA,eAAe,KAAI,GAAA,2CAA0C,OAAO,GAAA;AACxE,MAAI,mBAAkB,GAAA,2CAAuB;IAC3C,GAAG;;EAEL,CAAA;AACA,MAAI,EAAA,WAAU,kBAAkB,kBAAmB,IAAI,gBAAgB;AACvE,MAAI,EAAA,YAAW,YAAY,kBAAkB,kBAAmB,KAAI,GAAA,2CAAS;IAC3E,GAAG;;IAEH,cAAc,MAAM,gBAAgB;EACtC,CAAA;AACA,MAAI,YAAW,GAAA,2CAAe,OAAO;IAAC,WAAW;EAAI,CAAA;AAErD,QAAM,iBAAiB;;IAErB,SAAS,MAAM;EACjB;AAEA,GAAA,GAAA,2CAAa,KAAK,OAAO,QAAA;AACzB,GAAA,GAAA,2CAAkB,OAAO,iBAAiB,GAAA;AAE1C,GAAA,GAAA,cAAAC,WAAU,MAAA;AAQR,QAAI,IAAI,oBAAmB,GAAA,2CAAe,IAAI,OAAO,EAAE,qBAAqB;AAC1E,UAAIC,SAAQ,IAAI;AAChB,aAAO,eAAeA,QAAO,gBAAgB;QAC3C,KAAK,MAAMA,OAAM;QACjB,KAAK,MAAA;QAAO;QACZ,cAAc;MAChB,CAAA;IACF;EACF,GAAG;IAAC;GAAI;AAER,SAAO;;IAEL,aAAY,GAAA,2CACV,UACA,qBAAqB,UAAU,iBAAiB,QAChD;MACE,UAAU;MACV,UAAU;MACV,UAAU,cAAc,uBAAuB;MAC/C,iBAAkB,cAAc,uBAAuB,UAAW;MAClE,gBAAgB,aAAa;MAC7B,qBAAqB,MAAM,mBAAA;MAC3B,yBAAyB,MAAM,uBAAA;MAC/B,qBAAqB,MAAM,mBAAA;MAC3B,iBAAiB,MAAM,eAAA;MACvB,iBAAiB,MAAM,eAAA;;MAEvB,UAAU,CAAC,MAAqC,SAAS,EAAE,OAAO,KAAK;MACvE,cAAc,MAAM;MACpB,gBAAgB,MAAM;MACtB,WAAW,MAAM;MACjB,WAAW,MAAM;MACjB,MAAM,MAAM;MACZ,aAAa,MAAM;MACnB,WAAW,MAAM;MACjB,aAAa,MAAM;MACnB,YAAY,MAAM;MAClB,CAAC,UAAS,GAAA,cAAAC,SAAM,SAAS,EAAA,KAAO,KAAK,iBAAiB,cAAA,GAAiB,MAAM;;MAG7E,QAAQ,MAAM;MACd,OAAO,MAAM;MACb,SAAS,MAAM;;MAGf,kBAAkB,MAAM;MACxB,oBAAoB,MAAM;MAC1B,qBAAqB,MAAM;;MAG3B,UAAU,MAAM;;MAGhB,eAAe,MAAM;MACrB,SAAS,MAAM;MACf,GAAG;MACH,GAAG;IACL,CAAA;;;;;;EAOJ;AACF;;;;ACpMA,SAAS,uDAAA;AACP,SAAO,OAAO,WAAW,eACvB,OAAO,cACP,OAAO,WAAW,UAAU,oBAAoB;AACpD;AAEO,SAAS,0CAAsB,OAA2B,OAAgC,UAA4C;AAO3I,MAAI,yBAAwB,GAAA,2CAAe,CAAC,MAAA;AAC1C,QAAIC,SAAQ,SAAS;AACrB,QAAI,CAACA;AACH;AAKF,QAAI,YAA2B;AAC/B,YAAQ,EAAE,WAAS;MACjB,KAAK;MACL,KAAK;AAGH;MACF,KAAK;AAEH;MACF,KAAK;MACL,KAAK;MACL,KAAK;AACH,oBAAYA,OAAM,MAAM,MAAM,GAAGA,OAAM,cAAc,IAAKA,OAAM,MAAM,MAAMA,OAAM,YAAY;AAC9F;MACF,KAAK;AAKH,oBAAYA,OAAM,iBAAiBA,OAAM,iBACrCA,OAAM,MAAM,MAAM,GAAGA,OAAM,cAAc,IAAKA,OAAM,MAAM,MAAMA,OAAM,eAAgB,CAAA,IACtFA,OAAM,MAAM,MAAM,GAAGA,OAAM,cAAc,IAAKA,OAAM,MAAM,MAAMA,OAAM,YAAY;AACtF;MACF,KAAK;AACH,oBAAYA,OAAM,iBAAiBA,OAAM,iBACrCA,OAAM,MAAM,MAAM,GAAGA,OAAM,iBAAkB,CAAA,IAAKA,OAAM,MAAM,MAAMA,OAAM,cAAc,IACxFA,OAAM,MAAM,MAAM,GAAGA,OAAM,cAAc,IAAKA,OAAM,MAAM,MAAMA,OAAM,YAAY;AACtF;MACF,KAAK;MACL,KAAK;AACH,oBAAYA,OAAM,MAAM,MAAMA,OAAM,cAAc;AAClD;MACF;AACE,YAAI,EAAE,QAAQ;AACZ,sBACEA,OAAM,MAAM,MAAM,GAAGA,OAAM,cAAc,IACzC,EAAE,OACFA,OAAM,MAAM,MAAMA,OAAM,YAAY;AAExC;IACJ;AAKA,QAAI,aAAa,QAAQ,CAAC,MAAM,SAAS,SAAA;AACvC,QAAE,eAAc;EAEpB,CAAA;AAEA,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,QAAI,CAAC,qDAAA,KAAoC,CAAC,SAAS;AACjD;AAGF,QAAID,SAAQ,SAAS;AACrB,IAAAA,OAAM,iBAAiB,eAAe,uBAAuB,KAAA;AAC7D,WAAO,MAAA;AACL,MAAAA,OAAM,oBAAoB,eAAe,uBAAuB,KAAA;IAClE;EACF,GAAG;IAAC;IAAU;GAAsB;AAEpC,MAAI,gBAAgB,CAAC,qDAAA,IACjB,CAAA,MAAA;AACA,QAAI,YACF,EAAE,OAAO,MAAM,MAAM,GAAG,EAAE,OAAO,cAAc,IAC/C,EAAE,OACF,EAAE,OAAO,MAAM,MAAM,EAAE,OAAO,YAAY;AAE5C,QAAI,CAAC,MAAM,SAAS,SAAA;AAClB,QAAE,eAAc;EAEpB,IACE;AAEJ,MAAI,EAAA,YAAa,YAAY,gBAAc,kBAAkB,mBAAqB,GAAG,WAAA,KAAc,GAAA,0CAAa,OAAO,QAAA;AAEvH,MAAI,yBAAwB,GAAA,cAAAE,QAA2F,IAAA;AACvH,SAAO;IACL,aAAY,GAAA,2CACV,gBACA;;MAEE,qBAAA;AAaE,YAAI,EAAA,OAAM,gBAAgB,aAAc,IAAI,SAAS;AACrD,8BAAsB,UAAU;;;;QAAoC;MACtE;MACA,mBAAA;AACE,YAAI,SAAS,WAAW,CAAC,MAAM,SAAS,SAAS,QAAQ,KAAK,GAAG;AAG/D,cAAI,EAAA,OAAM,gBAAgB,aAAc,IAAI,sBAAsB;AAClE,mBAAS,QAAQ,QAAQ;AACzB,mBAAS,QAAQ,kBAAkB,gBAAgB,YAAA;AACnD,gBAAM,cAAc,KAAA;QACtB;MACF;IACF,CAAA;;;;IAKF,GAAG;EACL;AACF;;;AC/JA,IAAAC,gBAAoC;AAEpC,IAAI,eAAe,OAAO,SAAS;AACnC,SAAS,kBAAkB,SAAS,MAAM;AACxC,MAAI,UAAM,0BAAW,OAAO;AAC5B,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,OAAO,IAAI,OAAO;AACjE,QAAI,iBAAiB,IAAI,KAAK,WAAW,EAAE,OAAO,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC;AAC7F,QAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,YAAY,GAAG;AACrC,YAAM,IAAI,MAAM,iDAAiD,cAAc,GAAG;AAAA,IACpF;AACA,QAAI,UAAU,QAAQ;AACtB,QAAI,CAAC,IAAI,MAAM,OAAO,GAAG;AACvB,YAAM,IAAI,MAAM,iBAAiB,IAAI,2BAA2B,cAAc,GAAG;AAAA,IACnF;AACA,WAAO,IAAI,MAAM,OAAO;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO,KAAK,SAAS;AAC5C,MAAI,MAAM,kBAAkB,SAAS,MAAM,IAAI,KAAK,CAAC;AACrD,MAAI,EAAE,KAAK,YAAY,GAAG,aAAa,IAAI;AAC3C,MAAI,YAAY,8CAAa,uBAAQ,MAAM,0CAAU,KAAK,UAAU,GAAG,CAAC,KAAK,UAAU,CAAC,CAAC;AACzF,MAAI,cAAc,0CAAW,cAAc,KAAK;AAChD,MAAI,WAAW,gBAAgB,aAAa,SAAS,WAAW,SAAS,MAAM,OAAO;AACpF,QAAI,OAAO,aAAa,UAAU,cAAc,OAAO,MAAM,UAAU,YAAY;AACjF,kBAAY,QAAQ,CAAC,gBAAgB;AACnC,YAAI,eAAe,OAAO,aAAa,UAAU,aAAa,aAAa,MAAM,WAAW,IAAI,aAAa;AAC7G,YAAI,eAAe,EAAE,GAAG,YAAY,cAAc,GAAG,aAAa;AAClE,YAAI,QAAQ,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,EAAE,GAAG,aAAa,aAAa,CAAC,IAAI,MAAM;AACtG,eAAO,EAAE,GAAG,cAAc,GAAG,MAAM;AAAA,MACrC;AAAA,IACF,OAAO;AACL,kBAAY,QAAQ,EAAE,GAAG,aAAa,OAAO,GAAG,MAAM,MAAM;AAAA,IAC9D;AAAA,EACF;AACA,SAAO,CAAC,aAAa,SAAS;AAChC;;;ACnCA,IAAAC,gBAAmD;AAEnD,yBAAoB;AACpB,IAAI,kBAAc,6BAAc,IAAI;AACpC,IAAI,WAAO,0BAAW,SAAS,MAAM,OAAO,KAAK;AAC/C,GAAC,OAAO,GAAG,IAAI,gBAAgB,OAAO,KAAK,WAAW;AACtD,MAAI,EAAE,kBAAkB,qBAAqB,UAAU,UAAU,WAAW,GAAG,SAAS,IAAI;AAC5F,QAAM,aAAS,uBAAQ,MAAM,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;AAC7D,aAAuB,wBAAI,QAAQ,EAAE,YAAY,uBAAuB,UAAU,GAAG,UAAU,KAAK,WAAW,QAAQ,cAA0B,wBAAI,YAAY,UAAU,EAAE,OAAO,EAAE,GAAG,OAAO,mBAAmB,GAAG,cAA0B,wBAAI,0CAAsB,UAAU,EAAE,OAAO,oBAAoB,OAAO,mBAAmB,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;AACjW,CAAC;;;ACTD,IAAAC,gBAA2B;AAC3B,IAAAC,sBAAoB;AACpB,IAAIC,aAAQ,0BAAW,SAAS,MAAM,OAAO,KAAK;AAChD,MAAI,IAAI;AACR,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,sBAAsB,MAAM,KAAK,MAAM,uBAAuB,OAAO,KAAK,iBAAiB,OAAO,SAAS,cAAc,uBAAuB,OAAO,KAAK;AAClK,aAAuB,yBAAI,MAAM,EAAE,GAAG,OAAO,KAAK,mBAAmB,CAAC;AACxE,CAAC;;;ATCD,SAAS,SAAS,eAAe;AAC/B,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5B,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,EAAE,oBAAoB,uBAAuB,IAAI,kBAAkB,WAAW,KAAK,CAAC;AAC1F,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,MAAM,WAAW;AAC/E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,sBAAsB,KAAK,0BAA0B,OAAO,yBAAyB,iBAAiB,OAAO,SAAS,cAAc,uBAAuB,OAAO,KAAK;AAAA,IACvK,iBAAiB;AAAA,IACjB,gBAAgB,MAAM;AAAA,IACtB;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,wBAAoB;AAAA,IACxB,CAAC,UAAU;AACT,oBAAc,SAAS,OAAO,QAAQ,EAAE;AAAA,IAC1C;AAAA,IACA,CAAC,aAAa;AAAA,EAChB;AACA,QAAM,CAAC,eAAe,cAAc,QAAI,wBAAS,KAAK;AACtD,QAAM,YAAY,MAAM;AACxB,QAAM,oBAAoB,MAAM,KAAK,cAAc,qBAAqB,OAAO,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK;AACpK,QAAM,SAAS,UAAU,GAAG;AAC5B,QAAM,aAAa,UAAU,OAAO;AACpC,QAAM,kBAAkB,UAAU,UAAU;AAC5C,QAAM,kBAAkB,UAAU,mBAAmB;AACrD,QAAM,CAAC,YAAY,aAAa,IAAI;AAAA,IAClC,MAAM;AAAA,KACL,KAAK,MAAM,iBAAiB,OAAO,KAAK;AAAA,IACzC;AAAA,EACF;AACA,QAAM,kBAAkB,SAAS;AACjC,QAAM,qBAAqB,MAAM,MAAM,KAAK,UAAU,OAAO,SAAS,OAAO,YAAY,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS,GAAG,WAAW,OAAO,KAAK,KAAK;AACrK,QAAM,oBAAoB,CAAC,QAAQ,QAAQ,SAAS,QAAQ,OAAO,EAAE,SAAS,IAAI;AAClF,QAAM,WAAW,CAAC,QAAQ,UAAU,KAAK,qBAAqB;AAC9D,QAAM,iBAAiB,YAAY;AACnC,QAAM,eAAe,SAAS;AAC9B,QAAM,cAAc,cAAc;AAClC,QAAM,aAAa,KAAK,cAAc,OAAO,SAAS,WAAW,MAAM,WAAW,WAAW,cAAc,EAAE;AAC7G,QAAM,kBAAc,2BAAY,MAAM;AACpC,QAAI;AACJ,QAAI,iBAAiB;AACnB,aAAO,QAAQ,QAAQ;AAAA,IACzB,OAAO;AACL,oBAAc,EAAE;AAAA,IAClB;AACA,eAAW,OAAO,SAAS,QAAQ;AACnC,KAAC,MAAM,OAAO,YAAY,OAAO,SAAS,IAAI,MAAM;AAAA,EACtD,GAAG,CAAC,eAAe,SAAS,eAAe,CAAC;AAC5C,sBAAoB,MAAM;AACxB,QAAI,CAAC,OAAO;AAAS;AACrB,kBAAc,OAAO,QAAQ,KAAK;AAAA,EACpC,GAAG,CAAC,OAAO,OAAO,CAAC;AACnB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,MACE,GAAG;AAAA,MACH;AAAA,MACA,gBAAgB,cAAc;AAAA,MAC9B,OAAO;AAAA,MACP,cAAc;AAAA,QACZ,cAAc,YAAY;AAAA,QAC1B,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,MACA,kBAAkB,cAAc,aAAa;AAAA,MAC7C,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,MAAI,iBAAiB;AACnB,WAAO,WAAW;AAClB,WAAO,WAAW;AAAA,EACpB;AACA,QAAM,EAAE,gBAAgB,WAAW,WAAW,IAAI,0CAAa;AAAA,IAC7D;AAAA,IACA,aAAa;AAAA,EACf,CAAC;AACD,QAAM,EAAE,WAAW,WAAW,IAAI,0CAAS,EAAE,YAAY,CAAC,EAAE,iBAAiB,OAAO,SAAS,cAAc,YAAY,CAAC;AACxH,QAAM,EAAE,WAAW,gBAAgB,YAAY,gBAAgB,IAAI,0CAAS;AAAA,IAC1E,YAAY,CAAC,EAAE,iBAAiB,OAAO,SAAS,cAAc;AAAA,EAChE,CAAC;AACD,QAAM,EAAE,YAAY,iBAAiB,gBAAgB,0BAA0B,IAAI,0CAAa;AAChG,QAAM,EAAE,iBAAiB,IAAI,0CAAe;AAAA,IAC1C,qBAAqB;AAAA,EACvB,CAAC;AACD,QAAM,EAAE,YAAY,gBAAgB,IAAI,0CAAS;AAAA,IAC/C,YAAY,CAAC,EAAE,iBAAiB,OAAO,SAAS,cAAc,eAAe,CAAC,EAAE,iBAAiB,OAAO,SAAS,cAAc;AAAA,IAC/H,SAAS;AAAA,EACX,CAAC;AACD,QAAM,YAAY,oBAAoB,aAAa;AACnD,QAAM,iBAAiB,kBAAkB;AAAA,IACvC,gBAAgB,cAAc;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,eAAe,OAAO,MAAM,iBAAiB,aAAa,MAAM,aAAa,EAAE,WAAW,kBAAkB,kBAAkB,CAAC,IAAI,MAAM,iBAAiB,oBAAoB,OAAO,SAAS,iBAAiB,KAAK,GAAG;AAC7N,QAAM,cAAc,CAAC,CAAC,WAAW,cAAc;AAC/C,QAAM,cAAc,CAAC,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC;AAClD,QAAM,iBAAiB,CAAC,CAAC,MAAM;AAC/B,QAAM,WAAW,CAAC,CAAC;AACnB,QAAM,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC;AACrC,QAAM,uBAAuB,mBAAmB,aAAa,mBAAmB;AAChF,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,qBAAqB,OAAO,WAAW,CAAC,OAAO,QAAQ,SAAS,OAAO,QAAQ,UAAU,MAAM,CAAC,cAAc,eAAe,OAAO,iBAAiB;AAC3J,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,kBAAkB,CAAC,CAAC;AAC1B,QAAM,iBAAiB,uBAAuB,mBAAmB,kBAAkB,kBAAkB,mBAAmB,aAAa,kBAAkB;AACvJ,QAAM,8BAA8B,mBAAmB,aAAa,CAAC,kBAAkB,CAAC;AACxF,QAAM,YAAQ;AAAA,IACZ,MAAM,MAAM;AAAA,MACV,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE,aAAa,YAAY;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAe;AAAA,IACnB,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,KAAK;AAAA,QACL,WAAW,MAAM,KAAK,EAAE,OAAO,WAAW,CAAC;AAAA,QAC3C,aAAa;AAAA,QACb,eAAe;AAAA,UACb,YAAY,kBAAkB,mBAAmB,sBAAsB;AAAA,QACzE;AAAA,QACA,sBAAsB;AAAA,UACpB,kBAAkB,kBAAkB,mBAAmB,sBAAsB;AAAA,QAC/E;AAAA,QACA,qBAAqB,SAAS,aAAa;AAAA,QAC3C,sBAAsB,SAAS,cAAc;AAAA,QAC7C,iBAAiB,SAAS,cAAc,UAAU;AAAA,QAClD,cAAc,SAAS,SAAS;AAAA,QAChC,cAAc,SAAS,aAAa,cAAc;AAAA,QAClD,iBAAiB,SAAS,cAAc,UAAU;AAAA,QAClD,gBAAgB,SAAS,SAAS;AAAA,QAClC,iBAAiB,SAAS,cAAc,UAAU;AAAA,QAClD,qBAAqB,SAAS,WAAW;AAAA,QACzC,mBAAmB,SAAS,SAAS;AAAA,QACrC,kBAAkB,SAAS,QAAQ;AAAA,QACnC,kBAAkB,SAAS,CAAC,kBAAkB;AAAA,QAC9C,eAAe,SAAS,YAAY;AAAA,QACpC,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,IAChB;AAAA,EACF;AACA,QAAM,oBAAgB;AAAA,IACpB,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,aAAa;AAAA,QACb,WAAW,MAAM,MAAM,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,MAAM,CAAC;AAAA,QAChF,GAAG,0CAAW,YAAY,iBAAiB,MAAM;AAAA,MACnD;AAAA,IACF;AAAA,IACA,CAAC,OAAO,gBAAgB,YAAY,cAAc,OAAO,SAAS,WAAW,KAAK;AAAA,EACpF;AACA,QAAM,oBAAgB;AAAA,IACpB,CAAC,MAAM;AACL,UAAI,EAAE,QAAQ,YAAY,eAAe,eAAe,YAAY,CAAC,cAAc,YAAY;AAC7F,sBAAc,EAAE;AAChB,mBAAW,OAAO,SAAS,QAAQ;AAAA,MACrC;AAAA,IACF;AAAA,IACA,CAAC,YAAY,eAAe,SAAS,aAAa,cAAc,UAAU;AAAA,EAC5E;AACA,QAAM,oBAAgB;AAAA,IACpB,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,aAAa;AAAA,QACb,eAAe,SAAS,QAAQ;AAAA,QAChC,sBAAsB,SAAS,cAAc;AAAA,QAC7C,0BAA0B,SAAS,eAAe;AAAA,QAClD,wBAAwB,SAAS,CAAC,CAAC,UAAU;AAAA,QAC7C,aAAa;AAAA,QACb,WAAW,MAAM,MAAM;AAAA,UACrB,OAAO;AAAA,YACL,cAAc,OAAO,SAAS,WAAW;AAAA,YACzC,WAAW,cAAc;AAAA,YACzB,cAAc,SAAS;AAAA,YACvB,SAAS,aAAa,2BAA2B;AAAA,UACnD;AAAA,QACF,CAAC;AAAA,QACD,GAAG;AAAA,UACD;AAAA,UACA;AAAA,UACA,eAAe,YAAY;AAAA,YACzB,SAAS;AAAA,YACT,WAAW;AAAA,YACX,gBAAgB,IAAI,IAAI,OAAO,KAAK,UAAU,CAAC;AAAA,UACjD,CAAC;AAAA,UACD;AAAA,QACF;AAAA,QACA,iBAAiB,SAAS,cAAc,UAAU;AAAA,QAClD,UAAU,0CAAM,WAAW,UAAU,QAAQ;AAAA,QAC7C,WAAW,0CAAM,WAAW,WAAW,OAAO,WAAW,aAAa;AAAA,QACtE,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO,SAAS,WAAW;AAAA,MACzC,cAAc;AAAA,MACd,cAAc;AAAA,MACd;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,2BAAuB;AAAA,IAC3B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc,SAAS,aAAa,cAAc;AAAA,QAClD,sBAAsB,SAAS,cAAc;AAAA,QAC7C,cAAc,SAAS,SAAS;AAAA,QAChC,WAAW,MAAM,aAAa;AAAA,UAC5B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,cAAc,WAAW,cAAc,EAAE;AAAA,QAChG,CAAC;AAAA,QACD,GAAG,0CAAW,QAAQ,UAAU;AAAA,QAChC,SAAS,CAAC,MAAM;AACd,cAAI,OAAO,WAAW,EAAE,kBAAkB,EAAE,QAAQ;AAClD,mBAAO,QAAQ,MAAM;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,QAAQ;AAAA,UACR,GAAG,OAAO;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO,SAAS,WAAW;AAAA,IAC3C;AAAA,EACF;AACA,QAAM,2BAAuB;AAAA,IAC3B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,KAAK;AAAA,QACL,aAAa;AAAA,QACb,SAAS,CAAC,MAAM;AACd,cAAI,OAAO,WAAW,EAAE,kBAAkB,EAAE,QAAQ;AAClD,mBAAO,QAAQ,MAAM;AAAA,UACvB;AAAA,QACF;AAAA,QACA,WAAW,MAAM,aAAa;AAAA,UAC5B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,cAAc,UAAU,OAAO,SAAS,OAAO,SAAS;AAAA,QAC/G,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,CAAC,OAAO,cAAc,OAAO,SAAS,WAAW,YAAY;AAAA,EAC/D;AACA,QAAM,0BAAsB;AAAA,IAC1B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,aAAa;AAAA,QACb,WAAW,MAAM,YAAY;AAAA,UAC3B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,aAAa,UAAU,OAAO,SAAS,OAAO,SAAS;AAAA,QAC9G,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,CAAC,OAAO,cAAc,OAAO,SAAS,WAAW,WAAW;AAAA,EAC9D;AACA,QAAM,4BAAwB;AAAA,IAC5B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,aAAa;AAAA,QACb,WAAW,MAAM,cAAc;AAAA,UAC7B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,eAAe,UAAU,OAAO,SAAS,OAAO,SAAS;AAAA,QAChH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,CAAC,OAAO,cAAc,OAAO,SAAS,WAAW,aAAa;AAAA,EAChE;AACA,QAAM,0BAAsB;AAAA,IAC1B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,aAAa;AAAA,QACb,WAAW,MAAM,YAAY,EAAE,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,aAAa,UAAU,OAAO,SAAS,OAAO,SAAS,EAAE,CAAC;AAAA,MAChJ;AAAA,IACF;AAAA,IACA,CAAC,OAAO,cAAc,OAAO,SAAS,WAAW,WAAW;AAAA,EAC9D;AACA,QAAM,2BAAuB;AAAA,IAC3B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,aAAa;AAAA,QACb,WAAW,MAAM,aAAa,EAAE,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,cAAc,UAAU,OAAO,SAAS,OAAO,SAAS,EAAE,CAAC;AAAA,MAClJ;AAAA,IACF;AAAA,IACA,CAAC,OAAO,mBAAmB,cAAc,OAAO,SAAS,WAAW,YAAY;AAAA,EAClF;AACA,QAAM,0BAAsB;AAAA,IAC1B,CAAC,SAAS,CAAC,MAAM;AACf,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU,cAAc;AAAA,QACxB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,sBAAsB,SAAS,yBAAyB;AAAA,QACxD,WAAW,MAAM,YAAY;AAAA,UAC3B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,aAAa,UAAU,OAAO,SAAS,OAAO,SAAS;AAAA,QAC9G,CAAC;AAAA,QACD,GAAG,0CAAW,iBAAiB,eAAe;AAAA,MAChD;AAAA,IACF;AAAA,IACA,CAAC,OAAO,2BAA2B,iBAAiB,iBAAiB,cAAc,OAAO,SAAS,WAAW,WAAW;AAAA,EAC3H;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AUtaA,IAAAC,gBAAwB;AAExB,IAAAC,sBAAoC;AACpC,IAAI,QAAQ,WAAW,CAAC,OAAO,QAAQ;AACrC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,SAAS,EAAE,GAAG,OAAO,IAAI,CAAC;AAC9B,QAAM,eAAe,YAAwB,yBAAI,SAAS,EAAE,GAAG,cAAc,GAAG,UAAU,MAAM,CAAC,IAAI;AACrG,QAAM,UAAM,uBAAQ,MAAM;AACxB,QAAI,aAAa;AACf,iBAAuB,yBAAI,UAAU,EAAE,GAAG,oBAAoB,GAAG,UAAU,kBAA8B,yBAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC;AAAA,IACrI;AACA,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,mBAAmB,CAAC;AACrC,QAAM,oBAAgB,uBAAQ,MAAM;AAClC,UAAM,kBAAkB,aAAa;AACrC,UAAM,aAAa,mBAAmB;AACtC,QAAI,CAAC,aAAa,CAAC;AAAY,aAAO;AACtC,eAAuB,yBAAI,OAAO,EAAE,GAAG,sBAAsB,GAAG,UAAU,sBAAkC,yBAAI,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU,aAAa,CAAC,QAAoB,yBAAI,OAAO,EAAE,GAAG,oBAAoB,GAAG,UAAU,YAAY,CAAC,EAAE,CAAC;AAAA,EACnQ,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,mBAAe,uBAAQ,MAAM;AACjC,eAAuB,0BAAK,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU;AAAA,MACxE;AAAA,UACgB,yBAAI,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;AAAA,MACnD;AAAA,IACF,EAAE,CAAC;AAAA,EACL,GAAG,CAAC,cAAc,KAAK,eAAe,oBAAoB,CAAC;AAC3D,QAAM,kBAAc,uBAAQ,MAAM;AAChC,QAAI,sBAAsB;AACxB,iBAAuB,0BAAK,OAAO,EAAE,GAAG,oBAAoB,GAAG,UAAU;AAAA,YACvD,0BAAK,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU;AAAA,UACjE,CAAC,gBAAgB,eAAe;AAAA,UAChC;AAAA,QACF,EAAE,CAAC;AAAA,QACH;AAAA,MACF,EAAE,CAAC;AAAA,IACL;AACA,eAAuB,0BAAK,8BAAU,EAAE,UAAU;AAAA,UAChC,0BAAK,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU;AAAA,QACjE;AAAA,QACA;AAAA,MACF,EAAE,CAAC;AAAA,MACH;AAAA,IACF,EAAE,CAAC;AAAA,EACL,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAuB,0BAAK,WAAW,EAAE,GAAG,aAAa,GAAG,UAAU;AAAA,IACpE,gBAAgB,eAAe;AAAA,IAC/B;AAAA,EACF,EAAE,CAAC;AACL,CAAC;AACD,MAAM,cAAc;AACpB,IAAI,gBAAgB;;;AC1FpB,IAAAC,iBAAkC;;;ACTlC,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK;AAAG,SAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACRA,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ;AAAG,WAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK;AAAG,QAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,UAAI,OAAO,EAAE,QAAQ,CAAC;AAAG;AACzB,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACZ;AACA,SAAO;AACT;;;ACNA,IAAAC,SAAuB;;;ACFvB,IAAAC,iBAAkB;;;ACAlB,IAAAC,iBAAgC;AAEhC,IAAI,QAAQ;;;ADCZ,IAAI,YAAY,SAASC,WAAU,OAAO;AACxC,MAAI,MAAM,eAAAC,QAAM,OAAO,KAAK;AAC5B,QAA0B,WAAY;AACpC,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;;;AETA,IAAAC,iBAAkB;AAIlB,IAAI,YAAY,SAASC,WAAU,KAAK,OAAO;AAC7C,MAAI,OAAO,QAAQ,YAAY;AAC7B,QAAI,KAAK;AACT;AAAA,EACF;AACA,MAAI,UAAU;AAChB;AACA,IAAI,iBAAiB,SAASC,gBAAe,QAAQ,SAAS;AAC5D,MAAI,cAAc,eAAAC,QAAM,OAAO;AAC/B,SAAO,eAAAA,QAAM,YAAY,SAAU,UAAU;AAC3C,WAAO,UAAU;AACjB,QAAI,YAAY,SAAS;AACvB,gBAAU,YAAY,SAAS,IAAI;AAAA,IACrC;AACA,gBAAY,UAAU;AACtB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,cAAU,SAAS,QAAQ;AAAA,EAC7B,GAAG,CAAC,OAAO,CAAC;AACd;;;AHlBA,IAAI,wBAAwB;AAAA,EAC1B,cAAc;AAAA,EACd,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,KAAK;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAI,oBAAoB,SAASC,mBAAkB,MAAM;AACvD,SAAO,KAAK,qBAAqB,EAAE,QAAQ,SAAU,KAAK;AACxD,SAAK,MAAM,YAAY,KAAK,sBAAsB,GAAG,GAAG,WAAW;AAAA,EACrE,CAAC;AACH;AACA,IAAI,sBAAsB;AAE1B,IAAI,iBAAiB;AACrB,IAAI,YAAY,SAASC,WAAU,MAAM,YAAY;AACnD,MAAI,SAAS,KAAK;AAClB,MAAI,WAAW,YAAY,cAAc,cAAc;AAErD,WAAO,SAAS,WAAW;AAAA,EAC7B;AAGA,SAAO,SAAS,WAAW;AAC7B;AACA,SAAS,oBAAoB,YAAY,OAAO,SAAS,SAAS;AAChE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,gBAAgB;AACnB,qBAAiB,SAAS,cAAc,UAAU;AAClD,mBAAe,aAAa,YAAY,IAAI;AAC5C,mBAAe,aAAa,eAAe,MAAM;AACjD,wBAAoB,cAAc;AAAA,EACpC;AACA,MAAI,eAAe,eAAe,MAAM;AACtC,aAAS,KAAK,YAAY,cAAc;AAAA,EAC1C;AACA,MAAI,cAAc,WAAW,aAC3B,aAAa,WAAW,YACxB,cAAc,WAAW;AAC3B,MAAI,YAAY,YAAY;AAC5B,SAAO,KAAK,WAAW,EAAE,QAAQ,SAAU,MAAM;AAC/C,QAAI,MAAM;AACV,mBAAe,MAAM,GAAG,IAAI,YAAY,GAAG;AAAA,EAC7C,CAAC;AACD,sBAAoB,cAAc;AAClC,iBAAe,QAAQ;AACvB,MAAI,SAAS,UAAU,gBAAgB,UAAU;AAEjD,iBAAe,QAAQ;AACvB,WAAS,UAAU,gBAAgB,UAAU;AAG7C,iBAAe,QAAQ;AACvB,MAAI,YAAY,eAAe,eAAe;AAC9C,MAAI,YAAY,YAAY;AAC5B,MAAI,cAAc,cAAc;AAC9B,gBAAY,YAAY,cAAc;AAAA,EACxC;AACA,WAAS,KAAK,IAAI,WAAW,MAAM;AACnC,MAAI,YAAY,YAAY;AAC5B,MAAI,cAAc,cAAc;AAC9B,gBAAY,YAAY,cAAc;AAAA,EACxC;AACA,WAAS,KAAK,IAAI,WAAW,MAAM;AACnC,SAAO,CAAC,QAAQ,SAAS;AAC3B;AAEA,IAAI,OAAO,SAASC,QAAO;AAAC;AAC5B,IAAI,OAAO,SAASC,MAAK,OAAO,KAAK;AACnC,SAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACvC,QAAI,IAAI,IAAI,IAAI,IAAI;AACpB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAI,eAAe;AAAA,EAAC;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAc;AAAA,EAAY;AAAA,EAAa;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAgB;AAAA;AAAA,EAEnP;AAAA,EAAW;AAAA;AAAA,EAEX;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAe;AAAiB;AACxF,IAAI,OAAO,CAAC,CAAC,SAAS,gBAAgB;AACtC,IAAI,gBAAgB,SAASC,eAAc,MAAM;AAC/C,MAAI,QAAQ,OAAO,iBAAiB,IAAI;AACxC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,KAAK,cAAc,KAAK;AAC1C,MAAI,YAAY,YAAY;AAG5B,MAAI,cAAc,IAAI;AACpB,WAAO;AAAA,EACT;AAIA,MAAI,QAAQ,cAAc,cAAc;AACtC,gBAAY,QAAQ,WAAW,YAAY,KAAK,IAAI,WAAW,YAAY,gBAAgB,IAAI,WAAW,YAAY,eAAe,IAAI,WAAW,YAAY,YAAY,IAAI,WAAW,YAAY,WAAW,IAAI;AAAA,EACxN;AACA,MAAI,cAAc,WAAW,YAAY,aAAa,IAAI,WAAW,YAAY,UAAU;AAC3F,MAAI,aAAa,WAAW,YAAY,iBAAiB,IAAI,WAAW,YAAY,cAAc;AAClG,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,kBAAkB;AAEtB,SAAS,YAAY,QAAQ,MAAM,UAAU;AAC3C,MAAI,iBAAiB,UAAU,QAAQ;AACvC,EAAM,uBAAgB,WAAY;AAChC,QAAI,UAAU,SAASC,SAAQ,IAAI;AACjC,aAAO,eAAe,QAAQ,EAAE;AAAA,IAClC;AAEA,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,iBAAiB,MAAM,OAAO;AACrC,WAAO,WAAY;AACjB,aAAO,OAAO,oBAAoB,MAAM,OAAO;AAAA,IACjD;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,IAAI,uBAAuB,SAASC,sBAAqB,QAAQ,UAAU;AACzE,cAAY,SAAS,MAAM,SAAS,SAAU,IAAI;AAChD,QAAI,OAAO,QAAQ,SAAS,GAAG,QAAQ;AACrC,eAAS,EAAE;AAAA,IACb;AAAA,EACF,CAAC;AACH;AACA,IAAI,0BAA0B,SAASC,yBAAwB,UAAU;AACvE,cAAY,QAAQ,UAAU,QAAQ;AACxC;AACA,IAAI,yBAAyB,SAASC,wBAAuB,UAAU;AACrE,cAAY,SAAS,OAAO,eAAe,QAAQ;AACrD;AAEA,IAAI,YAAY,CAAC,qBAAqB,WAAW,WAAW,YAAY,gBAAgB;AACxF,IAAI,mBAAmB,SAASC,kBAAiB,MAAM,SAAS;AAC9D,MAAI,oBAAoB,KAAK,mBAC3B,UAAU,KAAK,SACf,UAAU,KAAK,SACf,gBAAgB,KAAK,UACrB,WAAW,kBAAkB,SAAS,OAAO,eAC7C,sBAAsB,KAAK,gBAC3B,iBAAiB,wBAAwB,SAAS,OAAO,qBACzD,QAAQ,8BAA8B,MAAM,SAAS;AACvD,MAAI,MAAM,OAAO;AACf,QAAI,eAAe,MAAM,OAAO;AAC9B,YAAM,IAAI,MAAM,yFAAyF;AAAA,IAC3G;AACA,QAAI,eAAe,MAAM,OAAO;AAC9B,YAAM,IAAI,MAAM,yFAAyF;AAAA,IAC3G;AAAA,EACF;AACA,MAAI,eAAe,MAAM,UAAU;AACnC,MAAI,SAAe,cAAO,IAAI;AAC9B,MAAI,MAAM,eAAe,QAAQ,OAAO;AACxC,MAAI,YAAkB,cAAO,CAAC;AAC9B,MAAI,uBAA6B,cAAO;AACxC,MAAI,iBAAiB,SAASC,kBAAiB;AAC7C,QAAI,OAAO,OAAO;AAClB,QAAI,iBAAiB,qBAAqB,qBAAqB,UAAU,qBAAqB,UAAU,gBAAgB,IAAI;AAC5H,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,yBAAqB,UAAU;AAC/B,QAAI,uBAAuB,oBAAoB,gBAAgB,KAAK,SAAS,KAAK,eAAe,KAAK,SAAS,OAAO,GACpH,SAAS,qBAAqB,CAAC,GAC/B,YAAY,qBAAqB,CAAC;AACpC,QAAI,UAAU,YAAY,QAAQ;AAChC,gBAAU,UAAU;AACpB,WAAK,MAAM,YAAY,UAAU,SAAS,MAAM,WAAW;AAC3D,qBAAe,QAAQ;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,eAAe,SAASC,cAAa,OAAO;AAC9C,QAAI,CAAC,cAAc;AACjB,qBAAe;AAAA,IACjB;AACA,aAAS,KAAK;AAAA,EAChB;AACA;AACE,IAAM,uBAAgB,cAAc;AACpC,yBAAqB,QAAQ,WAAY;AACvC,UAAI,CAAC,cAAc;AACjB,YAAI,eAAe,OAAO,QAAQ;AAClC,8BAAsB,WAAY;AAChC,cAAI,OAAO,OAAO;AAClB,cAAI,QAAQ,iBAAiB,KAAK,OAAO;AACvC,2BAAe;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,4BAAwB,cAAc;AACtC,2BAAuB,cAAc;AACrC,WAA0B,qBAAc,YAAY,SAAS,CAAC,GAAG,OAAO;AAAA,MACtE,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAIC,SAA6B,kBAAW,gBAAgB;;;AHnN5D,IAAAC,sBAA0B;AAC1B,IAAI,WAAW;AAAA,EACb,CAAC;AAAA,IACC;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB;AAAA,IACA,GAAG;AAAA,EACL,GAAG,QAAQ;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,SAAS,EAAE,GAAG,YAAY,KAAK,aAAa,KAAK,CAAC;AACtD,UAAM,CAAC,iBAAiB,oBAAoB,QAAI,yBAAS,UAAU,CAAC;AACpE,UAAM,CAAC,gBAAgB,iBAAiB,QAAI,yBAAS,KAAK;AAC1D,UAAM,eAAe,YAAwB,yBAAI,SAAS,EAAE,GAAG,cAAc,GAAG,UAAU,MAAM,CAAC,IAAI;AACrG,UAAM,aAAa,cAAc;AACjC,UAAM,qBAAqB,CAAC,QAAQ,SAAS;AAC3C,UAAI,YAAY,GAAG;AACjB,6BAAqB,UAAU,KAAK,YAAY,CAAC;AAAA,MACnD;AACA,UAAI,UAAU,SAAS;AACrB,cAAM,eAAe,UAAU,UAAU,KAAK;AAC9C,0BAAkB,YAAY;AAAA,MAChC;AACA,wBAAkB,OAAO,SAAS,eAAe,QAAQ,IAAI;AAAA,IAC/D;AACA,UAAM,UAAU,sBAAkC,yBAAI,YAAY,EAAE,GAAG,YAAY,OAAO,0CAAW,WAAW,OAAO,SAAS,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAoB;AAAA,MACtKC;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH;AAAA,QACA,oBAAoB,SAAS,CAAC,cAAc;AAAA,QAC5C;AAAA,QACA;AAAA,QACA,OAAO,0CAAW,WAAW,OAAO,SAAS,OAAO,QAAQ,CAAC,CAAC;AAAA,QAC9D,gBAAgB;AAAA,MAClB;AAAA,IACF;AACA,UAAM,yBAAqB,wBAAQ,MAAM;AACvC,aAAO,kBAA8B,yBAAI,UAAU,EAAE,GAAG,oBAAoB,GAAG,cAA0B,yBAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,IAAI;AAAA,IACzI,GAAG,CAAC,aAAa,mBAAmB,CAAC;AACrC,UAAM,mBAAe,wBAAQ,MAAM;AACjC,UAAI,gBAAgB,YAAY;AAC9B,mBAAuB,0BAAK,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU;AAAA,UACxE;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AACA,iBAAuB,yBAAI,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU,QAAQ,CAAC;AAAA,IACpF,GAAG,CAAC,cAAc,YAAY,YAAY,oBAAoB,CAAC;AAC/D,UAAM,kBAAkB,aAAa;AACrC,UAAM,mBAAmB,mBAAmB;AAC5C,eAAuB,0BAAK,WAAW,EAAE,GAAG,aAAa,GAAG,UAAU;AAAA,MACpE,uBAAuB,eAAe;AAAA,UACtB,0BAAK,OAAO,EAAE,GAAG,qBAAqB,GAAG,0BAA0B,SAAS,eAAe,GAAG,UAAU;AAAA,QACtH,sBAAsB,eAAe;AAAA,QACrC;AAAA,QACA;AAAA,MACF,EAAE,CAAC;AAAA,MACH,aAAa,uBAAmC,yBAAI,OAAO,EAAE,GAAG,sBAAsB,GAAG,UAAU,sBAAkC,yBAAI,OAAO,EAAE,GAAG,qBAAqB,GAAG,UAAU,aAAa,CAAC,QAAoB,yBAAI,OAAO,EAAE,GAAG,oBAAoB,GAAG,UAAU,YAAY,CAAC,EAAE,CAAC,IAAI;AAAA,IAChS,EAAE,CAAC;AAAA,EACL;AACF;AACA,SAAS,cAAc;AACvB,IAAI,mBAAmB;", "names": ["import_react", "form", "$9Gacy$useEffect", "input", "$69F46$createContext", "$69F46$useMemo", "$69F46$useContext", "name", "$69F46$useState", "$69F46$useRef", "$69F46$useEffect", "value", "a", "$ig234$useEffect", "input", "$ig234$react", "input", "$jyGKS$useEffect", "$jyGKS$useRef", "import_react", "import_react", "import_react", "import_jsx_runtime", "Form2", "import_react", "import_jsx_runtime", "import_react", "React", "import_react", "import_react", "useLatest", "React", "import_react", "updateRef", "useComposedRef", "React", "forceHiddenStyles", "getHeight", "noop", "pick", "getSizingData", "handler", "useFormResetListener", "useWindowResizeListener", "useFontsLoadedListener", "TextareaAutosize", "resizeTextarea", "handleChange", "index", "import_jsx_runtime", "index"]}
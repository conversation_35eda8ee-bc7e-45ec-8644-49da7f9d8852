{"version": 3, "sources": ["../../@heroui/code/dist/chunk-UDFNFZDA.mjs", "../../@heroui/code/dist/chunk-C3KKIFEX.mjs"], "sourcesContent": ["// src/use-code.ts\nimport { code } from \"@heroui/theme\";\nimport { mapPropsVariants } from \"@heroui/system-rsc\";\nimport { useMemo } from \"react\";\nimport { objectToDeps } from \"@heroui/shared-utils\";\nfunction useCode(originalProps) {\n  const [props, variantProps] = mapPropsVariants(originalProps, code.variantKeys);\n  const { as, children, className, ...otherProps } = props;\n  const Component = as || \"code\";\n  const styles = useMemo(\n    () => code({\n      ...variantProps,\n      className\n    }),\n    [objectToDeps(variantProps), className]\n  );\n  const getCodeProps = () => {\n    return {\n      className: styles,\n      ...otherProps\n    };\n  };\n  return { Component, children, getCodeProps };\n}\n\nexport {\n  useCode\n};\n", "import {\n  useCode\n} from \"./chunk-UDFNFZDA.mjs\";\n\n// src/code.tsx\nimport { forwardRef } from \"@heroui/system-rsc\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Code = forwardRef((props, ref) => {\n  const { Component, children, getCodeProps } = useCode({ ...props });\n  return /* @__PURE__ */ jsx(Component, { ref, ...getCodeProps(), children });\n});\nCode.displayName = \"HeroUI.Code\";\nvar code_default = Code;\n\nexport {\n  code_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,mBAAwB;AAExB,SAAS,QAAQ,eAAe;AAC9B,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,KAAK,WAAW;AAC9E,QAAM,EAAE,IAAI,UAAU,WAAW,GAAG,WAAW,IAAI;AACnD,QAAM,YAAY,MAAM;AACxB,QAAM,aAAS;AAAA,IACb,MAAM,KAAK;AAAA,MACT,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,IACD,CAAC,aAAa,YAAY,GAAG,SAAS;AAAA,EACxC;AACA,QAAM,eAAe,MAAM;AACzB,WAAO;AAAA,MACL,WAAW;AAAA,MACX,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO,EAAE,WAAW,UAAU,aAAa;AAC7C;;;ACjBA,yBAAoB;AACpB,IAAI,OAAO,WAAW,CAAC,OAAO,QAAQ;AACpC,QAAM,EAAE,WAAW,UAAU,aAAa,IAAI,QAAQ,EAAE,GAAG,MAAM,CAAC;AAClE,aAAuB,wBAAI,WAAW,EAAE,KAAK,GAAG,aAAa,GAAG,SAAS,CAAC;AAC5E,CAAC;AACD,KAAK,cAAc;AACnB,IAAI,eAAe;", "names": []}
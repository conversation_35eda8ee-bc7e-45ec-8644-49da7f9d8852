"use client";
import {
  TRANSITION_VARIANTS
} from "./chunk-ZLNBKG4O.js";
import {
  CheckLinearIcon,
  CopyLinearIcon
} from "./chunk-MNMT4S24.js";
import {
  useSafeLayoutEffect
} from "./chunk-4HNJ6BCR.js";
import {
  button_default
} from "./chunk-735VBPO2.js";
import {
  $2a41e45df1593e64$export$d39e1813b3bdd0e1,
  $a11501f3d1d39e6c$export$ea8f71083e90600f,
  $f57aed4a881a3485$export$b47c3594eab58386,
  $f7dceffc5ad7768b$export$4e328f61c538687f,
  createDOMRef,
  filterDOMProps,
  mergeRefs,
  useDOMRef,
  useProviderContext
} from "./chunk-XXJ3QYII.js";
import {
  $3ef42575df84b30b$export$9d1611c77c2fe928,
  $458b0a5536c1a7cf$export$40bfa8c7b0832715,
  $507fabe10e71c6fb$export$630ff653c5ada6a9,
  $507fabe10e71c6fb$export$b9b3dfddab17db27,
  $6179b936705e76d3$export$ae780daf29e6d456,
  $65484d02dcb7eb3e$export$457c3d6518dd4c6f,
  $bdb11010cef70236$export$f680877a34711e37,
  $f645667febf57a63$export$4c014de7c8940b4c
} from "./chunk-P2562WEW.js";
import "./chunk-AJTCXCUR.js";
import {
  AnimatePresence,
  LazyMotion,
  m
} from "./chunk-5AT2OBO2.js";
import "./chunk-6NY3ZDQI.js";
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-C4VLZYLP.js";
import {
  clsx,
  dataAttr,
  objectToDeps,
  popover,
  snippet,
  warn
} from "./chunk-B4Z3JF7O.js";
import "./chunk-XDJEYPHN.js";
import {
  require_jsx_runtime
} from "./chunk-6HCJQXVG.js";
import {
  require_react
} from "./chunk-XEXUAUZA.js";
import {
  __toESM
} from "./chunk-LQ2VYIYD.js";

// node_modules/@heroui/use-clipboard/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var transformValue = (text) => {
  return text.replace(/[\u00A0]/g, " ");
};
function useClipboard({ timeout = 2e3 } = {}) {
  const [error, setError] = (0, import_react.useState)(null);
  const [copied, setCopied] = (0, import_react.useState)(false);
  const [copyTimeout, setCopyTimeout] = (0, import_react.useState)(null);
  const onClearTimeout = (0, import_react.useCallback)(() => {
    if (copyTimeout) {
      clearTimeout(copyTimeout);
    }
  }, [copyTimeout]);
  const handleCopyResult = (0, import_react.useCallback)(
    (value) => {
      onClearTimeout();
      setCopyTimeout(setTimeout(() => setCopied(false), timeout));
      setCopied(value);
    },
    [onClearTimeout, timeout]
  );
  const copy = (0, import_react.useCallback)(
    (valueToCopy) => {
      if ("clipboard" in navigator) {
        const transformedValue = typeof valueToCopy === "string" ? transformValue(valueToCopy) : valueToCopy;
        navigator.clipboard.writeText(transformedValue).then(() => handleCopyResult(true)).catch((err) => setError(err));
      } else {
        setError(new Error("useClipboard: navigator.clipboard is not supported"));
      }
    },
    [handleCopyResult]
  );
  const reset = (0, import_react.useCallback)(() => {
    setCopied(false);
    setError(null);
    onClearTimeout();
  }, [onClearTimeout]);
  return { copy, reset, error, copied };
}

// node_modules/@heroui/snippet/dist/chunk-UD35SZSW.mjs
var import_react2 = __toESM(require_react(), 1);
function useSnippet(originalProps) {
  var _a, _b, _c, _d;
  const globalContext = useProviderContext();
  const [props, variantProps] = mapPropsVariants(originalProps, snippet.variantKeys);
  const {
    ref,
    as,
    children,
    symbol = "$",
    classNames,
    timeout,
    copyIcon,
    checkIcon,
    codeString,
    disableCopy = false,
    disableTooltip = false,
    hideCopyButton = false,
    autoFocus = false,
    hideSymbol = false,
    onCopy: onCopyProp,
    tooltipProps: userTooltipProps = {},
    copyButtonProps: userButtonProps = {},
    className,
    ...otherProps
  } = props;
  const Component = as || "div";
  const shouldFilterDOMProps = typeof Component === "string";
  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const tooltipProps = {
    offset: 15,
    delay: 1e3,
    content: "Copy to clipboard",
    color: (_d = originalProps == null ? void 0 : originalProps.color) != null ? _d : (_c = snippet.defaultVariants) == null ? void 0 : _c.color,
    isDisabled: props.disableCopy,
    ...userTooltipProps
  };
  const domRef = useDOMRef(ref);
  const preRef = (0, import_react2.useRef)(null);
  const { copy, copied } = useClipboard({ timeout });
  const isMultiLine = children && Array.isArray(children);
  const { isFocusVisible, isFocused, focusProps } = $f7dceffc5ad7768b$export$4e328f61c538687f({
    autoFocus
  });
  const slots = (0, import_react2.useMemo)(
    () => snippet({
      ...variantProps,
      disableAnimation
    }),
    [objectToDeps(variantProps), disableAnimation]
  );
  const symbolBefore = (0, import_react2.useMemo)(() => {
    if (!symbol || typeof symbol !== "string")
      return symbol;
    const str = symbol.trim();
    return str ? `${str} ` : "";
  }, [symbol]);
  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);
  const getSnippetProps = (0, import_react2.useCallback)(
    () => ({
      className: slots.base({
        class: baseStyles
      }),
      ...filterDOMProps(otherProps, {
        enabled: shouldFilterDOMProps
      })
    }),
    [slots, baseStyles, isMultiLine, otherProps]
  );
  const onCopy = (0, import_react2.useCallback)(() => {
    var _a2;
    if (disableCopy) {
      return;
    }
    let stringValue = "";
    if (typeof children === "string") {
      stringValue = children;
    } else if (Array.isArray(children)) {
      children.forEach((child) => {
        var _a3, _b2;
        const childString = typeof child === "string" ? child : (_b2 = (_a3 = child == null ? void 0 : child.props) == null ? void 0 : _a3.children) == null ? void 0 : _b2.toString();
        if (childString) {
          stringValue += childString + "\n";
        }
      });
    }
    const valueToCopy = codeString || stringValue || ((_a2 = preRef.current) == null ? void 0 : _a2.textContent) || "";
    copy(valueToCopy);
    onCopyProp == null ? void 0 : onCopyProp(valueToCopy);
  }, [copy, codeString, disableCopy, onCopyProp, children]);
  const copyButtonProps = {
    "aria-label": typeof tooltipProps.content === "string" ? tooltipProps.content : "Copy to clipboard",
    size: "sm",
    variant: "light",
    isDisabled: disableCopy,
    onPress: onCopy,
    isIconOnly: true,
    ...userButtonProps
  };
  const getCopyButtonProps = (0, import_react2.useCallback)(
    () => ({
      ...copyButtonProps,
      "data-copied": dataAttr(copied),
      className: slots.copyButton({
        class: clsx(classNames == null ? void 0 : classNames.copyButton)
      })
    }),
    [
      slots,
      isFocusVisible,
      isFocused,
      disableCopy,
      classNames == null ? void 0 : classNames.copyButton,
      copyButtonProps,
      focusProps
    ]
  );
  return {
    Component,
    as,
    domRef,
    preRef,
    children,
    slots,
    classNames,
    copied,
    onCopy,
    copyIcon,
    checkIcon,
    symbolBefore,
    isMultiLine,
    isFocusVisible,
    hideCopyButton,
    disableCopy,
    disableTooltip,
    hideSymbol,
    tooltipProps,
    getSnippetProps,
    getCopyButtonProps
  };
}

// node_modules/@heroui/snippet/dist/chunk-VHMYBPCH.mjs
var import_react13 = __toESM(require_react(), 1);

// node_modules/@heroui/tooltip/dist/chunk-O2IDE4PL.mjs
var import_react10 = __toESM(require_react(), 1);

// node_modules/@react-stately/tooltip/dist/useTooltipTriggerState.mjs
var import_react4 = __toESM(require_react(), 1);

// node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs
var import_react3 = __toESM(require_react(), 1);
function $fc909762b330b746$export$61c6a8c84e605fb6(props) {
  let [isOpen, setOpen] = (0, $458b0a5536c1a7cf$export$40bfa8c7b0832715)(props.isOpen, props.defaultOpen || false, props.onOpenChange);
  const open = (0, import_react3.useCallback)(() => {
    setOpen(true);
  }, [
    setOpen
  ]);
  const close = (0, import_react3.useCallback)(() => {
    setOpen(false);
  }, [
    setOpen
  ]);
  const toggle = (0, import_react3.useCallback)(() => {
    setOpen(!isOpen);
  }, [
    setOpen,
    isOpen
  ]);
  return {
    isOpen,
    setOpen,
    open,
    close,
    toggle
  };
}

// node_modules/@react-stately/tooltip/dist/useTooltipTriggerState.mjs
var $8796f90736e175cb$var$TOOLTIP_DELAY = 1500;
var $8796f90736e175cb$var$TOOLTIP_COOLDOWN = 500;
var $8796f90736e175cb$var$tooltips = {};
var $8796f90736e175cb$var$tooltipId = 0;
var $8796f90736e175cb$var$globalWarmedUp = false;
var $8796f90736e175cb$var$globalWarmUpTimeout = null;
var $8796f90736e175cb$var$globalCooldownTimeout = null;
function $8796f90736e175cb$export$4d40659c25ecb50b(props = {}) {
  let { delay = $8796f90736e175cb$var$TOOLTIP_DELAY, closeDelay = $8796f90736e175cb$var$TOOLTIP_COOLDOWN } = props;
  let { isOpen, open, close } = (0, $fc909762b330b746$export$61c6a8c84e605fb6)(props);
  let id = (0, import_react4.useMemo)(() => `${++$8796f90736e175cb$var$tooltipId}`, []);
  let closeTimeout = (0, import_react4.useRef)(null);
  let closeCallback = (0, import_react4.useRef)(close);
  let ensureTooltipEntry = () => {
    $8796f90736e175cb$var$tooltips[id] = hideTooltip;
  };
  let closeOpenTooltips = () => {
    for (let hideTooltipId in $8796f90736e175cb$var$tooltips)
      if (hideTooltipId !== id) {
        $8796f90736e175cb$var$tooltips[hideTooltipId](true);
        delete $8796f90736e175cb$var$tooltips[hideTooltipId];
      }
  };
  let showTooltip = () => {
    if (closeTimeout.current)
      clearTimeout(closeTimeout.current);
    closeTimeout.current = null;
    closeOpenTooltips();
    ensureTooltipEntry();
    $8796f90736e175cb$var$globalWarmedUp = true;
    open();
    if ($8796f90736e175cb$var$globalWarmUpTimeout) {
      clearTimeout($8796f90736e175cb$var$globalWarmUpTimeout);
      $8796f90736e175cb$var$globalWarmUpTimeout = null;
    }
    if ($8796f90736e175cb$var$globalCooldownTimeout) {
      clearTimeout($8796f90736e175cb$var$globalCooldownTimeout);
      $8796f90736e175cb$var$globalCooldownTimeout = null;
    }
  };
  let hideTooltip = (immediate) => {
    if (immediate || closeDelay <= 0) {
      if (closeTimeout.current)
        clearTimeout(closeTimeout.current);
      closeTimeout.current = null;
      closeCallback.current();
    } else if (!closeTimeout.current)
      closeTimeout.current = setTimeout(() => {
        closeTimeout.current = null;
        closeCallback.current();
      }, closeDelay);
    if ($8796f90736e175cb$var$globalWarmUpTimeout) {
      clearTimeout($8796f90736e175cb$var$globalWarmUpTimeout);
      $8796f90736e175cb$var$globalWarmUpTimeout = null;
    }
    if ($8796f90736e175cb$var$globalWarmedUp) {
      if ($8796f90736e175cb$var$globalCooldownTimeout)
        clearTimeout($8796f90736e175cb$var$globalCooldownTimeout);
      $8796f90736e175cb$var$globalCooldownTimeout = setTimeout(() => {
        delete $8796f90736e175cb$var$tooltips[id];
        $8796f90736e175cb$var$globalCooldownTimeout = null;
        $8796f90736e175cb$var$globalWarmedUp = false;
      }, Math.max($8796f90736e175cb$var$TOOLTIP_COOLDOWN, closeDelay));
    }
  };
  let warmupTooltip = () => {
    closeOpenTooltips();
    ensureTooltipEntry();
    if (!isOpen && !$8796f90736e175cb$var$globalWarmUpTimeout && !$8796f90736e175cb$var$globalWarmedUp)
      $8796f90736e175cb$var$globalWarmUpTimeout = setTimeout(() => {
        $8796f90736e175cb$var$globalWarmUpTimeout = null;
        $8796f90736e175cb$var$globalWarmedUp = true;
        showTooltip();
      }, delay);
    else if (!isOpen)
      showTooltip();
  };
  (0, import_react4.useEffect)(() => {
    closeCallback.current = close;
  }, [
    close
  ]);
  (0, import_react4.useEffect)(() => {
    return () => {
      if (closeTimeout.current)
        clearTimeout(closeTimeout.current);
      let tooltip = $8796f90736e175cb$var$tooltips[id];
      if (tooltip)
        delete $8796f90736e175cb$var$tooltips[id];
    };
  }, [
    id
  ]);
  return {
    isOpen,
    open: (immediate) => {
      if (!immediate && delay > 0 && !closeTimeout.current)
        warmupTooltip();
      else
        showTooltip();
    },
    close: hideTooltip
  };
}

// node_modules/@react-aria/tooltip/dist/useTooltip.mjs
function $326e436e94273fe1$export$1c4b08e0eca38426(props, state) {
  let domProps = (0, $65484d02dcb7eb3e$export$457c3d6518dd4c6f)(props, {
    labelable: true
  });
  let { hoverProps } = (0, $6179b936705e76d3$export$ae780daf29e6d456)({
    onHoverStart: () => state === null || state === void 0 ? void 0 : state.open(true),
    onHoverEnd: () => state === null || state === void 0 ? void 0 : state.close()
  });
  return {
    tooltipProps: (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(domProps, hoverProps, {
      role: "tooltip"
    })
  };
}

// node_modules/@react-aria/tooltip/dist/useTooltipTrigger.mjs
var import_react5 = __toESM(require_react(), 1);
function $4e1b34546679e357$export$a6da6c504e4bba8b(props, state, ref) {
  let { isDisabled, trigger } = props;
  let tooltipId = (0, $bdb11010cef70236$export$f680877a34711e37)();
  let isHovered = (0, import_react5.useRef)(false);
  let isFocused = (0, import_react5.useRef)(false);
  let handleShow = () => {
    if (isHovered.current || isFocused.current)
      state.open(isFocused.current);
  };
  let handleHide = (immediate) => {
    if (!isHovered.current && !isFocused.current)
      state.close(immediate);
  };
  (0, import_react5.useEffect)(() => {
    let onKeyDown = (e) => {
      if (ref && ref.current) {
        if (e.key === "Escape") {
          e.stopPropagation();
          state.close(true);
        }
      }
    };
    if (state.isOpen) {
      document.addEventListener("keydown", onKeyDown, true);
      return () => {
        document.removeEventListener("keydown", onKeyDown, true);
      };
    }
  }, [
    ref,
    state
  ]);
  let onHoverStart = () => {
    if (trigger === "focus")
      return;
    if ((0, $507fabe10e71c6fb$export$630ff653c5ada6a9)() === "pointer")
      isHovered.current = true;
    else
      isHovered.current = false;
    handleShow();
  };
  let onHoverEnd = () => {
    if (trigger === "focus")
      return;
    isFocused.current = false;
    isHovered.current = false;
    handleHide();
  };
  let onPressStart = () => {
    isFocused.current = false;
    isHovered.current = false;
    handleHide(true);
  };
  let onFocus = () => {
    let isVisible = (0, $507fabe10e71c6fb$export$b9b3dfddab17db27)();
    if (isVisible) {
      isFocused.current = true;
      handleShow();
    }
  };
  let onBlur = () => {
    isFocused.current = false;
    isHovered.current = false;
    handleHide(true);
  };
  let { hoverProps } = (0, $6179b936705e76d3$export$ae780daf29e6d456)({
    isDisabled,
    onHoverStart,
    onHoverEnd
  });
  let { focusableProps } = (0, $f645667febf57a63$export$4c014de7c8940b4c)({
    isDisabled,
    onFocus,
    onBlur
  }, ref);
  return {
    triggerProps: {
      "aria-describedby": state.isOpen ? tooltipId : void 0,
      ...(0, $3ef42575df84b30b$export$9d1611c77c2fe928)(focusableProps, hoverProps, {
        onPointerDown: onPressStart,
        onKeyDown: onPressStart,
        tabIndex: void 0
      })
    },
    tooltipProps: {
      id: tooltipId
    }
  };
}

// node_modules/@heroui/tooltip/dist/chunk-O2IDE4PL.mjs
var import_react11 = __toESM(require_react(), 1);

// node_modules/@react-stately/collections/dist/Item.mjs
var import_react6 = __toESM(require_react(), 1);
function $c1d7fb2ec91bae71$var$Item(props) {
  return null;
}
$c1d7fb2ec91bae71$var$Item.getCollectionNode = function* getCollectionNode(props, context) {
  let { childItems, title, children } = props;
  let rendered = props.title || props.children;
  let textValue = props.textValue || (typeof rendered === "string" ? rendered : "") || props["aria-label"] || "";
  if (!textValue && !(context === null || context === void 0 ? void 0 : context.suppressTextValueWarning) && true)
    console.warn("<Item> with non-plain text contents is unsupported by type to select for accessibility. Please add a `textValue` prop.");
  yield {
    type: "item",
    props,
    rendered,
    textValue,
    "aria-label": props["aria-label"],
    hasChildNodes: $c1d7fb2ec91bae71$var$hasChildItems(props),
    *childNodes() {
      if (childItems)
        for (let child of childItems)
          yield {
            type: "item",
            value: child
          };
      else if (title) {
        let items = [];
        (0, import_react6.default).Children.forEach(children, (child) => {
          items.push({
            type: "item",
            element: child
          });
        });
        yield* items;
      }
    }
  };
};
function $c1d7fb2ec91bae71$var$hasChildItems(props) {
  if (props.hasChildItems != null)
    return props.hasChildItems;
  if (props.childItems)
    return true;
  if (props.title && (0, import_react6.default).Children.count(props.children) > 0)
    return true;
  return false;
}

// node_modules/@react-stately/collections/dist/Section.mjs
var import_react7 = __toESM(require_react(), 1);
function $9fc4852771d079eb$var$Section(props) {
  return null;
}
$9fc4852771d079eb$var$Section.getCollectionNode = function* getCollectionNode2(props) {
  let { children, title, items } = props;
  yield {
    type: "section",
    props,
    hasChildNodes: true,
    rendered: title,
    "aria-label": props["aria-label"],
    *childNodes() {
      if (typeof children === "function") {
        if (!items)
          throw new Error("props.children was a function but props.items is missing");
        for (let item of items)
          yield {
            type: "item",
            value: item,
            renderer: children
          };
      } else {
        let items2 = [];
        (0, import_react7.default).Children.forEach(children, (child) => {
          items2.push({
            type: "item",
            element: child
          });
        });
        yield* items2;
      }
    }
  };
};

// node_modules/@react-stately/collections/dist/CollectionBuilder.mjs
var import_react8 = __toESM(require_react(), 1);

// node_modules/@react-stately/collections/dist/useCollection.mjs
var import_react9 = __toESM(require_react(), 1);

// node_modules/@heroui/aria-utils/dist/chunk-WQVQ7P2I.mjs
var getTransformOrigins = (placement) => {
  const origins = {
    top: {
      originY: 1
    },
    bottom: {
      originY: 0
    },
    left: {
      originX: 1
    },
    right: {
      originX: 0
    },
    "top-start": {
      originX: 0,
      originY: 1
    },
    "top-end": {
      originX: 1,
      originY: 1
    },
    "bottom-start": {
      originX: 0,
      originY: 0
    },
    "bottom-end": {
      originX: 1,
      originY: 0
    },
    "right-start": {
      originX: 0,
      originY: 0
    },
    "right-end": {
      originX: 0,
      originY: 1
    },
    "left-start": {
      originX: 1,
      originY: 0
    },
    "left-end": {
      originX: 1,
      originY: 1
    }
  };
  return (origins == null ? void 0 : origins[placement]) || {};
};
var toReactAriaPlacement = (placement) => {
  const mapPositions = {
    top: "top",
    bottom: "bottom",
    left: "left",
    right: "right",
    "top-start": "top start",
    "top-end": "top end",
    "bottom-start": "bottom start",
    "bottom-end": "bottom end",
    "left-start": "left top",
    "left-end": "left bottom",
    "right-start": "right top",
    "right-end": "right bottom"
  };
  return mapPositions[placement];
};
var getArrowPlacement = (dynamicPlacement, placement) => {
  if (placement.includes("-")) {
    const [, position] = placement.split("-");
    return `${dynamicPlacement}-${position}`;
  }
  return dynamicPlacement;
};

// node_modules/@heroui/tooltip/dist/chunk-O2IDE4PL.mjs
function useTooltip(originalProps) {
  var _a, _b;
  const globalContext = useProviderContext();
  const [props, variantProps] = mapPropsVariants(originalProps, popover.variantKeys);
  const {
    ref,
    as,
    isOpen: isOpenProp,
    content,
    children,
    defaultOpen,
    onOpenChange,
    isDisabled,
    trigger: triggerAction,
    shouldFlip = true,
    containerPadding = 12,
    placement: placementProp = "top",
    delay = 0,
    closeDelay = 500,
    showArrow = false,
    offset = 7,
    crossOffset = 0,
    isDismissable,
    shouldCloseOnBlur = true,
    portalContainer,
    isKeyboardDismissDisabled = false,
    updatePositionDeps = [],
    shouldCloseOnInteractOutside,
    className,
    onClose,
    motionProps,
    classNames,
    ...otherProps
  } = props;
  const Component = as || "div";
  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const state = $8796f90736e175cb$export$4d40659c25ecb50b({
    delay,
    closeDelay,
    isDisabled,
    defaultOpen,
    isOpen: isOpenProp,
    onOpenChange: (isOpen2) => {
      onOpenChange == null ? void 0 : onOpenChange(isOpen2);
      if (!isOpen2) {
        onClose == null ? void 0 : onClose();
      }
    }
  });
  const triggerRef = (0, import_react11.useRef)(null);
  const overlayRef = (0, import_react11.useRef)(null);
  const tooltipId = (0, import_react10.useId)();
  const isOpen = state.isOpen && !isDisabled;
  (0, import_react10.useImperativeHandle)(
    ref,
    () => (
      // @ts-ignore
      createDOMRef(overlayRef)
    )
  );
  const { triggerProps, tooltipProps: triggerTooltipProps } = $4e1b34546679e357$export$a6da6c504e4bba8b(
    {
      isDisabled,
      trigger: triggerAction
    },
    state,
    triggerRef
  );
  const { tooltipProps } = $326e436e94273fe1$export$1c4b08e0eca38426(
    {
      isOpen,
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(props, triggerTooltipProps)
    },
    state
  );
  const {
    overlayProps: positionProps,
    placement,
    updatePosition
  } = $2a41e45df1593e64$export$d39e1813b3bdd0e1({
    isOpen,
    targetRef: triggerRef,
    placement: toReactAriaPlacement(placementProp),
    overlayRef,
    offset: showArrow ? offset + 3 : offset,
    crossOffset,
    shouldFlip,
    containerPadding
  });
  useSafeLayoutEffect(() => {
    if (!updatePositionDeps.length)
      return;
    updatePosition();
  }, updatePositionDeps);
  const { overlayProps } = $a11501f3d1d39e6c$export$ea8f71083e90600f(
    {
      isOpen,
      onClose: state.close,
      isDismissable,
      shouldCloseOnBlur,
      isKeyboardDismissDisabled,
      shouldCloseOnInteractOutside
    },
    overlayRef
  );
  const slots = (0, import_react11.useMemo)(
    () => {
      var _a2, _b2, _c;
      return popover({
        ...variantProps,
        disableAnimation,
        radius: (_a2 = originalProps == null ? void 0 : originalProps.radius) != null ? _a2 : "md",
        size: (_b2 = originalProps == null ? void 0 : originalProps.size) != null ? _b2 : "md",
        shadow: (_c = originalProps == null ? void 0 : originalProps.shadow) != null ? _c : "sm"
      });
    },
    [
      objectToDeps(variantProps),
      disableAnimation,
      originalProps == null ? void 0 : originalProps.radius,
      originalProps == null ? void 0 : originalProps.size,
      originalProps == null ? void 0 : originalProps.shadow
    ]
  );
  const getTriggerProps = (0, import_react11.useCallback)(
    (props2 = {}, _ref = null) => ({
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(triggerProps, props2),
      ref: mergeRefs(_ref, triggerRef),
      "aria-describedby": isOpen ? tooltipId : void 0
    }),
    [triggerProps, isOpen, tooltipId, state]
  );
  const getTooltipProps = (0, import_react11.useCallback)(
    () => ({
      ref: overlayRef,
      "data-slot": "base",
      "data-open": dataAttr(isOpen),
      "data-arrow": dataAttr(showArrow),
      "data-disabled": dataAttr(isDisabled),
      "data-placement": getArrowPlacement(placement || "top", placementProp),
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(tooltipProps, overlayProps, otherProps),
      style: $3ef42575df84b30b$export$9d1611c77c2fe928(positionProps.style, otherProps.style, props.style),
      className: slots.base({ class: classNames == null ? void 0 : classNames.base }),
      id: tooltipId
    }),
    [
      slots,
      isOpen,
      showArrow,
      isDisabled,
      placement,
      placementProp,
      tooltipProps,
      overlayProps,
      otherProps,
      positionProps,
      props,
      tooltipId
    ]
  );
  const getTooltipContentProps = (0, import_react11.useCallback)(
    () => ({
      "data-slot": "content",
      "data-open": dataAttr(isOpen),
      "data-arrow": dataAttr(showArrow),
      "data-disabled": dataAttr(isDisabled),
      "data-placement": getArrowPlacement(placement || "top", placementProp),
      className: slots.content({ class: clsx(classNames == null ? void 0 : classNames.content, className) })
    }),
    [slots, isOpen, showArrow, isDisabled, placement, placementProp, classNames]
  );
  return {
    Component,
    content,
    children,
    isOpen,
    triggerRef,
    showArrow,
    portalContainer,
    placement: placementProp,
    disableAnimation,
    isDisabled,
    motionProps,
    getTooltipContentProps,
    getTriggerProps,
    getTooltipProps
  };
}

// node_modules/@heroui/tooltip/dist/chunk-7ZZXWEXF.mjs
var import_react12 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var domAnimation = () => import("./dist-KSYLHTW5.js").then((res) => res.default);
var Tooltip = forwardRef((props, ref) => {
  var _a;
  const {
    Component,
    children,
    content,
    isOpen,
    portalContainer,
    placement,
    disableAnimation,
    motionProps,
    getTriggerProps,
    getTooltipProps,
    getTooltipContentProps
  } = useTooltip({
    ...props,
    ref
  });
  let trigger;
  try {
    const childrenNum = import_react12.Children.count(children);
    if (childrenNum !== 1)
      throw new Error();
    if (!(0, import_react12.isValidElement)(children)) {
      trigger = (0, import_jsx_runtime.jsx)("p", { ...getTriggerProps(), children });
    } else {
      const child = children;
      const childRef = (_a = child.props.ref) != null ? _a : child.ref;
      trigger = (0, import_react12.cloneElement)(child, getTriggerProps(child.props, childRef));
    }
  } catch {
    trigger = (0, import_jsx_runtime.jsx)("span", {});
    warn("Tooltip must have only one child node. Please, check your code.");
  }
  const { ref: tooltipRef, id, style, ...otherTooltipProps } = getTooltipProps();
  const animatedContent = (0, import_jsx_runtime.jsx)("div", { ref: tooltipRef, id, style, children: (0, import_jsx_runtime.jsx)(
    m.div,
    {
      animate: "enter",
      exit: "exit",
      initial: "exit",
      variants: TRANSITION_VARIANTS.scaleSpring,
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(motionProps, otherTooltipProps),
      style: {
        ...getTransformOrigins(placement)
      },
      children: (0, import_jsx_runtime.jsx)(Component, { ...getTooltipContentProps(), children: content })
    },
    `${id}-tooltip-inner`
  ) }, `${id}-tooltip-content`);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    trigger,
    disableAnimation ? isOpen && (0, import_jsx_runtime.jsx)($f57aed4a881a3485$export$b47c3594eab58386, { portalContainer, children: (0, import_jsx_runtime.jsx)("div", { ref: tooltipRef, id, style, ...otherTooltipProps, children: (0, import_jsx_runtime.jsx)(Component, { ...getTooltipContentProps(), children: content }) }) }) : (0, import_jsx_runtime.jsx)(LazyMotion, { features: domAnimation, children: (0, import_jsx_runtime.jsx)(AnimatePresence, { children: isOpen && (0, import_jsx_runtime.jsx)($f57aed4a881a3485$export$b47c3594eab58386, { portalContainer, children: animatedContent }) }) })
  ] });
});
Tooltip.displayName = "HeroUI.Tooltip";
var tooltip_default = Tooltip;

// node_modules/@heroui/snippet/dist/chunk-VHMYBPCH.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var Snippet = forwardRef((props, ref) => {
  const {
    Component,
    domRef,
    preRef,
    children,
    slots,
    classNames,
    copied,
    copyIcon = (0, import_jsx_runtime2.jsx)(CopyLinearIcon, {}),
    checkIcon = (0, import_jsx_runtime2.jsx)(CheckLinearIcon, {}),
    symbolBefore,
    disableCopy,
    disableTooltip,
    hideSymbol,
    hideCopyButton,
    tooltipProps,
    isMultiLine,
    onCopy,
    getSnippetProps,
    getCopyButtonProps
  } = useSnippet({ ...props, ref });
  const TooltipContent = (0, import_react13.useCallback)(
    ({ children: children2 }) => (0, import_jsx_runtime2.jsx)(tooltip_default, { ...tooltipProps, isDisabled: copied || tooltipProps.isDisabled, children: children2 }),
    [objectToDeps(tooltipProps)]
  );
  const contents = (0, import_react13.useMemo)(() => {
    if (hideCopyButton) {
      return null;
    }
    const clonedCheckIcon = checkIcon && (0, import_react13.cloneElement)(checkIcon, { className: slots.checkIcon() });
    const clonedCopyIcon = copyIcon && (0, import_react13.cloneElement)(copyIcon, { className: slots.copyIcon() });
    const copyButton = (0, import_jsx_runtime2.jsxs)(button_default, { ...getCopyButtonProps(), children: [
      clonedCheckIcon,
      clonedCopyIcon
    ] });
    if (disableTooltip) {
      return copyButton;
    }
    return (0, import_jsx_runtime2.jsx)(TooltipContent, { children: copyButton });
  }, [
    slots,
    classNames == null ? void 0 : classNames.copyButton,
    copied,
    checkIcon,
    copyIcon,
    onCopy,
    TooltipContent,
    disableCopy,
    disableTooltip,
    hideCopyButton
  ]);
  const preContent = (0, import_react13.useMemo)(() => {
    if (isMultiLine && children && Array.isArray(children)) {
      return (0, import_jsx_runtime2.jsx)("div", { className: slots.content({ class: classNames == null ? void 0 : classNames.content }), children: children.map((t, index) => (0, import_jsx_runtime2.jsxs)("pre", { className: slots.pre({ class: classNames == null ? void 0 : classNames.pre }), children: [
        !hideSymbol && (0, import_jsx_runtime2.jsx)("span", { className: slots.symbol({ class: classNames == null ? void 0 : classNames.symbol }), children: symbolBefore }),
        t
      ] }, `${index}-${t}`)) });
    }
    return (0, import_jsx_runtime2.jsxs)("pre", { ref: preRef, className: slots.pre({ class: classNames == null ? void 0 : classNames.pre }), children: [
      !hideSymbol && (0, import_jsx_runtime2.jsx)("span", { className: slots.symbol({ class: classNames == null ? void 0 : classNames.symbol }), children: symbolBefore }),
      children
    ] });
  }, [children, hideSymbol, isMultiLine, symbolBefore, classNames == null ? void 0 : classNames.pre, slots]);
  return (0, import_jsx_runtime2.jsxs)(Component, { ref: domRef, ...getSnippetProps(), children: [
    preContent,
    contents
  ] });
});
Snippet.displayName = "HeroUI.Snippet";
var snippet_default = Snippet;
export {
  snippet_default as Snippet,
  useSnippet
};
//# sourceMappingURL=@heroui_snippet.js.map

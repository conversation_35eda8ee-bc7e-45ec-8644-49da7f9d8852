{"version": 3, "sources": ["../../@heroui/use-clipboard/dist/index.mjs", "../../@heroui/snippet/dist/chunk-UD35SZSW.mjs", "../../@heroui/snippet/dist/chunk-VHMYBPCH.mjs", "../../@heroui/tooltip/dist/chunk-O2IDE4PL.mjs", "../../@react-stately/overlays/dist/packages/@react-stately/overlays/src/useOverlayTriggerState.ts", "../../@react-stately/tooltip/dist/packages/@react-stately/tooltip/src/useTooltipTriggerState.ts", "../../@react-aria/tooltip/dist/packages/@react-aria/tooltip/src/useTooltip.ts", "../../@react-aria/tooltip/dist/packages/@react-aria/tooltip/src/useTooltipTrigger.ts", "../../@react-stately/collections/dist/packages/@react-stately/collections/src/Item.ts", "../../@react-stately/collections/dist/packages/@react-stately/collections/src/Section.ts", "../../@heroui/aria-utils/dist/chunk-WQVQ7P2I.mjs", "../../@heroui/tooltip/dist/chunk-7ZZXWEXF.mjs"], "sourcesContent": ["// src/index.ts\nimport { useCallback, useState } from \"react\";\nvar transformValue = (text) => {\n  return text.replace(/[\\u00A0]/g, \" \");\n};\nfunction useClipboard({ timeout = 2e3 } = {}) {\n  const [error, setError] = useState(null);\n  const [copied, setCopied] = useState(false);\n  const [copyTimeout, setCopyTimeout] = useState(null);\n  const onClearTimeout = useCallback(() => {\n    if (copyTimeout) {\n      clearTimeout(copyTimeout);\n    }\n  }, [copyTimeout]);\n  const handleCopyResult = useCallback(\n    (value) => {\n      onClearTimeout();\n      setCopyTimeout(setTimeout(() => setCopied(false), timeout));\n      setCopied(value);\n    },\n    [onClearTimeout, timeout]\n  );\n  const copy = useCallback(\n    (valueToCopy) => {\n      if (\"clipboard\" in navigator) {\n        const transformedValue = typeof valueToCopy === \"string\" ? transformValue(valueToCopy) : valueToCopy;\n        navigator.clipboard.writeText(transformedValue).then(() => handleCopyResult(true)).catch((err) => setError(err));\n      } else {\n        setError(new Error(\"useClipboard: navigator.clipboard is not supported\"));\n      }\n    },\n    [handleCopyResult]\n  );\n  const reset = useCallback(() => {\n    setCopied(false);\n    setError(null);\n    onClearTimeout();\n  }, [onClearTimeout]);\n  return { copy, reset, error, copied };\n}\nexport {\n  useClipboard\n};\n", "\"use client\";\n\n// src/use-snippet.ts\nimport { snippet } from \"@heroui/theme\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, objectToDeps } from \"@heroui/shared-utils\";\nimport { useClipboard } from \"@heroui/use-clipboard\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { useMemo, useCallback, useRef } from \"react\";\nfunction useSnippet(originalProps) {\n  var _a, _b, _c, _d;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, snippet.variantKeys);\n  const {\n    ref,\n    as,\n    children,\n    symbol = \"$\",\n    classNames,\n    timeout,\n    copyIcon,\n    checkIcon,\n    codeString,\n    disableCopy = false,\n    disableTooltip = false,\n    hideCopyButton = false,\n    autoFocus = false,\n    hideSymbol = false,\n    onCopy: onCopyProp,\n    tooltipProps: userTooltipProps = {},\n    copyButtonProps: userButtonProps = {},\n    className,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const tooltipProps = {\n    offset: 15,\n    delay: 1e3,\n    content: \"Copy to clipboard\",\n    color: (_d = originalProps == null ? void 0 : originalProps.color) != null ? _d : (_c = snippet.defaultVariants) == null ? void 0 : _c.color,\n    isDisabled: props.disableCopy,\n    ...userTooltipProps\n  };\n  const domRef = useDOMRef(ref);\n  const preRef = useRef(null);\n  const { copy, copied } = useClipboard({ timeout });\n  const isMultiLine = children && Array.isArray(children);\n  const { isFocusVisible, isFocused, focusProps } = useFocusRing({\n    autoFocus\n  });\n  const slots = useMemo(\n    () => snippet({\n      ...variantProps,\n      disableAnimation\n    }),\n    [objectToDeps(variantProps), disableAnimation]\n  );\n  const symbolBefore = useMemo(() => {\n    if (!symbol || typeof symbol !== \"string\") return symbol;\n    const str = symbol.trim();\n    return str ? `${str} ` : \"\";\n  }, [symbol]);\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const getSnippetProps = useCallback(\n    () => ({\n      className: slots.base({\n        class: baseStyles\n      }),\n      ...filterDOMProps(otherProps, {\n        enabled: shouldFilterDOMProps\n      })\n    }),\n    [slots, baseStyles, isMultiLine, otherProps]\n  );\n  const onCopy = useCallback(() => {\n    var _a2;\n    if (disableCopy) {\n      return;\n    }\n    let stringValue = \"\";\n    if (typeof children === \"string\") {\n      stringValue = children;\n    } else if (Array.isArray(children)) {\n      children.forEach((child) => {\n        var _a3, _b2;\n        const childString = typeof child === \"string\" ? child : (_b2 = (_a3 = child == null ? void 0 : child.props) == null ? void 0 : _a3.children) == null ? void 0 : _b2.toString();\n        if (childString) {\n          stringValue += childString + \"\\n\";\n        }\n      });\n    }\n    const valueToCopy = codeString || stringValue || ((_a2 = preRef.current) == null ? void 0 : _a2.textContent) || \"\";\n    copy(valueToCopy);\n    onCopyProp == null ? void 0 : onCopyProp(valueToCopy);\n  }, [copy, codeString, disableCopy, onCopyProp, children]);\n  const copyButtonProps = {\n    \"aria-label\": typeof tooltipProps.content === \"string\" ? tooltipProps.content : \"Copy to clipboard\",\n    size: \"sm\",\n    variant: \"light\",\n    isDisabled: disableCopy,\n    onPress: onCopy,\n    isIconOnly: true,\n    ...userButtonProps\n  };\n  const getCopyButtonProps = useCallback(\n    () => ({\n      ...copyButtonProps,\n      \"data-copied\": dataAttr(copied),\n      className: slots.copyButton({\n        class: clsx(classNames == null ? void 0 : classNames.copyButton)\n      })\n    }),\n    [\n      slots,\n      isFocusVisible,\n      isFocused,\n      disableCopy,\n      classNames == null ? void 0 : classNames.copyButton,\n      copyButtonProps,\n      focusProps\n    ]\n  );\n  return {\n    Component,\n    as,\n    domRef,\n    preRef,\n    children,\n    slots,\n    classNames,\n    copied,\n    onCopy,\n    copyIcon,\n    checkIcon,\n    symbolBefore,\n    isMultiLine,\n    isFocusVisible,\n    hideCopyButton,\n    disableCopy,\n    disableTooltip,\n    hideSymbol,\n    tooltipProps,\n    getSnippetProps,\n    getCopyButtonProps\n  };\n}\n\nexport {\n  useSnippet\n};\n", "\"use client\";\nimport {\n  useSnippet\n} from \"./chunk-UD35SZSW.mjs\";\n\n// src/snippet.tsx\nimport { useCallback, useMemo, cloneElement } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { Tooltip } from \"@heroui/tooltip\";\nimport { CopyLinearIcon, CheckLinearIcon } from \"@heroui/shared-icons\";\nimport { Button } from \"@heroui/button\";\nimport { objectToDeps } from \"@heroui/shared-utils\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Snippet = forwardRef((props, ref) => {\n  const {\n    Component,\n    domRef,\n    preRef,\n    children,\n    slots,\n    classNames,\n    copied,\n    copyIcon = /* @__PURE__ */ jsx(CopyLinearIcon, {}),\n    checkIcon = /* @__PURE__ */ jsx(CheckLinearIcon, {}),\n    symbolBefore,\n    disableCopy,\n    disableTooltip,\n    hideSymbol,\n    hideCopyButton,\n    tooltipProps,\n    isMultiLine,\n    onCopy,\n    getSnippetProps,\n    getCopyButtonProps\n  } = useSnippet({ ...props, ref });\n  const TooltipContent = useCallback(\n    ({ children: children2 }) => /* @__PURE__ */ jsx(Tooltip, { ...tooltipProps, isDisabled: copied || tooltipProps.isDisabled, children: children2 }),\n    [objectToDeps(tooltipProps)]\n  );\n  const contents = useMemo(() => {\n    if (hideCopyButton) {\n      return null;\n    }\n    const clonedCheckIcon = checkIcon && cloneElement(checkIcon, { className: slots.checkIcon() });\n    const clonedCopyIcon = copyIcon && cloneElement(copyIcon, { className: slots.copyIcon() });\n    const copyButton = /* @__PURE__ */ jsxs(Button, { ...getCopyButtonProps(), children: [\n      clonedCheckIcon,\n      clonedCopyIcon\n    ] });\n    if (disableTooltip) {\n      return copyButton;\n    }\n    return /* @__PURE__ */ jsx(TooltipContent, { children: copyButton });\n  }, [\n    slots,\n    classNames == null ? void 0 : classNames.copyButton,\n    copied,\n    checkIcon,\n    copyIcon,\n    onCopy,\n    TooltipContent,\n    disableCopy,\n    disableTooltip,\n    hideCopyButton\n  ]);\n  const preContent = useMemo(() => {\n    if (isMultiLine && children && Array.isArray(children)) {\n      return /* @__PURE__ */ jsx(\"div\", { className: slots.content({ class: classNames == null ? void 0 : classNames.content }), children: children.map((t, index) => /* @__PURE__ */ jsxs(\"pre\", { className: slots.pre({ class: classNames == null ? void 0 : classNames.pre }), children: [\n        !hideSymbol && /* @__PURE__ */ jsx(\"span\", { className: slots.symbol({ class: classNames == null ? void 0 : classNames.symbol }), children: symbolBefore }),\n        t\n      ] }, `${index}-${t}`)) });\n    }\n    return /* @__PURE__ */ jsxs(\"pre\", { ref: preRef, className: slots.pre({ class: classNames == null ? void 0 : classNames.pre }), children: [\n      !hideSymbol && /* @__PURE__ */ jsx(\"span\", { className: slots.symbol({ class: classNames == null ? void 0 : classNames.symbol }), children: symbolBefore }),\n      children\n    ] });\n  }, [children, hideSymbol, isMultiLine, symbolBefore, classNames == null ? void 0 : classNames.pre, slots]);\n  return /* @__PURE__ */ jsxs(Component, { ref: domRef, ...getSnippetProps(), children: [\n    preContent,\n    contents\n  ] });\n});\nSnippet.displayName = \"HeroUI.Snippet\";\nvar snippet_default = Snippet;\n\nexport {\n  snippet_default\n};\n", "\"use client\";\n\n// src/use-tooltip.ts\nimport { useId, useImperativeHandle } from \"react\";\nimport { useTooltipTriggerState } from \"@react-stately/tooltip\";\nimport { mergeProps } from \"@react-aria/utils\";\nimport { useTooltip as useReactAriaTooltip, useTooltipTrigger } from \"@react-aria/tooltip\";\nimport { useOverlayPosition, useOverlay } from \"@react-aria/overlays\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { popover } from \"@heroui/theme\";\nimport { clsx, dataAttr, objectToDeps } from \"@heroui/shared-utils\";\nimport { mergeRefs } from \"@heroui/react-utils\";\nimport { createDOMRef } from \"@heroui/react-utils\";\nimport { useMemo, useRef, useCallback } from \"react\";\nimport { toReactAriaPlacement, getArrowPlacement } from \"@heroui/aria-utils\";\nimport { useSafeLayoutEffect } from \"@heroui/use-safe-layout-effect\";\nfunction useTooltip(originalProps) {\n  var _a, _b;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, popover.variantKeys);\n  const {\n    ref,\n    as,\n    isOpen: isOpenProp,\n    content,\n    children,\n    defaultOpen,\n    onOpenChange,\n    isDisabled,\n    trigger: triggerAction,\n    shouldFlip = true,\n    containerPadding = 12,\n    placement: placementProp = \"top\",\n    delay = 0,\n    closeDelay = 500,\n    showArrow = false,\n    offset = 7,\n    crossOffset = 0,\n    isDismissable,\n    shouldCloseOnBlur = true,\n    portalContainer,\n    isKeyboardDismissDisabled = false,\n    updatePositionDeps = [],\n    shouldCloseOnInteractOutside,\n    className,\n    onClose,\n    motionProps,\n    classNames,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const state = useTooltipTriggerState({\n    delay,\n    closeDelay,\n    isDisabled,\n    defaultOpen,\n    isOpen: isOpenProp,\n    onOpenChange: (isOpen2) => {\n      onOpenChange == null ? void 0 : onOpenChange(isOpen2);\n      if (!isOpen2) {\n        onClose == null ? void 0 : onClose();\n      }\n    }\n  });\n  const triggerRef = useRef(null);\n  const overlayRef = useRef(null);\n  const tooltipId = useId();\n  const isOpen = state.isOpen && !isDisabled;\n  useImperativeHandle(\n    ref,\n    () => (\n      // @ts-ignore\n      createDOMRef(overlayRef)\n    )\n  );\n  const { triggerProps, tooltipProps: triggerTooltipProps } = useTooltipTrigger(\n    {\n      isDisabled,\n      trigger: triggerAction\n    },\n    state,\n    triggerRef\n  );\n  const { tooltipProps } = useReactAriaTooltip(\n    {\n      isOpen,\n      ...mergeProps(props, triggerTooltipProps)\n    },\n    state\n  );\n  const {\n    overlayProps: positionProps,\n    placement,\n    updatePosition\n  } = useOverlayPosition({\n    isOpen,\n    targetRef: triggerRef,\n    placement: toReactAriaPlacement(placementProp),\n    overlayRef,\n    offset: showArrow ? offset + 3 : offset,\n    crossOffset,\n    shouldFlip,\n    containerPadding\n  });\n  useSafeLayoutEffect(() => {\n    if (!updatePositionDeps.length) return;\n    updatePosition();\n  }, updatePositionDeps);\n  const { overlayProps } = useOverlay(\n    {\n      isOpen,\n      onClose: state.close,\n      isDismissable,\n      shouldCloseOnBlur,\n      isKeyboardDismissDisabled,\n      shouldCloseOnInteractOutside\n    },\n    overlayRef\n  );\n  const slots = useMemo(\n    () => {\n      var _a2, _b2, _c;\n      return popover({\n        ...variantProps,\n        disableAnimation,\n        radius: (_a2 = originalProps == null ? void 0 : originalProps.radius) != null ? _a2 : \"md\",\n        size: (_b2 = originalProps == null ? void 0 : originalProps.size) != null ? _b2 : \"md\",\n        shadow: (_c = originalProps == null ? void 0 : originalProps.shadow) != null ? _c : \"sm\"\n      });\n    },\n    [\n      objectToDeps(variantProps),\n      disableAnimation,\n      originalProps == null ? void 0 : originalProps.radius,\n      originalProps == null ? void 0 : originalProps.size,\n      originalProps == null ? void 0 : originalProps.shadow\n    ]\n  );\n  const getTriggerProps = useCallback(\n    (props2 = {}, _ref = null) => ({\n      ...mergeProps(triggerProps, props2),\n      ref: mergeRefs(_ref, triggerRef),\n      \"aria-describedby\": isOpen ? tooltipId : void 0\n    }),\n    [triggerProps, isOpen, tooltipId, state]\n  );\n  const getTooltipProps = useCallback(\n    () => ({\n      ref: overlayRef,\n      \"data-slot\": \"base\",\n      \"data-open\": dataAttr(isOpen),\n      \"data-arrow\": dataAttr(showArrow),\n      \"data-disabled\": dataAttr(isDisabled),\n      \"data-placement\": getArrowPlacement(placement || \"top\", placementProp),\n      ...mergeProps(tooltipProps, overlayProps, otherProps),\n      style: mergeProps(positionProps.style, otherProps.style, props.style),\n      className: slots.base({ class: classNames == null ? void 0 : classNames.base }),\n      id: tooltipId\n    }),\n    [\n      slots,\n      isOpen,\n      showArrow,\n      isDisabled,\n      placement,\n      placementProp,\n      tooltipProps,\n      overlayProps,\n      otherProps,\n      positionProps,\n      props,\n      tooltipId\n    ]\n  );\n  const getTooltipContentProps = useCallback(\n    () => ({\n      \"data-slot\": \"content\",\n      \"data-open\": dataAttr(isOpen),\n      \"data-arrow\": dataAttr(showArrow),\n      \"data-disabled\": dataAttr(isDisabled),\n      \"data-placement\": getArrowPlacement(placement || \"top\", placementProp),\n      className: slots.content({ class: clsx(classNames == null ? void 0 : classNames.content, className) })\n    }),\n    [slots, isOpen, showArrow, isDisabled, placement, placementProp, classNames]\n  );\n  return {\n    Component,\n    content,\n    children,\n    isOpen,\n    triggerRef,\n    showArrow,\n    portalContainer,\n    placement: placementProp,\n    disableAnimation,\n    isDisabled,\n    motionProps,\n    getTooltipContentProps,\n    getTriggerProps,\n    getTooltipProps\n  };\n}\n\nexport {\n  useTooltip\n};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {OverlayTriggerProps} from '@react-types/overlays';\nimport {useCallback} from 'react';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface OverlayTriggerState {\n  /** Whether the overlay is currently open. */\n  readonly isOpen: boolean,\n  /** Sets whether the overlay is open. */\n  setOpen(isOpen: boolean): void,\n  /** Opens the overlay. */\n  open(): void,\n  /** Closes the overlay. */\n  close(): void,\n  /** Toggles the overlay's visibility. */\n  toggle(): void\n}\n\n/**\n * Manages state for an overlay trigger. Tracks whether the overlay is open, and provides\n * methods to toggle this state.\n */\nexport function useOverlayTriggerState(props: OverlayTriggerProps): OverlayTriggerState  {\n  let [isOpen, setOpen] = useControlledState(props.isOpen, props.defaultOpen || false, props.onOpenChange);\n\n  const open = useCallback(() => {\n    setOpen(true);\n  }, [setOpen]);\n\n  const close = useCallback(() => {\n    setOpen(false);\n  }, [setOpen]);\n\n  const toggle = useCallback(() => {\n    setOpen(!isOpen);\n  }, [setOpen, isOpen]);\n\n  return {\n    isOpen,\n    setOpen,\n    open,\n    close,\n    toggle\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {TooltipTriggerProps} from '@react-types/tooltip';\nimport {useEffect, useMemo, useRef} from 'react';\nimport {useOverlayTriggerState} from '@react-stately/overlays';\n\nconst TOOLTIP_DELAY = 1500; // this seems to be a 1.5 second delay, check with design\nconst TOOLTIP_COOLDOWN = 500;\n\nexport interface TooltipTriggerState {\n  /** Whether the tooltip is currently showing. */\n  isOpen: boolean,\n  /**\n   * Shows the tooltip. By default, the tooltip becomes visible after a delay\n   * depending on a global warmup timer. The `immediate` option shows the\n   * tooltip immediately instead.\n   */\n  open(immediate?: boolean): void,\n  /** Hides the tooltip. */\n  close(immediate?: boolean): void\n}\n\nlet tooltips = {};\nlet tooltipId = 0;\nlet globalWarmedUp = false;\nlet globalWarmUpTimeout: ReturnType<typeof setTimeout> | null = null;\nlet globalCooldownTimeout: ReturnType<typeof setTimeout> | null = null;\n\n/**\n * Manages state for a tooltip trigger. Tracks whether the tooltip is open, and provides\n * methods to toggle this state. Ensures only one tooltip is open at a time and controls\n * the delay for showing a tooltip.\n */\nexport function useTooltipTriggerState(props: TooltipTriggerProps = {}): TooltipTriggerState {\n  let {delay = TOOLTIP_DELAY, closeDelay = TOOLTIP_COOLDOWN} = props;\n  let {isOpen, open, close} = useOverlayTriggerState(props);\n  let id = useMemo(() => `${++tooltipId}`, []);\n  let closeTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);\n  let closeCallback = useRef<() => void>(close);\n\n  let ensureTooltipEntry = () => {\n    tooltips[id] = hideTooltip;\n  };\n\n  let closeOpenTooltips = () => {\n    for (let hideTooltipId in tooltips) {\n      if (hideTooltipId !== id) {\n        tooltips[hideTooltipId](true);\n        delete tooltips[hideTooltipId];\n      }\n    }\n  };\n\n  let showTooltip = () => {\n    if (closeTimeout.current) {\n      clearTimeout(closeTimeout.current);\n    }\n    closeTimeout.current = null;\n    closeOpenTooltips();\n    ensureTooltipEntry();\n    globalWarmedUp = true;\n    open();\n    if (globalWarmUpTimeout) {\n      clearTimeout(globalWarmUpTimeout);\n      globalWarmUpTimeout = null;\n    }\n    if (globalCooldownTimeout) {\n      clearTimeout(globalCooldownTimeout);\n      globalCooldownTimeout = null;\n    }\n  };\n\n  let hideTooltip = (immediate?: boolean) => {\n    if (immediate || closeDelay <= 0) {\n      if (closeTimeout.current) {\n        clearTimeout(closeTimeout.current);\n      }\n      closeTimeout.current = null;\n      closeCallback.current();\n    } else if (!closeTimeout.current) {\n      closeTimeout.current = setTimeout(() => {\n        closeTimeout.current = null;\n        closeCallback.current();\n      }, closeDelay);\n    }\n\n    if (globalWarmUpTimeout) {\n      clearTimeout(globalWarmUpTimeout);\n      globalWarmUpTimeout = null;\n    }\n    if (globalWarmedUp) {\n      if (globalCooldownTimeout) {\n        clearTimeout(globalCooldownTimeout);\n      }\n      globalCooldownTimeout = setTimeout(() => {\n        delete tooltips[id];\n        globalCooldownTimeout = null;\n        globalWarmedUp = false;\n      }, Math.max(TOOLTIP_COOLDOWN, closeDelay));\n    }\n  };\n\n  let warmupTooltip = () => {\n    closeOpenTooltips();\n    ensureTooltipEntry();\n    if (!isOpen && !globalWarmUpTimeout && !globalWarmedUp) {\n      globalWarmUpTimeout = setTimeout(() => {\n        globalWarmUpTimeout = null;\n        globalWarmedUp = true;\n        showTooltip();\n      }, delay);\n    } else if (!isOpen) {\n      showTooltip();\n    }\n  };\n\n  useEffect(() => {\n    closeCallback.current = close;\n  }, [close]);\n\n\n  useEffect(() => {\n    return () => {\n      if (closeTimeout.current) {\n        clearTimeout(closeTimeout.current);\n      }\n      let tooltip = tooltips[id];\n      if (tooltip) {\n        delete tooltips[id];\n      }\n    };\n  }, [id]);\n\n  return {\n    isOpen,\n    open: (immediate) => {\n      if (!immediate && delay > 0 && !closeTimeout.current) {\n        warmupTooltip();\n      } else {\n        showTooltip();\n      }\n    },\n    close: hideTooltip\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTooltipProps} from '@react-types/tooltip';\nimport {DOMAttributes} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {TooltipTriggerState} from '@react-stately/tooltip';\nimport {useHover} from '@react-aria/interactions';\n\nexport interface TooltipAria {\n  /**\n   * Props for the tooltip element.\n   */\n  tooltipProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a Tooltip component.\n */\nexport function useTooltip(props: AriaTooltipProps, state?: TooltipTriggerState): TooltipAria {\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  let {hoverProps} = useHover({\n    onHoverStart: () => state?.open(true),\n    onHoverEnd: () => state?.close()\n  });\n\n  return {\n    tooltipProps: mergeProps(domProps, hoverProps, {\n      role: 'tooltip'\n    })\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getInteractionModality, isFocusVisible, useFocusable, useHover} from '@react-aria/interactions';\nimport {mergeProps, useId} from '@react-aria/utils';\nimport {TooltipTriggerProps} from '@react-types/tooltip';\nimport {TooltipTriggerState} from '@react-stately/tooltip';\nimport {useEffect, useRef} from 'react';\n\nexport interface TooltipTriggerAria {\n  /**\n   * Props for the trigger element.\n   */\n  triggerProps: DOMAttributes,\n\n  /**\n   * Props for the overlay container element.\n   */\n  tooltipProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a tooltip trigger, e.g. a button\n * that shows a description when focused or hovered.\n */\nexport function useTooltipTrigger(props: TooltipTriggerProps, state: TooltipTriggerState, ref: RefObject<FocusableElement | null>) : TooltipTriggerAria {\n  let {\n    isDisabled,\n    trigger\n  } = props;\n\n  let tooltipId = useId();\n\n  let isHovered = useRef(false);\n  let isFocused = useRef(false);\n\n  let handleShow = () => {\n    if (isHovered.current || isFocused.current) {\n      state.open(isFocused.current);\n    }\n  };\n\n  let handleHide = (immediate?: boolean) => {\n    if (!isHovered.current && !isFocused.current) {\n      state.close(immediate);\n    }\n  };\n\n  useEffect(() => {\n    let onKeyDown = (e) => {\n      if (ref && ref.current) {\n        // Escape after clicking something can give it keyboard focus\n        // dismiss tooltip on esc key press\n        if (e.key === 'Escape') {\n          e.stopPropagation();\n          state.close(true);\n        }\n      }\n    };\n    if (state.isOpen) {\n      document.addEventListener('keydown', onKeyDown, true);\n      return () => {\n        document.removeEventListener('keydown', onKeyDown, true);\n      };\n    }\n  }, [ref, state]);\n\n  let onHoverStart = () => {\n    if (trigger === 'focus') {\n      return;\n    }\n    // In chrome, if you hover a trigger, then another element obscures it, due to keyboard\n    // interactions for example, hover will end. When hover is restored after that element disappears,\n    // focus moves on for example, then the tooltip will reopen. We check the modality to know if the hover\n    // is the result of moving the mouse.\n    if (getInteractionModality() === 'pointer') {\n      isHovered.current = true;\n    } else {\n      isHovered.current = false;\n    }\n    handleShow();\n  };\n\n  let onHoverEnd = () => {\n    if (trigger === 'focus') {\n      return;\n    }\n    // no matter how the trigger is left, we should close the tooltip\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide();\n  };\n\n  let onPressStart = () => {\n    // no matter how the trigger is pressed, we should close the tooltip\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide(true);\n  };\n\n  let onFocus = () => {\n    let isVisible = isFocusVisible();\n    if (isVisible) {\n      isFocused.current = true;\n      handleShow();\n    }\n  };\n\n  let onBlur = () => {\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide(true);\n  };\n\n  let {hoverProps} = useHover({\n    isDisabled,\n    onHoverStart,\n    onHoverEnd\n  });\n\n  let {focusableProps} = useFocusable({\n    isDisabled,\n    onFocus,\n    onBlur\n  }, ref);\n\n  return {\n    triggerProps: {\n      'aria-describedby': state.isOpen ? tooltipId : undefined,\n      ...mergeProps(focusableProps, hoverProps, {\n        onPointerDown: onPressStart,\n        onKeyDown: onPressStart,\n        tabIndex: undefined\n      })\n    },\n    tooltipProps: {\n      id: tooltipId\n    }\n  };\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ItemElement, ItemProps} from '@react-types/shared';\nimport {PartialNode} from './types';\nimport React, {JSX, ReactElement} from 'react';\n\nfunction Item<T>(props: ItemProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nItem.getCollectionNode = function* getCollectionNode<T>(props: ItemProps<T>, context: any): Generator<PartialNode<T>> {\n  let {childItems, title, children} = props;\n\n  let rendered = props.title || props.children;\n  let textValue = props.textValue || (typeof rendered === 'string' ? rendered : '') || props['aria-label'] || '';\n\n  // suppressTextValueWarning is used in components like Tabs, which don't have type to select support.\n  if (!textValue && !context?.suppressTextValueWarning && process.env.NODE_ENV !== 'production') {\n    console.warn('<Item> with non-plain text contents is unsupported by type to select for accessibility. Please add a `textValue` prop.');\n  }\n\n  yield {\n    type: 'item',\n    props: props,\n    rendered,\n    textValue,\n    'aria-label': props['aria-label'],\n    hasChildNodes: hasChildItems(props),\n    *childNodes() {\n      if (childItems) {\n        for (let child of childItems) {\n          yield {\n            type: 'item',\n            value: child\n          };\n        }\n      } else if (title) {\n        let items: PartialNode<T>[] = [];\n        React.Children.forEach(children, child => {\n          items.push({\n            type: 'item',\n            element: child as ItemElement<T>\n          });\n        });\n\n        yield* items;\n      }\n    }\n  };\n};\n\nfunction hasChildItems<T>(props: ItemProps<T>) {\n  if (props.hasChildItems != null) {\n    return props.hasChildItems;\n  }\n\n  if (props.childItems) {\n    return true;\n  }\n\n  if (props.title && React.Children.count(props.children) > 0) {\n    return true;\n  }\n\n  return false;\n}\n\n// We don't want getCollectionNode to show up in the type definition\nlet _Item = Item as <T>(props: ItemProps<T>) => JSX.Element;\nexport {_Item as Item};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {PartialNode} from './types';\nimport React, {JSX, ReactElement} from 'react';\nimport {SectionProps} from '@react-types/shared';\n\nfunction Section<T>(props: SectionProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nSection.getCollectionNode = function* getCollectionNode<T>(props: SectionProps<T>): Generator<PartialNode<T>> {\n  let {children, title, items} = props;\n  yield {\n    type: 'section',\n    props: props,\n    hasChildNodes: true,\n    rendered: title,\n    'aria-label': props['aria-label'],\n    *childNodes() {\n      if (typeof children === 'function') {\n        if (!items) {\n          throw new Error('props.children was a function but props.items is missing');\n        }\n\n        for (let item of items) {\n          yield {\n            type: 'item',\n            value: item,\n            renderer: children\n          };\n        }\n      } else {\n        let items: PartialNode<T>[] = [];\n        React.Children.forEach(children, child => {\n          items.push({\n            type: 'item',\n            element: child\n          });\n        });\n\n        yield* items;\n      }\n    }\n  };\n};\n\n// We don't want getCollectionNode to show up in the type definition\nlet _Section = Section as <T>(props: SectionProps<T>) => JSX.Element;\nexport {_Section as Section};\n", "\"use client\";\n\n// src/overlays/utils.ts\nvar getTransformOrigins = (placement) => {\n  const origins = {\n    top: {\n      originY: 1\n    },\n    bottom: {\n      originY: 0\n    },\n    left: {\n      originX: 1\n    },\n    right: {\n      originX: 0\n    },\n    \"top-start\": {\n      originX: 0,\n      originY: 1\n    },\n    \"top-end\": {\n      originX: 1,\n      originY: 1\n    },\n    \"bottom-start\": {\n      originX: 0,\n      originY: 0\n    },\n    \"bottom-end\": {\n      originX: 1,\n      originY: 0\n    },\n    \"right-start\": {\n      originX: 0,\n      originY: 0\n    },\n    \"right-end\": {\n      originX: 0,\n      originY: 1\n    },\n    \"left-start\": {\n      originX: 1,\n      originY: 0\n    },\n    \"left-end\": {\n      originX: 1,\n      originY: 1\n    }\n  };\n  return (origins == null ? void 0 : origins[placement]) || {};\n};\nvar toReactAriaPlacement = (placement) => {\n  const mapPositions = {\n    top: \"top\",\n    bottom: \"bottom\",\n    left: \"left\",\n    right: \"right\",\n    \"top-start\": \"top start\",\n    \"top-end\": \"top end\",\n    \"bottom-start\": \"bottom start\",\n    \"bottom-end\": \"bottom end\",\n    \"left-start\": \"left top\",\n    \"left-end\": \"left bottom\",\n    \"right-start\": \"right top\",\n    \"right-end\": \"right bottom\"\n  };\n  return mapPositions[placement];\n};\nvar toOverlayPlacement = (placement) => {\n  const mapPositions = {\n    top: \"top\",\n    bottom: \"bottom\",\n    left: \"left\",\n    right: \"right\",\n    center: \"top\"\n  };\n  return mapPositions[placement];\n};\nvar getShouldUseAxisPlacement = (axisPlacement, overlayPlacement) => {\n  if (overlayPlacement.includes(\"-\")) {\n    const [position] = overlayPlacement.split(\"-\");\n    if (position.includes(axisPlacement)) {\n      return false;\n    }\n  }\n  return true;\n};\nvar getArrowPlacement = (dynamicPlacement, placement) => {\n  if (placement.includes(\"-\")) {\n    const [, position] = placement.split(\"-\");\n    return `${dynamicPlacement}-${position}`;\n  }\n  return dynamicPlacement;\n};\n\nexport {\n  getTransformOrigins,\n  toReactAriaPlacement,\n  toOverlayPlacement,\n  getShouldUseAxisPlacement,\n  getArrowPlacement\n};\n", "\"use client\";\nimport {\n  useTooltip\n} from \"./chunk-O2IDE4PL.mjs\";\n\n// src/tooltip.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { OverlayContainer } from \"@react-aria/overlays\";\nimport { AnimatePresence, m, LazyMotion } from \"framer-motion\";\nimport { TRANSITION_VARIANTS } from \"@heroui/framer-utils\";\nimport { warn } from \"@heroui/shared-utils\";\nimport { Children, cloneElement, isValidElement } from \"react\";\nimport { getTransformOrigins } from \"@heroui/aria-utils\";\nimport { mergeProps } from \"@react-aria/utils\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar domAnimation = () => import(\"@heroui/dom-animation\").then((res) => res.default);\nvar Tooltip = forwardRef((props, ref) => {\n  var _a;\n  const {\n    Component,\n    children,\n    content,\n    isOpen,\n    portalContainer,\n    placement,\n    disableAnimation,\n    motionProps,\n    getTriggerProps,\n    getTooltipProps,\n    getTooltipContentProps\n  } = useTooltip({\n    ...props,\n    ref\n  });\n  let trigger;\n  try {\n    const childrenNum = Children.count(children);\n    if (childrenNum !== 1) throw new Error();\n    if (!isValidElement(children)) {\n      trigger = /* @__PURE__ */ jsx(\"p\", { ...getTriggerProps(), children });\n    } else {\n      const child = children;\n      const childRef = (_a = child.props.ref) != null ? _a : child.ref;\n      trigger = cloneElement(child, getTriggerProps(child.props, childRef));\n    }\n  } catch {\n    trigger = /* @__PURE__ */ jsx(\"span\", {});\n    warn(\"Tooltip must have only one child node. Please, check your code.\");\n  }\n  const { ref: tooltipRef, id, style, ...otherTooltipProps } = getTooltipProps();\n  const animatedContent = /* @__PURE__ */ jsx(\"div\", { ref: tooltipRef, id, style, children: /* @__PURE__ */ jsx(\n    m.div,\n    {\n      animate: \"enter\",\n      exit: \"exit\",\n      initial: \"exit\",\n      variants: TRANSITION_VARIANTS.scaleSpring,\n      ...mergeProps(motionProps, otherTooltipProps),\n      style: {\n        ...getTransformOrigins(placement)\n      },\n      children: /* @__PURE__ */ jsx(Component, { ...getTooltipContentProps(), children: content })\n    },\n    `${id}-tooltip-inner`\n  ) }, `${id}-tooltip-content`);\n  return /* @__PURE__ */ jsxs(Fragment, { children: [\n    trigger,\n    disableAnimation ? isOpen && /* @__PURE__ */ jsx(OverlayContainer, { portalContainer, children: /* @__PURE__ */ jsx(\"div\", { ref: tooltipRef, id, style, ...otherTooltipProps, children: /* @__PURE__ */ jsx(Component, { ...getTooltipContentProps(), children: content }) }) }) : /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(AnimatePresence, { children: isOpen && /* @__PURE__ */ jsx(OverlayContainer, { portalContainer, children: animatedContent }) }) })\n  ] });\n});\nTooltip.displayName = \"HeroUI.Tooltip\";\nvar tooltip_default = Tooltip;\n\nexport {\n  tooltip_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,mBAAsC;AACtC,IAAI,iBAAiB,CAAC,SAAS;AAC7B,SAAO,KAAK,QAAQ,aAAa,GAAG;AACtC;AACA,SAAS,aAAa,EAAE,UAAU,IAAI,IAAI,CAAC,GAAG;AAC5C,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,IAAI;AACvC,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAAS,KAAK;AAC1C,QAAM,CAAC,aAAa,cAAc,QAAI,uBAAS,IAAI;AACnD,QAAM,qBAAiB,0BAAY,MAAM;AACvC,QAAI,aAAa;AACf,mBAAa,WAAW;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,uBAAmB;AAAA,IACvB,CAAC,UAAU;AACT,qBAAe;AACf,qBAAe,WAAW,MAAM,UAAU,KAAK,GAAG,OAAO,CAAC;AAC1D,gBAAU,KAAK;AAAA,IACjB;AAAA,IACA,CAAC,gBAAgB,OAAO;AAAA,EAC1B;AACA,QAAM,WAAO;AAAA,IACX,CAAC,gBAAgB;AACf,UAAI,eAAe,WAAW;AAC5B,cAAM,mBAAmB,OAAO,gBAAgB,WAAW,eAAe,WAAW,IAAI;AACzF,kBAAU,UAAU,UAAU,gBAAgB,EAAE,KAAK,MAAM,iBAAiB,IAAI,CAAC,EAAE,MAAM,CAAC,QAAQ,SAAS,GAAG,CAAC;AAAA,MACjH,OAAO;AACL,iBAAS,IAAI,MAAM,oDAAoD,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,CAAC,gBAAgB;AAAA,EACnB;AACA,QAAM,YAAQ,0BAAY,MAAM;AAC9B,cAAU,KAAK;AACf,aAAS,IAAI;AACb,mBAAe;AAAA,EACjB,GAAG,CAAC,cAAc,CAAC;AACnB,SAAO,EAAE,MAAM,OAAO,OAAO,OAAO;AACtC;;;AC9BA,IAAAA,gBAA6C;AAC7C,SAAS,WAAW,eAAe;AACjC,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,QAAQ,WAAW;AACjF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,cAAc,mBAAmB,CAAC;AAAA,IAClC,iBAAiB,kBAAkB,CAAC;AAAA,IACpC;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAY,MAAM;AACxB,QAAM,uBAAuB,OAAO,cAAc;AAClD,QAAM,oBAAoB,MAAM,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK;AACrM,QAAM,eAAe;AAAA,IACnB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ,KAAK,iBAAiB,OAAO,SAAS,cAAc,UAAU,OAAO,MAAM,KAAK,QAAQ,oBAAoB,OAAO,SAAS,GAAG;AAAA,IACvI,YAAY,MAAM;AAAA,IAClB,GAAG;AAAA,EACL;AACA,QAAM,SAAS,UAAU,GAAG;AAC5B,QAAM,aAAS,sBAAO,IAAI;AAC1B,QAAM,EAAE,MAAM,OAAO,IAAI,aAAa,EAAE,QAAQ,CAAC;AACjD,QAAM,cAAc,YAAY,MAAM,QAAQ,QAAQ;AACtD,QAAM,EAAE,gBAAgB,WAAW,WAAW,IAAI,0CAAa;AAAA,IAC7D;AAAA,EACF,CAAC;AACD,QAAM,YAAQ;AAAA,IACZ,MAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,IACD,CAAC,aAAa,YAAY,GAAG,gBAAgB;AAAA,EAC/C;AACA,QAAM,mBAAe,uBAAQ,MAAM;AACjC,QAAI,CAAC,UAAU,OAAO,WAAW;AAAU,aAAO;AAClD,UAAM,MAAM,OAAO,KAAK;AACxB,WAAO,MAAM,GAAG,GAAG,MAAM;AAAA,EAC3B,GAAG,CAAC,MAAM,CAAC;AACX,QAAM,aAAa,KAAK,cAAc,OAAO,SAAS,WAAW,MAAM,SAAS;AAChF,QAAM,sBAAkB;AAAA,IACtB,OAAO;AAAA,MACL,WAAW,MAAM,KAAK;AAAA,QACpB,OAAO;AAAA,MACT,CAAC;AAAA,MACD,GAAG,eAAe,YAAY;AAAA,QAC5B,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,IACA,CAAC,OAAO,YAAY,aAAa,UAAU;AAAA,EAC7C;AACA,QAAM,aAAS,2BAAY,MAAM;AAC/B,QAAI;AACJ,QAAI,aAAa;AACf;AAAA,IACF;AACA,QAAI,cAAc;AAClB,QAAI,OAAO,aAAa,UAAU;AAChC,oBAAc;AAAA,IAChB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,eAAS,QAAQ,CAAC,UAAU;AAC1B,YAAI,KAAK;AACT,cAAM,cAAc,OAAO,UAAU,WAAW,SAAS,OAAO,MAAM,SAAS,OAAO,SAAS,MAAM,UAAU,OAAO,SAAS,IAAI,aAAa,OAAO,SAAS,IAAI,SAAS;AAC7K,YAAI,aAAa;AACf,yBAAe,cAAc;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,cAAc,cAAc,iBAAiB,MAAM,OAAO,YAAY,OAAO,SAAS,IAAI,gBAAgB;AAChH,SAAK,WAAW;AAChB,kBAAc,OAAO,SAAS,WAAW,WAAW;AAAA,EACtD,GAAG,CAAC,MAAM,YAAY,aAAa,YAAY,QAAQ,CAAC;AACxD,QAAM,kBAAkB;AAAA,IACtB,cAAc,OAAO,aAAa,YAAY,WAAW,aAAa,UAAU;AAAA,IAChF,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,GAAG;AAAA,EACL;AACA,QAAM,yBAAqB;AAAA,IACzB,OAAO;AAAA,MACL,GAAG;AAAA,MACH,eAAe,SAAS,MAAM;AAAA,MAC9B,WAAW,MAAM,WAAW;AAAA,QAC1B,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,UAAU;AAAA,MACjE,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO,SAAS,WAAW;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC9IA,IAAAC,iBAAmD;;;ACHnD,IAAAC,iBAA2C;;;;;;;AC8BpC,SAAS,0CAAuB,OAA0B;AAC/D,MAAI,CAAC,QAAQ,OAAA,KAAW,GAAA,2CAAmB,MAAM,QAAQ,MAAM,eAAe,OAAO,MAAM,YAAY;AAEvG,QAAM,QAAO,GAAA,cAAAC,aAAY,MAAA;AACvB,YAAQ,IAAA;EACV,GAAG;IAAC;GAAQ;AAEZ,QAAM,SAAQ,GAAA,cAAAA,aAAY,MAAA;AACxB,YAAQ,KAAA;EACV,GAAG;IAAC;GAAQ;AAEZ,QAAM,UAAS,GAAA,cAAAA,aAAY,MAAA;AACzB,YAAQ,CAAC,MAAA;EACX,GAAG;IAAC;IAAS;GAAO;AAEpB,SAAO;;;;;;EAMP;AACF;;;ACvCA,IAAM,sCAAgB;AACtB,IAAM,yCAAmB;AAezB,IAAI,iCAAW,CAAC;AAChB,IAAI,kCAAY;AAChB,IAAI,uCAAiB;AACrB,IAAI,4CAA4D;AAChE,IAAI,8CAA8D;AAO3D,SAAS,0CAAuB,QAA6B,CAAC,GAAC;AACpE,MAAI,EAAA,QAAS,qCAAA,aAA4B,uCAAA,IAAoB;AAC7D,MAAI,EAAA,QAAO,MAAM,MAAO,KAAI,GAAA,2CAAuB,KAAA;AACnD,MAAI,MAAK,GAAA,cAAAC,SAAQ,MAAM,GAAG,EAAE,+BAAA,IAAa,CAAA,CAAE;AAC3C,MAAI,gBAAe,GAAA,cAAAC,QAA6C,IAAA;AAChE,MAAI,iBAAgB,GAAA,cAAAA,QAAmB,KAAA;AAEvC,MAAI,qBAAqB,MAAA;AACvB,mCAAS,EAAA,IAAM;EACjB;AAEA,MAAI,oBAAoB,MAAA;AACtB,aAAS,iBAAiB;AACxB,UAAI,kBAAkB,IAAI;AACxB,uCAAS,aAAA,EAAe,IAAA;AACxB,eAAO,+BAAS,aAAA;MAClB;EAEJ;AAEA,MAAI,cAAc,MAAA;AAChB,QAAI,aAAa;AACf,mBAAa,aAAa,OAAO;AAEnC,iBAAa,UAAU;AACvB,sBAAA;AACA,uBAAA;AACA,2CAAiB;AACjB,SAAA;AACA,QAAI,2CAAqB;AACvB,mBAAa,yCAAA;AACb,kDAAsB;IACxB;AACA,QAAI,6CAAuB;AACzB,mBAAa,2CAAA;AACb,oDAAwB;IAC1B;EACF;AAEA,MAAI,cAAc,CAAC,cAAA;AACjB,QAAI,aAAa,cAAc,GAAG;AAChC,UAAI,aAAa;AACf,qBAAa,aAAa,OAAO;AAEnC,mBAAa,UAAU;AACvB,oBAAc,QAAO;IACvB,WAAW,CAAC,aAAa;AACvB,mBAAa,UAAU,WAAW,MAAA;AAChC,qBAAa,UAAU;AACvB,sBAAc,QAAO;MACvB,GAAG,UAAA;AAGL,QAAI,2CAAqB;AACvB,mBAAa,yCAAA;AACb,kDAAsB;IACxB;AACA,QAAI,sCAAgB;AAClB,UAAI;AACF,qBAAa,2CAAA;AAEf,oDAAwB,WAAW,MAAA;AACjC,eAAO,+BAAS,EAAA;AAChB,sDAAwB;AACxB,+CAAiB;MACnB,GAAG,KAAK,IAAI,wCAAkB,UAAA,CAAA;IAChC;EACF;AAEA,MAAI,gBAAgB,MAAA;AAClB,sBAAA;AACA,uBAAA;AACA,QAAI,CAAC,UAAU,CAAC,6CAAuB,CAAC;AACtC,kDAAsB,WAAW,MAAA;AAC/B,oDAAsB;AACtB,+CAAiB;AACjB,oBAAA;MACF,GAAG,KAAA;aACM,CAAC;AACV,kBAAA;EAEJ;AAEA,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,kBAAc,UAAU;EAC1B,GAAG;IAAC;GAAM;AAGV,GAAA,GAAA,cAAAA,WAAU,MAAA;AACR,WAAO,MAAA;AACL,UAAI,aAAa;AACf,qBAAa,aAAa,OAAO;AAEnC,UAAI,UAAU,+BAAS,EAAA;AACvB,UAAI;AACF,eAAO,+BAAS,EAAA;IAEpB;EACF,GAAG;IAAC;GAAG;AAEP,SAAO;;IAEL,MAAM,CAAC,cAAA;AACL,UAAI,CAAC,aAAa,QAAQ,KAAK,CAAC,aAAa;AAC3C,sBAAA;;AAEA,oBAAA;IAEJ;IACA,OAAO;EACT;AACF;;;AC9HO,SAAS,0CAAW,OAAyB,OAA2B;AAC7E,MAAI,YAAW,GAAA,2CAAe,OAAO;IAAC,WAAW;EAAI,CAAA;AAErD,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;IAC1B,cAAc,MAAM,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,KAAK,IAAA;IAChC,YAAY,MAAM,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,MAAK;EAChC,CAAA;AAEA,SAAO;IACL,eAAc,GAAA,2CAAW,UAAU,YAAY;MAC7C,MAAM;IACR,CAAA;EACF;AACF;;;;ACNO,SAAS,0CAAkB,OAA4B,OAA4B,KAAuC;AAC/H,MAAI,EAAA,YACQ,QACH,IACL;AAEJ,MAAI,aAAY,GAAA,2CAAI;AAEpB,MAAI,aAAY,GAAA,cAAAC,QAAO,KAAA;AACvB,MAAI,aAAY,GAAA,cAAAA,QAAO,KAAA;AAEvB,MAAI,aAAa,MAAA;AACf,QAAI,UAAU,WAAW,UAAU;AACjC,YAAM,KAAK,UAAU,OAAO;EAEhC;AAEA,MAAI,aAAa,CAAC,cAAA;AAChB,QAAI,CAAC,UAAU,WAAW,CAAC,UAAU;AACnC,YAAM,MAAM,SAAA;EAEhB;AAEA,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,QAAI,YAAY,CAAC,MAAA;AACf,UAAI,OAAO,IAAI,SAGb;AAAA,YAAI,EAAE,QAAQ,UAAU;AACtB,YAAE,gBAAe;AACjB,gBAAM,MAAM,IAAA;QACd;MAAA;IAEJ;AACA,QAAI,MAAM,QAAQ;AAChB,eAAS,iBAAiB,WAAW,WAAW,IAAA;AAChD,aAAO,MAAA;AACL,iBAAS,oBAAoB,WAAW,WAAW,IAAA;MACrD;IACF;EACF,GAAG;IAAC;IAAK;GAAM;AAEf,MAAI,eAAe,MAAA;AACjB,QAAI,YAAY;AACd;AAMF,SAAI,GAAA,2CAAqB,MAAQ;AAC/B,gBAAU,UAAU;;AAEpB,gBAAU,UAAU;AAEtB,eAAA;EACF;AAEA,MAAI,aAAa,MAAA;AACf,QAAI,YAAY;AACd;AAGF,cAAU,UAAU;AACpB,cAAU,UAAU;AACpB,eAAA;EACF;AAEA,MAAI,eAAe,MAAA;AAEjB,cAAU,UAAU;AACpB,cAAU,UAAU;AACpB,eAAW,IAAA;EACb;AAEA,MAAI,UAAU,MAAA;AACZ,QAAI,aAAY,GAAA,2CAAa;AAC7B,QAAI,WAAW;AACb,gBAAU,UAAU;AACpB,iBAAA;IACF;EACF;AAEA,MAAI,SAAS,MAAA;AACX,cAAU,UAAU;AACpB,cAAU,UAAU;AACpB,eAAW,IAAA;EACb;AAEA,MAAI,EAAA,WAAW,KAAI,GAAA,2CAAS;;;;EAI5B,CAAA;AAEA,MAAI,EAAA,eAAe,KAAI,GAAA,2CAAa;;;;EAIpC,GAAG,GAAA;AAEH,SAAO;IACL,cAAc;MACZ,oBAAoB,MAAM,SAAS,YAAY;MAC/C,IAAG,GAAA,2CAAW,gBAAgB,YAAY;QACxC,eAAe;QACf,WAAW;QACX,UAAU;MACZ,CAAA;IACF;IACA,cAAc;MACZ,IAAI;IACN;EACF;AACF;;;AJxIA,IAAAC,iBAA6C;;;;AKG7C,SAAS,2BAAQ,OAAmB;AAClC,SAAO;AACT;AAEA,2BAAK,oBAAoB,UAAU,kBAAqB,OAAqB,SAAY;AACvF,MAAI,EAAA,YAAW,OAAO,SAAU,IAAI;AAEpC,MAAI,WAAW,MAAM,SAAS,MAAM;AACpC,MAAI,YAAY,MAAM,cAAc,OAAO,aAAa,WAAW,WAAW,OAAO,MAAM,YAAA,KAAiB;AAG5G,MAAI,CAAC,aAAa,EAAC,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,6BAA4B;AACtD,YAAQ,KAAK,wHAAA;AAGf,QAAM;IACJ,MAAM;IACN;;;IAGA,cAAc,MAAM,YAAA;IACpB,eAAe,oCAAc,KAAA;IAC7B,CAAC,aAAA;AACC,UAAI;AACF,iBAAS,SAAS;AAChB,gBAAM;YACJ,MAAM;YACN,OAAO;UACT;eAEO,OAAO;AAChB,YAAI,QAA0B,CAAA;AAC9B,SAAA,GAAA,cAAAC,SAAM,SAAS,QAAQ,UAAU,CAAA,UAAA;AAC/B,gBAAM,KAAK;YACT,MAAM;YACN,SAAS;UACX,CAAA;QACF,CAAA;AAEA,eAAO;MACT;IACF;EACF;AACF;AAEA,SAAS,oCAAiB,OAAmB;AAC3C,MAAI,MAAM,iBAAiB;AACzB,WAAO,MAAM;AAGf,MAAI,MAAM;AACR,WAAO;AAGT,MAAI,MAAM,UAAS,GAAA,cAAAA,SAAM,SAAS,MAAM,MAAM,QAAQ,IAAI;AACxD,WAAO;AAGT,SAAO;AACT;;;;AC3DA,SAAS,8BAAW,OAAsB;AACxC,SAAO;AACT;AAEA,8BAAQ,oBAAoB,UAAUC,mBAAqB,OAAsB;AAC/E,MAAI,EAAA,UAAS,OAAO,MAAO,IAAI;AAC/B,QAAM;IACJ,MAAM;IACN;IACA,eAAe;IACf,UAAU;IACV,cAAc,MAAM,YAAA;IACpB,CAAC,aAAA;AACC,UAAI,OAAO,aAAa,YAAY;AAClC,YAAI,CAAC;AACH,gBAAM,IAAI,MAAM,0DAAA;AAGlB,iBAAS,QAAQ;AACf,gBAAM;YACJ,MAAM;YACN,OAAO;YACP,UAAU;UACZ;MAEJ,OAAO;AACL,YAAIC,SAA0B,CAAA;AAC9B,SAAA,GAAA,cAAAC,SAAM,SAAS,QAAQ,UAAU,CAAA,UAAA;AAC/B,UAAAD,OAAM,KAAK;YACT,MAAM;YACN,SAAS;UACX,CAAA;QACF,CAAA;AAEA,eAAOA;MACT;IACF;EACF;AACF;;;;;;;;;ACnDA,IAAI,sBAAsB,CAAC,cAAc;AACvC,QAAM,UAAU;AAAA,IACd,KAAK;AAAA,MACH,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AACA,UAAQ,WAAW,OAAO,SAAS,QAAQ,SAAS,MAAM,CAAC;AAC7D;AACA,IAAI,uBAAuB,CAAC,cAAc;AACxC,QAAM,eAAe;AAAA,IACnB,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,aAAa;AAAA,EACf;AACA,SAAO,aAAa,SAAS;AAC/B;AAoBA,IAAI,oBAAoB,CAAC,kBAAkB,cAAc;AACvD,MAAI,UAAU,SAAS,GAAG,GAAG;AAC3B,UAAM,CAAC,EAAE,QAAQ,IAAI,UAAU,MAAM,GAAG;AACxC,WAAO,GAAG,gBAAgB,IAAI,QAAQ;AAAA,EACxC;AACA,SAAO;AACT;;;AP9EA,SAAS,WAAW,eAAe;AACjC,MAAI,IAAI;AACR,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,QAAQ,WAAW;AACjF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,WAAW,gBAAgB;AAAA,IAC3B,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,IACd;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA,4BAA4B;AAAA,IAC5B,qBAAqB,CAAC;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAY,MAAM;AACxB,QAAM,oBAAoB,MAAM,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK;AACrM,QAAM,QAAQ,0CAAuB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,cAAc,CAAC,YAAY;AACzB,sBAAgB,OAAO,SAAS,aAAa,OAAO;AACpD,UAAI,CAAC,SAAS;AACZ,mBAAW,OAAO,SAAS,QAAQ;AAAA,MACrC;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,iBAAa,uBAAO,IAAI;AAC9B,QAAM,iBAAa,uBAAO,IAAI;AAC9B,QAAM,gBAAY,sBAAM;AACxB,QAAM,SAAS,MAAM,UAAU,CAAC;AAChC;AAAA,IACE;AAAA,IACA;AAAA;AAAA,MAEE,aAAa,UAAU;AAAA;AAAA,EAE3B;AACA,QAAM,EAAE,cAAc,cAAc,oBAAoB,IAAI;AAAA,IAC1D;AAAA,MACE;AAAA,MACA,SAAS;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,aAAa,IAAI;AAAA,IACvB;AAAA,MACE;AAAA,MACA,GAAG,0CAAW,OAAO,mBAAmB;AAAA,IAC1C;AAAA,IACA;AAAA,EACF;AACA,QAAM;AAAA,IACJ,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI,0CAAmB;AAAA,IACrB;AAAA,IACA,WAAW;AAAA,IACX,WAAW,qBAAqB,aAAa;AAAA,IAC7C;AAAA,IACA,QAAQ,YAAY,SAAS,IAAI;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,sBAAoB,MAAM;AACxB,QAAI,CAAC,mBAAmB;AAAQ;AAChC,mBAAe;AAAA,EACjB,GAAG,kBAAkB;AACrB,QAAM,EAAE,aAAa,IAAI;AAAA,IACvB;AAAA,MACE;AAAA,MACA,SAAS,MAAM;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAQ;AAAA,IACZ,MAAM;AACJ,UAAI,KAAK,KAAK;AACd,aAAO,QAAQ;AAAA,QACb,GAAG;AAAA,QACH;AAAA,QACA,SAAS,MAAM,iBAAiB,OAAO,SAAS,cAAc,WAAW,OAAO,MAAM;AAAA,QACtF,OAAO,MAAM,iBAAiB,OAAO,SAAS,cAAc,SAAS,OAAO,MAAM;AAAA,QAClF,SAAS,KAAK,iBAAiB,OAAO,SAAS,cAAc,WAAW,OAAO,KAAK;AAAA,MACtF,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE,aAAa,YAAY;AAAA,MACzB;AAAA,MACA,iBAAiB,OAAO,SAAS,cAAc;AAAA,MAC/C,iBAAiB,OAAO,SAAS,cAAc;AAAA,MAC/C,iBAAiB,OAAO,SAAS,cAAc;AAAA,IACjD;AAAA,EACF;AACA,QAAM,sBAAkB;AAAA,IACtB,CAAC,SAAS,CAAC,GAAG,OAAO,UAAU;AAAA,MAC7B,GAAG,0CAAW,cAAc,MAAM;AAAA,MAClC,KAAK,UAAU,MAAM,UAAU;AAAA,MAC/B,oBAAoB,SAAS,YAAY;AAAA,IAC3C;AAAA,IACA,CAAC,cAAc,QAAQ,WAAW,KAAK;AAAA,EACzC;AACA,QAAM,sBAAkB;AAAA,IACtB,OAAO;AAAA,MACL,KAAK;AAAA,MACL,aAAa;AAAA,MACb,aAAa,SAAS,MAAM;AAAA,MAC5B,cAAc,SAAS,SAAS;AAAA,MAChC,iBAAiB,SAAS,UAAU;AAAA,MACpC,kBAAkB,kBAAkB,aAAa,OAAO,aAAa;AAAA,MACrE,GAAG,0CAAW,cAAc,cAAc,UAAU;AAAA,MACpD,OAAO,0CAAW,cAAc,OAAO,WAAW,OAAO,MAAM,KAAK;AAAA,MACpE,WAAW,MAAM,KAAK,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,KAAK,CAAC;AAAA,MAC9E,IAAI;AAAA,IACN;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,6BAAyB;AAAA,IAC7B,OAAO;AAAA,MACL,aAAa;AAAA,MACb,aAAa,SAAS,MAAM;AAAA,MAC5B,cAAc,SAAS,SAAS;AAAA,MAChC,iBAAiB,SAAS,UAAU;AAAA,MACpC,kBAAkB,kBAAkB,aAAa,OAAO,aAAa;AAAA,MACrE,WAAW,MAAM,QAAQ,EAAE,OAAO,KAAK,cAAc,OAAO,SAAS,WAAW,SAAS,SAAS,EAAE,CAAC;AAAA,IACvG;AAAA,IACA,CAAC,OAAO,QAAQ,WAAW,YAAY,WAAW,eAAe,UAAU;AAAA,EAC7E;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AQ/LA,IAAAE,iBAAuD;AAGvD,yBAAoC;AACpC,IAAI,eAAe,MAAM,OAAO,oBAAuB,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAO;AAClF,IAAI,UAAU,WAAW,CAAC,OAAO,QAAQ;AACvC,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAW;AAAA,IACb,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AACF,UAAM,cAAc,wBAAS,MAAM,QAAQ;AAC3C,QAAI,gBAAgB;AAAG,YAAM,IAAI,MAAM;AACvC,QAAI,KAAC,+BAAe,QAAQ,GAAG;AAC7B,oBAA0B,wBAAI,KAAK,EAAE,GAAG,gBAAgB,GAAG,SAAS,CAAC;AAAA,IACvE,OAAO;AACL,YAAM,QAAQ;AACd,YAAM,YAAY,KAAK,MAAM,MAAM,QAAQ,OAAO,KAAK,MAAM;AAC7D,oBAAU,6BAAa,OAAO,gBAAgB,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtE;AAAA,EACF,QAAQ;AACN,kBAA0B,wBAAI,QAAQ,CAAC,CAAC;AACxC,SAAK,iEAAiE;AAAA,EACxE;AACA,QAAM,EAAE,KAAK,YAAY,IAAI,OAAO,GAAG,kBAAkB,IAAI,gBAAgB;AAC7E,QAAM,sBAAkC,wBAAI,OAAO,EAAE,KAAK,YAAY,IAAI,OAAO,cAA0B;AAAA,IACzG,EAAE;AAAA,IACF;AAAA,MACE,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU,oBAAoB;AAAA,MAC9B,GAAG,0CAAW,aAAa,iBAAiB;AAAA,MAC5C,OAAO;AAAA,QACL,GAAG,oBAAoB,SAAS;AAAA,MAClC;AAAA,MACA,cAA0B,wBAAI,WAAW,EAAE,GAAG,uBAAuB,GAAG,UAAU,QAAQ,CAAC;AAAA,IAC7F;AAAA,IACA,GAAG,EAAE;AAAA,EACP,EAAE,GAAG,GAAG,EAAE,kBAAkB;AAC5B,aAAuB,yBAAK,6BAAU,EAAE,UAAU;AAAA,IAChD;AAAA,IACA,mBAAmB,cAA0B,wBAAI,2CAAkB,EAAE,iBAAiB,cAA0B,wBAAI,OAAO,EAAE,KAAK,YAAY,IAAI,OAAO,GAAG,mBAAmB,cAA0B,wBAAI,WAAW,EAAE,GAAG,uBAAuB,GAAG,UAAU,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,QAAoB,wBAAI,YAAY,EAAE,UAAU,cAAc,cAA0B,wBAAI,iBAAiB,EAAE,UAAU,cAA0B,wBAAI,2CAAkB,EAAE,iBAAiB,UAAU,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,EAC/e,EAAE,CAAC;AACL,CAAC;AACD,QAAQ,cAAc;AACtB,IAAI,kBAAkB;;;AT3DtB,IAAAC,sBAA0B;AAC1B,IAAI,UAAU,WAAW,CAAC,OAAO,QAAQ;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAA2B,yBAAI,gBAAgB,CAAC,CAAC;AAAA,IACjD,gBAA4B,yBAAI,iBAAiB,CAAC,CAAC;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAW,EAAE,GAAG,OAAO,IAAI,CAAC;AAChC,QAAM,qBAAiB;AAAA,IACrB,CAAC,EAAE,UAAU,UAAU,UAAsB,yBAAI,iBAAS,EAAE,GAAG,cAAc,YAAY,UAAU,aAAa,YAAY,UAAU,UAAU,CAAC;AAAA,IACjJ,CAAC,aAAa,YAAY,CAAC;AAAA,EAC7B;AACA,QAAM,eAAW,wBAAQ,MAAM;AAC7B,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,iBAAa,6BAAa,WAAW,EAAE,WAAW,MAAM,UAAU,EAAE,CAAC;AAC7F,UAAM,iBAAiB,gBAAY,6BAAa,UAAU,EAAE,WAAW,MAAM,SAAS,EAAE,CAAC;AACzF,UAAM,iBAA6B,0BAAK,gBAAQ,EAAE,GAAG,mBAAmB,GAAG,UAAU;AAAA,MACnF;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AACH,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AACA,eAAuB,yBAAI,gBAAgB,EAAE,UAAU,WAAW,CAAC;AAAA,EACrE,GAAG;AAAA,IACD;AAAA,IACA,cAAc,OAAO,SAAS,WAAW;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,iBAAa,wBAAQ,MAAM;AAC/B,QAAI,eAAe,YAAY,MAAM,QAAQ,QAAQ,GAAG;AACtD,iBAAuB,yBAAI,OAAO,EAAE,WAAW,MAAM,QAAQ,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,QAAQ,CAAC,GAAG,UAAU,SAAS,IAAI,CAAC,GAAG,cAA0B,0BAAK,OAAO,EAAE,WAAW,MAAM,IAAI,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,IAAI,CAAC,GAAG,UAAU;AAAA,QACrR,CAAC,kBAA8B,yBAAI,QAAQ,EAAE,WAAW,MAAM,OAAO,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,OAAO,CAAC,GAAG,UAAU,aAAa,CAAC;AAAA,QAC1J;AAAA,MACF,EAAE,GAAG,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1B;AACA,eAAuB,0BAAK,OAAO,EAAE,KAAK,QAAQ,WAAW,MAAM,IAAI,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,IAAI,CAAC,GAAG,UAAU;AAAA,MACzI,CAAC,kBAA8B,yBAAI,QAAQ,EAAE,WAAW,MAAM,OAAO,EAAE,OAAO,cAAc,OAAO,SAAS,WAAW,OAAO,CAAC,GAAG,UAAU,aAAa,CAAC;AAAA,MAC1J;AAAA,IACF,EAAE,CAAC;AAAA,EACL,GAAG,CAAC,UAAU,YAAY,aAAa,cAAc,cAAc,OAAO,SAAS,WAAW,KAAK,KAAK,CAAC;AACzG,aAAuB,0BAAK,WAAW,EAAE,KAAK,QAAQ,GAAG,gBAAgB,GAAG,UAAU;AAAA,IACpF;AAAA,IACA;AAAA,EACF,EAAE,CAAC;AACL,CAAC;AACD,QAAQ,cAAc;AACtB,IAAI,kBAAkB;", "names": ["import_react", "import_react", "import_react", "$hnMvi$useCallback", "$50cCT$useMemo", "$50cCT$useRef", "$50cCT$useEffect", "$6VwSn$useRef", "$6VwSn$useEffect", "import_react", "$6Fm0V$react", "getCollectionNode", "items", "$gtysd$react", "import_react", "import_jsx_runtime"]}
import {
  useSafeLayoutEffect
} from "./chunk-7U5HACHX.js";
import {
  $3017fa7ffdddec74$export$8042c6c013fd5226
} from "./chunk-5TMR7R2E.js";
import {
  $f7dceffc5ad7768b$export$4e328f61c538687f,
  mergeRefs,
  useProviderContext
} from "./chunk-QXCXY6UY.js";
import {
  $3ef42575df84b30b$export$9d1611c77c2fe928,
  $6179b936705e76d3$export$ae780daf29e6d456,
  $65484d02dcb7eb3e$export$457c3d6518dd4c6f,
  $99facab73266f662$export$5add1d006293d136,
  $f645667febf57a63$export$4c014de7c8940b4c,
  $f6c31cce2adf654f$export$45712eceda6fad21,
  $ff5963eb1fccf552$export$e08e3b67e392101e
} from "./chunk-EWPKFISU.js";
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-5K7QB4ZL.js";
import {
  clsx,
  dataAttr,
  objectToDeps,
  toggle
} from "./chunk-CMXIESOC.js";
import {
  require_jsx_runtime
} from "./chunk-HVLLINLV.js";
import {
  require_react
} from "./chunk-HKLPI2XQ.js";
import {
  __toESM
} from "./chunk-ZS7NZCD4.js";

// node_modules/@heroui/switch/dist/chunk-K534ZJ2B.mjs
var import_react = __toESM(require_react(), 1);

// node_modules/@react-aria/toggle/dist/useToggle.mjs
function $d2c8e2b0480f3f34$export$cbe85ee05b554577(props, state, ref) {
  let { isDisabled = false, isReadOnly = false, value, name, children, "aria-label": ariaLabel, "aria-labelledby": ariaLabelledby, validationState = "valid", isInvalid } = props;
  let onChange = (e) => {
    e.stopPropagation();
    state.setSelected(e.target.checked);
  };
  let hasChildren = children != null;
  let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;
  if (!hasChildren && !hasAriaLabel && true)
    console.warn("If you do not provide children, you must specify an aria-label for accessibility");
  let { pressProps, isPressed } = (0, $f6c31cce2adf654f$export$45712eceda6fad21)({
    isDisabled
  });
  let { pressProps: labelProps, isPressed: isLabelPressed } = (0, $f6c31cce2adf654f$export$45712eceda6fad21)({
    onPress() {
      var _ref_current;
      state.toggle();
      (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.focus();
    },
    isDisabled: isDisabled || isReadOnly
  });
  let { focusableProps } = (0, $f645667febf57a63$export$4c014de7c8940b4c)(props, ref);
  let interactions = (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(pressProps, focusableProps);
  let domProps = (0, $65484d02dcb7eb3e$export$457c3d6518dd4c6f)(props, {
    labelable: true
  });
  (0, $99facab73266f662$export$5add1d006293d136)(ref, state.isSelected, state.setSelected);
  return {
    labelProps: (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(labelProps, {
      onClick: (e) => e.preventDefault()
    }),
    inputProps: (0, $3ef42575df84b30b$export$9d1611c77c2fe928)(domProps, {
      "aria-invalid": isInvalid || validationState === "invalid" || void 0,
      "aria-errormessage": props["aria-errormessage"],
      "aria-controls": props["aria-controls"],
      "aria-readonly": isReadOnly || void 0,
      onChange,
      disabled: isDisabled,
      ...value == null ? {} : {
        value
      },
      name,
      type: "checkbox",
      ...interactions
    }),
    isSelected: state.isSelected,
    isPressed: isPressed || isLabelPressed,
    isDisabled,
    isReadOnly,
    isInvalid: isInvalid || validationState === "invalid"
  };
}

// node_modules/@react-aria/switch/dist/useSwitch.mjs
function $b418ec0c85c52f27$export$d853f7095ae95f88(props, state, ref) {
  let { labelProps, inputProps, isSelected, isPressed, isDisabled, isReadOnly } = (0, $d2c8e2b0480f3f34$export$cbe85ee05b554577)(props, state, ref);
  return {
    labelProps,
    inputProps: {
      ...inputProps,
      role: "switch",
      checked: isSelected
    },
    isSelected,
    isPressed,
    isDisabled,
    isReadOnly
  };
}

// node_modules/@heroui/switch/dist/chunk-K534ZJ2B.mjs
var import_react2 = __toESM(require_react(), 1);
function useSwitch(originalProps = {}) {
  var _a, _b;
  const globalContext = useProviderContext();
  const [props, variantProps] = mapPropsVariants(originalProps, toggle.variantKeys);
  const {
    ref,
    as,
    name,
    value = "",
    isReadOnly: isReadOnlyProp = false,
    autoFocus = false,
    startContent,
    endContent,
    defaultSelected,
    isSelected: isSelectedProp,
    children,
    thumbIcon,
    className,
    classNames,
    onChange,
    onValueChange,
    ...otherProps
  } = props;
  const Component = as || "label";
  const domRef = (0, import_react.useRef)(null);
  const inputRef = (0, import_react.useRef)(null);
  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const labelId = (0, import_react.useId)();
  const ariaSwitchProps = (0, import_react2.useMemo)(() => {
    const ariaLabel = otherProps["aria-label"] || typeof children === "string" ? children : void 0;
    return {
      name,
      value,
      children,
      autoFocus,
      defaultSelected,
      isSelected: isSelectedProp,
      isDisabled: !!originalProps.isDisabled,
      isReadOnly: isReadOnlyProp,
      "aria-label": ariaLabel,
      "aria-labelledby": otherProps["aria-labelledby"] || labelId,
      onChange: onValueChange
    };
  }, [
    value,
    name,
    labelId,
    children,
    autoFocus,
    isReadOnlyProp,
    isSelectedProp,
    defaultSelected,
    originalProps.isDisabled,
    otherProps["aria-label"],
    otherProps["aria-labelledby"],
    onValueChange
  ]);
  const state = $3017fa7ffdddec74$export$8042c6c013fd5226(ariaSwitchProps);
  useSafeLayoutEffect(() => {
    if (!inputRef.current)
      return;
    const isInputRefChecked = !!inputRef.current.checked;
    state.setSelected(isInputRefChecked);
  }, [inputRef.current]);
  const { inputProps, isPressed, isReadOnly } = $b418ec0c85c52f27$export$d853f7095ae95f88(ariaSwitchProps, state, inputRef);
  const { focusProps, isFocused, isFocusVisible } = $f7dceffc5ad7768b$export$4e328f61c538687f({ autoFocus: inputProps.autoFocus });
  const { hoverProps, isHovered } = $6179b936705e76d3$export$ae780daf29e6d456({
    isDisabled: inputProps.disabled
  });
  const isInteractionDisabled = ariaSwitchProps.isDisabled || isReadOnly;
  const pressed = isInteractionDisabled ? false : isPressed;
  const isSelected = inputProps.checked;
  const isDisabled = inputProps.disabled;
  const slots = (0, import_react2.useMemo)(
    () => toggle({
      ...variantProps,
      disableAnimation
    }),
    [objectToDeps(variantProps), disableAnimation]
  );
  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);
  const getBaseProps = (props2) => {
    return {
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(hoverProps, otherProps, props2),
      ref: domRef,
      className: slots.base({ class: clsx(baseStyles, props2 == null ? void 0 : props2.className) }),
      "data-disabled": dataAttr(isDisabled),
      "data-selected": dataAttr(isSelected),
      "data-readonly": dataAttr(isReadOnly),
      "data-focus": dataAttr(isFocused),
      "data-focus-visible": dataAttr(isFocusVisible),
      "data-hover": dataAttr(isHovered),
      "data-pressed": dataAttr(pressed)
    };
  };
  const getWrapperProps = (0, import_react.useCallback)(
    (props2 = {}) => {
      return {
        ...props2,
        "aria-hidden": true,
        className: clsx(slots.wrapper({ class: clsx(classNames == null ? void 0 : classNames.wrapper, props2 == null ? void 0 : props2.className) }))
      };
    },
    [slots, classNames == null ? void 0 : classNames.wrapper]
  );
  const getInputProps = (props2 = {}) => {
    return {
      ...$3ef42575df84b30b$export$9d1611c77c2fe928(inputProps, focusProps, props2),
      ref: mergeRefs(inputRef, ref),
      id: inputProps.id,
      className: slots.hiddenInput({ class: classNames == null ? void 0 : classNames.hiddenInput }),
      onChange: $ff5963eb1fccf552$export$e08e3b67e392101e(onChange, inputProps.onChange)
    };
  };
  const getThumbProps = (0, import_react.useCallback)(
    (props2 = {}) => ({
      ...props2,
      className: slots.thumb({ class: clsx(classNames == null ? void 0 : classNames.thumb, props2 == null ? void 0 : props2.className) })
    }),
    [slots, classNames == null ? void 0 : classNames.thumb]
  );
  const getLabelProps = (0, import_react.useCallback)(
    (props2 = {}) => ({
      ...props2,
      id: labelId,
      className: slots.label({ class: clsx(classNames == null ? void 0 : classNames.label, props2 == null ? void 0 : props2.className) })
    }),
    [slots, classNames == null ? void 0 : classNames.label, isDisabled, isSelected]
  );
  const getThumbIconProps = (0, import_react.useCallback)(
    (props2 = {
      includeStateProps: false
    }) => $3ef42575df84b30b$export$9d1611c77c2fe928(
      {
        width: "1em",
        height: "1em",
        className: slots.thumbIcon({ class: clsx(classNames == null ? void 0 : classNames.thumbIcon) })
      },
      props2.includeStateProps ? {
        isSelected
      } : {}
    ),
    [slots, classNames == null ? void 0 : classNames.thumbIcon, isSelected]
  );
  const getStartContentProps = (0, import_react.useCallback)(
    (props2 = {}) => ({
      width: "1em",
      height: "1em",
      ...props2,
      className: slots.startContent({ class: clsx(classNames == null ? void 0 : classNames.startContent, props2 == null ? void 0 : props2.className) })
    }),
    [slots, classNames == null ? void 0 : classNames.startContent, isSelected]
  );
  const getEndContentProps = (0, import_react.useCallback)(
    (props2 = {}) => ({
      width: "1em",
      height: "1em",
      ...props2,
      className: slots.endContent({ class: clsx(classNames == null ? void 0 : classNames.endContent, props2 == null ? void 0 : props2.className) })
    }),
    [slots, classNames == null ? void 0 : classNames.endContent, isSelected]
  );
  return {
    Component,
    slots,
    classNames,
    domRef,
    children,
    thumbIcon,
    startContent,
    endContent,
    isHovered,
    isSelected,
    isPressed: pressed,
    isFocused,
    isFocusVisible,
    isDisabled,
    getBaseProps,
    getWrapperProps,
    getInputProps,
    getLabelProps,
    getThumbProps,
    getThumbIconProps,
    getStartContentProps,
    getEndContentProps
  };
}

// node_modules/@heroui/switch/dist/chunk-ECZ3LCTI.mjs
var import_react3 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Switch = forwardRef((props, ref) => {
  const {
    Component,
    children,
    startContent,
    endContent,
    thumbIcon,
    getBaseProps,
    getInputProps,
    getWrapperProps,
    getThumbProps,
    getThumbIconProps,
    getLabelProps,
    getStartContentProps,
    getEndContentProps
  } = useSwitch({ ...props, ref });
  const clonedThumbIcon = typeof thumbIcon === "function" ? thumbIcon(getThumbIconProps({ includeStateProps: true })) : thumbIcon && (0, import_react3.cloneElement)(thumbIcon, getThumbIconProps());
  const clonedStartContent = startContent && (0, import_react3.cloneElement)(startContent, getStartContentProps());
  const clonedEndContent = endContent && (0, import_react3.cloneElement)(endContent, getEndContentProps());
  return (0, import_jsx_runtime.jsxs)(Component, { ...getBaseProps(), children: [
    (0, import_jsx_runtime.jsx)("input", { ...getInputProps() }),
    (0, import_jsx_runtime.jsxs)("span", { ...getWrapperProps(), children: [
      startContent && clonedStartContent,
      (0, import_jsx_runtime.jsx)("span", { ...getThumbProps(), children: thumbIcon && clonedThumbIcon }),
      endContent && clonedEndContent
    ] }),
    children && (0, import_jsx_runtime.jsx)("span", { ...getLabelProps(), children })
  ] });
});
Switch.displayName = "HeroUI.Switch";
var switch_default = Switch;

export {
  $d2c8e2b0480f3f34$export$cbe85ee05b554577,
  useSwitch,
  switch_default
};
//# sourceMappingURL=chunk-Z6MMXBNZ.js.map

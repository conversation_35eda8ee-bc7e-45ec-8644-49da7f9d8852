import {
  require_react
} from "./chunk-XEXUAUZA.js";
import {
  __toESM
} from "./chunk-LQ2VYIYD.js";

// node_modules/@heroui/use-safe-layout-effect/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var useSafeLayoutEffect = Boolean(globalThis == null ? void 0 : globalThis.document) ? import_react.useLayoutEffect : import_react.useEffect;

export {
  useSafeLayoutEffect
};
//# sourceMappingURL=chunk-4HNJ6BCR.js.map

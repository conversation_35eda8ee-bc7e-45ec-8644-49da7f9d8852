{"version": 3, "sources": ["../../@heroui/link/dist/chunk-7LH7ZARU.mjs", "../../@heroui/use-aria-link/dist/index.mjs", "../../@heroui/link/dist/chunk-SGLWUJCW.mjs", "../../@heroui/link/dist/chunk-T45N425O.mjs"], "sourcesContent": ["\"use client\";\n\n// src/link-icon.tsx\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar LinkIcon = () => /* @__PURE__ */ jsxs(\n  \"svg\",\n  {\n    \"aria-hidden\": \"true\",\n    className: \"flex mx-1 text-current self-center\",\n    fill: \"none\",\n    height: \"1em\",\n    shapeRendering: \"geometricPrecision\",\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: \"1.5\",\n    viewBox: \"0 0 24 24\",\n    width: \"1em\",\n    children: [\n      /* @__PURE__ */ jsx(\"path\", { d: \"M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6\" }),\n      /* @__PURE__ */ jsx(\"path\", { d: \"M15 3h6v6\" }),\n      /* @__PURE__ */ jsx(\"path\", { d: \"M10 14L21 3\" })\n    ]\n  }\n);\n\nexport {\n  LinkIcon\n};\n", "// src/index.ts\nimport {\n  filterDOMProps,\n  mergeProps,\n  useRouter,\n  shouldClientNavigate,\n  useLinkProps\n} from \"@react-aria/utils\";\nimport { useFocusable } from \"@react-aria/focus\";\nimport { usePress } from \"@react-aria/interactions\";\nfunction useAriaLink(props, ref) {\n  let {\n    elementType = \"a\",\n    onPress,\n    onPressStart,\n    onPressEnd,\n    onClick,\n    isDisabled,\n    ...otherProps\n  } = props;\n  let linkProps = {};\n  if (elementType !== \"a\") {\n    linkProps = {\n      role: \"link\",\n      tabIndex: !isDisabled ? 0 : void 0\n    };\n  }\n  let { focusableProps } = useFocusable(props, ref);\n  let { pressProps, isPressed } = usePress({\n    onClick,\n    onPress,\n    onPressStart,\n    onPressEnd,\n    isDisabled,\n    ref\n  });\n  let domProps = filterDOMProps(otherProps, { labelable: true, isLink: elementType === \"a\" });\n  let interactionHandlers = mergeProps(focusableProps, pressProps);\n  let router = useRouter();\n  let routerLinkProps = useLinkProps(props);\n  return {\n    isPressed,\n    // Used to indicate press state for visual\n    linkProps: mergeProps(domProps, routerLinkProps, {\n      ...interactionHandlers,\n      ...linkProps,\n      \"aria-disabled\": isDisabled || void 0,\n      \"aria-current\": props[\"aria-current\"],\n      onClick: (e) => {\n        var _a;\n        (_a = pressProps.onClick) == null ? void 0 : _a.call(pressProps, e);\n        if (!router.isNative && e.currentTarget instanceof HTMLAnchorElement && e.currentTarget.href && // If props are applied to a router Link component, it may have already prevented default.\n        !e.isDefaultPrevented() && shouldClientNavigate(e.currentTarget, e) && props.href) {\n          e.preventDefault();\n          router.open(e.currentTarget, e, props.href, props.routerOptions);\n        }\n      }\n    })\n  };\n}\nexport {\n  useAriaLink\n};\n", "\"use client\";\n\n// src/use-link.ts\nimport { link } from \"@heroui/theme\";\nimport { useAriaLink } from \"@heroui/use-aria-link\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { dataAttr, objectToDeps } from \"@heroui/shared-utils\";\nimport { useMemo, useCallback } from \"react\";\nimport { mergeProps } from \"@react-aria/utils\";\nfunction useLink(originalProps) {\n  var _a, _b, _c, _d;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, link.variantKeys);\n  const {\n    ref,\n    as,\n    children,\n    anchorIcon,\n    isExternal = false,\n    showAnchorIcon = false,\n    autoFocus = false,\n    className,\n    onPress,\n    onPressStart,\n    onPressEnd,\n    onClick,\n    ...otherProps\n  } = props;\n  const Component = as || \"a\";\n  const domRef = useDOMRef(ref);\n  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const { linkProps } = useAriaLink(\n    {\n      ...otherProps,\n      onPress,\n      onPressStart,\n      onPressEnd,\n      // @ts-ignore React Aria Link does accept onClick as a prop but it's not in the types\n      onClick,\n      isDisabled: originalProps.isDisabled,\n      elementType: `${as}`\n    },\n    domRef\n  );\n  const { isFocused, isFocusVisible, focusProps } = useFocusRing({\n    autoFocus\n  });\n  if (isExternal) {\n    otherProps.rel = (_c = otherProps.rel) != null ? _c : \"noopener noreferrer\";\n    otherProps.target = (_d = otherProps.target) != null ? _d : \"_blank\";\n  }\n  const styles = useMemo(\n    () => link({\n      ...variantProps,\n      disableAnimation,\n      className\n    }),\n    [objectToDeps(variantProps), disableAnimation, className]\n  );\n  const getLinkProps = useCallback(() => {\n    return {\n      ref: domRef,\n      className: styles,\n      \"data-focus\": dataAttr(isFocused),\n      \"data-disabled\": dataAttr(originalProps.isDisabled),\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      ...mergeProps(focusProps, linkProps, otherProps)\n    };\n  }, [styles, isFocused, isFocusVisible, focusProps, linkProps, otherProps]);\n  return { Component, children, anchorIcon, showAnchorIcon, getLinkProps };\n}\n\nexport {\n  useLink\n};\n", "\"use client\";\nimport {\n  useLink\n} from \"./chunk-SGLWUJCW.mjs\";\n\n// src/link.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { LinkIcon } from \"@heroui/shared-icons\";\nimport { linkAnchorClasses } from \"@heroui/theme\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar Link = forwardRef((props, ref) => {\n  const {\n    Component,\n    children,\n    showAnchorIcon,\n    anchorIcon = /* @__PURE__ */ jsx(LinkIcon, { className: linkAnchorClasses }),\n    getLinkProps\n  } = useLink({\n    ref,\n    ...props\n  });\n  return /* @__PURE__ */ jsx(Component, { ...getLinkProps(), children: /* @__PURE__ */ jsxs(Fragment, { children: [\n    children,\n    showAnchorIcon && anchorIcon\n  ] }) });\n});\nLink.displayName = \"HeroUI.Link\";\nvar link_default = Link;\n\nexport {\n  link_default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,yBAA0B;AAC1B,IAAIA,YAAW,UAAsB;AAAA,EACnC;AAAA,EACA;AAAA,IACE,eAAe;AAAA,IACf,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,UACQ,wBAAI,QAAQ,EAAE,GAAG,uDAAuD,CAAC;AAAA,UACzE,wBAAI,QAAQ,EAAE,GAAG,YAAY,CAAC;AAAA,UAC9B,wBAAI,QAAQ,EAAE,GAAG,cAAc,CAAC;AAAA,IAClD;AAAA,EACF;AACF;;;ACdA,SAAS,YAAY,OAAO,KAAK;AAC/B,MAAI;AAAA,IACF,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,YAAY,CAAC;AACjB,MAAI,gBAAgB,KAAK;AACvB,gBAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU,CAAC,aAAa,IAAI;AAAA,IAC9B;AAAA,EACF;AACA,MAAI,EAAE,eAAe,IAAI,0CAAa,OAAO,GAAG;AAChD,MAAI,EAAE,YAAY,UAAU,IAAI,0CAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,WAAW,0CAAe,YAAY,EAAE,WAAW,MAAM,QAAQ,gBAAgB,IAAI,CAAC;AAC1F,MAAI,sBAAsB,0CAAW,gBAAgB,UAAU;AAC/D,MAAI,SAAS,0CAAU;AACvB,MAAI,kBAAkB,0CAAa,KAAK;AACxC,SAAO;AAAA,IACL;AAAA;AAAA,IAEA,WAAW,0CAAW,UAAU,iBAAiB;AAAA,MAC/C,GAAG;AAAA,MACH,GAAG;AAAA,MACH,iBAAiB,cAAc;AAAA,MAC/B,gBAAgB,MAAM,cAAc;AAAA,MACpC,SAAS,CAAC,MAAM;AACd,YAAI;AACJ,SAAC,KAAK,WAAW,YAAY,OAAO,SAAS,GAAG,KAAK,YAAY,CAAC;AAClE,YAAI,CAAC,OAAO,YAAY,EAAE,yBAAyB,qBAAqB,EAAE,cAAc;AAAA,QACxF,CAAC,EAAE,mBAAmB,KAAK,0CAAqB,EAAE,eAAe,CAAC,KAAK,MAAM,MAAM;AACjF,YAAE,eAAe;AACjB,iBAAO,KAAK,EAAE,eAAe,GAAG,MAAM,MAAM,MAAM,aAAa;AAAA,QACjE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AClDA,mBAAqC;AAErC,SAAS,QAAQ,eAAe;AAC9B,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,CAAC,OAAO,YAAY,IAAI,iBAAiB,eAAe,KAAK,WAAW;AAC9E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,YAAY,MAAM;AACxB,QAAM,SAAS,UAAU,GAAG;AAC5B,QAAM,oBAAoB,MAAM,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK,iBAAiB,OAAO,SAAS,cAAc,qBAAqB,OAAO,KAAK;AACrM,QAAM,EAAE,UAAU,IAAI;AAAA,IACpB;AAAA,MACE,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,YAAY,cAAc;AAAA,MAC1B,aAAa,GAAG,EAAE;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA,QAAM,EAAE,WAAW,gBAAgB,WAAW,IAAI,0CAAa;AAAA,IAC7D;AAAA,EACF,CAAC;AACD,MAAI,YAAY;AACd,eAAW,OAAO,KAAK,WAAW,QAAQ,OAAO,KAAK;AACtD,eAAW,UAAU,KAAK,WAAW,WAAW,OAAO,KAAK;AAAA,EAC9D;AACA,QAAM,aAAS;AAAA,IACb,MAAM,KAAK;AAAA,MACT,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,CAAC,aAAa,YAAY,GAAG,kBAAkB,SAAS;AAAA,EAC1D;AACA,QAAM,mBAAe,0BAAY,MAAM;AACrC,WAAO;AAAA,MACL,KAAK;AAAA,MACL,WAAW;AAAA,MACX,cAAc,SAAS,SAAS;AAAA,MAChC,iBAAiB,SAAS,cAAc,UAAU;AAAA,MAClD,sBAAsB,SAAS,cAAc;AAAA,MAC7C,GAAG,0CAAW,YAAY,WAAW,UAAU;AAAA,IACjD;AAAA,EACF,GAAG,CAAC,QAAQ,WAAW,gBAAgB,YAAY,WAAW,UAAU,CAAC;AACzE,SAAO,EAAE,WAAW,UAAU,YAAY,gBAAgB,aAAa;AACzE;;;AC/DA,IAAAC,sBAAoC;AACpC,IAAI,OAAO,WAAW,CAAC,OAAO,QAAQ;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAA6B,yBAAI,UAAU,EAAE,WAAW,kBAAkB,CAAC;AAAA,IAC3E;AAAA,EACF,IAAI,QAAQ;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACD,aAAuB,yBAAI,WAAW,EAAE,GAAG,aAAa,GAAG,cAA0B,0BAAK,8BAAU,EAAE,UAAU;AAAA,IAC9G;AAAA,IACA,kBAAkB;AAAA,EACpB,EAAE,CAAC,EAAE,CAAC;AACR,CAAC;AACD,KAAK,cAAc;AACnB,IAAI,eAAe;", "names": ["LinkIcon", "import_jsx_runtime"]}
import {
  forwardRef,
  mapPropsVariants
} from "./chunk-4XJAGZGH.js";
import {
  code,
  objectToDeps
} from "./chunk-X36EXHV7.js";
import {
  require_jsx_runtime
} from "./chunk-HVLLINLV.js";
import {
  require_react
} from "./chunk-HKLPI2XQ.js";
import {
  __toESM
} from "./chunk-ZS7NZCD4.js";

// node_modules/@heroui/code/dist/chunk-UDFNFZDA.mjs
var import_react = __toESM(require_react(), 1);
function useCode(originalProps) {
  const [props, variantProps] = mapPropsVariants(originalProps, code.variantKeys);
  const { as, children, className, ...otherProps } = props;
  const Component = as || "code";
  const styles = (0, import_react.useMemo)(
    () => code({
      ...variantProps,
      className
    }),
    [objectToDeps(variantProps), className]
  );
  const getCodeProps = () => {
    return {
      className: styles,
      ...otherProps
    };
  };
  return { Component, children, getCodeProps };
}

// node_modules/@heroui/code/dist/chunk-C3KKIFEX.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Code = forwardRef((props, ref) => {
  const { Component, children, getCodeProps } = useCode({ ...props });
  return (0, import_jsx_runtime.jsx)(Component, { ref, ...getCodeProps(), children });
});
Code.displayName = "HeroUI.Code";
var code_default = Code;

export {
  useCode,
  code_default
};
//# sourceMappingURL=chunk-AL5DU5D4.js.map

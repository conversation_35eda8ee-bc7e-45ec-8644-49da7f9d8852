import {
  require_react
} from "./chunk-HKLPI2XQ.js";
import {
  __toESM
} from "./chunk-ZS7NZCD4.js";

// node_modules/@heroui/use-safe-layout-effect/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var useSafeLayoutEffect = Boolean(globalThis == null ? void 0 : globalThis.document) ? import_react.useLayoutEffect : import_react.useEffect;

export {
  useSafeLayoutEffect
};
//# sourceMappingURL=chunk-7U5HACHX.js.map

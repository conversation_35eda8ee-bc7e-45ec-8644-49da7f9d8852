rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidImageFile() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    function isValidVideoFile() {
      return resource.contentType.matches('video/.*') &&
             resource.size < 100 * 1024 * 1024; // 100MB limit
    }
    
    function isValidFile() {
      return resource.size < 50 * 1024 * 1024; // 50MB limit for other files
    }

    // User profile images
    match /users/{userId}/avatar/{fileName} {
      // Users can read any user's avatar
      allow read: if isAuthenticated();
      
      // Users can only upload their own avatar
      allow write: if isOwner(userId) && isValidImageFile();
    }

    // User profile banners
    match /users/{userId}/banner/{fileName} {
      // Users can read any user's banner
      allow read: if isAuthenticated();
      
      // Users can only upload their own banner
      allow write: if isOwner(userId) && isValidImageFile();
    }

    // Post images
    match /posts/{postId}/images/{fileName} {
      // Anyone authenticated can read post images
      allow read: if isAuthenticated();
      
      // Users can upload images for their own posts
      allow write: if isAuthenticated() && isValidImageFile();
    }

    // Post videos
    match /posts/{postId}/videos/{fileName} {
      // Anyone authenticated can read post videos
      allow read: if isAuthenticated();
      
      // Users can upload videos for their own posts
      allow write: if isAuthenticated() && isValidVideoFile();
    }

    // Group images
    match /groups/{groupId}/avatar/{fileName} {
      // Anyone authenticated can read group avatars
      allow read: if isAuthenticated();
      
      // Only group moderators can upload group avatars
      allow write: if isAuthenticated() && isValidImageFile();
    }

    // Group banners
    match /groups/{groupId}/banner/{fileName} {
      // Anyone authenticated can read group banners
      allow read: if isAuthenticated();
      
      // Only group moderators can upload group banners
      allow write: if isAuthenticated() && isValidImageFile();
    }

    // Tournament images
    match /tournaments/{tournamentId}/images/{fileName} {
      // Anyone authenticated can read tournament images
      allow read: if isAuthenticated();
      
      // Tournament organizers can upload images
      allow write: if isAuthenticated() && isValidImageFile();
    }

    // Game images (admin only)
    match /games/{gameId}/{allPaths=**} {
      // Anyone authenticated can read game images
      allow read: if isAuthenticated();
      
      // Only admins can upload game images (handled server-side)
      allow write: if false;
    }

    // Achievement icons (admin only)
    match /achievements/{achievementId}/{allPaths=**} {
      // Anyone authenticated can read achievement icons
      allow read: if isAuthenticated();
      
      // Only admins can upload achievement icons (handled server-side)
      allow write: if false;
    }

    // Chat attachments (for future chat feature)
    match /messages/{messageId}/attachments/{fileName} {
      // Users can read attachments from messages they have access to
      allow read: if isAuthenticated();
      
      // Users can upload attachments to their own messages
      allow write: if isAuthenticated() && isValidFile();
    }

    // Temporary uploads (for processing)
    match /temp/{userId}/{fileName} {
      // Users can read their own temporary files
      allow read: if isOwner(userId);
      
      // Users can upload temporary files
      allow write: if isOwner(userId) && isValidFile();
    }

    // System files (admin only)
    match /system/{allPaths=**} {
      // Public read access for system files
      allow read: if true;
      
      // Only admins can write system files
      allow write: if false;
    }

    // Backup files (admin only)
    match /backups/{allPaths=**} {
      // No public access to backups
      allow read, write: if false;
    }
  }
}
